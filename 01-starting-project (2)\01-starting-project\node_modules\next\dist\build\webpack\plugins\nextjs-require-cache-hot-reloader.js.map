{"version": 3, "sources": ["../../../../build/webpack/plugins/nextjs-require-cache-hot-reloader.ts"], "sourcesContent": ["import { webpack } from 'next/dist/compiled/webpack/webpack'\nimport { isWebpack5 } from 'next/dist/compiled/webpack/webpack'\nimport { realpathSync } from 'fs'\nimport path from 'path'\n\nconst originModules = [\n  require.resolve('../../../server/require'),\n  require.resolve('../../../server/load-components'),\n]\n\nfunction deleteCache(filePath: string) {\n  try {\n    filePath = realpathSync(filePath)\n  } catch (e) {\n    if (e.code !== 'ENOENT') throw e\n  }\n  const module = require.cache[filePath]\n  if (module) {\n    // remove the child reference from the originModules\n    for (const originModule of originModules) {\n      const parent = require.cache[originModule]\n      if (parent) {\n        const idx = parent.children.indexOf(module)\n        if (idx >= 0) parent.children.splice(idx, 1)\n      }\n    }\n    // remove parent references from external modules\n    for (const child of module.children) {\n      child.parent = null\n    }\n  }\n  delete require.cache[filePath]\n}\n\nconst PLUGIN_NAME = 'NextJsRequireCacheHotReloader'\n\n// This plugin flushes require.cache after emitting the files. Providing 'hot reloading' of server files.\nexport class NextJsRequireCacheHotReloader implements webpack.Plugin {\n  prevAssets: any = null\n  previousOutputPathsWebpack5: Set<string> = new Set()\n  currentOutputPathsWebpack5: Set<string> = new Set()\n\n  apply(compiler: webpack.Compiler) {\n    if (isWebpack5) {\n      // @ts-ignored Webpack has this hooks\n      compiler.hooks.assetEmitted.tap(\n        PLUGIN_NAME,\n        (_file: any, { targetPath }: any) => {\n          this.currentOutputPathsWebpack5.add(targetPath)\n          deleteCache(targetPath)\n        }\n      )\n\n      compiler.hooks.afterEmit.tap(PLUGIN_NAME, (compilation) => {\n        const runtimeChunkPath = path.join(\n          compilation.outputOptions.path,\n          'webpack-runtime.js'\n        )\n        deleteCache(runtimeChunkPath)\n\n        // we need to make sure to clear all server entries from cache\n        // since they can have a stale webpack-runtime cache\n        // which needs to always be in-sync\n        const entries = [...compilation.entries.keys()].filter((entry) =>\n          entry.toString().startsWith('pages/')\n        )\n\n        entries.forEach((page) => {\n          const outputPath = path.join(\n            compilation.outputOptions.path,\n            page + '.js'\n          )\n          deleteCache(outputPath)\n        })\n      })\n\n      this.previousOutputPathsWebpack5 = new Set(\n        this.currentOutputPathsWebpack5\n      )\n      this.currentOutputPathsWebpack5.clear()\n      return\n    }\n\n    compiler.hooks.afterEmit.tapAsync(PLUGIN_NAME, (compilation, callback) => {\n      const { assets } = compilation\n\n      if (this.prevAssets) {\n        for (const f of Object.keys(assets)) {\n          deleteCache(assets[f].existsAt)\n        }\n        for (const f of Object.keys(this.prevAssets)) {\n          if (!assets[f]) {\n            deleteCache(this.prevAssets[f].existsAt)\n          }\n        }\n      }\n      this.prevAssets = assets\n\n      callback()\n    })\n  }\n}\n"], "names": [], "mappings": ";;;;AAC2B,GAAoC,CAApC,QAAoC;AAClC,GAAI,CAAJ,GAAI;AAChB,GAAM,CAAN,KAAM;;;;;;AAEvB,KAAK,CAAC,aAAa;IACjB,OAAO,CAAC,OAAO,EAAC,uBAAyB;IACzC,OAAO,CAAC,OAAO,EAAC,+BAAiC;;SAG1C,WAAW,CAAC,QAAgB,EAAE,CAAC;QAClC,CAAC;QACH,QAAQ,OAViB,GAAI,eAUL,QAAQ;IAClC,CAAC,QAAQ,CAAC,EAAE,CAAC;QACX,EAAE,EAAE,CAAC,CAAC,IAAI,MAAK,MAAQ,GAAE,KAAK,CAAC,CAAC;IAClC,CAAC;IACD,KAAK,CAAC,MAAM,GAAG,OAAO,CAAC,KAAK,CAAC,QAAQ;IACrC,EAAE,EAAE,MAAM,EAAE,CAAC;QACX,EAAoD,AAApD,kDAAoD;aAC/C,KAAK,CAAC,YAAY,IAAI,aAAa,CAAE,CAAC;YACzC,KAAK,CAAC,MAAM,GAAG,OAAO,CAAC,KAAK,CAAC,YAAY;YACzC,EAAE,EAAE,MAAM,EAAE,CAAC;gBACX,KAAK,CAAC,GAAG,GAAG,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,MAAM;gBAC1C,EAAE,EAAE,GAAG,IAAI,CAAC,EAAE,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,GAAG,EAAE,CAAC;YAC7C,CAAC;QACH,CAAC;QACD,EAAiD,AAAjD,+CAAiD;aAC5C,KAAK,CAAC,KAAK,IAAI,MAAM,CAAC,QAAQ,CAAE,CAAC;YACpC,KAAK,CAAC,MAAM,GAAG,IAAI;QACrB,CAAC;IACH,CAAC;WACM,OAAO,CAAC,KAAK,CAAC,QAAQ;AAC/B,CAAC;AAED,KAAK,CAAC,WAAW,IAAG,6BAA+B;MAGtC,6BAA6B;IAKxC,KAAK,CAAC,QAA0B,EAAE,CAAC;QACjC,EAAE,EA1CqB,QAAoC,aA0C3C,CAAC;YACf,EAAqC,AAArC,mCAAqC;YACrC,QAAQ,CAAC,KAAK,CAAC,YAAY,CAAC,GAAG,CAC7B,WAAW,GACV,KAAU,IAAI,UAAU,MAAY,CAAC;qBAC/B,0BAA0B,CAAC,GAAG,CAAC,UAAU;gBAC9C,WAAW,CAAC,UAAU;YACxB,CAAC;YAGH,QAAQ,CAAC,KAAK,CAAC,SAAS,CAAC,GAAG,CAAC,WAAW,GAAG,WAAW,GAAK,CAAC;gBAC1D,KAAK,CAAC,gBAAgB,GAnDb,KAAM,SAmDe,IAAI,CAChC,WAAW,CAAC,aAAa,CAAC,IAAI,GAC9B,kBAAoB;gBAEtB,WAAW,CAAC,gBAAgB;gBAE5B,EAA8D,AAA9D,4DAA8D;gBAC9D,EAAoD,AAApD,kDAAoD;gBACpD,EAAmC,AAAnC,iCAAmC;gBACnC,KAAK,CAAC,OAAO;uBAAO,WAAW,CAAC,OAAO,CAAC,IAAI;kBAAI,MAAM,EAAE,KAAK,GAC3D,KAAK,CAAC,QAAQ,GAAG,UAAU,EAAC,MAAQ;;gBAGtC,OAAO,CAAC,OAAO,EAAE,IAAI,GAAK,CAAC;oBACzB,KAAK,CAAC,UAAU,GAjET,KAAM,SAiEW,IAAI,CAC1B,WAAW,CAAC,aAAa,CAAC,IAAI,EAC9B,IAAI,IAAG,GAAK;oBAEd,WAAW,CAAC,UAAU;gBACxB,CAAC;YACH,CAAC;iBAEI,2BAA2B,GAAG,GAAG,CAAC,GAAG,MACnC,0BAA0B;iBAE5B,0BAA0B,CAAC,KAAK;;QAEvC,CAAC;QAED,QAAQ,CAAC,KAAK,CAAC,SAAS,CAAC,QAAQ,CAAC,WAAW,GAAG,WAAW,EAAE,QAAQ,GAAK,CAAC;YACzE,KAAK,GAAG,MAAM,MAAK,WAAW;YAE9B,EAAE,OAAO,UAAU,EAAE,CAAC;qBACf,KAAK,CAAC,CAAC,IAAI,MAAM,CAAC,IAAI,CAAC,MAAM,EAAG,CAAC;oBACpC,WAAW,CAAC,MAAM,CAAC,CAAC,EAAE,QAAQ;gBAChC,CAAC;qBACI,KAAK,CAAC,EAAC,IAAI,MAAM,CAAC,IAAI,MAAM,UAAU,EAAG,CAAC;oBAC7C,EAAE,GAAG,MAAM,CAAC,EAAC,GAAG,CAAC;wBACf,WAAW,MAAM,UAAU,CAAC,EAAC,EAAE,QAAQ;oBACzC,CAAC;gBACH,CAAC;YACH,CAAC;iBACI,UAAU,GAAG,MAAM;YAExB,QAAQ;QACV,CAAC;IACH,CAAC;;aA9DD,UAAU,GAAQ,IAAI;aACtB,2BAA2B,GAAgB,GAAG,CAAC,GAAG;aAClD,0BAA0B,GAAgB,GAAG,CAAC,GAAG;;;QAHtC,6BAA6B,GAA7B,6BAA6B"}