{"version": 3, "sources": ["../../../../build/webpack/loaders/next-swc-loader.js"], "sourcesContent": ["/*\nCopyright (c) 2017 The swc Project Developers\n\nPermission is hereby granted, free of charge, to any\nperson obtaining a copy of this software and associated\ndocumentation files (the \"Software\"), to deal in the\nSoftware without restriction, including without\nlimitation the rights to use, copy, modify, merge,\npublish, distribute, sublicense, and/or sell copies of\nthe Software, and to permit persons to whom the Software\nis furnished to do so, subject to the following\nconditions:\n\nThe above copyright notice and this permission notice\nshall be included in all copies or substantial portions\nof the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF\nANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED\nTO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A\nPARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT\nSHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY\nCLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR\nIN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER\nDEALINGS IN THE SOFTWARE.\n*/\n\nimport { getOptions } from 'next/dist/compiled/loader-utils'\nimport { transform } from '../../swc'\n\nfunction getSWCOptions({ isTypeScript, isServer, development }) {\n  const jsc = {\n    parser: {\n      syntax: isTypeScript ? 'typescript' : 'ecmascript',\n      dynamicImport: true,\n      [isTypeScript ? 'tsx' : 'jsx']: true,\n    },\n\n    transform: {\n      react: {\n        runtime: 'automatic',\n        pragma: 'React.createElement',\n        pragmaFrag: 'React.Fragment',\n        throwIfNamespace: true,\n        development: development,\n        useBuiltins: true,\n      },\n    },\n  }\n\n  if (isServer) {\n    return {\n      jsc,\n      env: {\n        targets: {\n          // Targets the current version of Node.js\n          node: process.versions.node,\n        },\n      },\n    }\n  } else {\n    // Matches default @babel/preset-env behavior\n    jsc.target = 'es5'\n    return { jsc }\n  }\n}\n\nasync function loaderTransform(parentTrace, source, inputSourceMap) {\n  // Make the loader async\n  const filename = this.resourcePath\n\n  const isTypeScript = filename.endsWith('.ts') || filename.endsWith('.tsx')\n\n  let loaderOptions = getOptions(this) || {}\n\n  const swcOptions = getSWCOptions({\n    isTypeScript,\n    isServer: loaderOptions.isServer,\n    development: this.mode === 'development',\n  })\n\n  const programmaticOptions = {\n    ...swcOptions,\n    filename,\n    inputSourceMap: inputSourceMap ? JSON.stringify(inputSourceMap) : undefined,\n\n    // Set the default sourcemap behavior based on Webpack's mapping flag,\n    sourceMaps: this.sourceMap,\n\n    // Ensure that Webpack will get a full absolute path in the sourcemap\n    // so that it can properly map the module back to its internal cached\n    // modules.\n    sourceFileName: filename,\n  }\n\n  if (!programmaticOptions.inputSourceMap) {\n    delete programmaticOptions.inputSourceMap\n  }\n\n  // auto detect development mode\n  if (\n    this.mode &&\n    programmaticOptions.jsc &&\n    programmaticOptions.jsc.transform &&\n    programmaticOptions.jsc.transform.react &&\n    !Object.prototype.hasOwnProperty.call(\n      programmaticOptions.jsc.transform.react,\n      'development'\n    )\n  ) {\n    programmaticOptions.jsc.transform.react.development =\n      this.mode === 'development'\n  }\n\n  const swcSpan = parentTrace.traceChild('next-swc-transform')\n  return swcSpan.traceAsyncFn(() =>\n    transform(source, programmaticOptions).then((output) => {\n      return [output.code, output.map ? JSON.parse(output.map) : undefined]\n    })\n  )\n}\n\nexport default function swcLoader(inputSource, inputSourceMap) {\n  const loaderSpan = this.currentTraceSpan.traceChild('next-swc-loader')\n  const callback = this.async()\n  loaderSpan\n    .traceAsyncFn(() =>\n      loaderTransform.call(this, loaderSpan, inputSource, inputSourceMap)\n    )\n    .then(\n      ([transformedSource, outputSourceMap]) =>\n        callback?.(null, transformedSource, outputSourceMap || inputSourceMap),\n      (err) => {\n        callback?.(err)\n      }\n    )\n}\n"], "names": [], "mappings": ";;;;kBA2HwB,SAAS;AA/FN,GAAiC,CAAjC,YAAiC;AAClC,GAAW,CAAX,IAAW;SAE5B,aAAa,GAAG,YAAY,GAAE,QAAQ,GAAE,WAAW,KAAI,CAAC;IAC/D,KAAK,CAAC,GAAG;QACP,MAAM;YACJ,MAAM,EAAE,YAAY,IAAG,UAAY,KAAG,UAAY;YAClD,aAAa,EAAE,IAAI;aAClB,YAAY,IAAG,GAAK,KAAG,GAAK,IAAG,IAAI;;QAGtC,SAAS;YACP,KAAK;gBACH,OAAO,GAAE,SAAW;gBACpB,MAAM,GAAE,mBAAqB;gBAC7B,UAAU,GAAE,cAAgB;gBAC5B,gBAAgB,EAAE,IAAI;gBACtB,WAAW,EAAE,WAAW;gBACxB,WAAW,EAAE,IAAI;;;;IAKvB,EAAE,EAAE,QAAQ,EAAE,CAAC;;YAEX,GAAG;YACH,GAAG;gBACD,OAAO;oBACL,EAAyC,AAAzC,uCAAyC;oBACzC,IAAI,EAAE,OAAO,CAAC,QAAQ,CAAC,IAAI;;;;IAInC,CAAC,MAAM,CAAC;QACN,EAA6C,AAA7C,2CAA6C;QAC7C,GAAG,CAAC,MAAM,IAAG,GAAK;;YACT,GAAG;;IACd,CAAC;AACH,CAAC;eAEc,eAAe,CAAC,WAAW,EAAE,MAAM,EAAE,cAAc,EAAE,CAAC;IACnE,EAAwB,AAAxB,sBAAwB;IACxB,KAAK,CAAC,QAAQ,QAAQ,YAAY;IAElC,KAAK,CAAC,YAAY,GAAG,QAAQ,CAAC,QAAQ,EAAC,GAAK,MAAK,QAAQ,CAAC,QAAQ,EAAC,IAAM;IAEzE,GAAG,CAAC,aAAa,OA9CQ,YAAiC;;IAgD1D,KAAK,CAAC,UAAU,GAAG,aAAa;QAC9B,YAAY;QACZ,QAAQ,EAAE,aAAa,CAAC,QAAQ;QAChC,WAAW,OAAO,IAAI,MAAK,WAAa;;IAG1C,KAAK,CAAC,mBAAmB;WACpB,UAAU;QACb,QAAQ;QACR,cAAc,EAAE,cAAc,GAAG,IAAI,CAAC,SAAS,CAAC,cAAc,IAAI,SAAS;QAE3E,EAAsE,AAAtE,oEAAsE;QACtE,UAAU,OAAO,SAAS;QAE1B,EAAqE,AAArE,mEAAqE;QACrE,EAAqE,AAArE,mEAAqE;QACrE,EAAW,AAAX,SAAW;QACX,cAAc,EAAE,QAAQ;;IAG1B,EAAE,GAAG,mBAAmB,CAAC,cAAc,EAAE,CAAC;eACjC,mBAAmB,CAAC,cAAc;IAC3C,CAAC;IAED,EAA+B,AAA/B,6BAA+B;IAC/B,EAAE,OACK,IAAI,IACT,mBAAmB,CAAC,GAAG,IACvB,mBAAmB,CAAC,GAAG,CAAC,SAAS,IACjC,mBAAmB,CAAC,GAAG,CAAC,SAAS,CAAC,KAAK,KACtC,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CACnC,mBAAmB,CAAC,GAAG,CAAC,SAAS,CAAC,KAAK,GACvC,WAAa,IAEf,CAAC;QACD,mBAAmB,CAAC,GAAG,CAAC,SAAS,CAAC,KAAK,CAAC,WAAW,QAC5C,IAAI,MAAK,WAAa;IAC/B,CAAC;IAED,KAAK,CAAC,OAAO,GAAG,WAAW,CAAC,UAAU,EAAC,kBAAoB;WACpD,OAAO,CAAC,YAAY,SAvFH,IAAW,YAwFvB,MAAM,EAAE,mBAAmB,EAAE,IAAI,EAAE,MAAM,GAAK,CAAC;;gBAC/C,MAAM,CAAC,IAAI;gBAAE,MAAM,CAAC,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,GAAG,IAAI,SAAS;;QACtE,CAAC;;AAEL,CAAC;SAEuB,SAAS,CAAC,WAAW,EAAE,cAAc,EAAE,CAAC;IAC9D,KAAK,CAAC,UAAU,QAAQ,gBAAgB,CAAC,UAAU,EAAC,eAAiB;IACrE,KAAK,CAAC,QAAQ,QAAQ,KAAK;IAC3B,UAAU,CACP,YAAY,KACX,eAAe,CAAC,IAAI,OAAO,UAAU,EAAE,WAAW,EAAE,cAAc;MAEnE,IAAI,GACD,iBAAiB,EAAE,eAAe;eAClC,QAAQ,aAAR,QAAQ,UAAR,CAAsE,QAAtE,CAAsE,GAAtE,QAAQ,CAAG,IAAI,EAAE,iBAAiB,EAAE,eAAe,IAAI,cAAc;QACtE,GAAG,GAAK,CAAC;QACR,QAAQ,aAAR,QAAQ,UAAR,CAAe,QAAf,CAAe,GAAf,QAAQ,CAAG,GAAG;IAChB,CAAC;AAEP,CAAC"}