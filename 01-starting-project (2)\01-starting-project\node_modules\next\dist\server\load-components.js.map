{"version": 3, "sources": ["../../server/load-components.ts"], "sourcesContent": ["import {\n  BUILD_MANIFEST,\n  REACT_LOADABLE_MANIFEST,\n} from '../shared/lib/constants'\nimport { join } from 'path'\nimport { requirePage } from './require'\nimport { BuildManifest } from './get-page-files'\nimport { AppType, DocumentType } from '../shared/lib/utils'\nimport {\n  PageConfig,\n  GetStaticPaths,\n  GetServerSideProps,\n  GetStaticProps,\n} from 'next/types'\n\nexport function interopDefault(mod: any) {\n  return mod.default || mod\n}\n\nexport type ManifestItem = {\n  id: number | string\n  files: string[]\n}\n\ntype ReactLoadableManifest = { [moduleId: string]: ManifestItem }\n\nexport type LoadComponentsReturnType = {\n  Component: React.ComponentType\n  pageConfig?: PageConfig\n  buildManifest: BuildManifest\n  reactLoadableManifest: ReactLoadableManifest\n  Document: DocumentType\n  App: AppType\n  getStaticProps?: GetStaticProps\n  getStaticPaths?: GetStaticPaths\n  getServerSideProps?: GetServerSideProps\n  ComponentMod: any\n}\n\nexport async function loadDefaultErrorComponents(distDir: string) {\n  const Document = interopDefault(require('next/dist/pages/_document'))\n  const App = interopDefault(require('next/dist/pages/_app'))\n  const ComponentMod = require('next/dist/pages/_error')\n  const Component = interopDefault(ComponentMod)\n\n  return {\n    App,\n    Document,\n    Component,\n    buildManifest: require(join(distDir, `fallback-${BUILD_MANIFEST}`)),\n    reactLoadableManifest: {},\n    ComponentMod,\n  }\n}\n\nexport async function loadComponents(\n  distDir: string,\n  pathname: string,\n  serverless: boolean\n): Promise<LoadComponentsReturnType> {\n  if (serverless) {\n    const Component = await requirePage(pathname, distDir, serverless)\n    let { getStaticProps, getStaticPaths, getServerSideProps } = Component\n\n    getStaticProps = await getStaticProps\n    getStaticPaths = await getStaticPaths\n    getServerSideProps = await getServerSideProps\n    const pageConfig = (await Component.config) || {}\n\n    return {\n      Component,\n      pageConfig,\n      getStaticProps,\n      getStaticPaths,\n      getServerSideProps,\n      ComponentMod: Component,\n    } as LoadComponentsReturnType\n  }\n\n  const DocumentMod = await requirePage('/_document', distDir, serverless)\n  const AppMod = await requirePage('/_app', distDir, serverless)\n  const ComponentMod = await requirePage(pathname, distDir, serverless)\n\n  const [buildManifest, reactLoadableManifest, Component, Document, App] =\n    await Promise.all([\n      require(join(distDir, BUILD_MANIFEST)),\n      require(join(distDir, REACT_LOADABLE_MANIFEST)),\n      interopDefault(ComponentMod),\n      interopDefault(DocumentMod),\n      interopDefault(AppMod),\n    ])\n\n  const { getServerSideProps, getStaticProps, getStaticPaths } = ComponentMod\n\n  return {\n    App,\n    Document,\n    Component,\n    buildManifest,\n    reactLoadableManifest,\n    pageConfig: ComponentMod.config || {},\n    ComponentMod,\n    getServerSideProps,\n    getStaticProps,\n    getStaticPaths,\n  }\n}\n"], "names": [], "mappings": ";;;;QAegB,cAAc,GAAd,cAAc;QAwBR,0BAA0B,GAA1B,0BAA0B;QAgB1B,cAAc,GAAd,cAAc;AApD7B,GAAyB,CAAzB,UAAyB;AACX,GAAM,CAAN,KAAM;AACC,GAAW,CAAX,QAAW;SAUvB,cAAc,CAAC,GAAQ,EAAE,CAAC;WACjC,GAAG,CAAC,OAAO,IAAI,GAAG;AAC3B,CAAC;eAsBqB,0BAA0B,CAAC,OAAe,EAAE,CAAC;IACjE,KAAK,CAAC,SAAQ,GAAG,cAAc,CAAC,OAAO,EAAC,yBAA2B;IACnE,KAAK,CAAC,GAAG,GAAG,cAAc,CAAC,OAAO,EAAC,oBAAsB;IACzD,KAAK,CAAC,YAAY,GAAG,OAAO,EAAC,sBAAwB;IACrD,KAAK,CAAC,SAAS,GAAG,cAAc,CAAC,YAAY;;QAG3C,GAAG;QACH,QAAQ,EAAR,SAAQ;QACR,SAAS;QACT,aAAa,EAAE,OAAO,KA7CL,KAAM,OA6CK,OAAO,GAAG,SAAS,EA9C5C,UAAyB;QA+C5B,qBAAqB;;QACrB,YAAY;;AAEhB,CAAC;eAEqB,cAAc,CAClC,OAAe,EACf,QAAgB,EAChB,UAAmB,EACgB,CAAC;IACpC,EAAE,EAAE,UAAU,EAAE,CAAC;QACf,KAAK,CAAC,SAAS,aAxDS,QAAW,cAwDC,QAAQ,EAAE,OAAO,EAAE,UAAU;QACjE,GAAG,GAAG,cAAc,GAAE,cAAc,GAAE,kBAAkB,MAAK,SAAS;QAEtE,cAAc,SAAS,cAAc;QACrC,cAAc,SAAS,cAAc;QACrC,kBAAkB,SAAS,kBAAkB;QAC7C,KAAK,CAAC,UAAU,SAAU,SAAS,CAAC,MAAM;;;YAGxC,SAAS;YACT,UAAU;YACV,cAAc;YACd,cAAc;YACd,kBAAkB;YAClB,YAAY,EAAE,SAAS;;IAE3B,CAAC;IAED,KAAK,CAAC,WAAW,aA1ES,QAAW,eA0EC,UAAY,GAAE,OAAO,EAAE,UAAU;IACvE,KAAK,CAAC,MAAM,aA3Ec,QAAW,eA2EJ,KAAO,GAAE,OAAO,EAAE,UAAU;IAC7D,KAAK,CAAC,YAAY,aA5EQ,QAAW,cA4EE,QAAQ,EAAE,OAAO,EAAE,UAAU;IAEpE,KAAK,EAAE,aAAa,EAAE,qBAAqB,EAAE,SAAS,EAAE,SAAQ,EAAE,GAAG,UAC7D,OAAO,CAAC,GAAG;QACf,OAAO,KAjFQ,KAAM,OAiFR,OAAO,EAlFnB,UAAyB;QAmF1B,OAAO,KAlFQ,KAAM,OAkFR,OAAO,EAnFnB,UAAyB;QAoF1B,cAAc,CAAC,YAAY;QAC3B,cAAc,CAAC,WAAW;QAC1B,cAAc,CAAC,MAAM;;IAGzB,KAAK,GAAG,kBAAkB,GAAE,cAAc,GAAE,cAAc,MAAK,YAAY;;QAGzE,GAAG;QACH,QAAQ,EAAR,SAAQ;QACR,SAAS;QACT,aAAa;QACb,qBAAqB;QACrB,UAAU,EAAE,YAAY,CAAC,MAAM;;QAC/B,YAAY;QACZ,kBAAkB;QAClB,cAAc;QACd,cAAc;;AAElB,CAAC"}