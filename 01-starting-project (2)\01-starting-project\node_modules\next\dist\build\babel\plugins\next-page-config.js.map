{"version": 3, "sources": ["../../../../build/babel/plugins/next-page-config.ts"], "sourcesContent": ["import {\n  <PERSON><PERSON><PERSON><PERSON>,\n  PluginObj,\n  PluginPass,\n  types as BabelTypes,\n  Visitor,\n} from 'next/dist/compiled/babel/core'\nimport { PageConfig } from 'next/types'\nimport { STRING_LITERAL_DROP_BUNDLE } from '../../../shared/lib/constants'\n\nconst CONFIG_KEY = 'config'\n\n// replace program path with just a variable with the drop identifier\nfunction replaceBundle(path: any, t: typeof BabelTypes): void {\n  path.parentPath.replaceWith(\n    t.program(\n      [\n        t.variableDeclaration('const', [\n          t.variableDeclarator(\n            t.identifier(STRING_LITERAL_DROP_BUNDLE),\n            t.stringLiteral(`${STRING_LITERAL_DROP_BUNDLE} ${Date.now()}`)\n          ),\n        ]),\n      ],\n      []\n    )\n  )\n}\n\nfunction errorMessage(state: any, details: string): string {\n  const pageName =\n    (state.filename || '').split(state.cwd || '').pop() || 'unknown'\n  return `Invalid page config export found. ${details} in file ${pageName}. See: https://nextjs.org/docs/messages/invalid-page-config`\n}\n\ninterface ConfigState extends PluginPass {\n  bundleDropped?: boolean\n}\n\n// config to parsing pageConfig for client bundles\nexport default function nextPageConfig({\n  types: t,\n}: {\n  types: typeof BabelTypes\n}): PluginObj {\n  return {\n    visitor: {\n      Program: {\n        enter(path, state) {\n          path.traverse(\n            {\n              ExportDeclaration(exportPath, exportState) {\n                if (\n                  BabelTypes.isExportNamedDeclaration(exportPath) &&\n                  (\n                    exportPath.node as BabelTypes.ExportNamedDeclaration\n                  ).specifiers?.some((specifier) => {\n                    return (\n                      (t.isIdentifier(specifier.exported)\n                        ? specifier.exported.name\n                        : specifier.exported.value) === CONFIG_KEY\n                    )\n                  }) &&\n                  BabelTypes.isStringLiteral(\n                    (exportPath.node as BabelTypes.ExportNamedDeclaration)\n                      .source\n                  )\n                ) {\n                  throw new Error(\n                    errorMessage(\n                      exportState,\n                      'Expected object but got export from'\n                    )\n                  )\n                }\n              },\n              ExportNamedDeclaration(\n                exportPath: NodePath<BabelTypes.ExportNamedDeclaration>,\n                exportState: any\n              ) {\n                if (\n                  exportState.bundleDropped ||\n                  (!exportPath.node.declaration &&\n                    exportPath.node.specifiers.length === 0)\n                ) {\n                  return\n                }\n\n                const config: PageConfig = {}\n                const declarations: BabelTypes.VariableDeclarator[] = [\n                  ...((\n                    exportPath.node\n                      .declaration as BabelTypes.VariableDeclaration\n                  )?.declarations || []),\n                  exportPath.scope.getBinding(CONFIG_KEY)?.path\n                    .node as BabelTypes.VariableDeclarator,\n                ].filter(Boolean)\n\n                for (const specifier of exportPath.node.specifiers) {\n                  if (\n                    (t.isIdentifier(specifier.exported)\n                      ? specifier.exported.name\n                      : specifier.exported.value) === CONFIG_KEY\n                  ) {\n                    // export {} from 'somewhere'\n                    if (BabelTypes.isStringLiteral(exportPath.node.source)) {\n                      throw new Error(\n                        errorMessage(\n                          exportState,\n                          `Expected object but got import`\n                        )\n                      )\n                      // import hello from 'world'\n                      // export { hello as config }\n                    } else if (\n                      BabelTypes.isIdentifier(\n                        (specifier as BabelTypes.ExportSpecifier).local\n                      )\n                    ) {\n                      if (\n                        BabelTypes.isImportSpecifier(\n                          exportPath.scope.getBinding(\n                            (specifier as BabelTypes.ExportSpecifier).local.name\n                          )?.path.node\n                        )\n                      ) {\n                        throw new Error(\n                          errorMessage(\n                            exportState,\n                            `Expected object but got import`\n                          )\n                        )\n                      }\n                    }\n                  }\n                }\n\n                for (const declaration of declarations) {\n                  if (\n                    !BabelTypes.isIdentifier(declaration.id, {\n                      name: CONFIG_KEY,\n                    })\n                  ) {\n                    continue\n                  }\n\n                  if (!BabelTypes.isObjectExpression(declaration.init)) {\n                    const got = declaration.init\n                      ? declaration.init.type\n                      : 'undefined'\n                    throw new Error(\n                      errorMessage(\n                        exportState,\n                        `Expected object but got ${got}`\n                      )\n                    )\n                  }\n\n                  for (const prop of declaration.init.properties) {\n                    if (BabelTypes.isSpreadElement(prop)) {\n                      throw new Error(\n                        errorMessage(\n                          exportState,\n                          `Property spread is not allowed`\n                        )\n                      )\n                    }\n                    const { name } = prop.key as BabelTypes.Identifier\n                    if (BabelTypes.isIdentifier(prop.key, { name: 'amp' })) {\n                      if (!BabelTypes.isObjectProperty(prop)) {\n                        throw new Error(\n                          errorMessage(\n                            exportState,\n                            `Invalid property \"${name}\"`\n                          )\n                        )\n                      }\n                      if (\n                        !BabelTypes.isBooleanLiteral(prop.value) &&\n                        !BabelTypes.isStringLiteral(prop.value)\n                      ) {\n                        throw new Error(\n                          errorMessage(\n                            exportState,\n                            `Invalid value for \"${name}\"`\n                          )\n                        )\n                      }\n                      config.amp = prop.value.value as PageConfig['amp']\n                    }\n                  }\n                }\n\n                if (config.amp === true) {\n                  if (!exportState.file?.opts?.caller.isDev) {\n                    // don't replace bundle in development so HMR can track\n                    // dependencies and trigger reload when they are changed\n                    replaceBundle(exportPath, t)\n                  }\n                  exportState.bundleDropped = true\n                  return\n                }\n              },\n            },\n            state\n          )\n        },\n      },\n    } as Visitor<ConfigState>,\n  }\n}\n"], "names": [], "mappings": ";;;;kBAwCwB,cAAc;AAlC/B,GAA+B,CAA/B,KAA+B;AAEK,GAA+B,CAA/B,UAA+B;AAE1E,KAAK,CAAC,UAAU,IAAG,MAAQ;AAE3B,EAAqE,AAArE,mEAAqE;SAC5D,aAAa,CAAC,IAAS,EAAE,CAAoB,EAAQ,CAAC;IAC7D,IAAI,CAAC,UAAU,CAAC,WAAW,CACzB,CAAC,CAAC,OAAO;QAEL,CAAC,CAAC,mBAAmB,EAAC,KAAO;YAC3B,CAAC,CAAC,kBAAkB,CAClB,CAAC,CAAC,UAAU,CAXmB,UAA+B,8BAY9D,CAAC,CAAC,aAAa,IAZgB,UAA+B,4BAYhB,CAAC,EAAE,IAAI,CAAC,GAAG;;;AAOrE,CAAC;SAEQ,YAAY,CAAC,KAAU,EAAE,OAAe,EAAU,CAAC;IAC1D,KAAK,CAAC,QAAQ,IACX,KAAK,CAAC,QAAQ,QAAQ,KAAK,CAAC,KAAK,CAAC,GAAG,QAAQ,GAAG,OAAM,OAAS;YAC1D,kCAAkC,EAAE,OAAO,CAAC,SAAS,EAAE,QAAQ,CAAC,2DAA2D;AACrI,CAAC;SAOuB,cAAc,GACpC,KAAK,EAAE,CAAC,KAGI,CAAC;;QAEX,OAAO;YACL,OAAO;gBACL,KAAK,EAAC,IAAI,EAAE,KAAK,EAAE,CAAC;oBAClB,IAAI,CAAC,QAAQ;wBAET,iBAAiB,EAAC,UAAU,EAAE,WAAW,EAAE,CAAC;gCAGxC,GAEY;4BAJd,EAAE,EA9CX,KAA+B,OA+CT,wBAAwB,CAAC,UAAU,OAC9C,GAEY,GADV,UAAU,CAAC,IAAI,CACf,UAAU,cAFZ,GAEY,UAFZ,CAEkB,QAFlB,CAEkB,GAFlB,GAEY,CAAE,IAAI,EAAE,SAAS,GAAK,CAAC;wCAE9B,CAAC,CAAC,YAAY,CAAC,SAAS,CAAC,QAAQ,IAC9B,SAAS,CAAC,QAAQ,CAAC,IAAI,GACvB,SAAS,CAAC,QAAQ,CAAC,KAAK,MAAM,UAAU;4BAEhD,CAAC,MAxDZ,KAA+B,OAyDT,eAAe,CACvB,UAAU,CAAC,IAAI,CACb,MAAM,GAEX,CAAC;gCACD,KAAK,CAAC,GAAG,CAAC,KAAK,CACb,YAAY,CACV,WAAW,GACX,mCAAqC;4BAG3C,CAAC;wBACH,CAAC;wBACD,sBAAsB,EACpB,UAAuD,EACvD,WAAgB,EAChB,CAAC;gCAWK,GAGH,EACD,IAAuC;4BAdzC,EAAE,EACA,WAAW,CAAC,aAAa,KACvB,UAAU,CAAC,IAAI,CAAC,WAAW,IAC3B,UAAU,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM,KAAK,CAAC,EACzC,CAAC;;4BAEH,CAAC;4BAED,KAAK,CAAC,MAAM;;4BACZ,KAAK,CAAC,YAAY;qCACZ,GAGH,GAFC,UAAU,CAAC,IAAI,CACZ,WAAW,cAFZ,GAGH,UAHG,CAGW,QAHX,CAGW,GAHX,GAGH,CAAE,YAAY;iCACf,IAAuC,GAAvC,UAAU,CAAC,KAAK,CAAC,UAAU,CAAC,UAAU,eAAtC,IAAuC,UAAvC,CAA6C,QAA7C,CAA6C,GAA7C,IAAuC,CAAE,IAAI,CAC1C,IAAI;8BACP,MAAM,CAAC,OAAO;iCAEX,KAAK,CAAC,SAAS,IAAI,UAAU,CAAC,IAAI,CAAC,UAAU,CAAE,CAAC;gCACnD,EAAE,GACC,CAAC,CAAC,YAAY,CAAC,SAAS,CAAC,QAAQ,IAC9B,SAAS,CAAC,QAAQ,CAAC,IAAI,GACvB,SAAS,CAAC,QAAQ,CAAC,KAAK,MAAM,UAAU,EAC5C,CAAC;oCACD,EAA6B,AAA7B,2BAA6B;oCAC7B,EAAE,EAnGf,KAA+B,OAmGH,eAAe,CAAC,UAAU,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC;wCACvD,KAAK,CAAC,GAAG,CAAC,KAAK,CACb,YAAY,CACV,WAAW,GACV,8BAA8B;oCAGnC,EAA4B,AAA5B,0BAA4B;oCAC5B,EAA6B,AAA7B,2BAA6B;oCAC/B,CAAC,MAAM,EAAE,EA5GtB,KAA+B,OA6GL,YAAY,CACpB,SAAS,CAAgC,KAAK,GAEjD,CAAC;4CAGG,IAEC;wCAJL,EAAE,EAjHjB,KAA+B,OAkHH,iBAAiB,EAC1B,IAEC,GAFD,UAAU,CAAC,KAAK,CAAC,UAAU,CACxB,SAAS,CAAgC,KAAK,CAAC,IAAI,eADtD,IAEC,UAFD,CAEO,QAFP,CAEO,GAFP,IAEC,CAAE,IAAI,CAAC,IAAI,GAEd,CAAC;4CACD,KAAK,CAAC,GAAG,CAAC,KAAK,CACb,YAAY,CACV,WAAW,GACV,8BAA8B;wCAGrC,CAAC;oCACH,CAAC;gCACH,CAAC;4BACH,CAAC;iCAEI,KAAK,CAAC,WAAW,IAAI,YAAY,CAAE,CAAC;gCACvC,EAAE,GApIb,KAA+B,OAqIN,YAAY,CAAC,WAAW,CAAC,EAAE;oCACrC,IAAI,EAAE,UAAU;oCAElB,CAAC;;gCAEH,CAAC;gCAED,EAAE,GA5Ib,KAA+B,OA4IJ,kBAAkB,CAAC,WAAW,CAAC,IAAI,GAAG,CAAC;oCACrD,KAAK,CAAC,GAAG,GAAG,WAAW,CAAC,IAAI,GACxB,WAAW,CAAC,IAAI,CAAC,IAAI,IACrB,SAAW;oCACf,KAAK,CAAC,GAAG,CAAC,KAAK,CACb,YAAY,CACV,WAAW,GACV,wBAAwB,EAAE,GAAG;gCAGpC,CAAC;qCAEI,KAAK,CAAC,IAAI,IAAI,WAAW,CAAC,IAAI,CAAC,UAAU,CAAE,CAAC;oCAC/C,EAAE,EAzJf,KAA+B,OAyJH,eAAe,CAAC,IAAI,GAAG,CAAC;wCACrC,KAAK,CAAC,GAAG,CAAC,KAAK,CACb,YAAY,CACV,WAAW,GACV,8BAA8B;oCAGrC,CAAC;oCACD,KAAK,GAAG,IAAI,MAAK,IAAI,CAAC,GAAG;oCACzB,EAAE,EAlKf,KAA+B,OAkKH,YAAY,CAAC,IAAI,CAAC,GAAG;wCAAI,IAAI,GAAE,GAAK;wCAAK,CAAC;wCACvD,EAAE,GAnKjB,KAA+B,OAmKA,gBAAgB,CAAC,IAAI,GAAG,CAAC;4CACvC,KAAK,CAAC,GAAG,CAAC,KAAK,CACb,YAAY,CACV,WAAW,GACV,kBAAkB,EAAE,IAAI,CAAC,CAAC;wCAGjC,CAAC;wCACD,EAAE,GA3KjB,KAA+B,OA4KF,gBAAgB,CAAC,IAAI,CAAC,KAAK,MA5KxD,KAA+B,OA6KF,eAAe,CAAC,IAAI,CAAC,KAAK,GACtC,CAAC;4CACD,KAAK,CAAC,GAAG,CAAC,KAAK,CACb,YAAY,CACV,WAAW,GACV,mBAAmB,EAAE,IAAI,CAAC,CAAC;wCAGlC,CAAC;wCACD,MAAM,CAAC,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK;oCAC/B,CAAC;gCACH,CAAC;4BACH,CAAC;4BAED,EAAE,EAAE,MAAM,CAAC,GAAG,KAAK,IAAI,EAAE,CAAC;oCACnB,IAAgB;gCAArB,EAAE,KAAG,IAAgB,GAAhB,WAAW,CAAC,IAAI,cAAhB,IAAgB,UAAhB,CAAsB,QAAtB,CAAsB,WAAtB,IAAgB,CAAE,IAAI,4BAAtB,CAAsB,QAAtB,CAAsB,QAAE,MAAM,CAAC,KAAK,GAAE,CAAC;oCAC1C,EAAuD,AAAvD,qDAAuD;oCACvD,EAAwD,AAAxD,sDAAwD;oCACxD,aAAa,CAAC,UAAU,EAAE,CAAC;gCAC7B,CAAC;gCACD,WAAW,CAAC,aAAa,GAAG,IAAI;;4BAElC,CAAC;wBACH,CAAC;uBAEH,KAAK;gBAET,CAAC;;;;AAIT,CAAC"}