{"version": 3, "sources": ["../../lib/is-serializable-props.ts"], "sourcesContent": ["const regexpPlainIdentifier = /^[A-Za-z_$][A-Za-z0-9_$]*$/\n\nfunction getObjectClassLabel(value: any): string {\n  return Object.prototype.toString.call(value)\n}\n\nfunction isPlainObject(value: any): boolean {\n  if (getObjectClassLabel(value) !== '[object Object]') {\n    return false\n  }\n\n  const prototype = Object.getPrototypeOf(value)\n  return prototype === null || prototype === Object.prototype\n}\n\nexport function isSerializableProps(\n  page: string,\n  method: string,\n  input: any\n): true {\n  if (!isPlainObject(input)) {\n    throw new SerializableError(\n      page,\n      method,\n      '',\n      `Props must be returned as a plain object from ${method}: \\`{ props: { ... } }\\` (received: \\`${getObjectClassLabel(\n        input\n      )}\\`).`\n    )\n  }\n\n  function visit(visited: Map<any, string>, value: any, path: string) {\n    if (visited.has(value)) {\n      throw new SerializableError(\n        page,\n        method,\n        path,\n        `Circular references cannot be expressed in JSON (references: \\`${\n          visited.get(value) || '(self)'\n        }\\`).`\n      )\n    }\n\n    visited.set(value, path)\n  }\n\n  function isSerializable(\n    refs: Map<any, string>,\n    value: any,\n    path: string\n  ): true {\n    const type = typeof value\n    if (\n      // `null` can be serialized, but not `undefined`.\n      value === null ||\n      // n.b. `bigint`, `function`, `symbol`, and `undefined` cannot be\n      // serialized.\n      //\n      // `object` is special-cased below, as it may represent `null`, an Array,\n      // a plain object, a class, et al.\n      type === 'boolean' ||\n      type === 'number' ||\n      type === 'string'\n    ) {\n      return true\n    }\n\n    if (type === 'undefined') {\n      throw new SerializableError(\n        page,\n        method,\n        path,\n        '`undefined` cannot be serialized as JSON. Please use `null` or omit this value.'\n      )\n    }\n\n    if (isPlainObject(value)) {\n      visit(refs, value, path)\n\n      if (\n        Object.entries(value).every(([key, nestedValue]) => {\n          const nextPath = regexpPlainIdentifier.test(key)\n            ? `${path}.${key}`\n            : `${path}[${JSON.stringify(key)}]`\n\n          const newRefs = new Map(refs)\n          return (\n            isSerializable(newRefs, key, nextPath) &&\n            isSerializable(newRefs, nestedValue, nextPath)\n          )\n        })\n      ) {\n        return true\n      }\n\n      throw new SerializableError(\n        page,\n        method,\n        path,\n        `invariant: Unknown error encountered in Object.`\n      )\n    }\n\n    if (Array.isArray(value)) {\n      visit(refs, value, path)\n\n      if (\n        value.every((nestedValue, index) => {\n          const newRefs = new Map(refs)\n          return isSerializable(newRefs, nestedValue, `${path}[${index}]`)\n        })\n      ) {\n        return true\n      }\n\n      throw new SerializableError(\n        page,\n        method,\n        path,\n        `invariant: Unknown error encountered in Array.`\n      )\n    }\n\n    // None of these can be expressed as JSON:\n    // const type: \"bigint\" | \"symbol\" | \"object\" | \"function\"\n    throw new SerializableError(\n      page,\n      method,\n      path,\n      '`' +\n        type +\n        '`' +\n        (type === 'object'\n          ? ` (\"${Object.prototype.toString.call(value)}\")`\n          : '') +\n        ' cannot be serialized as JSON. Please only return JSON serializable data types.'\n    )\n  }\n\n  return isSerializable(new Map(), input, '')\n}\n\nexport class SerializableError extends Error {\n  constructor(page: string, method: string, path: string, message: string) {\n    super(\n      path\n        ? `Error serializing \\`${path}\\` returned from \\`${method}\\` in \"${page}\".\\nReason: ${message}`\n        : `Error serializing props returned from \\`${method}\\` in \"${page}\".\\nReason: ${message}`\n    )\n  }\n}\n"], "names": [], "mappings": ";;;;QAegB,mBAAmB,GAAnB,mBAAmB;AAfnC,KAAK,CAAC,qBAAqB;SAElB,mBAAmB,CAAC,KAAU,EAAU,CAAC;WACzC,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK;AAC7C,CAAC;SAEQ,aAAa,CAAC,KAAU,EAAW,CAAC;IAC3C,EAAE,EAAE,mBAAmB,CAAC,KAAK,OAAM,eAAiB,GAAE,CAAC;eAC9C,KAAK;IACd,CAAC;IAED,KAAK,CAAC,SAAS,GAAG,MAAM,CAAC,cAAc,CAAC,KAAK;WACtC,SAAS,KAAK,IAAI,IAAI,SAAS,KAAK,MAAM,CAAC,SAAS;AAC7D,CAAC;SAEe,mBAAmB,CACjC,IAAY,EACZ,MAAc,EACd,KAAU,EACJ,CAAC;IACP,EAAE,GAAG,aAAa,CAAC,KAAK,GAAG,CAAC;QAC1B,KAAK,CAAC,GAAG,CAAC,iBAAiB,CACzB,IAAI,EACJ,MAAM,OAEL,8CAA8C,EAAE,MAAM,CAAC,sCAAsC,EAAE,mBAAmB,CACjH,KAAK,EACL,IAAI;IAEV,CAAC;aAEQ,KAAK,CAAC,OAAyB,EAAE,KAAU,EAAE,IAAY,EAAE,CAAC;QACnE,EAAE,EAAE,OAAO,CAAC,GAAG,CAAC,KAAK,GAAG,CAAC;YACvB,KAAK,CAAC,GAAG,CAAC,iBAAiB,CACzB,IAAI,EACJ,MAAM,EACN,IAAI,GACH,+DAA+D,EAC9D,OAAO,CAAC,GAAG,CAAC,KAAK,MAAK,MAAQ,EAC/B,IAAI;QAET,CAAC;QAED,OAAO,CAAC,GAAG,CAAC,KAAK,EAAE,IAAI;IACzB,CAAC;aAEQ,cAAc,CACrB,IAAsB,EACtB,KAAU,EACV,IAAY,EACN,CAAC;QACP,KAAK,CAAC,IAAI,UAAU,KAAK;QACzB,EAAE,EACA,EAAiD,AAAjD,+CAAiD;QACjD,KAAK,KAAK,IAAI,IACd,EAAiE,AAAjE,+DAAiE;QACjE,EAAc,AAAd,YAAc;QACd,EAAE;QACF,EAAyE,AAAzE,uEAAyE;QACzE,EAAkC,AAAlC,gCAAkC;QAClC,IAAI,MAAK,OAAS,KAClB,IAAI,MAAK,MAAQ,KACjB,IAAI,MAAK,MAAQ,GACjB,CAAC;mBACM,IAAI;QACb,CAAC;QAED,EAAE,EAAE,IAAI,MAAK,SAAW,GAAE,CAAC;YACzB,KAAK,CAAC,GAAG,CAAC,iBAAiB,CACzB,IAAI,EACJ,MAAM,EACN,IAAI,GACJ,+EAAiF;QAErF,CAAC;QAED,EAAE,EAAE,aAAa,CAAC,KAAK,GAAG,CAAC;YACzB,KAAK,CAAC,IAAI,EAAE,KAAK,EAAE,IAAI;YAEvB,EAAE,EACA,MAAM,CAAC,OAAO,CAAC,KAAK,EAAE,KAAK,GAAG,GAAG,EAAE,WAAW,IAAM,CAAC;gBACnD,KAAK,CAAC,QAAQ,GAAG,qBAAqB,CAAC,IAAI,CAAC,GAAG,OACxC,IAAI,CAAC,CAAC,EAAE,GAAG,QACX,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,SAAS,CAAC,GAAG,EAAE,CAAC;gBAEpC,KAAK,CAAC,OAAO,GAAG,GAAG,CAAC,GAAG,CAAC,IAAI;uBAE1B,cAAc,CAAC,OAAO,EAAE,GAAG,EAAE,QAAQ,KACrC,cAAc,CAAC,OAAO,EAAE,WAAW,EAAE,QAAQ;YAEjD,CAAC,GACD,CAAC;uBACM,IAAI;YACb,CAAC;YAED,KAAK,CAAC,GAAG,CAAC,iBAAiB,CACzB,IAAI,EACJ,MAAM,EACN,IAAI,GACH,+CAA+C;QAEpD,CAAC;QAED,EAAE,EAAE,KAAK,CAAC,OAAO,CAAC,KAAK,GAAG,CAAC;YACzB,KAAK,CAAC,IAAI,EAAE,KAAK,EAAE,IAAI;YAEvB,EAAE,EACA,KAAK,CAAC,KAAK,EAAE,WAAW,EAAE,KAAK,GAAK,CAAC;gBACnC,KAAK,CAAC,OAAO,GAAG,GAAG,CAAC,GAAG,CAAC,IAAI;uBACrB,cAAc,CAAC,OAAO,EAAE,WAAW,KAAK,IAAI,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;YAChE,CAAC,GACD,CAAC;uBACM,IAAI;YACb,CAAC;YAED,KAAK,CAAC,GAAG,CAAC,iBAAiB,CACzB,IAAI,EACJ,MAAM,EACN,IAAI,GACH,8CAA8C;QAEnD,CAAC;QAED,EAA0C,AAA1C,wCAA0C;QAC1C,EAA0D,AAA1D,wDAA0D;QAC1D,KAAK,CAAC,GAAG,CAAC,iBAAiB,CACzB,IAAI,EACJ,MAAM,EACN,IAAI,GACJ,CAAG,IACD,IAAI,IACJ,CAAG,KACF,IAAI,MAAK,MAAQ,KACb,GAAG,EAAE,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,EAAE,EAAE,WAElD,+EAAiF;IAEvF,CAAC;WAEM,cAAc,CAAC,GAAG,CAAC,GAAG,IAAI,KAAK;AACxC,CAAC;MAEY,iBAAiB,SAAS,KAAK;gBAC9B,IAAY,EAAE,MAAc,EAAE,IAAY,EAAE,OAAe,CAAE,CAAC;QACxE,KAAK,CACH,IAAI,IACC,oBAAoB,EAAE,IAAI,CAAC,mBAAmB,EAAE,MAAM,CAAC,OAAO,EAAE,IAAI,CAAC,YAAY,EAAE,OAAO,MAC1F,wCAAwC,EAAE,MAAM,CAAC,OAAO,EAAE,IAAI,CAAC,YAAY,EAAE,OAAO;IAE7F,CAAC;;QAPU,iBAAiB,GAAjB,iBAAiB"}