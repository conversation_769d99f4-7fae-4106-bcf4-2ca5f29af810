{"version": 3, "sources": ["../../client/next.js"], "sourcesContent": ["import { initNext, version, router, emitter, render, renderError } from './'\n\nwindow.next = {\n  version,\n  // router is initialized later so it has to be live-binded\n  get router() {\n    return router\n  },\n  emitter,\n  render,\n  renderError,\n}\n\ninitNext().catch(console.error)\n"], "names": [], "mappings": ";AAAwE,GAAI,CAAJ,CAAI;AAE5E,MAAM,CAAC,IAAI;IACT,OAAO,EAH+D,CAAI;IAI1E,EAA0D,AAA1D,wDAA0D;QACtD,MAAM,IAAG,CAAC;eALwD,CAAI;IAO1E,CAAC;IACD,OAAO,EAR+D,CAAI;IAS1E,MAAM,EATgE,CAAI;IAU1E,WAAW,EAV2D,CAAI;;IAAJ,CAAI,aAajE,KAAK,CAAC,OAAO,CAAC,KAAK"}