const { spawn } = require('child_process');
const path = require('path');

console.log('Starting Next.js development server with legacy OpenSSL provider...');

const nextBin = path.join(__dirname, 'node_modules', 'next', 'dist', 'bin', 'next');
const child = spawn('node', ['--openssl-legacy-provider', nextBin, 'dev'], {
  stdio: 'inherit',
  cwd: __dirname
});

child.on('error', (error) => {
  console.error('Error starting server:', error);
});

child.on('exit', (code) => {
  console.log(`Server exited with code ${code}`);
});
