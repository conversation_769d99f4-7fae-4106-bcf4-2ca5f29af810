{"version": 3, "sources": ["../../lib/recursive-delete.ts"], "sourcesContent": ["import { Dirent, promises } from 'fs'\nimport { join } from 'path'\nimport { promisify } from 'util'\n\nconst sleep = promisify(setTimeout)\n\nconst unlinkFile = async (p: string, t = 1): Promise<void> => {\n  try {\n    await promises.unlink(p)\n  } catch (e) {\n    if (\n      (e.code === 'EBUSY' ||\n        e.code === 'ENOTEMPTY' ||\n        e.code === 'EPERM' ||\n        e.code === 'EMFILE') &&\n      t < 3\n    ) {\n      await sleep(t * 100)\n      return unlinkFile(p, t++)\n    }\n\n    if (e.code === 'ENOENT') {\n      return\n    }\n\n    throw e\n  }\n}\n\n/**\n * Recursively delete directory contents\n * @param  {string} dir Directory to delete the contents of\n * @param  {RegExp} [exclude] Exclude based on relative file path\n * @param  {string} [previousPath] Ensures that parameter dir exists, this is not passed recursively\n * @returns Promise void\n */\nexport async function recursiveDelete(\n  dir: string,\n  exclude?: RegExp,\n  previousPath: string = ''\n): Promise<void> {\n  let result\n  try {\n    result = await promises.readdir(dir, { withFileTypes: true })\n  } catch (e) {\n    if (e.code === 'ENOENT') {\n      return\n    }\n    throw e\n  }\n\n  await Promise.all(\n    result.map(async (part: Dirent) => {\n      const absolutePath = join(dir, part.name)\n\n      // readdir does not follow symbolic links\n      // if part is a symbolic link, follow it using stat\n      let isDirectory = part.isDirectory()\n      if (part.isSymbolicLink()) {\n        const stats = await promises.stat(absolutePath)\n        isDirectory = stats.isDirectory()\n      }\n\n      const pp = join(previousPath, part.name)\n      if (isDirectory && (!exclude || !exclude.test(pp))) {\n        await recursiveDelete(absolutePath, exclude, pp)\n        return promises.rmdir(absolutePath)\n      }\n\n      if (!exclude || !exclude.test(pp)) {\n        return unlinkFile(absolutePath)\n      }\n    })\n  )\n}\n"], "names": [], "mappings": ";;;;QAoCsB,eAAe,GAAf,eAAe;AApCJ,GAAI,CAAJ,GAAI;AAChB,GAAM,CAAN,KAAM;AACD,GAAM,CAAN,KAAM;AAEhC,KAAK,CAAC,KAAK,OAFe,KAAM,YAER,UAAU;AAElC,KAAK,CAAC,UAAU,UAAU,CAAS,EAAE,CAAC,GAAG,CAAC,GAAoB,CAAC;QACzD,CAAC;cAP0B,GAAI,UAQlB,MAAM,CAAC,CAAC;IACzB,CAAC,QAAQ,CAAC,EAAE,CAAC;QACX,EAAE,GACC,CAAC,CAAC,IAAI,MAAK,KAAO,KACjB,CAAC,CAAC,IAAI,MAAK,SAAW,KACtB,CAAC,CAAC,IAAI,MAAK,KAAO,KAClB,CAAC,CAAC,IAAI,MAAK,MAAQ,MACrB,CAAC,GAAG,CAAC,EACL,CAAC;kBACK,KAAK,CAAC,CAAC,GAAG,GAAG;mBACZ,UAAU,CAAC,CAAC,EAAE,CAAC;QACxB,CAAC;QAED,EAAE,EAAE,CAAC,CAAC,IAAI,MAAK,MAAQ,GAAE,CAAC;;QAE1B,CAAC;QAED,KAAK,CAAC,CAAC;IACT,CAAC;AACH,CAAC;eASqB,eAAe,CACnC,GAAW,EACX,OAAgB,EAChB,YAAoB,OACL,CAAC;IAChB,GAAG,CAAC,MAAM;QACN,CAAC;QACH,MAAM,SA3CuB,GAAI,UA2CT,OAAO,CAAC,GAAG;YAAI,aAAa,EAAE,IAAI;;IAC5D,CAAC,QAAQ,CAAC,EAAE,CAAC;QACX,EAAE,EAAE,CAAC,CAAC,IAAI,MAAK,MAAQ,GAAE,CAAC;;QAE1B,CAAC;QACD,KAAK,CAAC,CAAC;IACT,CAAC;UAEK,OAAO,CAAC,GAAG,CACf,MAAM,CAAC,GAAG,QAAQ,IAAY,GAAK,CAAC;QAClC,KAAK,CAAC,YAAY,OApDH,KAAM,OAoDK,GAAG,EAAE,IAAI,CAAC,IAAI;QAExC,EAAyC,AAAzC,uCAAyC;QACzC,EAAmD,AAAnD,iDAAmD;QACnD,GAAG,CAAC,WAAW,GAAG,IAAI,CAAC,WAAW;QAClC,EAAE,EAAE,IAAI,CAAC,cAAc,IAAI,CAAC;YAC1B,KAAK,CAAC,KAAK,SA3Dc,GAAI,UA2DA,IAAI,CAAC,YAAY;YAC9C,WAAW,GAAG,KAAK,CAAC,WAAW;QACjC,CAAC;QAED,KAAK,CAAC,EAAE,OA9DO,KAAM,OA8DL,YAAY,EAAE,IAAI,CAAC,IAAI;QACvC,EAAE,EAAE,WAAW,MAAM,OAAO,KAAK,OAAO,CAAC,IAAI,CAAC,EAAE,IAAI,CAAC;kBAC7C,eAAe,CAAC,YAAY,EAAE,OAAO,EAAE,EAAE;mBAjEtB,GAAI,UAkEb,KAAK,CAAC,YAAY;QACpC,CAAC;QAED,EAAE,GAAG,OAAO,KAAK,OAAO,CAAC,IAAI,CAAC,EAAE,GAAG,CAAC;mBAC3B,UAAU,CAAC,YAAY;QAChC,CAAC;IACH,CAAC;AAEL,CAAC"}