{"version": 3, "sources": ["../../../../../../build/webpack/plugins/terser-webpack-plugin/src/minify.js"], "sourcesContent": ["// @ts-nocheck\nimport terser from 'next/dist/compiled/terser'\n\nfunction buildTerserOptions(terserOptions = {}) {\n  return {\n    ...terserOptions,\n    mangle:\n      terserOptions.mangle == null\n        ? true\n        : typeof terserOptions.mangle === 'boolean'\n        ? terserOptions.mangle\n        : { ...terserOptions.mangle },\n    // Ignoring sourceMap from options\n    // eslint-disable-next-line no-undefined\n    sourceMap: undefined,\n    // the `output` option is deprecated\n    ...(terserOptions.format\n      ? { format: { beautify: false, ...terserOptions.format } }\n      : { output: { beautify: false, ...terserOptions.output } }),\n  }\n}\n\nexport async function minify(options) {\n  const { name, input, inputSourceMap, terserOptions } = options\n  // Copy terser options\n  const opts = buildTerserOptions(terserOptions)\n\n  // Let terser generate a SourceMap\n  if (inputSourceMap) {\n    // @ts-ignore\n    opts.sourceMap = { asObject: true }\n  }\n\n  const result = await terser.minify({ [name]: input }, opts)\n  return result\n}\n"], "names": [], "mappings": ";;;;QAsBsB,MAAM,GAAN,MAAM;AArBT,GAA2B,CAA3B,OAA2B;;;;;;SAErC,kBAAkB,CAAC,aAAa;GAAO,CAAC;;WAE1C,aAAa;QAChB,MAAM,EACJ,aAAa,CAAC,MAAM,IAAI,IAAI,GACxB,IAAI,UACG,aAAa,CAAC,MAAM,MAAK,OAAS,IACzC,aAAa,CAAC,MAAM;eACf,aAAa,CAAC,MAAM;;QAC/B,EAAkC,AAAlC,gCAAkC;QAClC,EAAwC,AAAxC,sCAAwC;QACxC,SAAS,EAAE,SAAS;QACpB,EAAoC,AAApC,kCAAoC;WAChC,aAAa,CAAC,MAAM;YAClB,MAAM;gBAAI,QAAQ,EAAE,KAAK;mBAAK,aAAa,CAAC,MAAM;;;YAClD,MAAM;gBAAI,QAAQ,EAAE,KAAK;mBAAK,aAAa,CAAC,MAAM;;;;AAE5D,CAAC;eAEqB,MAAM,CAAC,OAAO,EAAE,CAAC;IACrC,KAAK,GAAG,IAAI,GAAE,KAAK,GAAE,cAAc,GAAE,aAAa,MAAK,OAAO;IAC9D,EAAsB,AAAtB,oBAAsB;IACtB,KAAK,CAAC,IAAI,GAAG,kBAAkB,CAAC,aAAa;IAE7C,EAAkC,AAAlC,gCAAkC;IAClC,EAAE,EAAE,cAAc,EAAE,CAAC;QACnB,EAAa,AAAb,WAAa;QACb,IAAI,CAAC,SAAS;YAAK,QAAQ,EAAE,IAAI;;IACnC,CAAC;IAED,KAAK,CAAC,MAAM,SAhCK,OAA2B,SAgChB,MAAM;SAAI,IAAI,GAAG,KAAK;OAAI,IAAI;WACnD,MAAM;AACf,CAAC"}