{"version": 3, "sources": ["../../../../build/webpack/config/utils.ts"], "sourcesContent": ["import { webpack } from 'next/dist/compiled/webpack/webpack'\nimport { NextConfigComplete } from '../../../server/config-shared'\n\nexport type ConfigurationContext = {\n  rootDirectory: string\n  customAppFile: RegExp\n\n  isDevelopment: boolean\n  isProduction: boolean\n\n  isServer: boolean\n  isClient: boolean\n\n  assetPrefix: string\n\n  sassOptions: any\n  productionBrowserSourceMaps: boolean\n\n  future: NextConfigComplete['future']\n\n  isCraCompat?: boolean\n}\n\nexport type ConfigurationFn = (\n  a: webpack.Configuration\n) => webpack.Configuration\n\nexport const pipe =\n  <R>(...fns: Array<(a: R) => R | Promise<R>>) =>\n  (param: R) =>\n    fns.reduce(\n      async (result: R | Promise<R>, next) => next(await result),\n      param\n    )\n"], "names": [], "mappings": ";;;;;AA2BO,KAAK,CAAC,IAAI,OACR,GAAG,IACT,KAAQ,GACP,GAAG,CAAC,MAAM,QACD,MAAsB,EAAE,IAAI,GAAK,IAAI,OAAO,MAAM;UACzD,KAAK;;QALE,IAAI,GAAJ,IAAI"}