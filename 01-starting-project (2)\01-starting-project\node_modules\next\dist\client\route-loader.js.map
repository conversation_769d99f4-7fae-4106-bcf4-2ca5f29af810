{"version": 3, "sources": ["../../client/route-loader.ts"], "sourcesContent": ["import { ComponentType } from 'react'\nimport { ClientBuildManifest } from '../build/webpack/plugins/build-manifest-plugin'\nimport getAssetPathFromRoute from '../shared/lib/router/utils/get-asset-path-from-route'\nimport { requestIdleCallback } from './request-idle-callback'\n\n// 3.8s was arbitrarily chosen as it's what https://web.dev/interactive\n// considers as \"Good\" time-to-interactive. We must assume something went\n// wrong beyond this point, and then fall-back to a full page transition to\n// show the user something of value.\nconst MS_MAX_IDLE_DELAY = 3800\n\ndeclare global {\n  interface Window {\n    __BUILD_MANIFEST?: ClientBuildManifest\n    __BUILD_MANIFEST_CB?: Function\n  }\n}\n\nexport interface LoadedEntrypointSuccess {\n  component: ComponentType\n  exports: any\n}\nexport interface LoadedEntrypointFailure {\n  error: unknown\n}\nexport type RouteEntrypoint = LoadedEntrypointSuccess | LoadedEntrypointFailure\n\nexport interface RouteStyleSheet {\n  href: string\n  content: string\n}\n\nexport interface LoadedRouteSuccess extends LoadedEntrypointSuccess {\n  styles: RouteStyleSheet[]\n}\nexport interface LoadedRouteFailure {\n  error: unknown\n}\nexport type RouteLoaderEntry = LoadedRouteSuccess | LoadedRouteFailure\n\nexport type Future<V> = {\n  resolve: (entrypoint: V) => void\n  future: Promise<V>\n}\nfunction withFuture<T>(\n  key: string,\n  map: Map<string, Future<T> | T>,\n  generator?: () => Promise<T>\n): Promise<T> {\n  let entry: Future<T> | T | undefined = map.get(key)\n  if (entry) {\n    if ('future' in entry) {\n      return entry.future\n    }\n    return Promise.resolve(entry)\n  }\n  let resolver: (entrypoint: T) => void\n  const prom: Promise<T> = new Promise<T>((resolve) => {\n    resolver = resolve\n  })\n  map.set(key, (entry = { resolve: resolver!, future: prom }))\n  return generator\n    ? // eslint-disable-next-line no-sequences\n      generator().then((value) => (resolver(value), value))\n    : prom\n}\n\nexport interface RouteLoader {\n  whenEntrypoint(route: string): Promise<RouteEntrypoint>\n  onEntrypoint(route: string, execute: () => unknown): void\n  loadRoute(route: string, prefetch?: boolean): Promise<RouteLoaderEntry>\n  prefetch(route: string): Promise<void>\n}\n\nfunction hasPrefetch(link?: HTMLLinkElement): boolean {\n  try {\n    link = document.createElement('link')\n    return (\n      // detect IE11 since it supports prefetch but isn't detected\n      // with relList.support\n      (!!window.MSInputMethodContext && !!(document as any).documentMode) ||\n      link.relList.supports('prefetch')\n    )\n  } catch {\n    return false\n  }\n}\n\nconst canPrefetch: boolean = hasPrefetch()\n\nfunction prefetchViaDom(\n  href: string,\n  as: string,\n  link?: HTMLLinkElement\n): Promise<any> {\n  return new Promise<void>((res, rej) => {\n    if (document.querySelector(`link[rel=\"prefetch\"][href^=\"${href}\"]`)) {\n      return res()\n    }\n\n    link = document.createElement('link')\n\n    // The order of property assignment here is intentional:\n    if (as) link!.as = as\n    link!.rel = `prefetch`\n    link!.crossOrigin = process.env.__NEXT_CROSS_ORIGIN!\n    link!.onload = res as any\n    link!.onerror = rej\n\n    // `href` should always be last:\n    link!.href = href\n\n    document.head.appendChild(link)\n  })\n}\n\nconst ASSET_LOAD_ERROR = Symbol('ASSET_LOAD_ERROR')\n// TODO: unexport\nexport function markAssetError(err: Error): Error {\n  return Object.defineProperty(err, ASSET_LOAD_ERROR, {})\n}\n\nexport function isAssetError(err?: Error): boolean | undefined {\n  return err && ASSET_LOAD_ERROR in err\n}\n\nfunction appendScript(\n  src: string,\n  script?: HTMLScriptElement\n): Promise<unknown> {\n  return new Promise((resolve, reject) => {\n    script = document.createElement('script')\n\n    // The order of property assignment here is intentional.\n    // 1. Setup success/failure hooks in case the browser synchronously\n    //    executes when `src` is set.\n    script.onload = resolve\n    script.onerror = () =>\n      reject(markAssetError(new Error(`Failed to load script: ${src}`)))\n\n    // 2. Configure the cross-origin attribute before setting `src` in case the\n    //    browser begins to fetch.\n    script.crossOrigin = process.env.__NEXT_CROSS_ORIGIN!\n\n    // 3. Finally, set the source and inject into the DOM in case the child\n    //    must be appended for fetching to start.\n    script.src = src\n    document.body.appendChild(script)\n  })\n}\n\n// We wait for pages to be built in dev before we start the route transition\n// timeout to prevent an un-necessary hard navigation in development.\nlet devBuildPromise: Promise<void> | undefined\n\n// Resolve a promise that times out after given amount of milliseconds.\nfunction resolvePromiseWithTimeout<T>(\n  p: Promise<T>,\n  ms: number,\n  err: Error\n): Promise<T> {\n  return new Promise((resolve, reject) => {\n    let cancelled = false\n\n    p.then((r) => {\n      // Resolved, cancel the timeout\n      cancelled = true\n      resolve(r)\n    }).catch(reject)\n\n    // We wrap these checks separately for better dead-code elimination in\n    // production bundles.\n    if (process.env.NODE_ENV === 'development') {\n      ;(devBuildPromise || Promise.resolve()).then(() => {\n        requestIdleCallback(() =>\n          setTimeout(() => {\n            if (!cancelled) {\n              reject(err)\n            }\n          }, ms)\n        )\n      })\n    }\n\n    if (process.env.NODE_ENV !== 'development') {\n      requestIdleCallback(() =>\n        setTimeout(() => {\n          if (!cancelled) {\n            reject(err)\n          }\n        }, ms)\n      )\n    }\n  })\n}\n\n// TODO: stop exporting or cache the failure\n// It'd be best to stop exporting this. It's an implementation detail. We're\n// only exporting it for backwards compatibility with the `page-loader`.\n// Only cache this response as a last resort if we cannot eliminate all other\n// code branches that use the Build Manifest Callback and push them through\n// the Route Loader interface.\nexport function getClientBuildManifest(): Promise<ClientBuildManifest> {\n  if (self.__BUILD_MANIFEST) {\n    return Promise.resolve(self.__BUILD_MANIFEST)\n  }\n\n  const onBuildManifest: Promise<ClientBuildManifest> =\n    new Promise<ClientBuildManifest>((resolve) => {\n      // Mandatory because this is not concurrent safe:\n      const cb = self.__BUILD_MANIFEST_CB\n      self.__BUILD_MANIFEST_CB = () => {\n        resolve(self.__BUILD_MANIFEST!)\n        cb && cb()\n      }\n    })\n\n  return resolvePromiseWithTimeout<ClientBuildManifest>(\n    onBuildManifest,\n    MS_MAX_IDLE_DELAY,\n    markAssetError(new Error('Failed to load client build manifest'))\n  )\n}\n\ninterface RouteFiles {\n  scripts: string[]\n  css: string[]\n}\nfunction getFilesForRoute(\n  assetPrefix: string,\n  route: string\n): Promise<RouteFiles> {\n  if (process.env.NODE_ENV === 'development') {\n    return Promise.resolve({\n      scripts: [\n        assetPrefix +\n          '/_next/static/chunks/pages' +\n          encodeURI(getAssetPathFromRoute(route, '.js')),\n      ],\n      // Styles are handled by `style-loader` in development:\n      css: [],\n    })\n  }\n  return getClientBuildManifest().then((manifest) => {\n    if (!(route in manifest)) {\n      throw markAssetError(new Error(`Failed to lookup route: ${route}`))\n    }\n    const allFiles = manifest[route].map(\n      (entry) => assetPrefix + '/_next/' + encodeURI(entry)\n    )\n    return {\n      scripts: allFiles.filter((v) => v.endsWith('.js')),\n      css: allFiles.filter((v) => v.endsWith('.css')),\n    }\n  })\n}\n\nexport function createRouteLoader(assetPrefix: string): RouteLoader {\n  const entrypoints: Map<string, Future<RouteEntrypoint> | RouteEntrypoint> =\n    new Map()\n  const loadedScripts: Map<string, Promise<unknown>> = new Map()\n  const styleSheets: Map<string, Promise<RouteStyleSheet>> = new Map()\n  const routes: Map<string, Future<RouteLoaderEntry> | RouteLoaderEntry> =\n    new Map()\n\n  function maybeExecuteScript(src: string): Promise<unknown> {\n    let prom: Promise<unknown> | undefined = loadedScripts.get(src)\n    if (prom) {\n      return prom\n    }\n\n    // Skip executing script if it's already in the DOM:\n    if (document.querySelector(`script[src^=\"${src}\"]`)) {\n      return Promise.resolve()\n    }\n\n    loadedScripts.set(src, (prom = appendScript(src)))\n    return prom\n  }\n\n  function fetchStyleSheet(href: string): Promise<RouteStyleSheet> {\n    let prom: Promise<RouteStyleSheet> | undefined = styleSheets.get(href)\n    if (prom) {\n      return prom\n    }\n\n    styleSheets.set(\n      href,\n      (prom = fetch(href)\n        .then((res) => {\n          if (!res.ok) {\n            throw new Error(`Failed to load stylesheet: ${href}`)\n          }\n          return res.text().then((text) => ({ href: href, content: text }))\n        })\n        .catch((err) => {\n          throw markAssetError(err)\n        }))\n    )\n    return prom\n  }\n\n  return {\n    whenEntrypoint(route: string) {\n      return withFuture(route, entrypoints)\n    },\n    onEntrypoint(route: string, execute: () => unknown) {\n      Promise.resolve(execute)\n        .then((fn) => fn())\n        .then(\n          (exports: any) => ({\n            component: (exports && exports.default) || exports,\n            exports: exports,\n          }),\n          (err) => ({ error: err })\n        )\n        .then((input: RouteEntrypoint) => {\n          const old = entrypoints.get(route)\n          entrypoints.set(route, input)\n          if (old && 'resolve' in old) old.resolve(input)\n        })\n    },\n    loadRoute(route: string, prefetch?: boolean) {\n      return withFuture<RouteLoaderEntry>(route, routes, () => {\n        const routeFilesPromise = getFilesForRoute(assetPrefix, route)\n          .then(({ scripts, css }) => {\n            return Promise.all([\n              entrypoints.has(route)\n                ? []\n                : Promise.all(scripts.map(maybeExecuteScript)),\n              Promise.all(css.map(fetchStyleSheet)),\n            ] as const)\n          })\n          .then((res) => {\n            return this.whenEntrypoint(route).then((entrypoint) => ({\n              entrypoint,\n              styles: res[1],\n            }))\n          })\n\n        if (process.env.NODE_ENV === 'development') {\n          devBuildPromise = new Promise<void>((resolve) => {\n            if (routeFilesPromise) {\n              return routeFilesPromise.finally(() => {\n                resolve()\n              })\n            }\n          })\n        }\n\n        return resolvePromiseWithTimeout(\n          routeFilesPromise,\n          MS_MAX_IDLE_DELAY,\n          markAssetError(new Error(`Route did not complete loading: ${route}`))\n        )\n          .then(({ entrypoint, styles }) => {\n            const res: RouteLoaderEntry = Object.assign<\n              { styles: RouteStyleSheet[] },\n              RouteEntrypoint\n            >({ styles: styles! }, entrypoint)\n            return 'error' in entrypoint ? entrypoint : res\n          })\n          .catch((err) => {\n            if (prefetch) {\n              // we don't want to cache errors during prefetch\n              throw err\n            }\n            return { error: err }\n          })\n      })\n    },\n    prefetch(route: string): Promise<void> {\n      // https://github.com/GoogleChromeLabs/quicklink/blob/453a661fa1fa940e2d2e044452398e38c67a98fb/src/index.mjs#L115-L118\n      // License: Apache 2.0\n      let cn\n      if ((cn = (navigator as any).connection)) {\n        // Don't prefetch if using 2G or if Save-Data is enabled.\n        if (cn.saveData || /2g/.test(cn.effectiveType)) return Promise.resolve()\n      }\n      return getFilesForRoute(assetPrefix, route)\n        .then((output) =>\n          Promise.all(\n            canPrefetch\n              ? output.scripts.map((script) => prefetchViaDom(script, 'script'))\n              : []\n          )\n        )\n        .then(() => {\n          requestIdleCallback(() => this.loadRoute(route, true).catch(() => {}))\n        })\n        .catch(\n          // swallow prefetch errors\n          () => {}\n        )\n    },\n  }\n}\n"], "names": [], "mappings": ";;;;QAsHgB,cAAc,GAAd,cAAc;QAId,YAAY,GAAZ,YAAY;QAgFZ,sBAAsB,GAAtB,sBAAsB;QAuDtB,iBAAiB,GAAjB,iBAAiB;AA/PC,GAAsD,CAAtD,sBAAsD;AACpD,GAAyB,CAAzB,oBAAyB;;;;;;AAE7D,EAAuE,AAAvE,qEAAuE;AACvE,EAAyE,AAAzE,uEAAyE;AACzE,EAA2E,AAA3E,yEAA2E;AAC3E,EAAoC,AAApC,kCAAoC;AACpC,KAAK,CAAC,iBAAiB,GAAG,IAAI;SAmCrB,UAAU,CACjB,GAAW,EACX,GAA+B,EAC/B,SAA4B,EAChB,CAAC;IACb,GAAG,CAAC,KAAK,GAA8B,GAAG,CAAC,GAAG,CAAC,GAAG;IAClD,EAAE,EAAE,KAAK,EAAE,CAAC;QACV,EAAE,GAAE,MAAQ,KAAI,KAAK,EAAE,CAAC;mBACf,KAAK,CAAC,MAAM;QACrB,CAAC;eACM,OAAO,CAAC,OAAO,CAAC,KAAK;IAC9B,CAAC;IACD,GAAG,CAAC,QAAQ;IACZ,KAAK,CAAC,IAAI,GAAe,GAAG,CAAC,OAAO,EAAK,OAAO,GAAK,CAAC;QACpD,QAAQ,GAAG,OAAO;IACpB,CAAC;IACD,GAAG,CAAC,GAAG,CAAC,GAAG,EAAG,KAAK;QAAK,OAAO,EAAE,QAAQ;QAAG,MAAM,EAAE,IAAI;;WACjD,SAAS,GAEZ,SAAS,GAAG,IAAI,EAAE,KAAK,IAAM,QAAQ,CAAC,KAAK,GAAG,KAAK;QACnD,IAAI;AACV,CAAC;SASQ,WAAW,CAAC,IAAsB,EAAW,CAAC;QACjD,CAAC;QACH,IAAI,GAAG,QAAQ,CAAC,aAAa,EAAC,IAAM;eAElC,EAA4D,AAA5D,0DAA4D;QAC5D,EAAuB,AAAvB,qBAAuB;WACpB,MAAM,CAAC,oBAAoB,MAAO,QAAQ,CAAS,YAAY,KAClE,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAC,QAAU;IAEpC,CAAC,WAAO,CAAC;eACA,KAAK;IACd,CAAC;AACH,CAAC;AAED,KAAK,CAAC,WAAW,GAAY,WAAW;SAE/B,cAAc,CACrB,IAAY,EACZ,EAAU,EACV,IAAsB,EACR,CAAC;WACR,GAAG,CAAC,OAAO,EAAQ,GAAG,EAAE,GAAG,GAAK,CAAC;QACtC,EAAE,EAAE,QAAQ,CAAC,aAAa,EAAE,4BAA4B,EAAE,IAAI,CAAC,EAAE,IAAI,CAAC;mBAC7D,GAAG;QACZ,CAAC;QAED,IAAI,GAAG,QAAQ,CAAC,aAAa,EAAC,IAAM;QAEpC,EAAwD,AAAxD,sDAAwD;QACxD,EAAE,EAAE,EAAE,EAAE,IAAI,CAAE,EAAE,GAAG,EAAE;QACrB,IAAI,CAAE,GAAG,IAAI,QAAQ;QACrB,IAAI,CAAE,WAAW,GAAG,OAAO,CAAC,GAAG,CAAC,mBAAmB;QACnD,IAAI,CAAE,MAAM,GAAG,GAAG;QAClB,IAAI,CAAE,OAAO,GAAG,GAAG;QAEnB,EAAgC,AAAhC,8BAAgC;QAChC,IAAI,CAAE,IAAI,GAAG,IAAI;QAEjB,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI;IAChC,CAAC;AACH,CAAC;AAED,KAAK,CAAC,gBAAgB,GAAG,MAAM,EAAC,gBAAkB;SAElC,cAAc,CAAC,GAAU,EAAS,CAAC;WAC1C,MAAM,CAAC,cAAc,CAAC,GAAG,EAAE,gBAAgB;;AACpD,CAAC;SAEe,YAAY,CAAC,GAAW,EAAuB,CAAC;WACvD,GAAG,IAAI,gBAAgB,IAAI,GAAG;AACvC,CAAC;SAEQ,YAAY,CACnB,GAAW,EACX,MAA0B,EACR,CAAC;WACZ,GAAG,CAAC,OAAO,EAAE,OAAO,EAAE,MAAM,GAAK,CAAC;QACvC,MAAM,GAAG,QAAQ,CAAC,aAAa,EAAC,MAAQ;QAExC,EAAwD,AAAxD,sDAAwD;QACxD,EAAmE,AAAnE,iEAAmE;QACnE,EAAiC,AAAjC,+BAAiC;QACjC,MAAM,CAAC,MAAM,GAAG,OAAO;QACvB,MAAM,CAAC,OAAO,OACZ,MAAM,CAAC,cAAc,CAAC,GAAG,CAAC,KAAK,EAAE,uBAAuB,EAAE,GAAG;;QAE/D,EAA2E,AAA3E,yEAA2E;QAC3E,EAA8B,AAA9B,4BAA8B;QAC9B,MAAM,CAAC,WAAW,GAAG,OAAO,CAAC,GAAG,CAAC,mBAAmB;QAEpD,EAAuE,AAAvE,qEAAuE;QACvE,EAA6C,AAA7C,2CAA6C;QAC7C,MAAM,CAAC,GAAG,GAAG,GAAG;QAChB,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM;IAClC,CAAC;AACH,CAAC;AAED,EAA4E,AAA5E,0EAA4E;AAC5E,EAAqE,AAArE,mEAAqE;AACrE,GAAG,CAAC,eAAe;AAEnB,EAAuE,AAAvE,qEAAuE;SAC9D,yBAAyB,CAChC,CAAa,EACb,EAAU,EACV,GAAU,EACE,CAAC;WACN,GAAG,CAAC,OAAO,EAAE,OAAO,EAAE,MAAM,GAAK,CAAC;QACvC,GAAG,CAAC,SAAS,GAAG,KAAK;QAErB,CAAC,CAAC,IAAI,EAAE,CAAC,GAAK,CAAC;YACb,EAA+B,AAA/B,6BAA+B;YAC/B,SAAS,GAAG,IAAI;YAChB,OAAO,CAAC,CAAC;QACX,CAAC,EAAE,KAAK,CAAC,MAAM;QAEf,EAAsE,AAAtE,oEAAsE;QACtE,EAAsB,AAAtB,oBAAsB;QACtB,EAAE,EAAE,OAAO,CAAC,GAAG,CAAC,QAAQ,MAAK,WAAa,GAAE,CAAC;aACzC,eAAe,IAAI,OAAO,CAAC,OAAO,IAAI,IAAI,KAAO,CAAC;oBA1KtB,oBAAyB,0BA4KnD,UAAU,KAAO,CAAC;wBAChB,EAAE,GAAG,SAAS,EAAE,CAAC;4BACf,MAAM,CAAC,GAAG;wBACZ,CAAC;oBACH,CAAC,EAAE,EAAE;;YAET,CAAC;QACH,CAAC;QAED,EAAE,EAAE,OAAO,CAAC,GAAG,CAAC,QAAQ,MAAK,WAAa,GAAE,CAAC;gBArLb,oBAAyB,0BAuLrD,UAAU,KAAO,CAAC;oBAChB,EAAE,GAAG,SAAS,EAAE,CAAC;wBACf,MAAM,CAAC,GAAG;oBACZ,CAAC;gBACH,CAAC,EAAE,EAAE;;QAET,CAAC;IACH,CAAC;AACH,CAAC;SAQe,sBAAsB,GAAiC,CAAC;IACtE,EAAE,EAAE,IAAI,CAAC,gBAAgB,EAAE,CAAC;eACnB,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,gBAAgB;IAC9C,CAAC;IAED,KAAK,CAAC,eAAe,GACnB,GAAG,CAAC,OAAO,EAAuB,OAAO,GAAK,CAAC;QAC7C,EAAiD,AAAjD,+CAAiD;QACjD,KAAK,CAAC,EAAE,GAAG,IAAI,CAAC,mBAAmB;QACnC,IAAI,CAAC,mBAAmB,OAAS,CAAC;YAChC,OAAO,CAAC,IAAI,CAAC,gBAAgB;YAC7B,EAAE,IAAI,EAAE;QACV,CAAC;IACH,CAAC;WAEI,yBAAyB,CAC9B,eAAe,EACf,iBAAiB,EACjB,cAAc,CAAC,GAAG,CAAC,KAAK,EAAC,oCAAsC;AAEnE,CAAC;SAMQ,gBAAgB,CACvB,WAAmB,EACnB,KAAa,EACQ,CAAC;IACtB,EAAE,EAAE,OAAO,CAAC,GAAG,CAAC,QAAQ,MAAK,WAAa,GAAE,CAAC;eACpC,OAAO,CAAC,OAAO;YACpB,OAAO;gBACL,WAAW,IACT,0BAA4B,IAC5B,SAAS,KA3Oe,sBAAsD,UA2O9C,KAAK,GAAE,GAAK;;YAEhD,EAAuD,AAAvD,qDAAuD;YACvD,GAAG;;IAEP,CAAC;WACM,sBAAsB,GAAG,IAAI,EAAE,QAAQ,GAAK,CAAC;QAClD,EAAE,IAAI,KAAK,IAAI,QAAQ,GAAG,CAAC;YACzB,KAAK,CAAC,cAAc,CAAC,GAAG,CAAC,KAAK,EAAE,wBAAwB,EAAE,KAAK;QACjE,CAAC;QACD,KAAK,CAAC,QAAQ,GAAG,QAAQ,CAAC,KAAK,EAAE,GAAG,EACjC,KAAK,GAAK,WAAW,IAAG,OAAS,IAAG,SAAS,CAAC,KAAK;;;YAGpD,OAAO,EAAE,QAAQ,CAAC,MAAM,EAAE,CAAC,GAAK,CAAC,CAAC,QAAQ,EAAC,GAAK;;YAChD,GAAG,EAAE,QAAQ,CAAC,MAAM,EAAE,CAAC,GAAK,CAAC,CAAC,QAAQ,EAAC,IAAM;;;IAEjD,CAAC;AACH,CAAC;SAEe,iBAAiB,CAAC,WAAmB,EAAe,CAAC;IACnE,KAAK,CAAC,WAAW,GACf,GAAG,CAAC,GAAG;IACT,KAAK,CAAC,aAAa,GAAkC,GAAG,CAAC,GAAG;IAC5D,KAAK,CAAC,WAAW,GAA0C,GAAG,CAAC,GAAG;IAClE,KAAK,CAAC,MAAM,GACV,GAAG,CAAC,GAAG;aAEA,kBAAkB,CAAC,GAAW,EAAoB,CAAC;QAC1D,GAAG,CAAC,IAAI,GAAiC,aAAa,CAAC,GAAG,CAAC,GAAG;QAC9D,EAAE,EAAE,IAAI,EAAE,CAAC;mBACF,IAAI;QACb,CAAC;QAED,EAAoD,AAApD,kDAAoD;QACpD,EAAE,EAAE,QAAQ,CAAC,aAAa,EAAE,aAAa,EAAE,GAAG,CAAC,EAAE,IAAI,CAAC;mBAC7C,OAAO,CAAC,OAAO;QACxB,CAAC;QAED,aAAa,CAAC,GAAG,CAAC,GAAG,EAAG,IAAI,GAAG,YAAY,CAAC,GAAG;eACxC,IAAI;IACb,CAAC;aAEQ,eAAe,CAAC,IAAY,EAA4B,CAAC;QAChE,GAAG,CAAC,IAAI,GAAyC,WAAW,CAAC,GAAG,CAAC,IAAI;QACrE,EAAE,EAAE,IAAI,EAAE,CAAC;mBACF,IAAI;QACb,CAAC;QAED,WAAW,CAAC,GAAG,CACb,IAAI,EACH,IAAI,GAAG,KAAK,CAAC,IAAI,EACf,IAAI,EAAE,GAAG,GAAK,CAAC;YACd,EAAE,GAAG,GAAG,CAAC,EAAE,EAAE,CAAC;gBACZ,KAAK,CAAC,GAAG,CAAC,KAAK,EAAE,2BAA2B,EAAE,IAAI;YACpD,CAAC;mBACM,GAAG,CAAC,IAAI,GAAG,IAAI,EAAE,IAAI;oBAAQ,IAAI,EAAE,IAAI;oBAAE,OAAO,EAAE,IAAI;;;QAC/D,CAAC,EACA,KAAK,EAAE,GAAG,GAAK,CAAC;YACf,KAAK,CAAC,cAAc,CAAC,GAAG;QAC1B,CAAC;eAEE,IAAI;IACb,CAAC;;QAGC,cAAc,EAAC,KAAa,EAAE,CAAC;mBACtB,UAAU,CAAC,KAAK,EAAE,WAAW;QACtC,CAAC;QACD,YAAY,EAAC,KAAa,EAAE,OAAsB,EAAE,CAAC;YACnD,OAAO,CAAC,OAAO,CAAC,OAAO,EACpB,IAAI,EAAE,EAAE,GAAK,EAAE;cACf,IAAI,EACF,OAAY;oBACX,SAAS,EAAG,OAAO,IAAI,OAAO,CAAC,OAAO,IAAK,OAAO;oBAClD,OAAO,EAAE,OAAO;;eAEjB,GAAG;oBAAQ,KAAK,EAAE,GAAG;;cAEvB,IAAI,EAAE,KAAsB,GAAK,CAAC;gBACjC,KAAK,CAAC,GAAG,GAAG,WAAW,CAAC,GAAG,CAAC,KAAK;gBACjC,WAAW,CAAC,GAAG,CAAC,KAAK,EAAE,KAAK;gBAC5B,EAAE,EAAE,GAAG,KAAI,OAAS,KAAI,GAAG,EAAE,GAAG,CAAC,OAAO,CAAC,KAAK;YAChD,CAAC;QACL,CAAC;QACD,SAAS,EAAC,KAAa,EAAE,QAAkB,EAAE,CAAC;mBACrC,UAAU,CAAmB,KAAK,EAAE,MAAM,MAAQ,CAAC;gBACxD,KAAK,CAAC,iBAAiB,GAAG,gBAAgB,CAAC,WAAW,EAAE,KAAK,EAC1D,IAAI,IAAI,OAAO,GAAE,GAAG,MAAO,CAAC;2BACpB,OAAO,CAAC,GAAG;wBAChB,WAAW,CAAC,GAAG,CAAC,KAAK,SAEjB,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,kBAAkB;wBAC9C,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,eAAe;;gBAEvC,CAAC,EACA,IAAI,EAAE,GAAG,GAAK,CAAC;gCACF,cAAc,CAAC,KAAK,EAAE,IAAI,EAAE,UAAU;4BAChD,UAAU;4BACV,MAAM,EAAE,GAAG,CAAC,CAAC;;;gBAEjB,CAAC;gBAEH,EAAE,EAAE,OAAO,CAAC,GAAG,CAAC,QAAQ,MAAK,WAAa,GAAE,CAAC;oBAC3C,eAAe,GAAG,GAAG,CAAC,OAAO,EAAQ,OAAO,GAAK,CAAC;wBAChD,EAAE,EAAE,iBAAiB,EAAE,CAAC;mCACf,iBAAiB,CAAC,OAAO,KAAO,CAAC;gCACtC,OAAO;4BACT,CAAC;wBACH,CAAC;oBACH,CAAC;gBACH,CAAC;uBAEM,yBAAyB,CAC9B,iBAAiB,EACjB,iBAAiB,EACjB,cAAc,CAAC,GAAG,CAAC,KAAK,EAAE,gCAAgC,EAAE,KAAK,MAEhE,IAAI,IAAI,UAAU,GAAE,MAAM,MAAO,CAAC;oBACjC,KAAK,CAAC,GAAG,GAAqB,MAAM,CAAC,MAAM;wBAGvC,MAAM,EAAE,MAAM;uBAAK,UAAU;4BAC1B,KAAO,KAAI,UAAU,GAAG,UAAU,GAAG,GAAG;gBACjD,CAAC,EACA,KAAK,EAAE,GAAG,GAAK,CAAC;oBACf,EAAE,EAAE,QAAQ,EAAE,CAAC;wBACb,EAAgD,AAAhD,8CAAgD;wBAChD,KAAK,CAAC,GAAG;oBACX,CAAC;;wBACQ,KAAK,EAAE,GAAG;;gBACrB,CAAC;YACL,CAAC;QACH,CAAC;QACD,QAAQ,EAAC,KAAa,EAAiB,CAAC;YACtC,EAAsH,AAAtH,oHAAsH;YACtH,EAAsB,AAAtB,oBAAsB;YACtB,GAAG,CAAC,EAAE;YACN,EAAE,EAAG,EAAE,GAAI,SAAS,CAAS,UAAU,EAAG,CAAC;gBACzC,EAAyD,AAAzD,uDAAyD;gBACzD,EAAE,EAAE,EAAE,CAAC,QAAQ,SAAS,IAAI,CAAC,EAAE,CAAC,aAAa,UAAU,OAAO,CAAC,OAAO;YACxE,CAAC;mBACM,gBAAgB,CAAC,WAAW,EAAE,KAAK,EACvC,IAAI,EAAE,MAAM,GACX,OAAO,CAAC,GAAG,CACT,WAAW,GACP,MAAM,CAAC,OAAO,CAAC,GAAG,EAAE,MAAM,GAAK,cAAc,CAAC,MAAM,GAAE,MAAQ;;cAIrE,IAAI,KAAO,CAAC;oBAhYe,oBAAyB,+BAiYpB,SAAS,CAAC,KAAK,EAAE,IAAI,EAAE,KAAK,KAAO,CAAC;oBAAA,CAAC;;YACtE,CAAC,EACA,KAAK,CACJ,EAA0B,AAA1B,wBAA0B;gBACpB,CAAC;YAAA,CAAC;QAEd,CAAC;;AAEL,CAAC"}