{"version": 3, "sources": ["../../../../../shared/lib/router/utils/prepare-destination.ts"], "sourcesContent": ["import type { IncomingMessage } from 'http'\nimport type { ParsedUrlQuery } from 'querystring'\nimport { parseUrl } from './parse-url'\nimport * as pathToRegexp from 'next/dist/compiled/path-to-regexp'\nimport type { RouteHas } from '../../../../lib/load-custom-routes'\n\ntype Params = { [param: string]: any }\n\n// ensure only a-zA-Z are used for param names for proper interpolating\n// with path-to-regexp\nexport const getSafeParamName = (paramName: string) => {\n  let newParamName = ''\n\n  for (let i = 0; i < paramName.length; i++) {\n    const charCode = paramName.charCodeAt(i)\n\n    if (\n      (charCode > 64 && charCode < 91) || // A-Z\n      (charCode > 96 && charCode < 123) // a-z\n    ) {\n      newParamName += paramName[i]\n    }\n  }\n  return newParamName\n}\n\nexport function matchHas(\n  req: IncomingMessage,\n  has: RouteHas[],\n  query: Params\n): false | Params {\n  const params: Params = {}\n\n  const allMatch = has.every((hasItem) => {\n    let value: undefined | string\n    let key = hasItem.key\n\n    switch (hasItem.type) {\n      case 'header': {\n        key = key!.toLowerCase()\n        value = req.headers[key] as string\n        break\n      }\n      case 'cookie': {\n        value = (req as any).cookies[hasItem.key]\n        break\n      }\n      case 'query': {\n        value = query[key!]\n        break\n      }\n      case 'host': {\n        const { host } = req?.headers || {}\n        // remove port from host if present\n        const hostname = host?.split(':')[0].toLowerCase()\n        value = hostname\n        break\n      }\n      default: {\n        break\n      }\n    }\n\n    if (!hasItem.value && value) {\n      params[getSafeParamName(key!)] = value\n      return true\n    } else if (value) {\n      const matcher = new RegExp(`^${hasItem.value}$`)\n      const matches = value.match(matcher)\n\n      if (matches) {\n        if (matches.groups) {\n          Object.keys(matches.groups).forEach((groupKey) => {\n            params[groupKey] = matches.groups![groupKey]\n          })\n        } else if (hasItem.type === 'host' && matches[0]) {\n          params.host = matches[0]\n        }\n        return true\n      }\n    }\n    return false\n  })\n\n  if (allMatch) {\n    return params\n  }\n  return false\n}\n\nexport function compileNonPath(value: string, params: Params): string {\n  if (!value.includes(':')) {\n    return value\n  }\n\n  for (const key of Object.keys(params)) {\n    if (value.includes(`:${key}`)) {\n      value = value\n        .replace(\n          new RegExp(`:${key}\\\\*`, 'g'),\n          `:${key}--ESCAPED_PARAM_ASTERISKS`\n        )\n        .replace(\n          new RegExp(`:${key}\\\\?`, 'g'),\n          `:${key}--ESCAPED_PARAM_QUESTION`\n        )\n        .replace(new RegExp(`:${key}\\\\+`, 'g'), `:${key}--ESCAPED_PARAM_PLUS`)\n        .replace(\n          new RegExp(`:${key}(?!\\\\w)`, 'g'),\n          `--ESCAPED_PARAM_COLON${key}`\n        )\n    }\n  }\n  value = value\n    .replace(/(:|\\*|\\?|\\+|\\(|\\)|\\{|\\})/g, '\\\\$1')\n    .replace(/--ESCAPED_PARAM_PLUS/g, '+')\n    .replace(/--ESCAPED_PARAM_COLON/g, ':')\n    .replace(/--ESCAPED_PARAM_QUESTION/g, '?')\n    .replace(/--ESCAPED_PARAM_ASTERISKS/g, '*')\n\n  // the value needs to start with a forward-slash to be compiled\n  // correctly\n  return pathToRegexp\n    .compile(`/${value}`, { validate: false })(params)\n    .substr(1)\n}\n\nexport default function prepareDestination(\n  destination: string,\n  params: Params,\n  query: ParsedUrlQuery,\n  appendParamsToQuery: boolean\n) {\n  // clone query so we don't modify the original\n  query = Object.assign({}, query)\n  const hadLocale = query.__nextLocale\n  delete query.__nextLocale\n  delete query.__nextDefaultLocale\n\n  const parsedDestination = parseUrl(destination)\n  const destQuery = parsedDestination.query\n  const destPath = `${parsedDestination.pathname!}${\n    parsedDestination.hash || ''\n  }`\n  const destPathParamKeys: pathToRegexp.Key[] = []\n  pathToRegexp.pathToRegexp(destPath, destPathParamKeys)\n\n  const destPathParams = destPathParamKeys.map((key) => key.name)\n\n  let destinationCompiler = pathToRegexp.compile(\n    destPath,\n    // we don't validate while compiling the destination since we should\n    // have already validated before we got to this point and validating\n    // breaks compiling destinations with named pattern params from the source\n    // e.g. /something:hello(.*) -> /another/:hello is broken with validation\n    // since compile validation is meant for reversing and not for inserting\n    // params from a separate path-regex into another\n    { validate: false }\n  )\n  let newUrl\n\n  // update any params in query values\n  for (const [key, strOrArray] of Object.entries(destQuery)) {\n    // the value needs to start with a forward-slash to be compiled\n    // correctly\n    if (Array.isArray(strOrArray)) {\n      destQuery[key] = strOrArray.map((value) => compileNonPath(value, params))\n    } else {\n      destQuery[key] = compileNonPath(strOrArray, params)\n    }\n  }\n\n  // add path params to query if it's not a redirect and not\n  // already defined in destination query or path\n  let paramKeys = Object.keys(params)\n\n  // remove internal param for i18n\n  if (hadLocale) {\n    paramKeys = paramKeys.filter((name) => name !== 'nextInternalLocale')\n  }\n\n  if (\n    appendParamsToQuery &&\n    !paramKeys.some((key) => destPathParams.includes(key))\n  ) {\n    for (const key of paramKeys) {\n      if (!(key in destQuery)) {\n        destQuery[key] = params[key]\n      }\n    }\n  }\n\n  try {\n    newUrl = destinationCompiler(params)\n\n    const [pathname, hash] = newUrl.split('#')\n    parsedDestination.pathname = pathname\n    parsedDestination.hash = `${hash ? '#' : ''}${hash || ''}`\n    delete (parsedDestination as any).search\n  } catch (err) {\n    if (err.message.match(/Expected .*? to not repeat, but got an array/)) {\n      throw new Error(\n        `To use a multi-match in the destination you must add \\`*\\` at the end of the param name to signify it should repeat. https://nextjs.org/docs/messages/invalid-multi-match`\n      )\n    }\n    throw err\n  }\n\n  // Query merge order lowest priority to highest\n  // 1. initial URL query values\n  // 2. path segment values\n  // 3. destination specified query values\n  parsedDestination.query = {\n    ...query,\n    ...parsedDestination.query,\n  }\n\n  return {\n    newUrl,\n    parsedDestination,\n  }\n}\n"], "names": [], "mappings": ";;;;QA0BgB,QAAQ,GAAR,QAAQ;QAgER,cAAc,GAAd,cAAc;kBAqCN,kBAAkB;;AA7HjB,GAAa,CAAb,SAAa;AAC1B,GAAY,CAAZ,YAAY;;;;;;;;;;;;;;;;;;;;;;;;AAOjB,KAAK,CAAC,gBAAgB,IAAI,SAAiB,GAAK,CAAC;IACtD,GAAG,CAAC,YAAY;QAEX,GAAG,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,MAAM,EAAE,CAAC,GAAI,CAAC;QAC1C,KAAK,CAAC,QAAQ,GAAG,SAAS,CAAC,UAAU,CAAC,CAAC;QAEvC,EAAE,EACC,QAAQ,GAAG,EAAE,IAAI,QAAQ,GAAG,EAAE,IAC9B,QAAQ,GAAG,EAAE,IAAI,QAAQ,GAAG,GAAG,AAAE,CAAM,AAAN,EAAM,AAAN,IAAM;UACxC,CAAC;YACD,YAAY,IAAI,SAAS,CAAC,CAAC;QAC7B,CAAC;IACH,CAAC;WACM,YAAY;AACrB,CAAC;QAdY,gBAAgB,GAAhB,gBAAgB;SAgBb,QAAQ,CACtB,GAAoB,EACpB,GAAe,EACf,KAAa,EACG,CAAC;IACjB,KAAK,CAAC,MAAM;;IAEZ,KAAK,CAAC,QAAQ,GAAG,GAAG,CAAC,KAAK,EAAE,OAAO,GAAK,CAAC;QACvC,GAAG,CAAC,KAAK;QACT,GAAG,CAAC,GAAG,GAAG,OAAO,CAAC,GAAG;eAEb,OAAO,CAAC,IAAI;kBACb,MAAQ;gBAAE,CAAC;oBACd,GAAG,GAAG,GAAG,CAAE,WAAW;oBACtB,KAAK,GAAG,GAAG,CAAC,OAAO,CAAC,GAAG;;gBAEzB,CAAC;kBACI,MAAQ;gBAAE,CAAC;oBACd,KAAK,GAAI,GAAG,CAAS,OAAO,CAAC,OAAO,CAAC,GAAG;;gBAE1C,CAAC;kBACI,KAAO;gBAAE,CAAC;oBACb,KAAK,GAAG,KAAK,CAAC,GAAG;;gBAEnB,CAAC;kBACI,IAAM;gBAAE,CAAC;oBACZ,KAAK,GAAG,IAAI,OAAK,GAAG,aAAH,GAAG,UAAH,CAAY,QAAZ,CAAY,GAAZ,GAAG,CAAE,OAAO;;oBAC7B,EAAmC,AAAnC,iCAAmC;oBACnC,KAAK,CAAC,QAAQ,GAAG,IAAI,aAAJ,IAAI,UAAJ,CAAW,QAAX,CAAW,GAAX,IAAI,CAAE,KAAK,EAAC,CAAG,GAAE,CAAC,EAAE,WAAW;oBAChD,KAAK,GAAG,QAAQ;;gBAElB,CAAC;;gBACQ,CAAC;;gBAEV,CAAC;;QAGH,EAAE,GAAG,OAAO,CAAC,KAAK,IAAI,KAAK,EAAE,CAAC;YAC5B,MAAM,CAAC,gBAAgB,CAAC,GAAG,KAAM,KAAK;mBAC/B,IAAI;QACb,CAAC,MAAM,EAAE,EAAE,KAAK,EAAE,CAAC;YACjB,KAAK,CAAC,OAAO,GAAG,GAAG,CAAC,MAAM,EAAE,CAAC,EAAE,OAAO,CAAC,KAAK,CAAC,CAAC;YAC9C,KAAK,CAAC,OAAO,GAAG,KAAK,CAAC,KAAK,CAAC,OAAO;YAEnC,EAAE,EAAE,OAAO,EAAE,CAAC;gBACZ,EAAE,EAAE,OAAO,CAAC,MAAM,EAAE,CAAC;oBACnB,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,OAAO,EAAE,QAAQ,GAAK,CAAC;wBACjD,MAAM,CAAC,QAAQ,IAAI,OAAO,CAAC,MAAM,CAAE,QAAQ;oBAC7C,CAAC;gBACH,CAAC,MAAM,EAAE,EAAE,OAAO,CAAC,IAAI,MAAK,IAAM,KAAI,OAAO,CAAC,CAAC,GAAG,CAAC;oBACjD,MAAM,CAAC,IAAI,GAAG,OAAO,CAAC,CAAC;gBACzB,CAAC;uBACM,IAAI;YACb,CAAC;QACH,CAAC;eACM,KAAK;IACd,CAAC;IAED,EAAE,EAAE,QAAQ,EAAE,CAAC;eACN,MAAM;IACf,CAAC;WACM,KAAK;AACd,CAAC;SAEe,cAAc,CAAC,KAAa,EAAE,MAAc,EAAU,CAAC;IACrE,EAAE,GAAG,KAAK,CAAC,QAAQ,EAAC,CAAG,IAAG,CAAC;eAClB,KAAK;IACd,CAAC;SAEI,KAAK,CAAC,GAAG,IAAI,MAAM,CAAC,IAAI,CAAC,MAAM,EAAG,CAAC;QACtC,EAAE,EAAE,KAAK,CAAC,QAAQ,EAAE,CAAC,EAAE,GAAG,KAAK,CAAC;YAC9B,KAAK,GAAG,KAAK,CACV,OAAO,CACN,GAAG,CAAC,MAAM,EAAE,CAAC,EAAE,GAAG,CAAC,GAAG,IAAG,CAAG,KAC3B,CAAC,EAAE,GAAG,CAAC,yBAAyB,GAElC,OAAO,CACN,GAAG,CAAC,MAAM,EAAE,CAAC,EAAE,GAAG,CAAC,GAAG,IAAG,CAAG,KAC3B,CAAC,EAAE,GAAG,CAAC,wBAAwB,GAEjC,OAAO,CAAC,GAAG,CAAC,MAAM,EAAE,CAAC,EAAE,GAAG,CAAC,GAAG,IAAG,CAAG,KAAI,CAAC,EAAE,GAAG,CAAC,oBAAoB,GACnE,OAAO,CACN,GAAG,CAAC,MAAM,EAAE,CAAC,EAAE,GAAG,CAAC,OAAO,IAAG,CAAG,KAC/B,qBAAqB,EAAE,GAAG;QAEjC,CAAC;IACH,CAAC;IACD,KAAK,GAAG,KAAK,CACV,OAAO,+BAA8B,IAAM,GAC3C,OAAO,2BAA0B,CAAG,GACpC,OAAO,4BAA2B,CAAG,GACrC,OAAO,+BAA8B,CAAG,GACxC,OAAO,gCAA+B,CAAG;IAE5C,EAA+D,AAA/D,6DAA+D;IAC/D,EAAY,AAAZ,UAAY;WAtHF,YAAY,CAwHnB,OAAO,EAAE,CAAC,EAAE,KAAK;QAAM,QAAQ,EAAE,KAAK;OAAI,MAAM,EAChD,MAAM,CAAC,CAAC;AACb,CAAC;SAEuB,kBAAkB,CACxC,WAAmB,EACnB,MAAc,EACd,KAAqB,EACrB,mBAA4B,EAC5B,CAAC;IACD,EAA8C,AAA9C,4CAA8C;IAC9C,KAAK,GAAG,MAAM,CAAC,MAAM;OAAK,KAAK;IAC/B,KAAK,CAAC,SAAS,GAAG,KAAK,CAAC,YAAY;WAC7B,KAAK,CAAC,YAAY;WAClB,KAAK,CAAC,mBAAmB;IAEhC,KAAK,CAAC,iBAAiB,OAzIA,SAAa,WAyID,WAAW;IAC9C,KAAK,CAAC,SAAS,GAAG,iBAAiB,CAAC,KAAK;IACzC,KAAK,CAAC,QAAQ,MAAM,iBAAiB,CAAC,QAAQ,GAC5C,iBAAiB,CAAC,IAAI;IAExB,KAAK,CAAC,iBAAiB;IA7Ib,YAAY,CA8IT,YAAY,CAAC,QAAQ,EAAE,iBAAiB;IAErD,KAAK,CAAC,cAAc,GAAG,iBAAiB,CAAC,GAAG,EAAE,GAAG,GAAK,GAAG,CAAC,IAAI;;IAE9D,GAAG,CAAC,mBAAmB,GAlJb,YAAY,CAkJiB,OAAO,CAC5C,QAAQ,EACR,EAAoE,AAApE,kEAAoE;IACpE,EAAoE,AAApE,kEAAoE;IACpE,EAA0E,AAA1E,wEAA0E;IAC1E,EAAyE,AAAzE,uEAAyE;IACzE,EAAwE,AAAxE,sEAAwE;IACxE,EAAiD,AAAjD,+CAAiD;;QAC/C,QAAQ,EAAE,KAAK;;IAEnB,GAAG,CAAC,MAAM;IAEV,EAAoC,AAApC,kCAAoC;SAC/B,KAAK,EAAE,GAAG,EAAE,UAAU,KAAK,MAAM,CAAC,OAAO,CAAC,SAAS,EAAG,CAAC;QAC1D,EAA+D,AAA/D,6DAA+D;QAC/D,EAAY,AAAZ,UAAY;QACZ,EAAE,EAAE,KAAK,CAAC,OAAO,CAAC,UAAU,GAAG,CAAC;YAC9B,SAAS,CAAC,GAAG,IAAI,UAAU,CAAC,GAAG,EAAE,KAAK,GAAK,cAAc,CAAC,KAAK,EAAE,MAAM;;QACzE,CAAC,MAAM,CAAC;YACN,SAAS,CAAC,GAAG,IAAI,cAAc,CAAC,UAAU,EAAE,MAAM;QACpD,CAAC;IACH,CAAC;IAED,EAA0D,AAA1D,wDAA0D;IAC1D,EAA+C,AAA/C,6CAA+C;IAC/C,GAAG,CAAC,SAAS,GAAG,MAAM,CAAC,IAAI,CAAC,MAAM;IAElC,EAAiC,AAAjC,+BAAiC;IACjC,EAAE,EAAE,SAAS,EAAE,CAAC;QACd,SAAS,GAAG,SAAS,CAAC,MAAM,EAAE,IAAI,GAAK,IAAI,MAAK,kBAAoB;;IACtE,CAAC;IAED,EAAE,EACA,mBAAmB,KAClB,SAAS,CAAC,IAAI,EAAE,IAAG,GAAK,cAAc,CAAC,QAAQ,CAAC,IAAG;OACpD,CAAC;aACI,KAAK,CAAC,IAAG,IAAI,SAAS,CAAE,CAAC;YAC5B,EAAE,IAAI,IAAG,IAAI,SAAS,GAAG,CAAC;gBACxB,SAAS,CAAC,IAAG,IAAI,MAAM,CAAC,IAAG;YAC7B,CAAC;QACH,CAAC;IACH,CAAC;QAEG,CAAC;QACH,MAAM,GAAG,mBAAmB,CAAC,MAAM;QAEnC,KAAK,EAAE,QAAQ,EAAE,IAAI,IAAI,MAAM,CAAC,KAAK,EAAC,CAAG;QACzC,iBAAiB,CAAC,QAAQ,GAAG,QAAQ;QACrC,iBAAiB,CAAC,IAAI,MAAM,IAAI,IAAG,CAAG,SAAQ,IAAI;eAC1C,iBAAiB,CAAS,MAAM;IAC1C,CAAC,QAAQ,GAAG,EAAE,CAAC;QACb,EAAE,EAAE,GAAG,CAAC,OAAO,CAAC,KAAK,kDAAkD,CAAC;YACtE,KAAK,CAAC,GAAG,CAAC,KAAK,EACZ,yKAAyK;QAE9K,CAAC;QACD,KAAK,CAAC,GAAG;IACX,CAAC;IAED,EAA+C,AAA/C,6CAA+C;IAC/C,EAA8B,AAA9B,4BAA8B;IAC9B,EAAyB,AAAzB,uBAAyB;IACzB,EAAwC,AAAxC,sCAAwC;IACxC,iBAAiB,CAAC,KAAK;WAClB,KAAK;WACL,iBAAiB,CAAC,KAAK;;;QAI1B,MAAM;QACN,iBAAiB;;AAErB,CAAC"}