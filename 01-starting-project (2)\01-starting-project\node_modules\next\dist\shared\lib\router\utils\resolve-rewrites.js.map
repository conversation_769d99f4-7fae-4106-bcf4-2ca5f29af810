{"version": 3, "sources": ["../../../../../shared/lib/router/utils/resolve-rewrites.ts"], "sourcesContent": ["import { ParsedUrlQuery } from 'querystring'\nimport pathMatch from './path-match'\nimport prepareDestination, { matchHas } from './prepare-destination'\nimport { Rewrite } from '../../../../lib/load-custom-routes'\nimport { removePathTrailingSlash } from '../../../../client/normalize-trailing-slash'\nimport { normalizeLocalePath } from '../../i18n/normalize-locale-path'\nimport { parseRelativeUrl } from './parse-relative-url'\nimport { delBasePath } from '../router'\n\nconst customRouteMatcher = pathMatch(true)\n\nexport default function resolveRewrites(\n  asPath: string,\n  pages: string[],\n  rewrites: {\n    beforeFiles: Rewrite[]\n    afterFiles: Rewrite[]\n    fallback: Rewrite[]\n  },\n  query: ParsedUrlQuery,\n  resolveHref: (path: string) => string,\n  locales?: string[]\n): {\n  matchedPage: boolean\n  parsedAs: ReturnType<typeof parseRelativeUrl>\n  asPath: string\n  resolvedHref?: string\n} {\n  let matchedPage = false\n  let parsedAs = parseRelativeUrl(asPath)\n  let fsPathname = removePathTrailingSlash(\n    normalizeLocalePath(delBasePath(parsedAs.pathname), locales).pathname\n  )\n  let resolvedHref\n\n  const handleRewrite = (rewrite: Rewrite) => {\n    const matcher = customRouteMatcher(rewrite.source)\n    let params = matcher(parsedAs.pathname)\n\n    if (rewrite.has && params) {\n      const hasParams = matchHas(\n        {\n          headers: {\n            host: document.location.hostname,\n          },\n          cookies: document.cookie\n            .split('; ')\n            .reduce<Record<string, string>>((acc, item) => {\n              const [key, ...value] = item.split('=')\n              acc[key] = value.join('=')\n              return acc\n            }, {}),\n        } as any,\n        rewrite.has,\n        parsedAs.query\n      )\n\n      if (hasParams) {\n        Object.assign(params, hasParams)\n      } else {\n        params = false\n      }\n    }\n\n    if (params) {\n      if (!rewrite.destination) {\n        // this is a proxied rewrite which isn't handled on the client\n        return true\n      }\n      const destRes = prepareDestination(\n        rewrite.destination,\n        params,\n        query,\n        true\n      )\n      parsedAs = destRes.parsedDestination\n      asPath = destRes.newUrl\n      Object.assign(query, destRes.parsedDestination.query)\n\n      fsPathname = removePathTrailingSlash(\n        normalizeLocalePath(delBasePath(asPath), locales).pathname\n      )\n\n      if (pages.includes(fsPathname)) {\n        // check if we now match a page as this means we are done\n        // resolving the rewrites\n        matchedPage = true\n        resolvedHref = fsPathname\n        return true\n      }\n\n      // check if we match a dynamic-route, if so we break the rewrites chain\n      resolvedHref = resolveHref(fsPathname)\n\n      if (resolvedHref !== asPath && pages.includes(resolvedHref)) {\n        matchedPage = true\n        return true\n      }\n    }\n  }\n  let finished = false\n\n  for (let i = 0; i < rewrites.beforeFiles.length; i++) {\n    // we don't end after match in beforeFiles to allow\n    // continuing through all beforeFiles rewrites\n    handleRewrite(rewrites.beforeFiles[i])\n  }\n  matchedPage = pages.includes(fsPathname)\n\n  if (!matchedPage) {\n    if (!finished) {\n      for (let i = 0; i < rewrites.afterFiles.length; i++) {\n        if (handleRewrite(rewrites.afterFiles[i])) {\n          finished = true\n          break\n        }\n      }\n    }\n\n    // check dynamic route before processing fallback rewrites\n    if (!finished) {\n      resolvedHref = resolveHref(fsPathname)\n      matchedPage = pages.includes(resolvedHref)\n      finished = matchedPage\n    }\n\n    if (!finished) {\n      for (let i = 0; i < rewrites.fallback.length; i++) {\n        if (handleRewrite(rewrites.fallback[i])) {\n          finished = true\n          break\n        }\n      }\n    }\n  }\n\n  return {\n    asPath,\n    parsedAs,\n    matchedPage,\n    resolvedHref,\n  }\n}\n"], "names": [], "mappings": ";;;;kBAWwB,eAAe;AAVjB,GAAc,CAAd,UAAc;AACS,GAAuB,CAAvB,mBAAuB;AAE5B,GAA6C,CAA7C,uBAA6C;AACjD,GAAkC,CAAlC,oBAAkC;AACrC,GAAsB,CAAtB,iBAAsB;AAC3B,GAAW,CAAX,OAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEvC,KAAK,CAAC,kBAAkB,OARF,UAAc,UAQC,IAAI;SAEjB,eAAe,CACrC,MAAc,EACd,KAAe,EACf,QAIC,EACD,KAAqB,EACrB,WAAqC,EACrC,OAAkB,EAMlB,CAAC;IACD,GAAG,CAAC,WAAW,GAAG,KAAK;IACvB,GAAG,CAAC,QAAQ,OAvBmB,iBAAsB,mBAuBrB,MAAM;IACtC,GAAG,CAAC,UAAU,OA1BwB,uBAA6C,8BACjD,oBAAkC,0BAE1C,OAAW,cAwBH,QAAQ,CAAC,QAAQ,GAAG,OAAO,EAAE,QAAQ;IAEvE,GAAG,CAAC,YAAY;IAEhB,KAAK,CAAC,aAAa,IAAI,OAAgB,GAAK,CAAC;QAC3C,KAAK,CAAC,OAAO,GAAG,kBAAkB,CAAC,OAAO,CAAC,MAAM;QACjD,GAAG,CAAC,MAAM,GAAG,OAAO,CAAC,QAAQ,CAAC,QAAQ;QAEtC,EAAE,EAAE,OAAO,CAAC,GAAG,IAAI,MAAM,EAAE,CAAC;YAC1B,KAAK,CAAC,SAAS,OAtCwB,mBAAuB;gBAwC1D,OAAO;oBACL,IAAI,EAAE,QAAQ,CAAC,QAAQ,CAAC,QAAQ;;gBAElC,OAAO,EAAE,QAAQ,CAAC,MAAM,CACrB,KAAK,EAAC,EAAI,GACV,MAAM,EAA0B,GAAG,EAAE,IAAI,GAAK,CAAC;oBAC9C,KAAK,EAAE,GAAG,KAAK,KAAK,IAAI,IAAI,CAAC,KAAK,EAAC,CAAG;oBACtC,GAAG,CAAC,GAAG,IAAI,KAAK,CAAC,IAAI,EAAC,CAAG;2BAClB,GAAG;gBACZ,CAAC;;eAEL,OAAO,CAAC,GAAG,EACX,QAAQ,CAAC,KAAK;YAGhB,EAAE,EAAE,SAAS,EAAE,CAAC;gBACd,MAAM,CAAC,MAAM,CAAC,MAAM,EAAE,SAAS;YACjC,CAAC,MAAM,CAAC;gBACN,MAAM,GAAG,KAAK;YAChB,CAAC;QACH,CAAC;QAED,EAAE,EAAE,MAAM,EAAE,CAAC;YACX,EAAE,GAAG,OAAO,CAAC,WAAW,EAAE,CAAC;gBACzB,EAA8D,AAA9D,4DAA8D;uBACvD,IAAI;YACb,CAAC;YACD,KAAK,CAAC,OAAO,OAnE0B,mBAAuB,UAoE5D,OAAO,CAAC,WAAW,EACnB,MAAM,EACN,KAAK,EACL,IAAI;YAEN,QAAQ,GAAG,OAAO,CAAC,iBAAiB;YACpC,MAAM,GAAG,OAAO,CAAC,MAAM;YACvB,MAAM,CAAC,MAAM,CAAC,KAAK,EAAE,OAAO,CAAC,iBAAiB,CAAC,KAAK;YAEpD,UAAU,OA3EwB,uBAA6C,8BACjD,oBAAkC,0BAE1C,OAAW,cAyEC,MAAM,GAAG,OAAO,EAAE,QAAQ;YAG5D,EAAE,EAAE,KAAK,CAAC,QAAQ,CAAC,UAAU,GAAG,CAAC;gBAC/B,EAAyD,AAAzD,uDAAyD;gBACzD,EAAyB,AAAzB,uBAAyB;gBACzB,WAAW,GAAG,IAAI;gBAClB,YAAY,GAAG,UAAU;uBAClB,IAAI;YACb,CAAC;YAED,EAAuE,AAAvE,qEAAuE;YACvE,YAAY,GAAG,WAAW,CAAC,UAAU;YAErC,EAAE,EAAE,YAAY,KAAK,MAAM,IAAI,KAAK,CAAC,QAAQ,CAAC,YAAY,GAAG,CAAC;gBAC5D,WAAW,GAAG,IAAI;uBACX,IAAI;YACb,CAAC;QACH,CAAC;IACH,CAAC;IACD,GAAG,CAAC,QAAQ,GAAG,KAAK;QAEf,GAAG,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,WAAW,CAAC,MAAM,EAAE,CAAC,GAAI,CAAC;QACrD,EAAmD,AAAnD,iDAAmD;QACnD,EAA8C,AAA9C,4CAA8C;QAC9C,aAAa,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC;IACtC,CAAC;IACD,WAAW,GAAG,KAAK,CAAC,QAAQ,CAAC,UAAU;IAEvC,EAAE,GAAG,WAAW,EAAE,CAAC;QACjB,EAAE,GAAG,QAAQ,EAAE,CAAC;gBACT,GAAG,CAAC,EAAC,GAAG,CAAC,EAAE,EAAC,GAAG,QAAQ,CAAC,UAAU,CAAC,MAAM,EAAE,EAAC,GAAI,CAAC;gBACpD,EAAE,EAAE,aAAa,CAAC,QAAQ,CAAC,UAAU,CAAC,EAAC,IAAI,CAAC;oBAC1C,QAAQ,GAAG,IAAI;;gBAEjB,CAAC;YACH,CAAC;QACH,CAAC;QAED,EAA0D,AAA1D,wDAA0D;QAC1D,EAAE,GAAG,QAAQ,EAAE,CAAC;YACd,YAAY,GAAG,WAAW,CAAC,UAAU;YACrC,WAAW,GAAG,KAAK,CAAC,QAAQ,CAAC,YAAY;YACzC,QAAQ,GAAG,WAAW;QACxB,CAAC;QAED,EAAE,GAAG,QAAQ,EAAE,CAAC;gBACT,GAAG,CAAC,EAAC,GAAG,CAAC,EAAE,EAAC,GAAG,QAAQ,CAAC,QAAQ,CAAC,MAAM,EAAE,EAAC,GAAI,CAAC;gBAClD,EAAE,EAAE,aAAa,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAC,IAAI,CAAC;oBACxC,QAAQ,GAAG,IAAI;;gBAEjB,CAAC;YACH,CAAC;QACH,CAAC;IACH,CAAC;;QAGC,MAAM;QACN,QAAQ;QACR,WAAW;QACX,YAAY;;AAEhB,CAAC"}