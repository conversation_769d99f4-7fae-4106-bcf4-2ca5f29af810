{"version": 3, "sources": ["../../../server/lib/start-server.ts"], "sourcesContent": ["import http from 'http'\nimport next from '../next'\n\nexport default async function start(\n  serverOptions: any,\n  port?: number,\n  hostname?: string\n) {\n  const app = next({\n    ...serverOptions,\n    customServer: false,\n  })\n  const srv = http.createServer(app.getRequestHandler())\n  await new Promise<void>((resolve, reject) => {\n    // This code catches EADDRINUSE error if the port is already in use\n    srv.on('error', reject)\n    srv.on('listening', () => resolve())\n    srv.listen(port, hostname)\n  })\n  // It's up to caller to run `app.prepare()`, so it can notify that the server\n  // is listening before starting any intensive operations.\n  return app\n}\n"], "names": [], "mappings": ";;;;kBAG8B,KAAK;AAHlB,GAAM,CAAN,KAAM;AACN,GAAS,CAAT,KAAS;;;;;;eAEI,KAAK,CACjC,aAAkB,EAClB,IAAa,EACb,QAAiB,EACjB,CAAC;IACD,KAAK,CAAC,GAAG,OAPM,KAAS;WAQnB,aAAa;QAChB,YAAY,EAAE,KAAK;;IAErB,KAAK,CAAC,GAAG,GAZM,KAAM,SAYJ,YAAY,CAAC,GAAG,CAAC,iBAAiB;UAC7C,GAAG,CAAC,OAAO,EAAQ,OAAO,EAAE,MAAM,GAAK,CAAC;QAC5C,EAAmE,AAAnE,iEAAmE;QACnE,GAAG,CAAC,EAAE,EAAC,KAAO,GAAE,MAAM;QACtB,GAAG,CAAC,EAAE,EAAC,SAAW,OAAQ,OAAO;;QACjC,GAAG,CAAC,MAAM,CAAC,IAAI,EAAE,QAAQ;IAC3B,CAAC;IACD,EAA6E,AAA7E,2EAA6E;IAC7E,EAAyD,AAAzD,uDAAyD;WAClD,GAAG;AACZ,CAAC"}