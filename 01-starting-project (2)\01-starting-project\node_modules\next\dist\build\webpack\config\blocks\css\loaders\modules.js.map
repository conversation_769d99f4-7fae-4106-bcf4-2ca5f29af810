{"version": 3, "sources": ["../../../../../../../build/webpack/config/blocks/css/loaders/modules.ts"], "sourcesContent": ["import { AcceptedPlugin } from 'postcss'\nimport { webpack } from 'next/dist/compiled/webpack/webpack'\nimport { ConfigurationContext } from '../../../utils'\nimport { getClientStyleLoader } from './client'\nimport { cssFileResolve } from './file-resolve'\nimport { getCssModuleLocalIdent } from './getCssModuleLocalIdent'\n\nexport function getCssModuleLoader(\n  ctx: ConfigurationContext,\n  postCssPlugins: readonly AcceptedPlugin[],\n  preProcessors: readonly webpack.RuleSetUseItem[] = []\n): webpack.RuleSetUseItem[] {\n  const loaders: webpack.RuleSetUseItem[] = []\n\n  if (ctx.isClient) {\n    // Add appropriate development more or production mode style\n    // loader\n    loaders.push(\n      getClientStyleLoader({\n        isDevelopment: ctx.isDevelopment,\n        assetPrefix: ctx.assetPrefix,\n      })\n    )\n  }\n\n  // Resolve CSS `@import`s and `url()`s\n  loaders.push({\n    loader: require.resolve('next/dist/compiled/css-loader'),\n    options: {\n      importLoaders: 1 + preProcessors.length,\n      // Use CJS mode for backwards compatibility:\n      esModule: false,\n      url: cssFileResolve,\n      import: (url: string, _: any, resourcePath: string) =>\n        cssFileResolve(url, resourcePath),\n      modules: {\n        // Do not transform class names (CJS mode backwards compatibility):\n        exportLocalsConvention: 'asIs',\n        // Server-side (Node.js) rendering support:\n        exportOnlyLocals: ctx.isServer,\n        // Disallow global style exports so we can code-split CSS and\n        // not worry about loading order.\n        mode: 'pure',\n        // Generate a friendly production-ready name so it's\n        // reasonably understandable. The same name is used for\n        // development.\n        // TODO: Consider making production reduce this to a single\n        // character?\n        getLocalIdent: getCssModuleLocalIdent,\n      },\n    },\n  })\n\n  // Compile CSS\n  loaders.push({\n    loader: require.resolve('next/dist/compiled/postcss-loader'),\n    options: {\n      postcssOptions: { plugins: postCssPlugins, config: false },\n    },\n  })\n\n  loaders.push(\n    // Webpack loaders run like a stack, so we need to reverse the natural\n    // order of preprocessors.\n    ...preProcessors.slice().reverse()\n  )\n\n  return loaders\n}\n"], "names": [], "mappings": ";;;;QAOgB,kBAAkB,GAAlB,kBAAkB;AAJG,GAAU,CAAV,OAAU;AAChB,GAAgB,CAAhB,YAAgB;AACR,GAA0B,CAA1B,uBAA0B;SAEjD,kBAAkB,CAChC,GAAyB,EACzB,cAAyC,EACzC,aAAgD,OACtB,CAAC;IAC3B,KAAK,CAAC,OAAO;IAEb,EAAE,EAAE,GAAG,CAAC,QAAQ,EAAE,CAAC;QACjB,EAA4D,AAA5D,0DAA4D;QAC5D,EAAS,AAAT,OAAS;QACT,OAAO,CAAC,IAAI,KAdqB,OAAU;YAgBvC,aAAa,EAAE,GAAG,CAAC,aAAa;YAChC,WAAW,EAAE,GAAG,CAAC,WAAW;;IAGlC,CAAC;IAED,EAAsC,AAAtC,oCAAsC;IACtC,OAAO,CAAC,IAAI;QACV,MAAM,EAAE,OAAO,CAAC,OAAO,EAAC,6BAA+B;QACvD,OAAO;YACL,aAAa,EAAE,CAAC,GAAG,aAAa,CAAC,MAAM;YACvC,EAA4C,AAA5C,0CAA4C;YAC5C,QAAQ,EAAE,KAAK;YACf,GAAG,EA5BsB,YAAgB;YA6BzC,MAAM,GAAG,GAAW,EAAE,CAAM,EAAE,YAAoB,OA7BzB,YAAgB,iBA8BxB,GAAG,EAAE,YAAY;;YAClC,OAAO;gBACL,EAAmE,AAAnE,iEAAmE;gBACnE,sBAAsB,GAAE,IAAM;gBAC9B,EAA2C,AAA3C,yCAA2C;gBAC3C,gBAAgB,EAAE,GAAG,CAAC,QAAQ;gBAC9B,EAA6D,AAA7D,2DAA6D;gBAC7D,EAAiC,AAAjC,+BAAiC;gBACjC,IAAI,GAAE,IAAM;gBACZ,EAAoD,AAApD,kDAAoD;gBACpD,EAAuD,AAAvD,qDAAuD;gBACvD,EAAe,AAAf,aAAe;gBACf,EAA2D,AAA3D,yDAA2D;gBAC3D,EAAa,AAAb,WAAa;gBACb,aAAa,EA3CkB,uBAA0B;;;;IAgD/D,EAAc,AAAd,YAAc;IACd,OAAO,CAAC,IAAI;QACV,MAAM,EAAE,OAAO,CAAC,OAAO,EAAC,iCAAmC;QAC3D,OAAO;YACL,cAAc;gBAAI,OAAO,EAAE,cAAc;gBAAE,MAAM,EAAE,KAAK;;;;IAI5D,OAAO,CAAC,IAAI,CACV,EAAsE,AAAtE,oEAAsE;IACtE,EAA0B,AAA1B,wBAA0B;OACvB,aAAa,CAAC,KAAK,GAAG,OAAO;WAG3B,OAAO;AAChB,CAAC"}