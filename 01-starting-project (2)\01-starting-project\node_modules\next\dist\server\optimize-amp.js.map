{"version": 3, "sources": ["../../server/optimize-amp.ts"], "sourcesContent": ["export default async function optimize(\n  html: string,\n  config: any\n): Promise<string> {\n  let AmpOptimizer\n  try {\n    AmpOptimizer = require('next/dist/compiled/@ampproject/toolbox-optimizer')\n  } catch (_) {\n    return html\n  }\n  const optimizer = AmpOptimizer.create(config)\n  return optimizer.transformHtml(html, config)\n}\n"], "names": [], "mappings": ";;;;kBAA8B,QAAQ;eAAR,QAAQ,CACpC,IAAY,EACZ,MAAW,EACM,CAAC;IAClB,GAAG,CAAC,YAAY;QACZ,CAAC;QACH,YAAY,GAAG,OAAO,EAAC,gDAAkD;IAC3E,CAAC,QAAQ,CAAC,EAAE,CAAC;eACJ,IAAI;IACb,CAAC;IACD,KAAK,CAAC,SAAS,GAAG,YAAY,CAAC,MAAM,CAAC,MAAM;WACrC,SAAS,CAAC,aAAa,CAAC,IAAI,EAAE,MAAM;AAC7C,CAAC"}