{"version": 3, "sources": ["../../client/use-intersection.tsx"], "sourcesContent": ["import { useCallback, useEffect, useRef, useState } from 'react'\nimport {\n  requestIdleCallback,\n  cancelIdleCallback,\n} from './request-idle-callback'\n\ntype UseIntersectionObserverInit = Pick<IntersectionObserverInit, 'rootMargin'>\ntype UseIntersection = { disabled?: boolean } & UseIntersectionObserverInit\ntype ObserveCallback = (isVisible: boolean) => void\ntype Observer = {\n  id: string\n  observer: IntersectionObserver\n  elements: Map<Element, ObserveCallback>\n}\n\nconst hasIntersectionObserver = typeof IntersectionObserver !== 'undefined'\n\nexport function useIntersection<T extends Element>({\n  rootMargin,\n  disabled,\n}: UseIntersection): [(element: T | null) => void, boolean] {\n  const isDisabled: boolean = disabled || !hasIntersectionObserver\n\n  const unobserve = useRef<Function>()\n  const [visible, setVisible] = useState(false)\n\n  const setRef = useCallback(\n    (el: T | null) => {\n      if (unobserve.current) {\n        unobserve.current()\n        unobserve.current = undefined\n      }\n\n      if (isDisabled || visible) return\n\n      if (el && el.tagName) {\n        unobserve.current = observe(\n          el,\n          (isVisible) => isVisible && setVisible(isVisible),\n          { rootMargin }\n        )\n      }\n    },\n    [isDisabled, rootMargin, visible]\n  )\n\n  useEffect(() => {\n    if (!hasIntersectionObserver) {\n      if (!visible) {\n        const idleCallback = requestIdleCallback(() => setVisible(true))\n        return () => cancelIdleCallback(idleCallback)\n      }\n    }\n  }, [visible])\n\n  return [setRef, visible]\n}\n\nfunction observe(\n  element: Element,\n  callback: ObserveCallback,\n  options: UseIntersectionObserverInit\n): () => void {\n  const { id, observer, elements } = createObserver(options)\n  elements.set(element, callback)\n\n  observer.observe(element)\n  return function unobserve(): void {\n    elements.delete(element)\n    observer.unobserve(element)\n\n    // Destroy observer when there's nothing left to watch:\n    if (elements.size === 0) {\n      observer.disconnect()\n      observers.delete(id)\n    }\n  }\n}\n\nconst observers = new Map<string, Observer>()\nfunction createObserver(options: UseIntersectionObserverInit): Observer {\n  const id = options.rootMargin || ''\n  let instance = observers.get(id)\n  if (instance) {\n    return instance\n  }\n\n  const elements = new Map<Element, ObserveCallback>()\n  const observer = new IntersectionObserver((entries) => {\n    entries.forEach((entry) => {\n      const callback = elements.get(entry.target)\n      const isVisible = entry.isIntersecting || entry.intersectionRatio > 0\n      if (callback && isVisible) {\n        callback(isVisible)\n      }\n    })\n  }, options)\n\n  observers.set(\n    id,\n    (instance = {\n      id,\n      observer,\n      elements,\n    })\n  )\n  return instance\n}\n"], "names": [], "mappings": ";;;;QAiBgB,eAAe,GAAf,eAAe;AAjB0B,GAAO,CAAP,MAAO;AAIzD,GAAyB,CAAzB,oBAAyB;AAWhC,KAAK,CAAC,uBAAuB,UAAU,oBAAoB,MAAK,SAAW;SAE3D,eAAe,GAC7B,UAAU,GACV,QAAQ,KACkD,CAAC;IAC3D,KAAK,CAAC,UAAU,GAAY,QAAQ,KAAK,uBAAuB;IAEhE,KAAK,CAAC,SAAS,OAvBwC,MAAO;IAwB9D,KAAK,EAAE,OAAO,EAAE,UAAU,QAxB6B,MAAO,WAwBvB,KAAK;IAE5C,KAAK,CAAC,MAAM,OA1B2C,MAAO,eA2B3D,EAAY,GAAK,CAAC;QACjB,EAAE,EAAE,SAAS,CAAC,OAAO,EAAE,CAAC;YACtB,SAAS,CAAC,OAAO;YACjB,SAAS,CAAC,OAAO,GAAG,SAAS;QAC/B,CAAC;QAED,EAAE,EAAE,UAAU,IAAI,OAAO;QAEzB,EAAE,EAAE,EAAE,IAAI,EAAE,CAAC,OAAO,EAAE,CAAC;YACrB,SAAS,CAAC,OAAO,GAAG,OAAO,CACzB,EAAE,GACD,SAAS,GAAK,SAAS,IAAI,UAAU,CAAC,SAAS;;gBAC9C,UAAU;;QAEhB,CAAC;IACH,CAAC;QACA,UAAU;QAAE,UAAU;QAAE,OAAO;;QA3CqB,MAAO,gBA8C9C,CAAC;QACf,EAAE,GAAG,uBAAuB,EAAE,CAAC;YAC7B,EAAE,GAAG,OAAO,EAAE,CAAC;gBACb,KAAK,CAAC,YAAY,OA7CnB,oBAAyB,0BA6CuB,UAAU,CAAC,IAAI;;+BA7C/D,oBAAyB,qBA8CQ,YAAY;;YAC9C,CAAC;QACH,CAAC;IACH,CAAC;QAAG,OAAO;;;QAEH,MAAM;QAAE,OAAO;;AACzB,CAAC;SAEQ,OAAO,CACd,OAAgB,EAChB,QAAyB,EACzB,OAAoC,EACxB,CAAC;IACb,KAAK,GAAG,EAAE,GAAE,QAAQ,GAAE,QAAQ,MAAK,cAAc,CAAC,OAAO;IACzD,QAAQ,CAAC,GAAG,CAAC,OAAO,EAAE,QAAQ;IAE9B,QAAQ,CAAC,OAAO,CAAC,OAAO;oBACR,SAAS,GAAS,CAAC;QACjC,QAAQ,CAAC,MAAM,CAAC,OAAO;QACvB,QAAQ,CAAC,SAAS,CAAC,OAAO;QAE1B,EAAuD,AAAvD,qDAAuD;QACvD,EAAE,EAAE,QAAQ,CAAC,IAAI,KAAK,CAAC,EAAE,CAAC;YACxB,QAAQ,CAAC,UAAU;YACnB,SAAS,CAAC,MAAM,CAAC,EAAE;QACrB,CAAC;IACH,CAAC;AACH,CAAC;AAED,KAAK,CAAC,SAAS,GAAG,GAAG,CAAC,GAAG;SAChB,cAAc,CAAC,OAAoC,EAAY,CAAC;IACvE,KAAK,CAAC,EAAE,GAAG,OAAO,CAAC,UAAU;IAC7B,GAAG,CAAC,QAAQ,GAAG,SAAS,CAAC,GAAG,CAAC,EAAE;IAC/B,EAAE,EAAE,QAAQ,EAAE,CAAC;eACN,QAAQ;IACjB,CAAC;IAED,KAAK,CAAC,QAAQ,GAAG,GAAG,CAAC,GAAG;IACxB,KAAK,CAAC,QAAQ,GAAG,GAAG,CAAC,oBAAoB,EAAE,OAAO,GAAK,CAAC;QACtD,OAAO,CAAC,OAAO,EAAE,KAAK,GAAK,CAAC;YAC1B,KAAK,CAAC,QAAQ,GAAG,QAAQ,CAAC,GAAG,CAAC,KAAK,CAAC,MAAM;YAC1C,KAAK,CAAC,SAAS,GAAG,KAAK,CAAC,cAAc,IAAI,KAAK,CAAC,iBAAiB,GAAG,CAAC;YACrE,EAAE,EAAE,QAAQ,IAAI,SAAS,EAAE,CAAC;gBAC1B,QAAQ,CAAC,SAAS;YACpB,CAAC;QACH,CAAC;IACH,CAAC,EAAE,OAAO;IAEV,SAAS,CAAC,GAAG,CACX,EAAE,EACD,QAAQ;QACP,EAAE;QACF,QAAQ;QACR,QAAQ;;WAGL,QAAQ;AACjB,CAAC"}