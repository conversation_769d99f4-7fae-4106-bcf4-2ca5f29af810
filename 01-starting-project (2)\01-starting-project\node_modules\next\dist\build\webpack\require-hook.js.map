{"version": 3, "sources": ["../../../build/webpack/require-hook.ts"], "sourcesContent": ["// sync injects a hook for webpack and webpack/... requires to use the internal ncc webpack version\n// this is in order for userland plugins to attach to the same webpack instance as next.js\n// the individual compiled modules are as defined for the compilation in bundles/webpack/packages/*\n\nconst hookPropertyMap = new Map(\n  [\n    ['webpack', 'next/dist/compiled/webpack/webpack-lib'],\n    ['webpack/package', 'next/dist/compiled/webpack/package'],\n    ['webpack/package.json', 'next/dist/compiled/webpack/package'],\n    ['webpack/lib/webpack', 'next/dist/compiled/webpack/webpack-lib'],\n    ['webpack/lib/webpack.js', 'next/dist/compiled/webpack/webpack-lib'],\n    [\n      'webpack/lib/node/NodeEnvironmentPlugin',\n      'next/dist/compiled/webpack/NodeEnvironmentPlugin',\n    ],\n    [\n      'webpack/lib/node/NodeEnvironmentPlugin.js',\n      'next/dist/compiled/webpack/NodeEnvironmentPlugin',\n    ],\n    [\n      'webpack/lib/BasicEvaluatedExpression',\n      'next/dist/compiled/webpack/BasicEvaluatedExpression',\n    ],\n    [\n      'webpack/lib/BasicEvaluatedExpression.js',\n      'next/dist/compiled/webpack/BasicEvaluatedExpression',\n    ],\n    [\n      'webpack/lib/node/NodeTargetPlugin',\n      'next/dist/compiled/webpack/NodeTargetPlugin',\n    ],\n    [\n      'webpack/lib/node/NodeTargetPlugin.js',\n      'next/dist/compiled/webpack/NodeTargetPlugin',\n    ],\n    [\n      'webpack/lib/ModuleFilenameHelpers',\n      'next/dist/compiled/webpack/ModuleFilenameHelpers',\n    ],\n    [\n      'webpack/lib/ModuleFilenameHelpers.js',\n      'next/dist/compiled/webpack/ModuleFilenameHelpers',\n    ],\n    ['webpack/lib/GraphHelpers', 'next/dist/compiled/webpack/GraphHelpers'],\n    ['webpack/lib/GraphHelpers.js', 'next/dist/compiled/webpack/GraphHelpers'],\n    ['webpack/lib/NormalModule', 'next/dist/compiled/webpack/NormalModule'],\n    ['webpack-sources', 'next/dist/compiled/webpack/sources'],\n    ['webpack-sources/lib', 'next/dist/compiled/webpack/sources'],\n    ['webpack-sources/lib/index', 'next/dist/compiled/webpack/sources'],\n    ['webpack-sources/lib/index.js', 'next/dist/compiled/webpack/sources'],\n  ].map(([request, replacement]) => [request, require.resolve(replacement)])\n)\n\nconst mod = require('module')\nconst resolveFilename = mod._resolveFilename\nmod._resolveFilename = function (\n  request: string,\n  parent: any,\n  isMain: boolean,\n  options: any\n) {\n  const hookResolved = hookPropertyMap.get(request)\n  if (hookResolved) request = hookResolved\n  return resolveFilename.call(mod, request, parent, isMain, options)\n}\n"], "names": [], "mappings": ";AAAA,EAAmG,AAAnG,iGAAmG;AACnG,EAA0F,AAA1F,wFAA0F;AAC1F,EAAmG,AAAnG,iGAAmG;AAEnG,KAAK,CAAC,eAAe,GAAG,GAAG,CAAC,GAAG;;SAE1B,OAAS;SAAE,sCAAwC;;;SACnD,eAAiB;SAAE,kCAAoC;;;SACvD,oBAAsB;SAAE,kCAAoC;;;SAC5D,mBAAqB;SAAE,sCAAwC;;;SAC/D,sBAAwB;SAAE,sCAAwC;;;SAEjE,sCAAwC;SACxC,gDAAkD;;;SAGlD,yCAA2C;SAC3C,gDAAkD;;;SAGlD,oCAAsC;SACtC,mDAAqD;;;SAGrD,uCAAyC;SACzC,mDAAqD;;;SAGrD,iCAAmC;SACnC,2CAA6C;;;SAG7C,oCAAsC;SACtC,2CAA6C;;;SAG7C,iCAAmC;SACnC,gDAAkD;;;SAGlD,oCAAsC;SACtC,gDAAkD;;;SAEnD,wBAA0B;SAAE,uCAAyC;;;SACrE,2BAA6B;SAAE,uCAAyC;;;SACxE,wBAA0B;SAAE,uCAAyC;;;SACrE,eAAiB;SAAE,kCAAoC;;;SACvD,mBAAqB;SAAE,kCAAoC;;;SAC3D,yBAA2B;SAAE,kCAAoC;;;SACjE,4BAA8B;SAAE,kCAAoC;;EACrE,GAAG,GAAG,OAAO,EAAE,WAAW;QAAO,OAAO;QAAE,OAAO,CAAC,OAAO,CAAC,WAAW;;;AAGzE,KAAK,CAAC,GAAG,GAAG,OAAO,EAAC,MAAQ;AAC5B,KAAK,CAAC,eAAe,GAAG,GAAG,CAAC,gBAAgB;AAC5C,GAAG,CAAC,gBAAgB,YAClB,OAAe,EACf,MAAW,EACX,MAAe,EACf,OAAY,EACZ,CAAC;IACD,KAAK,CAAC,YAAY,GAAG,eAAe,CAAC,GAAG,CAAC,OAAO;IAChD,EAAE,EAAE,YAAY,EAAE,OAAO,GAAG,YAAY;WACjC,eAAe,CAAC,IAAI,CAAC,GAAG,EAAE,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,OAAO;AACnE,CAAC"}