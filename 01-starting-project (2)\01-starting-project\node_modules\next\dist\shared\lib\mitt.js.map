{"version": 3, "sources": ["../../../shared/lib/mitt.ts"], "sourcesContent": ["/*\nMIT License\n\nCopyright (c) <PERSON> (https://jasonformat.com/)\n\nPermission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the \"Software\"), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n*/\n\n// This file is based on https://github.com/developit/mitt/blob/v1.1.3/src/index.js\n// It's been edited for the needs of this script\n// See the LICENSE at the top of the file\n\ntype Handler = (...evts: any[]) => void\n\nexport type MittEmitter<T> = {\n  on(type: T, handler: Handler): void\n  off(type: T, handler: Handler): void\n  emit(type: T, ...evts: any[]): void\n}\n\nexport default function mitt(): MittEmitter<string> {\n  const all: { [s: string]: Handler[] } = Object.create(null)\n\n  return {\n    on(type: string, handler: Handler) {\n      ;(all[type] || (all[type] = [])).push(handler)\n    },\n\n    off(type: string, handler: Handler) {\n      if (all[type]) {\n        all[type].splice(all[type].indexOf(handler) >>> 0, 1)\n      }\n    },\n\n    emit(type: string, ...evts: any[]) {\n      // eslint-disable-next-line array-callback-return\n      ;(all[type] || []).slice().map((handler: Handler) => {\n        handler(...evts)\n      })\n    },\n  }\n}\n"], "names": [], "mappings": ";;;;kBAwBwB,IAAI;SAAJ,IAAI,GAAwB,CAAC;IACnD,KAAK,CAAC,GAAG,GAA+B,MAAM,CAAC,MAAM,CAAC,IAAI;;QAGxD,EAAE,EAAC,IAAY,EAAE,OAAgB,EAAE,CAAC;aAChC,GAAG,CAAC,IAAI,MAAM,GAAG,CAAC,IAAI,SAAS,IAAI,CAAC,OAAO;QAC/C,CAAC;QAED,GAAG,EAAC,IAAY,EAAE,OAAgB,EAAE,CAAC;YACnC,EAAE,EAAE,GAAG,CAAC,IAAI,GAAG,CAAC;gBACd,GAAG,CAAC,IAAI,EAAE,MAAM,CAAC,GAAG,CAAC,IAAI,EAAE,OAAO,CAAC,OAAO,MAAM,CAAC,EAAE,CAAC;YACtD,CAAC;QACH,CAAC;QAED,IAAI,EAAC,IAAY,KAAK,IAAI,EAAS,CAAC;aAEhC,GAAG,CAAC,IAAI,SAAS,KAAK,GAAG,GAAG,EAAE,OAAgB,GAAK,CAAC;gBACpD,OAAO,IAAI,IAAI;YACjB,CAAC;QACH,CAAC;;AAEL,CAAC"}