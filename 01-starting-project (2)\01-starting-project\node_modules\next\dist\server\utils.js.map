{"version": 3, "sources": ["../../server/utils.ts"], "sourcesContent": ["import Observable from 'next/dist/compiled/zen-observable'\nimport { BLOCKED_PAGES } from '../shared/lib/constants'\n\nexport function isBlockedPage(pathname: string): boolean {\n  return BLOCKED_PAGES.includes(pathname)\n}\n\nexport function cleanAmpPath(pathname: string): string {\n  if (pathname.match(/\\?amp=(y|yes|true|1)/)) {\n    pathname = pathname.replace(/\\?amp=(y|yes|true|1)&?/, '?')\n  }\n  if (pathname.match(/&amp=(y|yes|true|1)/)) {\n    pathname = pathname.replace(/&amp=(y|yes|true|1)/, '')\n  }\n  pathname = pathname.replace(/\\?$/, '')\n  return pathname\n}\n\nexport type RenderResult = Observable<string>\n\nexport function mergeResults(results: Array<RenderResult>): RenderResult {\n  // @ts-ignore\n  return Observable.prototype.concat.call(...results)\n}\n\nexport async function resultsToString(\n  results: Array<RenderResult>\n): Promise<string> {\n  const chunks: string[] = []\n  await mergeResults(results).forEach((chunk: string) => {\n    chunks.push(chunk)\n  })\n  return chunks.join('')\n}\n"], "names": [], "mappings": ";;;;QAGgB,aAAa,GAAb,aAAa;QAIb,YAAY,GAAZ,YAAY;QAaZ,YAAY,GAAZ,YAAY;QAKN,eAAe,GAAf,eAAe;AAzBd,GAAmC,CAAnC,cAAmC;AAC5B,GAAyB,CAAzB,UAAyB;;;;;;SAEvC,aAAa,CAAC,QAAgB,EAAW,CAAC;WAF5B,UAAyB,eAGhC,QAAQ,CAAC,QAAQ;AACxC,CAAC;SAEe,YAAY,CAAC,QAAgB,EAAU,CAAC;IACtD,EAAE,EAAE,QAAQ,CAAC,KAAK,0BAA0B,CAAC;QAC3C,QAAQ,GAAG,QAAQ,CAAC,OAAO,4BAA2B,CAAG;IAC3D,CAAC;IACD,EAAE,EAAE,QAAQ,CAAC,KAAK,yBAAyB,CAAC;QAC1C,QAAQ,GAAG,QAAQ,CAAC,OAAO;IAC7B,CAAC;IACD,QAAQ,GAAG,QAAQ,CAAC,OAAO;WACpB,QAAQ;AACjB,CAAC;SAIe,YAAY,CAAC,OAA4B,EAAgB,CAAC;IACxE,EAAa,AAAb,WAAa;WArBQ,cAAmC,SAsBtC,SAAS,CAAC,MAAM,CAAC,IAAI,IAAI,OAAO;AACpD,CAAC;eAEqB,eAAe,CACnC,OAA4B,EACX,CAAC;IAClB,KAAK,CAAC,MAAM;UACN,YAAY,CAAC,OAAO,EAAE,OAAO,EAAE,KAAa,GAAK,CAAC;QACtD,MAAM,CAAC,IAAI,CAAC,KAAK;IACnB,CAAC;WACM,MAAM,CAAC,IAAI;AACpB,CAAC"}