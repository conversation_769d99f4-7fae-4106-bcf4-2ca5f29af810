{"version": 3, "sources": ["../../server/render.tsx"], "sourcesContent": ["import { IncomingMessage, ServerResponse } from 'http'\nimport { ParsedUrlQuery } from 'querystring'\nimport { PassThrough } from 'stream'\nimport React from 'react'\nimport * as ReactDOMServer from 'react-dom/server'\nimport flush from 'styled-jsx/server'\nimport Observable from 'next/dist/compiled/zen-observable'\nimport { warn } from '../build/output/log'\nimport { UnwrapPromise } from '../lib/coalesced-function'\nimport {\n  GSP_NO_RETURNED_VALUE,\n  GSSP_COMPONENT_MEMBER_ERROR,\n  GSSP_NO_RETURNED_VALUE,\n  STATIC_STATUS_PAGE_GET_INITIAL_PROPS_ERROR,\n  SERVER_PROPS_GET_INIT_PROPS_CONFLICT,\n  SERVER_PROPS_SSG_CONFLICT,\n  SSG_GET_INITIAL_PROPS_CONFLICT,\n  UNSTABLE_REVALIDATE_RENAME_ERROR,\n} from '../lib/constants'\nimport { isSerializableProps } from '../lib/is-serializable-props'\nimport { GetServerSideProps, GetStaticProps, PreviewData } from '../types'\nimport { isInAmpMode } from '../shared/lib/amp'\nimport { AmpStateContext } from '../shared/lib/amp-context'\nimport {\n  BODY_RENDER_TARGET,\n  SERVER_PROPS_ID,\n  STATIC_PROPS_ID,\n  STATIC_STATUS_PAGES,\n} from '../shared/lib/constants'\nimport { defaultHead } from '../shared/lib/head'\nimport { HeadManagerContext } from '../shared/lib/head-manager-context'\nimport Loadable from '../shared/lib/loadable'\nimport { LoadableContext } from '../shared/lib/loadable-context'\nimport postProcess from '../shared/lib/post-process'\nimport { RouterContext } from '../shared/lib/router-context'\nimport { NextRouter } from '../shared/lib/router/router'\nimport { isDynamicRoute } from '../shared/lib/router/utils/is-dynamic'\nimport {\n  AppType,\n  ComponentsEnhancer,\n  DocumentInitialProps,\n  DocumentProps,\n  HtmlContext,\n  HtmlProps,\n  getDisplayName,\n  isResSent,\n  loadGetInitialProps,\n  NextComponentType,\n  RenderPage,\n  RenderPageResult,\n} from '../shared/lib/utils'\nimport {\n  tryGetPreviewData,\n  NextApiRequestCookies,\n  __ApiPreviewProps,\n} from './api-utils'\nimport { denormalizePagePath } from './denormalize-page-path'\nimport { FontManifest, getFontDefinitionFromManifest } from './font-utils'\nimport { LoadComponentsReturnType, ManifestItem } from './load-components'\nimport { normalizePagePath } from './normalize-page-path'\nimport optimizeAmp from './optimize-amp'\nimport {\n  allowedStatusCodes,\n  getRedirectStatus,\n  Redirect,\n} from '../lib/load-custom-routes'\nimport { DomainLocale } from './config'\nimport { mergeResults, RenderResult, resultsToString } from './utils'\n\nfunction noRouter() {\n  const message =\n    'No router instance found. you should only use \"next/router\" inside the client side of your app. https://nextjs.org/docs/messages/no-router-instance'\n  throw new Error(message)\n}\n\nclass ServerRouter implements NextRouter {\n  route: string\n  pathname: string\n  query: ParsedUrlQuery\n  asPath: string\n  basePath: string\n  events: any\n  isFallback: boolean\n  locale?: string\n  isReady: boolean\n  locales?: string[]\n  defaultLocale?: string\n  domainLocales?: DomainLocale[]\n  isPreview: boolean\n  isLocaleDomain: boolean\n\n  constructor(\n    pathname: string,\n    query: ParsedUrlQuery,\n    as: string,\n    { isFallback }: { isFallback: boolean },\n    isReady: boolean,\n    basePath: string,\n    locale?: string,\n    locales?: string[],\n    defaultLocale?: string,\n    domainLocales?: DomainLocale[],\n    isPreview?: boolean,\n    isLocaleDomain?: boolean\n  ) {\n    this.route = pathname.replace(/\\/$/, '') || '/'\n    this.pathname = pathname\n    this.query = query\n    this.asPath = as\n    this.isFallback = isFallback\n    this.basePath = basePath\n    this.locale = locale\n    this.locales = locales\n    this.defaultLocale = defaultLocale\n    this.isReady = isReady\n    this.domainLocales = domainLocales\n    this.isPreview = !!isPreview\n    this.isLocaleDomain = !!isLocaleDomain\n  }\n\n  push(): any {\n    noRouter()\n  }\n  replace(): any {\n    noRouter()\n  }\n  reload() {\n    noRouter()\n  }\n  back() {\n    noRouter()\n  }\n  prefetch(): any {\n    noRouter()\n  }\n  beforePopState() {\n    noRouter()\n  }\n}\n\nfunction enhanceComponents(\n  options: ComponentsEnhancer,\n  App: AppType,\n  Component: NextComponentType\n): {\n  App: AppType\n  Component: NextComponentType\n} {\n  // For backwards compatibility\n  if (typeof options === 'function') {\n    return {\n      App,\n      Component: options(Component),\n    }\n  }\n\n  return {\n    App: options.enhanceApp ? options.enhanceApp(App) : App,\n    Component: options.enhanceComponent\n      ? options.enhanceComponent(Component)\n      : Component,\n  }\n}\n\nexport type RenderOptsPartial = {\n  buildId: string\n  canonicalBase: string\n  runtimeConfig?: { [key: string]: any }\n  assetPrefix?: string\n  err?: Error | null\n  nextExport?: boolean\n  dev?: boolean\n  ampPath?: string\n  ErrorDebug?: React.ComponentType<{ error: Error }>\n  ampValidator?: (html: string, pathname: string) => Promise<void>\n  ampSkipValidation?: boolean\n  ampOptimizerConfig?: { [key: string]: any }\n  isDataReq?: boolean\n  params?: ParsedUrlQuery\n  previewProps: __ApiPreviewProps\n  basePath: string\n  unstable_runtimeJS?: false\n  unstable_JsPreload?: false\n  optimizeFonts: boolean\n  fontManifest?: FontManifest\n  optimizeImages: boolean\n  optimizeCss: any\n  devOnlyCacheBusterQueryString?: string\n  resolvedUrl?: string\n  resolvedAsPath?: string\n  distDir?: string\n  locale?: string\n  locales?: string[]\n  defaultLocale?: string\n  domainLocales?: DomainLocale[]\n  disableOptimizedLoading?: boolean\n  requireStaticHTML?: boolean\n  concurrentFeatures?: boolean\n  customServer?: boolean\n}\n\nexport type RenderOpts = LoadComponentsReturnType & RenderOptsPartial\n\nconst invalidKeysMsg = (methodName: string, invalidKeys: string[]) => {\n  return (\n    `Additional keys were returned from \\`${methodName}\\`. Properties intended for your component must be nested under the \\`props\\` key, e.g.:` +\n    `\\n\\n\\treturn { props: { title: 'My Title', content: '...' } }` +\n    `\\n\\nKeys that need to be moved: ${invalidKeys.join(', ')}.` +\n    `\\nRead more: https://nextjs.org/docs/messages/invalid-getstaticprops-value`\n  )\n}\n\nfunction checkRedirectValues(\n  redirect: Redirect,\n  req: IncomingMessage,\n  method: 'getStaticProps' | 'getServerSideProps'\n) {\n  const { destination, permanent, statusCode, basePath } = redirect\n  let errors: string[] = []\n\n  const hasStatusCode = typeof statusCode !== 'undefined'\n  const hasPermanent = typeof permanent !== 'undefined'\n\n  if (hasPermanent && hasStatusCode) {\n    errors.push(`\\`permanent\\` and \\`statusCode\\` can not both be provided`)\n  } else if (hasPermanent && typeof permanent !== 'boolean') {\n    errors.push(`\\`permanent\\` must be \\`true\\` or \\`false\\``)\n  } else if (hasStatusCode && !allowedStatusCodes.has(statusCode!)) {\n    errors.push(\n      `\\`statusCode\\` must undefined or one of ${[...allowedStatusCodes].join(\n        ', '\n      )}`\n    )\n  }\n  const destinationType = typeof destination\n\n  if (destinationType !== 'string') {\n    errors.push(\n      `\\`destination\\` should be string but received ${destinationType}`\n    )\n  }\n\n  const basePathType = typeof basePath\n\n  if (basePathType !== 'undefined' && basePathType !== 'boolean') {\n    errors.push(\n      `\\`basePath\\` should be undefined or a false, received ${basePathType}`\n    )\n  }\n\n  if (errors.length > 0) {\n    throw new Error(\n      `Invalid redirect object returned from ${method} for ${req.url}\\n` +\n        errors.join(' and ') +\n        '\\n' +\n        `See more info here: https://nextjs.org/docs/messages/invalid-redirect-gssp`\n    )\n  }\n}\n\nexport async function renderToHTML(\n  req: IncomingMessage,\n  res: ServerResponse,\n  pathname: string,\n  query: ParsedUrlQuery,\n  renderOpts: RenderOpts\n): Promise<RenderResult | null> {\n  // In dev we invalidate the cache by appending a timestamp to the resource URL.\n  // This is a workaround to fix https://github.com/vercel/next.js/issues/5860\n  // TODO: remove this workaround when https://bugs.webkit.org/show_bug.cgi?id=187726 is fixed.\n  renderOpts.devOnlyCacheBusterQueryString = renderOpts.dev\n    ? renderOpts.devOnlyCacheBusterQueryString || `?ts=${Date.now()}`\n    : ''\n\n  // don't modify original query object\n  query = Object.assign({}, query)\n\n  const {\n    err,\n    dev = false,\n    ampPath = '',\n    App,\n    Document,\n    pageConfig = {},\n    Component,\n    buildManifest,\n    fontManifest,\n    reactLoadableManifest,\n    ErrorDebug,\n    getStaticProps,\n    getStaticPaths,\n    getServerSideProps,\n    isDataReq,\n    params,\n    previewProps,\n    basePath,\n    devOnlyCacheBusterQueryString,\n    requireStaticHTML,\n    concurrentFeatures,\n  } = renderOpts\n\n  const getFontDefinition = (url: string): string => {\n    if (fontManifest) {\n      return getFontDefinitionFromManifest(url, fontManifest)\n    }\n    return ''\n  }\n\n  const callMiddleware = async (method: string, args: any[], props = false) => {\n    let results: any = props ? {} : []\n\n    if ((Document as any)[`${method}Middleware`]) {\n      let middlewareFunc = await (Document as any)[`${method}Middleware`]\n      middlewareFunc = middlewareFunc.default || middlewareFunc\n\n      const curResults = await middlewareFunc(...args)\n      if (props) {\n        for (const result of curResults) {\n          results = {\n            ...results,\n            ...result,\n          }\n        }\n      } else {\n        results = curResults\n      }\n    }\n    return results\n  }\n\n  const headTags = (...args: any) => callMiddleware('headTags', args)\n\n  const isFallback = !!query.__nextFallback\n  delete query.__nextFallback\n  delete query.__nextLocale\n  delete query.__nextDefaultLocale\n\n  const isSSG = !!getStaticProps\n  const isBuildTimeSSG = isSSG && renderOpts.nextExport\n  const defaultAppGetInitialProps =\n    App.getInitialProps === (App as any).origGetInitialProps\n\n  const hasPageGetInitialProps = !!(Component as any).getInitialProps\n\n  const pageIsDynamic = isDynamicRoute(pathname)\n\n  const isAutoExport =\n    !hasPageGetInitialProps &&\n    defaultAppGetInitialProps &&\n    !isSSG &&\n    !getServerSideProps\n\n  for (const methodName of [\n    'getStaticProps',\n    'getServerSideProps',\n    'getStaticPaths',\n  ]) {\n    if ((Component as any)[methodName]) {\n      throw new Error(\n        `page ${pathname} ${methodName} ${GSSP_COMPONENT_MEMBER_ERROR}`\n      )\n    }\n  }\n\n  if (hasPageGetInitialProps && isSSG) {\n    throw new Error(SSG_GET_INITIAL_PROPS_CONFLICT + ` ${pathname}`)\n  }\n\n  if (hasPageGetInitialProps && getServerSideProps) {\n    throw new Error(SERVER_PROPS_GET_INIT_PROPS_CONFLICT + ` ${pathname}`)\n  }\n\n  if (getServerSideProps && isSSG) {\n    throw new Error(SERVER_PROPS_SSG_CONFLICT + ` ${pathname}`)\n  }\n\n  if (getStaticPaths && !pageIsDynamic) {\n    throw new Error(\n      `getStaticPaths is only allowed for dynamic SSG pages and was found on '${pathname}'.` +\n        `\\nRead more: https://nextjs.org/docs/messages/non-dynamic-getstaticpaths-usage`\n    )\n  }\n\n  if (!!getStaticPaths && !isSSG) {\n    throw new Error(\n      `getStaticPaths was added without a getStaticProps in ${pathname}. Without getStaticProps, getStaticPaths does nothing`\n    )\n  }\n\n  if (isSSG && pageIsDynamic && !getStaticPaths) {\n    throw new Error(\n      `getStaticPaths is required for dynamic SSG pages and is missing for '${pathname}'.` +\n        `\\nRead more: https://nextjs.org/docs/messages/invalid-getstaticpaths-value`\n    )\n  }\n\n  let asPath: string = renderOpts.resolvedAsPath || (req.url as string)\n\n  if (dev) {\n    const { isValidElementType } = require('react-is')\n    if (!isValidElementType(Component)) {\n      throw new Error(\n        `The default export is not a React Component in page: \"${pathname}\"`\n      )\n    }\n\n    if (!isValidElementType(App)) {\n      throw new Error(\n        `The default export is not a React Component in page: \"/_app\"`\n      )\n    }\n\n    if (!isValidElementType(Document)) {\n      throw new Error(\n        `The default export is not a React Component in page: \"/_document\"`\n      )\n    }\n\n    if (isAutoExport || isFallback) {\n      // remove query values except ones that will be set during export\n      query = {\n        ...(query.amp\n          ? {\n              amp: query.amp,\n            }\n          : {}),\n      }\n      asPath = `${pathname}${\n        // ensure trailing slash is present for non-dynamic auto-export pages\n        req.url!.endsWith('/') && pathname !== '/' && !pageIsDynamic ? '/' : ''\n      }`\n      req.url = pathname\n    }\n\n    if (pathname === '/404' && (hasPageGetInitialProps || getServerSideProps)) {\n      throw new Error(\n        `\\`pages/404\\` ${STATIC_STATUS_PAGE_GET_INITIAL_PROPS_ERROR}`\n      )\n    }\n    if (\n      STATIC_STATUS_PAGES.includes(pathname) &&\n      (hasPageGetInitialProps || getServerSideProps)\n    ) {\n      throw new Error(\n        `\\`pages${pathname}\\` ${STATIC_STATUS_PAGE_GET_INITIAL_PROPS_ERROR}`\n      )\n    }\n  }\n\n  await Loadable.preloadAll() // Make sure all dynamic imports are loaded\n\n  let isPreview\n  let previewData: PreviewData\n\n  if ((isSSG || getServerSideProps) && !isFallback) {\n    // Reads of this are cached on the `req` object, so this should resolve\n    // instantly. There's no need to pass this data down from a previous\n    // invoke, where we'd have to consider server & serverless.\n    previewData = tryGetPreviewData(req, res, previewProps)\n    isPreview = previewData !== false\n  }\n\n  // url will always be set\n  const routerIsReady = !!(\n    getServerSideProps ||\n    hasPageGetInitialProps ||\n    (!defaultAppGetInitialProps && !isSSG)\n  )\n  const router = new ServerRouter(\n    pathname,\n    query,\n    asPath,\n    {\n      isFallback: isFallback,\n    },\n    routerIsReady,\n    basePath,\n    renderOpts.locale,\n    renderOpts.locales,\n    renderOpts.defaultLocale,\n    renderOpts.domainLocales,\n    isPreview,\n    (req as any).__nextIsLocaleDomain\n  )\n  const ctx = {\n    err,\n    req: isAutoExport ? undefined : req,\n    res: isAutoExport ? undefined : res,\n    pathname,\n    query,\n    asPath,\n    locale: renderOpts.locale,\n    locales: renderOpts.locales,\n    defaultLocale: renderOpts.defaultLocale,\n    AppTree: (props: any) => {\n      return (\n        <AppContainer>\n          <App {...props} Component={Component} router={router} />\n        </AppContainer>\n      )\n    },\n  }\n  let props: any\n\n  const ampState = {\n    ampFirst: pageConfig.amp === true,\n    hasQuery: Boolean(query.amp),\n    hybrid: pageConfig.amp === 'hybrid',\n  }\n\n  const inAmpMode = isInAmpMode(ampState)\n\n  const reactLoadableModules: string[] = []\n\n  let head: JSX.Element[] = defaultHead(inAmpMode)\n\n  let scriptLoader: any = {}\n  const nextExport =\n    !isSSG && (renderOpts.nextExport || (dev && (isAutoExport || isFallback)))\n\n  const AppContainer = ({ children }: { children: JSX.Element }) => (\n    <RouterContext.Provider value={router}>\n      <AmpStateContext.Provider value={ampState}>\n        <HeadManagerContext.Provider\n          value={{\n            updateHead: (state) => {\n              head = state\n            },\n            updateScripts: (scripts) => {\n              scriptLoader = scripts\n            },\n            scripts: {},\n            mountedInstances: new Set(),\n          }}\n        >\n          <LoadableContext.Provider\n            value={(moduleName) => reactLoadableModules.push(moduleName)}\n          >\n            {children}\n          </LoadableContext.Provider>\n        </HeadManagerContext.Provider>\n      </AmpStateContext.Provider>\n    </RouterContext.Provider>\n  )\n\n  try {\n    props = await loadGetInitialProps(App, {\n      AppTree: ctx.AppTree,\n      Component,\n      router,\n      ctx,\n    })\n\n    if ((isSSG || getServerSideProps) && isPreview) {\n      props.__N_PREVIEW = true\n    }\n\n    if (isSSG) {\n      props[STATIC_PROPS_ID] = true\n    }\n\n    if (isSSG && !isFallback) {\n      let data: UnwrapPromise<ReturnType<GetStaticProps>>\n\n      try {\n        data = await getStaticProps!({\n          ...(pageIsDynamic ? { params: query as ParsedUrlQuery } : undefined),\n          ...(isPreview\n            ? { preview: true, previewData: previewData }\n            : undefined),\n          locales: renderOpts.locales,\n          locale: renderOpts.locale,\n          defaultLocale: renderOpts.defaultLocale,\n        })\n      } catch (staticPropsError) {\n        // remove not found error code to prevent triggering legacy\n        // 404 rendering\n        if (staticPropsError.code === 'ENOENT') {\n          delete staticPropsError.code\n        }\n        throw staticPropsError\n      }\n\n      if (data == null) {\n        throw new Error(GSP_NO_RETURNED_VALUE)\n      }\n\n      const invalidKeys = Object.keys(data).filter(\n        (key) =>\n          key !== 'revalidate' &&\n          key !== 'props' &&\n          key !== 'redirect' &&\n          key !== 'notFound'\n      )\n\n      if (invalidKeys.includes('unstable_revalidate')) {\n        throw new Error(UNSTABLE_REVALIDATE_RENAME_ERROR)\n      }\n\n      if (invalidKeys.length) {\n        throw new Error(invalidKeysMsg('getStaticProps', invalidKeys))\n      }\n\n      if (process.env.NODE_ENV !== 'production') {\n        if (\n          typeof (data as any).notFound !== 'undefined' &&\n          typeof (data as any).redirect !== 'undefined'\n        ) {\n          throw new Error(\n            `\\`redirect\\` and \\`notFound\\` can not both be returned from ${\n              isSSG ? 'getStaticProps' : 'getServerSideProps'\n            } at the same time. Page: ${pathname}\\nSee more info here: https://nextjs.org/docs/messages/gssp-mixed-not-found-redirect`\n          )\n        }\n      }\n\n      if ('notFound' in data && data.notFound) {\n        if (pathname === '/404') {\n          throw new Error(\n            `The /404 page can not return notFound in \"getStaticProps\", please remove it to continue!`\n          )\n        }\n\n        ;(renderOpts as any).isNotFound = true\n      }\n\n      if (\n        'redirect' in data &&\n        data.redirect &&\n        typeof data.redirect === 'object'\n      ) {\n        checkRedirectValues(data.redirect as Redirect, req, 'getStaticProps')\n\n        if (isBuildTimeSSG) {\n          throw new Error(\n            `\\`redirect\\` can not be returned from getStaticProps during prerendering (${req.url})\\n` +\n              `See more info here: https://nextjs.org/docs/messages/gsp-redirect-during-prerender`\n          )\n        }\n\n        ;(data as any).props = {\n          __N_REDIRECT: data.redirect.destination,\n          __N_REDIRECT_STATUS: getRedirectStatus(data.redirect),\n        }\n        if (typeof data.redirect.basePath !== 'undefined') {\n          ;(data as any).props.__N_REDIRECT_BASE_PATH = data.redirect.basePath\n        }\n        ;(renderOpts as any).isRedirect = true\n      }\n\n      if (\n        (dev || isBuildTimeSSG) &&\n        !(renderOpts as any).isNotFound &&\n        !isSerializableProps(pathname, 'getStaticProps', (data as any).props)\n      ) {\n        // this fn should throw an error instead of ever returning `false`\n        throw new Error(\n          'invariant: getStaticProps did not return valid props. Please report this.'\n        )\n      }\n\n      if ('revalidate' in data) {\n        if (typeof data.revalidate === 'number') {\n          if (!Number.isInteger(data.revalidate)) {\n            throw new Error(\n              `A page's revalidate option must be seconds expressed as a natural number for ${req.url}. Mixed numbers, such as '${data.revalidate}', cannot be used.` +\n                `\\nTry changing the value to '${Math.ceil(\n                  data.revalidate\n                )}' or using \\`Math.ceil()\\` if you're computing the value.`\n            )\n          } else if (data.revalidate <= 0) {\n            throw new Error(\n              `A page's revalidate option can not be less than or equal to zero for ${req.url}. A revalidate option of zero means to revalidate after _every_ request, and implies stale data cannot be tolerated.` +\n                `\\n\\nTo never revalidate, you can set revalidate to \\`false\\` (only ran once at build-time).` +\n                `\\nTo revalidate as soon as possible, you can set the value to \\`1\\`.`\n            )\n          } else if (data.revalidate > 31536000) {\n            // if it's greater than a year for some reason error\n            console.warn(\n              `Warning: A page's revalidate option was set to more than a year for ${req.url}. This may have been done in error.` +\n                `\\nTo only run getStaticProps at build-time and not revalidate at runtime, you can set \\`revalidate\\` to \\`false\\`!`\n            )\n          }\n        } else if (data.revalidate === true) {\n          // When enabled, revalidate after 1 second. This value is optimal for\n          // the most up-to-date page possible, but without a 1-to-1\n          // request-refresh ratio.\n          data.revalidate = 1\n        } else if (\n          data.revalidate === false ||\n          typeof data.revalidate === 'undefined'\n        ) {\n          // By default, we never revalidate.\n          data.revalidate = false\n        } else {\n          throw new Error(\n            `A page's revalidate option must be seconds expressed as a natural number. Mixed numbers and strings cannot be used. Received '${JSON.stringify(\n              data.revalidate\n            )}' for ${req.url}`\n          )\n        }\n      } else {\n        // By default, we never revalidate.\n        ;(data as any).revalidate = false\n      }\n\n      props.pageProps = Object.assign(\n        {},\n        props.pageProps,\n        'props' in data ? data.props : undefined\n      )\n\n      // pass up revalidate and props for export\n      // TODO: change this to a different passing mechanism\n      ;(renderOpts as any).revalidate =\n        'revalidate' in data ? data.revalidate : undefined\n      ;(renderOpts as any).pageData = props\n\n      // this must come after revalidate is added to renderOpts\n      if ((renderOpts as any).isNotFound) {\n        return null\n      }\n    }\n\n    if (getServerSideProps) {\n      props[SERVER_PROPS_ID] = true\n    }\n\n    if (getServerSideProps && !isFallback) {\n      let data: UnwrapPromise<ReturnType<GetServerSideProps>>\n\n      try {\n        data = await getServerSideProps({\n          req: req as IncomingMessage & {\n            cookies: NextApiRequestCookies\n          },\n          res,\n          query,\n          resolvedUrl: renderOpts.resolvedUrl as string,\n          ...(pageIsDynamic ? { params: params as ParsedUrlQuery } : undefined),\n          ...(previewData !== false\n            ? { preview: true, previewData: previewData }\n            : undefined),\n          locales: renderOpts.locales,\n          locale: renderOpts.locale,\n          defaultLocale: renderOpts.defaultLocale,\n        })\n      } catch (serverSidePropsError) {\n        // remove not found error code to prevent triggering legacy\n        // 404 rendering\n        if (serverSidePropsError.code === 'ENOENT') {\n          delete serverSidePropsError.code\n        }\n        throw serverSidePropsError\n      }\n\n      if (data == null) {\n        throw new Error(GSSP_NO_RETURNED_VALUE)\n      }\n\n      const invalidKeys = Object.keys(data).filter(\n        (key) => key !== 'props' && key !== 'redirect' && key !== 'notFound'\n      )\n\n      if ((data as any).unstable_notFound) {\n        throw new Error(\n          `unstable_notFound has been renamed to notFound, please update the field to continue. Page: ${pathname}`\n        )\n      }\n      if ((data as any).unstable_redirect) {\n        throw new Error(\n          `unstable_redirect has been renamed to redirect, please update the field to continue. Page: ${pathname}`\n        )\n      }\n\n      if (invalidKeys.length) {\n        throw new Error(invalidKeysMsg('getServerSideProps', invalidKeys))\n      }\n\n      if ('notFound' in data && data.notFound) {\n        if (pathname === '/404') {\n          throw new Error(\n            `The /404 page can not return notFound in \"getStaticProps\", please remove it to continue!`\n          )\n        }\n\n        ;(renderOpts as any).isNotFound = true\n        return null\n      }\n\n      if ('redirect' in data && typeof data.redirect === 'object') {\n        checkRedirectValues(\n          data.redirect as Redirect,\n          req,\n          'getServerSideProps'\n        )\n        ;(data as any).props = {\n          __N_REDIRECT: data.redirect.destination,\n          __N_REDIRECT_STATUS: getRedirectStatus(data.redirect),\n        }\n        if (typeof data.redirect.basePath !== 'undefined') {\n          ;(data as any).props.__N_REDIRECT_BASE_PATH = data.redirect.basePath\n        }\n        ;(renderOpts as any).isRedirect = true\n      }\n\n      if ((data as any).props instanceof Promise) {\n        ;(data as any).props = await (data as any).props\n      }\n\n      if (\n        (dev || isBuildTimeSSG) &&\n        !isSerializableProps(\n          pathname,\n          'getServerSideProps',\n          (data as any).props\n        )\n      ) {\n        // this fn should throw an error instead of ever returning `false`\n        throw new Error(\n          'invariant: getServerSideProps did not return valid props. Please report this.'\n        )\n      }\n\n      props.pageProps = Object.assign({}, props.pageProps, (data as any).props)\n      ;(renderOpts as any).pageData = props\n    }\n  } catch (dataFetchError) {\n    throw dataFetchError\n  }\n\n  if (\n    !isSSG && // we only show this warning for legacy pages\n    !getServerSideProps &&\n    process.env.NODE_ENV !== 'production' &&\n    Object.keys(props?.pageProps || {}).includes('url')\n  ) {\n    console.warn(\n      `The prop \\`url\\` is a reserved prop in Next.js for legacy reasons and will be overridden on page ${pathname}\\n` +\n        `See more info here: https://nextjs.org/docs/messages/reserved-page-prop`\n    )\n  }\n\n  // Avoid rendering page un-necessarily for getServerSideProps data request\n  // and getServerSideProps/getStaticProps redirects\n  if ((isDataReq && !isSSG) || (renderOpts as any).isRedirect) {\n    return Observable.of(JSON.stringify(props))\n  }\n\n  // We don't call getStaticProps or getServerSideProps while generating\n  // the fallback so make sure to set pageProps to an empty object\n  if (isFallback) {\n    props.pageProps = {}\n  }\n\n  // the response might be finished on the getInitialProps call\n  if (isResSent(res) && !isSSG) return null\n\n  // we preload the buildManifest for auto-export dynamic pages\n  // to speed up hydrating query values\n  let filteredBuildManifest = buildManifest\n  if (isAutoExport && pageIsDynamic) {\n    const page = denormalizePagePath(normalizePagePath(pathname))\n    // This code would be much cleaner using `immer` and directly pushing into\n    // the result from `getPageFiles`, we could maybe consider that in the\n    // future.\n    if (page in filteredBuildManifest.pages) {\n      filteredBuildManifest = {\n        ...filteredBuildManifest,\n        pages: {\n          ...filteredBuildManifest.pages,\n          [page]: [\n            ...filteredBuildManifest.pages[page],\n            ...filteredBuildManifest.lowPriorityFiles.filter((f) =>\n              f.includes('_buildManifest')\n            ),\n          ],\n        },\n        lowPriorityFiles: filteredBuildManifest.lowPriorityFiles.filter(\n          (f) => !f.includes('_buildManifest')\n        ),\n      }\n    }\n  }\n\n  const generateStaticHTML = requireStaticHTML || inAmpMode\n  const renderToStream = (element: React.ReactElement) =>\n    new Promise<RenderResult>((resolve, reject) => {\n      const stream = new PassThrough()\n      let resolved = false\n      const doResolve = () => {\n        if (!resolved) {\n          resolved = true\n\n          resolve(\n            new Observable((observer) => {\n              stream.on('data', (chunk) => {\n                observer.next(chunk.toString('utf-8'))\n              })\n              stream.once('end', () => {\n                observer.complete()\n              })\n\n              startWriting()\n              return () => {\n                abort()\n              }\n            })\n          )\n        }\n      }\n\n      const { abort, startWriting } = (\n        ReactDOMServer as any\n      ).pipeToNodeWritable(element, stream, {\n        onError(error: Error) {\n          if (!resolved) {\n            resolved = true\n            reject(error)\n          }\n          abort()\n        },\n        onReadyToStream() {\n          if (!generateStaticHTML) {\n            doResolve()\n          }\n        },\n        onCompleteAll() {\n          doResolve()\n        },\n      })\n    }).then(multiplexResult)\n\n  const renderDocument = async () => {\n    if (Document.getInitialProps) {\n      const renderPage: RenderPage = (\n        options: ComponentsEnhancer = {}\n      ): RenderPageResult | Promise<RenderPageResult> => {\n        if (ctx.err && ErrorDebug) {\n          const html = ReactDOMServer.renderToString(\n            <ErrorDebug error={ctx.err} />\n          )\n          return { html, head }\n        }\n\n        if (dev && (props.router || props.Component)) {\n          throw new Error(\n            `'router' and 'Component' can not be returned in getInitialProps from _app.js https://nextjs.org/docs/messages/cant-override-next-props`\n          )\n        }\n\n        const { App: EnhancedApp, Component: EnhancedComponent } =\n          enhanceComponents(options, App, Component)\n\n        const html = ReactDOMServer.renderToString(\n          <AppContainer>\n            <EnhancedApp\n              Component={EnhancedComponent}\n              router={router}\n              {...props}\n            />\n          </AppContainer>\n        )\n        return { html, head }\n      }\n      const documentCtx = { ...ctx, renderPage }\n      const docProps: DocumentInitialProps = await loadGetInitialProps(\n        Document,\n        documentCtx\n      )\n      // the response might be finished on the getInitialProps call\n      if (isResSent(res) && !isSSG) return null\n\n      if (!docProps || typeof docProps.html !== 'string') {\n        const message = `\"${getDisplayName(\n          Document\n        )}.getInitialProps()\" should resolve to an object with a \"html\" prop set with a valid html string`\n        throw new Error(message)\n      }\n\n      return {\n        bodyResult: Observable.of(docProps.html),\n        documentElement: (htmlProps: HtmlProps) => (\n          <Document {...htmlProps} {...docProps} />\n        ),\n        head: docProps.head,\n        headTags: await headTags(documentCtx),\n        styles: docProps.styles,\n      }\n    } else {\n      const content =\n        ctx.err && ErrorDebug ? (\n          <ErrorDebug error={ctx.err} />\n        ) : (\n          <AppContainer>\n            <App {...props} Component={Component} router={router} />\n          </AppContainer>\n        )\n      const bodyResult = concurrentFeatures\n        ? await renderToStream(content)\n        : Observable.of(ReactDOMServer.renderToString(content))\n\n      return {\n        bodyResult,\n        documentElement: () => (Document as any)(),\n        head,\n        headTags: [],\n        // TODO: Experimental styled-jsx 5 support\n        styles: [...flush()],\n      }\n    }\n  }\n\n  const documentResult = await renderDocument()\n  if (!documentResult) {\n    return null\n  }\n\n  const dynamicImportsIds = new Set<string | number>()\n  const dynamicImports = new Set<string>()\n\n  for (const mod of reactLoadableModules) {\n    const manifestItem: ManifestItem = reactLoadableManifest[mod]\n\n    if (manifestItem) {\n      dynamicImportsIds.add(manifestItem.id)\n      manifestItem.files.forEach((item) => {\n        dynamicImports.add(item)\n      })\n    }\n  }\n\n  const hybridAmp = ampState.hybrid\n\n  const docComponentsRendered: DocumentProps['docComponentsRendered'] = {}\n  const {\n    assetPrefix,\n    buildId,\n    customServer,\n    defaultLocale,\n    disableOptimizedLoading,\n    domainLocales,\n    locale,\n    locales,\n    runtimeConfig,\n  } = renderOpts\n  const htmlProps: any = {\n    __NEXT_DATA__: {\n      props, // The result of getInitialProps\n      page: pathname, // The rendered page\n      query, // querystring parsed / passed by the user\n      buildId, // buildId is used to facilitate caching of page bundles, we send it to the client so that pageloader knows where to load bundles\n      assetPrefix: assetPrefix === '' ? undefined : assetPrefix, // send assetPrefix to the client side when configured, otherwise don't sent in the resulting HTML\n      runtimeConfig, // runtimeConfig if provided, otherwise don't sent in the resulting HTML\n      nextExport: nextExport === true ? true : undefined, // If this is a page exported by `next export`\n      autoExport: isAutoExport === true ? true : undefined, // If this is an auto exported page\n      isFallback,\n      dynamicIds:\n        dynamicImportsIds.size === 0\n          ? undefined\n          : Array.from(dynamicImportsIds),\n      err: renderOpts.err ? serializeError(dev, renderOpts.err) : undefined, // Error if one happened, otherwise don't sent in the resulting HTML\n      gsp: !!getStaticProps ? true : undefined, // whether the page is getStaticProps\n      gssp: !!getServerSideProps ? true : undefined, // whether the page is getServerSideProps\n      customServer, // whether the user is using a custom server\n      gip: hasPageGetInitialProps ? true : undefined, // whether the page has getInitialProps\n      appGip: !defaultAppGetInitialProps ? true : undefined, // whether the _app has getInitialProps\n      locale,\n      locales,\n      defaultLocale,\n      domainLocales,\n      isPreview: isPreview === true ? true : undefined,\n    },\n    buildManifest: filteredBuildManifest,\n    docComponentsRendered,\n    dangerousAsPath: router.asPath,\n    canonicalBase:\n      !renderOpts.ampPath && (req as any).__nextStrippedLocale\n        ? `${renderOpts.canonicalBase || ''}/${renderOpts.locale}`\n        : renderOpts.canonicalBase,\n    ampPath,\n    inAmpMode,\n    isDevelopment: !!dev,\n    hybridAmp,\n    dynamicImports: Array.from(dynamicImports),\n    assetPrefix,\n    // Only enabled in production as development mode has features relying on HMR (style injection for example)\n    unstable_runtimeJS:\n      process.env.NODE_ENV === 'production'\n        ? pageConfig.unstable_runtimeJS\n        : undefined,\n    unstable_JsPreload: pageConfig.unstable_JsPreload,\n    devOnlyCacheBusterQueryString,\n    scriptLoader,\n    locale,\n    disableOptimizedLoading,\n    head: documentResult.head,\n    headTags: documentResult?.headTags,\n    styles: documentResult.styles,\n  }\n  const documentHTML = ReactDOMServer.renderToStaticMarkup(\n    <AmpStateContext.Provider value={ampState}>\n      <HtmlContext.Provider value={htmlProps}>\n        {documentResult.documentElement(htmlProps)}\n      </HtmlContext.Provider>\n    </AmpStateContext.Provider>\n  )\n\n  if (process.env.NODE_ENV !== 'production') {\n    const nonRenderedComponents = []\n    const expectedDocComponents = ['Main', 'Head', 'NextScript', 'Html']\n\n    for (const comp of expectedDocComponents) {\n      if (!(docComponentsRendered as any)[comp]) {\n        nonRenderedComponents.push(comp)\n      }\n    }\n    const plural = nonRenderedComponents.length !== 1 ? 's' : ''\n\n    if (nonRenderedComponents.length) {\n      const missingComponentList = nonRenderedComponents\n        .map((e) => `<${e} />`)\n        .join(', ')\n      warn(\n        `Your custom Document (pages/_document) did not render all the required subcomponent${plural}.\\n` +\n          `Missing component${plural}: ${missingComponentList}\\n` +\n          'Read how to fix here: https://nextjs.org/docs/messages/missing-document-component'\n      )\n    }\n  }\n\n  let results: Array<RenderResult> = []\n  const renderTargetIdx = documentHTML.indexOf(BODY_RENDER_TARGET)\n  results.push(\n    Observable.of(\n      '<!DOCTYPE html>' + documentHTML.substring(0, renderTargetIdx)\n    )\n  )\n  if (inAmpMode) {\n    results.push(Observable.of('<!-- __NEXT_DATA__ -->'))\n  }\n  results.push(documentResult.bodyResult)\n  results.push(\n    Observable.of(\n      documentHTML.substring(renderTargetIdx + BODY_RENDER_TARGET.length)\n    )\n  )\n\n  const postProcessors: Array<((html: string) => Promise<string>) | null> = (\n    generateStaticHTML\n      ? [\n          inAmpMode\n            ? async (html: string) => {\n                html = await optimizeAmp(html, renderOpts.ampOptimizerConfig)\n                if (!renderOpts.ampSkipValidation && renderOpts.ampValidator) {\n                  await renderOpts.ampValidator(html, pathname)\n                }\n                return html\n              }\n            : null,\n          process.env.__NEXT_OPTIMIZE_FONTS ||\n          process.env.__NEXT_OPTIMIZE_IMAGES\n            ? async (html: string) => {\n                return await postProcess(\n                  html,\n                  { getFontDefinition },\n                  {\n                    optimizeFonts: renderOpts.optimizeFonts,\n                    optimizeImages: renderOpts.optimizeImages,\n                  }\n                )\n              }\n            : null,\n          renderOpts.optimizeCss\n            ? async (html: string) => {\n                // eslint-disable-next-line import/no-extraneous-dependencies\n                const Critters = require('critters')\n                const cssOptimizer = new Critters({\n                  ssrMode: true,\n                  reduceInlineStyles: false,\n                  path: renderOpts.distDir,\n                  publicPath: `${renderOpts.assetPrefix}/_next/`,\n                  preload: 'media',\n                  fonts: false,\n                  ...renderOpts.optimizeCss,\n                })\n                return await cssOptimizer.process(html)\n              }\n            : null,\n          inAmpMode || hybridAmp\n            ? async (html: string) => {\n                return html.replace(/&amp;amp=1/g, '&amp=1')\n              }\n            : null,\n        ]\n      : []\n  ).filter(Boolean)\n\n  if (postProcessors.length > 0) {\n    let html = await resultsToString(results)\n    for (const postProcessor of postProcessors) {\n      if (postProcessor) {\n        html = await postProcessor(html)\n      }\n    }\n    results = [Observable.of(html)]\n  }\n\n  return mergeResults(results)\n}\n\nfunction multiplexResult(result: RenderResult): RenderResult {\n  const chunks: Array<string> = []\n  const subscribers: Set<ZenObservable.SubscriptionObserver<string>> = new Set()\n  let terminator:\n    | ((subscriber: ZenObservable.SubscriptionObserver<string>) => void)\n    | null = null\n\n  result.subscribe({\n    next(chunk) {\n      chunks.push(chunk)\n      subscribers.forEach((subscriber) => subscriber.next(chunk))\n    },\n    error(error) {\n      if (!terminator) {\n        terminator = (subscriber) => subscriber.error(error)\n        subscribers.forEach(terminator)\n        subscribers.clear()\n      }\n    },\n    complete() {\n      if (!terminator) {\n        terminator = (subscriber) => subscriber.complete()\n        subscribers.forEach(terminator)\n        subscribers.clear()\n      }\n    },\n  })\n\n  return new Observable((observer) => {\n    for (const chunk of chunks) {\n      if (observer.closed) {\n        return\n      }\n      observer.next(chunk)\n    }\n\n    if (terminator) {\n      terminator(observer)\n      return\n    }\n\n    subscribers.add(observer)\n    return () => {\n      subscribers.delete(observer)\n    }\n  })\n}\n\nfunction errorToJSON(err: Error): Error {\n  const { name, message, stack } = err\n  return { name, message, stack }\n}\n\nfunction serializeError(\n  dev: boolean | undefined,\n  err: Error\n): Error & { statusCode?: number } {\n  if (dev) {\n    return errorToJSON(err)\n  }\n\n  return {\n    name: 'Internal Server Error.',\n    message: '500 - Internal Server Error.',\n    statusCode: 500,\n  }\n}\n"], "names": [], "mappings": ";;;;QAoQsB,YAAY,GAAZ,YAAY;AAlQN,GAAQ,CAAR,OAAQ;AAClB,GAAO,CAAP,MAAO;AACb,GAAc,CAAd,cAAc;AACR,GAAmB,CAAnB,OAAmB;AACd,GAAmC,CAAnC,cAAmC;AACrC,GAAqB,CAArB,IAAqB;AAWnC,GAAkB,CAAlB,UAAkB;AACW,GAA8B,CAA9B,oBAA8B;AAEtC,GAAmB,CAAnB,IAAmB;AACf,GAA2B,CAA3B,WAA2B;AAMpD,GAAyB,CAAzB,WAAyB;AACJ,GAAoB,CAApB,KAAoB;AACb,GAAoC,CAApC,mBAAoC;AAClD,GAAwB,CAAxB,SAAwB;AACb,GAAgC,CAAhC,gBAAgC;AACxC,GAA4B,CAA5B,YAA4B;AACtB,GAA8B,CAA9B,cAA8B;AAE7B,GAAuC,CAAvC,UAAuC;AAc/D,GAAqB,CAArB,MAAqB;AAKrB,GAAa,CAAb,SAAa;AACgB,GAAyB,CAAzB,oBAAyB;AACD,GAAc,CAAd,UAAc;AAExC,GAAuB,CAAvB,kBAAuB;AACjC,GAAgB,CAAhB,YAAgB;AAKjC,GAA2B,CAA3B,iBAA2B;AAE0B,GAAS,CAAT,OAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;SAE5D,QAAQ,GAAG,CAAC;IACnB,KAAK,CAAC,OAAO,IACX,mJAAqJ;IACvJ,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,OAAO;AACzB,CAAC;MAEK,YAAY;gBAiBd,QAAgB,EAChB,KAAqB,EACrB,EAAU,IACR,UAAU,KACZ,OAAgB,EAChB,QAAgB,EAChB,MAAe,EACf,OAAkB,EAClB,aAAsB,EACtB,aAA8B,EAC9B,SAAmB,EACnB,cAAwB,CACxB,CAAC;aACI,KAAK,GAAG,QAAQ,CAAC,OAAO,gBAAe,CAAG;aAC1C,QAAQ,GAAG,QAAQ;aACnB,KAAK,GAAG,KAAK;aACb,MAAM,GAAG,EAAE;aACX,UAAU,GAAG,UAAU;aACvB,QAAQ,GAAG,QAAQ;aACnB,MAAM,GAAG,MAAM;aACf,OAAO,GAAG,OAAO;aACjB,aAAa,GAAG,aAAa;aAC7B,OAAO,GAAG,OAAO;aACjB,aAAa,GAAG,aAAa;aAC7B,SAAS,KAAK,SAAS;aACvB,cAAc,KAAK,cAAc;IACxC,CAAC;IAED,IAAI,GAAQ,CAAC;QACX,QAAQ;IACV,CAAC;IACD,OAAO,GAAQ,CAAC;QACd,QAAQ;IACV,CAAC;IACD,MAAM,GAAG,CAAC;QACR,QAAQ;IACV,CAAC;IACD,IAAI,GAAG,CAAC;QACN,QAAQ;IACV,CAAC;IACD,QAAQ,GAAQ,CAAC;QACf,QAAQ;IACV,CAAC;IACD,cAAc,GAAG,CAAC;QAChB,QAAQ;IACV,CAAC;;SAGM,iBAAiB,CACxB,OAA2B,EAC3B,GAAY,EACZ,SAA4B,EAI5B,CAAC;IACD,EAA8B,AAA9B,4BAA8B;IAC9B,EAAE,SAAS,OAAO,MAAK,QAAU,GAAE,CAAC;;YAEhC,GAAG;YACH,SAAS,EAAE,OAAO,CAAC,SAAS;;IAEhC,CAAC;;QAGC,GAAG,EAAE,OAAO,CAAC,UAAU,GAAG,OAAO,CAAC,UAAU,CAAC,GAAG,IAAI,GAAG;QACvD,SAAS,EAAE,OAAO,CAAC,gBAAgB,GAC/B,OAAO,CAAC,gBAAgB,CAAC,SAAS,IAClC,SAAS;;AAEjB,CAAC;AAyCD,KAAK,CAAC,cAAc,IAAI,UAAkB,EAAE,WAAqB,GAAK,CAAC;YAElE,qCAAqC,EAAE,UAAU,CAAC,wFAAwF,KAC1I,6DAA6D,KAC7D,gCAAgC,EAAE,WAAW,CAAC,IAAI,EAAC,EAAI,GAAE,CAAC,KAC1D,0EAA0E;AAE/E,CAAC;SAEQ,mBAAmB,CAC1B,QAAkB,EAClB,GAAoB,EACpB,MAA+C,EAC/C,CAAC;IACD,KAAK,GAAG,WAAW,GAAE,SAAS,GAAE,UAAU,GAAE,QAAQ,EAAR,SAAQ,MAAK,QAAQ;IACjE,GAAG,CAAC,MAAM;IAEV,KAAK,CAAC,aAAa,UAAU,UAAU,MAAK,SAAW;IACvD,KAAK,CAAC,YAAY,UAAU,SAAS,MAAK,SAAW;IAErD,EAAE,EAAE,YAAY,IAAI,aAAa,EAAE,CAAC;QAClC,MAAM,CAAC,IAAI,EAAE,yDAAyD;IACxE,CAAC,MAAM,EAAE,EAAE,YAAY,WAAW,SAAS,MAAK,OAAS,GAAE,CAAC;QAC1D,MAAM,CAAC,IAAI,EAAE,2CAA2C;IAC1D,CAAC,MAAM,EAAE,EAAE,aAAa,KAlKnB,iBAA2B,oBAkKgB,GAAG,CAAC,UAAU,GAAI,CAAC;QACjE,MAAM,CAAC,IAAI,EACR,wCAAwC;eApKxC,iBAA2B;UAoKuC,IAAI,EACrE,EAAI;IAGV,CAAC;IACD,KAAK,CAAC,eAAe,UAAU,WAAW;IAE1C,EAAE,EAAE,eAAe,MAAK,MAAQ,GAAE,CAAC;QACjC,MAAM,CAAC,IAAI,EACR,8CAA8C,EAAE,eAAe;IAEpE,CAAC;IAED,KAAK,CAAC,YAAY,UAAU,SAAQ;IAEpC,EAAE,EAAE,YAAY,MAAK,SAAW,KAAI,YAAY,MAAK,OAAS,GAAE,CAAC;QAC/D,MAAM,CAAC,IAAI,EACR,sDAAsD,EAAE,YAAY;IAEzE,CAAC;IAED,EAAE,EAAE,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;QACtB,KAAK,CAAC,GAAG,CAAC,KAAK,EACZ,sCAAsC,EAAE,MAAM,CAAC,KAAK,EAAE,GAAG,CAAC,GAAG,CAAC,EAAE,IAC/D,MAAM,CAAC,IAAI,EAAC,KAAO,MACnB,EAAI,KACH,0EAA0E;IAEjF,CAAC;AACH,CAAC;eAEqB,YAAY,CAChC,GAAoB,EACpB,GAAmB,EACnB,SAAgB,EAChB,MAAqB,EACrB,UAAsB,EACQ,CAAC;IAC/B,EAA+E,AAA/E,6EAA+E;IAC/E,EAA4E,AAA5E,0EAA4E;IAC5E,EAA6F,AAA7F,2FAA6F;IAC7F,UAAU,CAAC,6BAA6B,GAAG,UAAU,CAAC,GAAG,GACrD,UAAU,CAAC,6BAA6B,KAAK,IAAI,EAAE,IAAI,CAAC,GAAG;IAG/D,EAAqC,AAArC,mCAAqC;IACrC,MAAK,GAAG,MAAM,CAAC,MAAM;OAAK,MAAK;IAE/B,KAAK,GACH,GAAG,GACH,GAAG,EAAG,KAAK,GACX,OAAO,OACP,GAAG,GACH,QAAQ,EAAR,SAAQ,GACR,UAAU;QACV,SAAS,GACT,aAAa,GACb,YAAY,GACZ,qBAAqB,GACrB,UAAU,GACV,cAAc,GACd,cAAc,GACd,kBAAkB,GAClB,SAAS,GACT,MAAM,GACN,YAAY,GACZ,QAAQ,EAAR,SAAQ,GACR,6BAA6B,GAC7B,iBAAiB,GACjB,kBAAkB,QAChB,UAAU;IAEd,KAAK,CAAC,iBAAiB,IAAI,GAAW,GAAa,CAAC;QAClD,EAAE,EAAE,YAAY,EAAE,CAAC;uBArPqC,UAAc,gCAsP/B,GAAG,EAAE,YAAY;QACxD,CAAC;;IAEH,CAAC;IAED,KAAK,CAAC,cAAc,UAAU,MAAc,EAAE,IAAW,EAAE,KAAK,GAAG,KAAK,GAAK,CAAC;QAC5E,GAAG,CAAC,OAAO,GAAQ,KAAK;;QAExB,EAAE,EAAG,SAAQ,IAAY,MAAM,CAAC,UAAU,IAAI,CAAC;YAC7C,GAAG,CAAC,cAAc,SAAU,SAAQ,IAAY,MAAM,CAAC,UAAU;YACjE,cAAc,GAAG,cAAc,CAAC,OAAO,IAAI,cAAc;YAEzD,KAAK,CAAC,UAAU,SAAS,cAAc,IAAI,IAAI;YAC/C,EAAE,EAAE,KAAK,EAAE,CAAC;qBACL,KAAK,CAAC,MAAM,IAAI,UAAU,CAAE,CAAC;oBAChC,OAAO;2BACF,OAAO;2BACP,MAAM;;gBAEb,CAAC;YACH,CAAC,MAAM,CAAC;gBACN,OAAO,GAAG,UAAU;YACtB,CAAC;QACH,CAAC;eACM,OAAO;IAChB,CAAC;IAED,KAAK,CAAC,QAAQ,OAAO,IAAI,GAAU,cAAc,EAAC,QAAU,GAAE,IAAI;;IAElE,KAAK,CAAC,WAAU,KAAK,MAAK,CAAC,cAAc;WAClC,MAAK,CAAC,cAAc;WACpB,MAAK,CAAC,YAAY;WAClB,MAAK,CAAC,mBAAmB;IAEhC,KAAK,CAAC,KAAK,KAAK,cAAc;IAC9B,KAAK,CAAC,cAAc,GAAG,KAAK,IAAI,UAAU,CAAC,UAAU;IACrD,KAAK,CAAC,yBAAyB,GAC7B,GAAG,CAAC,eAAe,KAAM,GAAG,CAAS,mBAAmB;IAE1D,KAAK,CAAC,sBAAsB,KAAM,SAAS,CAAS,eAAe;IAEnE,KAAK,CAAC,aAAa,OApTU,UAAuC,iBAoT/B,SAAQ;IAE7C,KAAK,CAAC,YAAY,IACf,sBAAsB,IACvB,yBAAyB,KACxB,KAAK,KACL,kBAAkB;SAEhB,KAAK,CAAC,UAAU;SACnB,cAAgB;SAChB,kBAAoB;SACpB,cAAgB;MACf,CAAC;QACF,EAAE,EAAG,SAAS,CAAS,UAAU,GAAG,CAAC;YACnC,KAAK,CAAC,GAAG,CAAC,KAAK,EACZ,KAAK,EAAE,SAAQ,CAAC,CAAC,EAAE,UAAU,CAAC,CAAC,EArVjC,UAAkB;QAuVrB,CAAC;IACH,CAAC;IAED,EAAE,EAAE,sBAAsB,IAAI,KAAK,EAAE,CAAC;QACpC,KAAK,CAAC,GAAG,CAAC,KAAK,CA3VZ,UAAkB,mCA2V6B,CAAC,EAAE,SAAQ;IAC/D,CAAC;IAED,EAAE,EAAE,sBAAsB,IAAI,kBAAkB,EAAE,CAAC;QACjD,KAAK,CAAC,GAAG,CAAC,KAAK,CA/VZ,UAAkB,yCA+VmC,CAAC,EAAE,SAAQ;IACrE,CAAC;IAED,EAAE,EAAE,kBAAkB,IAAI,KAAK,EAAE,CAAC;QAChC,KAAK,CAAC,GAAG,CAAC,KAAK,CAnWZ,UAAkB,8BAmWwB,CAAC,EAAE,SAAQ;IAC1D,CAAC;IAED,EAAE,EAAE,cAAc,KAAK,aAAa,EAAE,CAAC;QACrC,KAAK,CAAC,GAAG,CAAC,KAAK,EACZ,uEAAuE,EAAE,SAAQ,CAAC,EAAE,KAClF,8EAA8E;IAErF,CAAC;IAED,EAAE,IAAI,cAAc,KAAK,KAAK,EAAE,CAAC;QAC/B,KAAK,CAAC,GAAG,CAAC,KAAK,EACZ,qDAAqD,EAAE,SAAQ,CAAC,qDAAqD;IAE1H,CAAC;IAED,EAAE,EAAE,KAAK,IAAI,aAAa,KAAK,cAAc,EAAE,CAAC;QAC9C,KAAK,CAAC,GAAG,CAAC,KAAK,EACZ,qEAAqE,EAAE,SAAQ,CAAC,EAAE,KAChF,0EAA0E;IAEjF,CAAC;IAED,GAAG,CAAC,MAAM,GAAW,UAAU,CAAC,cAAc,IAAK,GAAG,CAAC,GAAG;IAE1D,EAAE,EAAE,GAAG,EAAE,CAAC;QACR,KAAK,GAAG,kBAAkB,MAAK,OAAO,EAAC,QAAU;QACjD,EAAE,GAAG,kBAAkB,CAAC,SAAS,GAAG,CAAC;YACnC,KAAK,CAAC,GAAG,CAAC,KAAK,EACZ,sDAAsD,EAAE,SAAQ,CAAC,CAAC;QAEvE,CAAC;QAED,EAAE,GAAG,kBAAkB,CAAC,GAAG,GAAG,CAAC;YAC7B,KAAK,CAAC,GAAG,CAAC,KAAK,EACZ,4DAA4D;QAEjE,CAAC;QAED,EAAE,GAAG,kBAAkB,CAAC,SAAQ,GAAG,CAAC;YAClC,KAAK,CAAC,GAAG,CAAC,KAAK,EACZ,iEAAiE;QAEtE,CAAC;QAED,EAAE,EAAE,YAAY,IAAI,WAAU,EAAE,CAAC;YAC/B,EAAiE,AAAjE,+DAAiE;YACjE,MAAK;mBACC,MAAK,CAAC,GAAG;oBAEP,GAAG,EAAE,MAAK,CAAC,GAAG;;;;YAItB,MAAM,MAAM,SAAQ,GAClB,EAAqE,AAArE,mEAAqE;YACrE,GAAG,CAAC,GAAG,CAAE,QAAQ,EAAC,CAAG,MAAK,SAAQ,MAAK,CAAG,MAAK,aAAa,IAAG,CAAG;YAEpE,GAAG,CAAC,GAAG,GAAG,SAAQ;QACpB,CAAC;QAED,EAAE,EAAE,SAAQ,MAAK,IAAM,MAAK,sBAAsB,IAAI,kBAAkB,GAAG,CAAC;YAC1E,KAAK,CAAC,GAAG,CAAC,KAAK,EACZ,cAAc,EAlahB,UAAkB;QAoarB,CAAC;QACD,EAAE,EA3ZC,WAAyB,qBA4ZN,QAAQ,CAAC,SAAQ,MACpC,sBAAsB,IAAI,kBAAkB,GAC7C,CAAC;YACD,KAAK,CAAC,GAAG,CAAC,KAAK,EACZ,OAAO,EAAE,SAAQ,CAAC,GAAG,EA1avB,UAAkB;QA4arB,CAAC;IACH,CAAC;UAhakB,SAAwB,SAka5B,UAAU,EAAG,CAA2C,AAA3C,EAA2C,AAA3C,yCAA2C;;IAEvE,GAAG,CAAC,UAAS;IACb,GAAG,CAAC,WAAW;IAEf,EAAE,GAAG,KAAK,IAAI,kBAAkB,MAAM,WAAU,EAAE,CAAC;QACjD,EAAuE,AAAvE,qEAAuE;QACvE,EAAoE,AAApE,kEAAoE;QACpE,EAA2D,AAA3D,yDAA2D;QAC3D,WAAW,OAnZR,SAAa,oBAmZgB,GAAG,EAAE,GAAG,EAAE,YAAY;QACtD,UAAS,GAAG,WAAW,KAAK,KAAK;IACnC,CAAC;IAED,EAAyB,AAAzB,uBAAyB;IACzB,KAAK,CAAC,aAAa,MACjB,kBAAkB,IAClB,sBAAsB,KACpB,yBAAyB,KAAK,KAAK;IAEvC,KAAK,CAAC,MAAM,GAAG,GAAG,CAAC,YAAY,CAC7B,SAAQ,EACR,MAAK,EACL,MAAM;QAEJ,UAAU,EAAE,WAAU;OAExB,aAAa,EACb,SAAQ,EACR,UAAU,CAAC,MAAM,EACjB,UAAU,CAAC,OAAO,EAClB,UAAU,CAAC,aAAa,EACxB,UAAU,CAAC,aAAa,EACxB,UAAS,EACR,GAAG,CAAS,oBAAoB;IAEnC,KAAK,CAAC,GAAG;QACP,GAAG;QACH,GAAG,EAAE,YAAY,GAAG,SAAS,GAAG,GAAG;QACnC,GAAG,EAAE,YAAY,GAAG,SAAS,GAAG,GAAG;QACnC,QAAQ,EAAR,SAAQ;QACR,KAAK,EAAL,MAAK;QACL,MAAM;QACN,MAAM,EAAE,UAAU,CAAC,MAAM;QACzB,OAAO,EAAE,UAAU,CAAC,OAAO;QAC3B,aAAa,EAAE,UAAU,CAAC,aAAa;QACvC,OAAO,GAAG,KAAU,GAAK,CAAC;iCA3eZ,MAAO,uBA6ehB,YAAY,sBA7eH,MAAO,uBA8ed,GAAG;eAAK,KAAK;gBAAE,SAAS,EAAE,SAAS;gBAAE,MAAM,EAAE,MAAM;;QAG1D,CAAC;;IAEH,GAAG,CAAC,KAAK;IAET,KAAK,CAAC,QAAQ;QACZ,QAAQ,EAAE,UAAU,CAAC,GAAG,KAAK,IAAI;QACjC,QAAQ,EAAE,OAAO,CAAC,MAAK,CAAC,GAAG;QAC3B,MAAM,EAAE,UAAU,CAAC,GAAG,MAAK,MAAQ;;IAGrC,KAAK,CAAC,SAAS,OAzeW,IAAmB,cAyef,QAAQ;IAEtC,KAAK,CAAC,oBAAoB;IAE1B,GAAG,CAAC,IAAI,OArekB,KAAoB,cAqeR,SAAS;IAE/C,GAAG,CAAC,YAAY;;IAChB,KAAK,CAAC,UAAU,IACb,KAAK,KAAK,UAAU,CAAC,UAAU,IAAK,GAAG,KAAK,YAAY,IAAI,WAAU;IAEzE,KAAK,CAAC,YAAY,MAAM,QAAQ,oBArgBhB,MAAO,uBA+BK,cAA8B,eAuezC,QAAQ;YAAC,KAAK,EAAE,MAAM;yBAtgBvB,MAAO,uBAmBO,WAA2B,iBAofpC,QAAQ;YAAC,KAAK,EAAE,QAAQ;yBAvgB7B,MAAO,uBA2BU,mBAAoC,oBA6e3C,QAAQ;YAC1B,KAAK;gBACH,UAAU,GAAG,KAAK,GAAK,CAAC;oBACtB,IAAI,GAAG,KAAK;gBACd,CAAC;gBACD,aAAa,GAAG,OAAO,GAAK,CAAC;oBAC3B,YAAY,GAAG,OAAO;gBACxB,CAAC;gBACD,OAAO;;gBACP,gBAAgB,EAAE,GAAG,CAAC,GAAG;;yBAjhBnB,MAAO,uBA6BO,gBAAgC,iBAufrC,QAAQ;YACvB,KAAK,GAAG,UAAU,GAAK,oBAAoB,CAAC,IAAI,CAAC,UAAU;WAE1D,QAAQ;;QAOf,CAAC;QACH,KAAK,aAhfF,MAAqB,sBAgfU,GAAG;YACnC,OAAO,EAAE,GAAG,CAAC,OAAO;YACpB,SAAS;YACT,MAAM;YACN,GAAG;;QAGL,EAAE,GAAG,KAAK,IAAI,kBAAkB,KAAK,UAAS,EAAE,CAAC;YAC/C,KAAK,CAAC,WAAW,GAAG,IAAI;QAC1B,CAAC;QAED,EAAE,EAAE,KAAK,EAAE,CAAC;YACV,KAAK,CAlhBJ,WAAyB,oBAkhBD,IAAI;QAC/B,CAAC;QAED,EAAE,EAAE,KAAK,KAAK,WAAU,EAAE,CAAC;YACzB,GAAG,CAAC,IAAI;gBAEJ,CAAC;gBACH,IAAI,SAAS,cAAc;uBACrB,aAAa;wBAAK,MAAM,EAAE,MAAK;wBAAuB,SAAS;uBAC/D,UAAS;wBACP,OAAO,EAAE,IAAI;wBAAE,WAAW,EAAE,WAAW;wBACzC,SAAS;oBACb,OAAO,EAAE,UAAU,CAAC,OAAO;oBAC3B,MAAM,EAAE,UAAU,CAAC,MAAM;oBACzB,aAAa,EAAE,UAAU,CAAC,aAAa;;YAE3C,CAAC,QAAQ,gBAAgB,EAAE,CAAC;gBAC1B,EAA2D,AAA3D,yDAA2D;gBAC3D,EAAgB,AAAhB,cAAgB;gBAChB,EAAE,EAAE,gBAAgB,CAAC,IAAI,MAAK,MAAQ,GAAE,CAAC;2BAChC,gBAAgB,CAAC,IAAI;gBAC9B,CAAC;gBACD,KAAK,CAAC,gBAAgB;YACxB,CAAC;YAED,EAAE,EAAE,IAAI,IAAI,IAAI,EAAE,CAAC;gBACjB,KAAK,CAAC,GAAG,CAAC,KAAK,CAtjBhB,UAAkB;YAujBnB,CAAC;YAED,KAAK,CAAC,WAAW,GAAG,MAAM,CAAC,IAAI,CAAC,IAAI,EAAE,MAAM,EACzC,GAAG,GACF,GAAG,MAAK,UAAY,KACpB,GAAG,MAAK,KAAO,KACf,GAAG,MAAK,QAAU,KAClB,GAAG,MAAK,QAAU;;YAGtB,EAAE,EAAE,WAAW,CAAC,QAAQ,EAAC,mBAAqB,IAAG,CAAC;gBAChD,KAAK,CAAC,GAAG,CAAC,KAAK,CAlkBhB,UAAkB;YAmkBnB,CAAC;YAED,EAAE,EAAE,WAAW,CAAC,MAAM,EAAE,CAAC;gBACvB,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,cAAc,EAAC,cAAgB,GAAE,WAAW;YAC9D,CAAC;YAED,EAAE,EAAE,OAAO,CAAC,GAAG,CAAC,QAAQ,MAAK,UAAY,GAAE,CAAC;gBAC1C,EAAE,SACQ,IAAI,CAAS,QAAQ,MAAK,SAAW,YACrC,IAAI,CAAS,QAAQ,MAAK,SAAW,GAC7C,CAAC;oBACD,KAAK,CAAC,GAAG,CAAC,KAAK,EACZ,4DAA4D,EAC3D,KAAK,IAAG,cAAgB,KAAG,kBAAoB,EAChD,yBAAyB,EAAE,SAAQ,CAAC,oFAAoF;gBAE7H,CAAC;YACH,CAAC;YAED,EAAE,GAAE,QAAU,KAAI,IAAI,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;gBACxC,EAAE,EAAE,SAAQ,MAAK,IAAM,GAAE,CAAC;oBACxB,KAAK,CAAC,GAAG,CAAC,KAAK,EACZ,wFAAwF;gBAE7F,CAAC;gBAEC,UAAU,CAAS,UAAU,GAAG,IAAI;YACxC,CAAC;YAED,EAAE,GACA,QAAU,KAAI,IAAI,IAClB,IAAI,CAAC,QAAQ,WACN,IAAI,CAAC,QAAQ,MAAK,MAAQ,GACjC,CAAC;gBACD,mBAAmB,CAAC,IAAI,CAAC,QAAQ,EAAc,GAAG,GAAE,cAAgB;gBAEpE,EAAE,EAAE,cAAc,EAAE,CAAC;oBACnB,KAAK,CAAC,GAAG,CAAC,KAAK,EACZ,0EAA0E,EAAE,GAAG,CAAC,GAAG,CAAC,GAAG,KACrF,kFAAkF;gBAEzF,CAAC;gBAEC,IAAI,CAAS,KAAK;oBAClB,YAAY,EAAE,IAAI,CAAC,QAAQ,CAAC,WAAW;oBACvC,mBAAmB,MAjkBtB,iBAA2B,oBAikBe,IAAI,CAAC,QAAQ;;gBAEtD,EAAE,SAAS,IAAI,CAAC,QAAQ,CAAC,QAAQ,MAAK,SAAW,GAAE,CAAC;oBAChD,IAAI,CAAS,KAAK,CAAC,sBAAsB,GAAG,IAAI,CAAC,QAAQ,CAAC,QAAQ;gBACtE,CAAC;gBACC,UAAU,CAAS,UAAU,GAAG,IAAI;YACxC,CAAC;YAED,EAAE,GACC,GAAG,IAAI,cAAc,MACpB,UAAU,CAAS,UAAU,SAznBH,oBAA8B,sBA0nBrC,SAAQ,GAAE,cAAgB,GAAG,IAAI,CAAS,KAAK,GACpE,CAAC;gBACD,EAAkE,AAAlE,gEAAkE;gBAClE,KAAK,CAAC,GAAG,CAAC,KAAK,EACb,yEAA2E;YAE/E,CAAC;YAED,EAAE,GAAE,UAAY,KAAI,IAAI,EAAE,CAAC;gBACzB,EAAE,SAAS,IAAI,CAAC,UAAU,MAAK,MAAQ,GAAE,CAAC;oBACxC,EAAE,GAAG,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,UAAU,GAAG,CAAC;wBACvC,KAAK,CAAC,GAAG,CAAC,KAAK,EACZ,6EAA6E,EAAE,GAAG,CAAC,GAAG,CAAC,0BAA0B,EAAE,IAAI,CAAC,UAAU,CAAC,kBAAkB,KACnJ,6BAA6B,EAAE,IAAI,CAAC,IAAI,CACvC,IAAI,CAAC,UAAU,EACf,yDAAyD;oBAEjE,CAAC,MAAM,EAAE,EAAE,IAAI,CAAC,UAAU,IAAI,CAAC,EAAE,CAAC;wBAChC,KAAK,CAAC,GAAG,CAAC,KAAK,EACZ,qEAAqE,EAAE,GAAG,CAAC,GAAG,CAAC,oHAAoH,KACjM,2FAA2F,KAC3F,oEAAoE;oBAE3E,CAAC,MAAM,EAAE,EAAE,IAAI,CAAC,UAAU,GAAG,QAAQ,EAAE,CAAC;wBACtC,EAAoD,AAApD,kDAAoD;wBACpD,OAAO,CAAC,IAAI,EACT,oEAAoE,EAAE,GAAG,CAAC,GAAG,CAAC,mCAAmC,KAC/G,kHAAkH;oBAEzH,CAAC;gBACH,CAAC,MAAM,EAAE,EAAE,IAAI,CAAC,UAAU,KAAK,IAAI,EAAE,CAAC;oBACpC,EAAqE,AAArE,mEAAqE;oBACrE,EAA0D,AAA1D,wDAA0D;oBAC1D,EAAyB,AAAzB,uBAAyB;oBACzB,IAAI,CAAC,UAAU,GAAG,CAAC;gBACrB,CAAC,MAAM,EAAE,EACP,IAAI,CAAC,UAAU,KAAK,KAAK,WAClB,IAAI,CAAC,UAAU,MAAK,SAAW,GACtC,CAAC;oBACD,EAAmC,AAAnC,iCAAmC;oBACnC,IAAI,CAAC,UAAU,GAAG,KAAK;gBACzB,CAAC,MAAM,CAAC;oBACN,KAAK,CAAC,GAAG,CAAC,KAAK,EACZ,8HAA8H,EAAE,IAAI,CAAC,SAAS,CAC7I,IAAI,CAAC,UAAU,EACf,MAAM,EAAE,GAAG,CAAC,GAAG;gBAErB,CAAC;YACH,CAAC,MAAM,CAAC;gBAEJ,IAAI,CAAS,UAAU,GAAG,KAAK;YACnC,CAAC;YAED,KAAK,CAAC,SAAS,GAAG,MAAM,CAAC,MAAM;eAE7B,KAAK,CAAC,SAAS,GACf,KAAO,KAAI,IAAI,GAAG,IAAI,CAAC,KAAK,GAAG,SAAS;YAKxC,UAAU,CAAS,UAAU,IAC7B,UAAY,KAAI,IAAI,GAAG,IAAI,CAAC,UAAU,GAAG,SAAS;YAClD,UAAU,CAAS,QAAQ,GAAG,KAAK;YAErC,EAAyD,AAAzD,uDAAyD;YACzD,EAAE,EAAG,UAAU,CAAS,UAAU,EAAE,CAAC;uBAC5B,IAAI;YACb,CAAC;QACH,CAAC;QAED,EAAE,EAAE,kBAAkB,EAAE,CAAC;YACvB,KAAK,CAzrBJ,WAAyB,oBAyrBD,IAAI;QAC/B,CAAC;QAED,EAAE,EAAE,kBAAkB,KAAK,WAAU,EAAE,CAAC;YACtC,GAAG,CAAC,IAAI;gBAEJ,CAAC;gBACH,IAAI,SAAS,kBAAkB;oBAC7B,GAAG,EAAE,GAAG;oBAGR,GAAG;oBACH,KAAK,EAAL,MAAK;oBACL,WAAW,EAAE,UAAU,CAAC,WAAW;uBAC/B,aAAa;wBAAK,MAAM,EAAE,MAAM;wBAAuB,SAAS;uBAChE,WAAW,KAAK,KAAK;wBACnB,OAAO,EAAE,IAAI;wBAAE,WAAW,EAAE,WAAW;wBACzC,SAAS;oBACb,OAAO,EAAE,UAAU,CAAC,OAAO;oBAC3B,MAAM,EAAE,UAAU,CAAC,MAAM;oBACzB,aAAa,EAAE,UAAU,CAAC,aAAa;;YAE3C,CAAC,QAAQ,oBAAoB,EAAE,CAAC;gBAC9B,EAA2D,AAA3D,yDAA2D;gBAC3D,EAAgB,AAAhB,cAAgB;gBAChB,EAAE,EAAE,oBAAoB,CAAC,IAAI,MAAK,MAAQ,GAAE,CAAC;2BACpC,oBAAoB,CAAC,IAAI;gBAClC,CAAC;gBACD,KAAK,CAAC,oBAAoB;YAC5B,CAAC;YAED,EAAE,EAAE,IAAI,IAAI,IAAI,EAAE,CAAC;gBACjB,KAAK,CAAC,GAAG,CAAC,KAAK,CAnuBhB,UAAkB;YAouBnB,CAAC;YAED,KAAK,CAAC,WAAW,GAAG,MAAM,CAAC,IAAI,CAAC,IAAI,EAAE,MAAM,EACzC,GAAG,GAAK,GAAG,MAAK,KAAO,KAAI,GAAG,MAAK,QAAU,KAAI,GAAG,MAAK,QAAU;;YAGtE,EAAE,EAAG,IAAI,CAAS,iBAAiB,EAAE,CAAC;gBACpC,KAAK,CAAC,GAAG,CAAC,KAAK,EACZ,2FAA2F,EAAE,SAAQ;YAE1G,CAAC;YACD,EAAE,EAAG,IAAI,CAAS,iBAAiB,EAAE,CAAC;gBACpC,KAAK,CAAC,GAAG,CAAC,KAAK,EACZ,2FAA2F,EAAE,SAAQ;YAE1G,CAAC;YAED,EAAE,EAAE,WAAW,CAAC,MAAM,EAAE,CAAC;gBACvB,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,cAAc,EAAC,kBAAoB,GAAE,WAAW;YAClE,CAAC;YAED,EAAE,GAAE,QAAU,KAAI,IAAI,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;gBACxC,EAAE,EAAE,SAAQ,MAAK,IAAM,GAAE,CAAC;oBACxB,KAAK,CAAC,GAAG,CAAC,KAAK,EACZ,wFAAwF;gBAE7F,CAAC;gBAEC,UAAU,CAAS,UAAU,GAAG,IAAI;uBAC/B,IAAI;YACb,CAAC;YAED,EAAE,GAAE,QAAU,KAAI,IAAI,WAAW,IAAI,CAAC,QAAQ,MAAK,MAAQ,GAAE,CAAC;gBAC5D,mBAAmB,CACjB,IAAI,CAAC,QAAQ,EACb,GAAG,GACH,kBAAoB;gBAEpB,IAAI,CAAS,KAAK;oBAClB,YAAY,EAAE,IAAI,CAAC,QAAQ,CAAC,WAAW;oBACvC,mBAAmB,MA7tBtB,iBAA2B,oBA6tBe,IAAI,CAAC,QAAQ;;gBAEtD,EAAE,SAAS,IAAI,CAAC,QAAQ,CAAC,QAAQ,MAAK,SAAW,GAAE,CAAC;oBAChD,IAAI,CAAS,KAAK,CAAC,sBAAsB,GAAG,IAAI,CAAC,QAAQ,CAAC,QAAQ;gBACtE,CAAC;gBACC,UAAU,CAAS,UAAU,GAAG,IAAI;YACxC,CAAC;YAED,EAAE,EAAG,IAAI,CAAS,KAAK,YAAY,OAAO,EAAE,CAAC;gBACzC,IAAI,CAAS,KAAK,SAAU,IAAI,CAAS,KAAK;YAClD,CAAC;YAED,EAAE,GACC,GAAG,IAAI,cAAc,UAxxBM,oBAA8B,sBA0xBxD,SAAQ,GACR,kBAAoB,GACnB,IAAI,CAAS,KAAK,GAErB,CAAC;gBACD,EAAkE,AAAlE,gEAAkE;gBAClE,KAAK,CAAC,GAAG,CAAC,KAAK,EACb,6EAA+E;YAEnF,CAAC;YAED,KAAK,CAAC,SAAS,GAAG,MAAM,CAAC,MAAM;eAAK,KAAK,CAAC,SAAS,EAAG,IAAI,CAAS,KAAK;YACtE,UAAU,CAAS,QAAQ,GAAG,KAAK;QACvC,CAAC;IACH,CAAC,QAAQ,cAAc,EAAE,CAAC;QACxB,KAAK,CAAC,cAAc;IACtB,CAAC;IAED,EAAE,GACC,KAAK,KACL,kBAAkB,IACnB,OAAO,CAAC,GAAG,CAAC,QAAQ,MAAK,UAAY,KACrC,MAAM,CAAC,IAAI,EAAC,KAAK,aAAL,KAAK,UAAL,CAAgB,QAAhB,CAAgB,GAAhB,KAAK,CAAE,SAAS;OAAQ,QAAQ,EAAC,GAAK,IAClD,CAAC;QACD,OAAO,CAAC,IAAI,EACT,iGAAiG,EAAE,SAAQ,CAAC,EAAE,KAC5G,uEAAuE;IAE9E,CAAC;IAED,EAA0E,AAA1E,wEAA0E;IAC1E,EAAkD,AAAlD,gDAAkD;IAClD,EAAE,EAAG,SAAS,KAAK,KAAK,IAAM,UAAU,CAAS,UAAU,EAAE,CAAC;eAv0BzC,cAAmC,SAw0BpC,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,KAAK;IAC3C,CAAC;IAED,EAAsE,AAAtE,oEAAsE;IACtE,EAAgE,AAAhE,8DAAgE;IAChE,EAAE,EAAE,WAAU,EAAE,CAAC;QACf,KAAK,CAAC,SAAS;;IACjB,CAAC;IAED,EAA6D,AAA7D,2DAA6D;IAC7D,EAAE,MAtyBG,MAAqB,YAsyBZ,GAAG,MAAM,KAAK,SAAS,IAAI;IAEzC,EAA6D,AAA7D,2DAA6D;IAC7D,EAAqC,AAArC,mCAAqC;IACrC,GAAG,CAAC,qBAAqB,GAAG,aAAa;IACzC,EAAE,EAAE,YAAY,IAAI,aAAa,EAAE,CAAC;QAClC,KAAK,CAAC,IAAI,OAtyBsB,oBAAyB,0BAG3B,kBAAuB,oBAmyBF,SAAQ;QAC3D,EAA0E,AAA1E,wEAA0E;QAC1E,EAAsE,AAAtE,oEAAsE;QACtE,EAAU,AAAV,QAAU;QACV,EAAE,EAAE,IAAI,IAAI,qBAAqB,CAAC,KAAK,EAAE,CAAC;YACxC,qBAAqB;mBAChB,qBAAqB;gBACxB,KAAK;uBACA,qBAAqB,CAAC,KAAK;qBAC7B,IAAI;2BACA,qBAAqB,CAAC,KAAK,CAAC,IAAI;2BAChC,qBAAqB,CAAC,gBAAgB,CAAC,MAAM,EAAE,CAAC,GACjD,CAAC,CAAC,QAAQ,EAAC,cAAgB;;;;gBAIjC,gBAAgB,EAAE,qBAAqB,CAAC,gBAAgB,CAAC,MAAM,EAC5D,CAAC,IAAM,CAAC,CAAC,QAAQ,EAAC,cAAgB;;;QAGzC,CAAC;IACH,CAAC;IAED,KAAK,CAAC,kBAAkB,GAAG,iBAAiB,IAAI,SAAS;IACzD,KAAK,CAAC,cAAc,IAAI,OAA2B,GACjD,GAAG,CAAC,OAAO,EAAgB,OAAO,EAAE,MAAM,GAAK,CAAC;YAC9C,KAAK,CAAC,MAAM,GAAG,GAAG,CAt3BI,OAAQ;YAu3B9B,GAAG,CAAC,QAAQ,GAAG,KAAK;YACpB,KAAK,CAAC,SAAS,OAAS,CAAC;gBACvB,EAAE,GAAG,QAAQ,EAAE,CAAC;oBACd,QAAQ,GAAG,IAAI;oBAEf,OAAO,CACL,GAAG,CAz3BQ,cAAmC,UAy3B9B,QAAQ,GAAK,CAAC;wBAC5B,MAAM,CAAC,EAAE,EAAC,IAAM,IAAG,KAAK,GAAK,CAAC;4BAC5B,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAC,KAAO;wBACtC,CAAC;wBACD,MAAM,CAAC,IAAI,EAAC,GAAK,OAAQ,CAAC;4BACxB,QAAQ,CAAC,QAAQ;wBACnB,CAAC;wBAED,YAAY;mCACC,CAAC;4BACZ,KAAK;wBACP,CAAC;oBACH,CAAC;gBAEL,CAAC;YACH,CAAC;YAED,KAAK,GAAG,KAAK,GAAE,YAAY,MA54BrB,cAAc,CA84BlB,kBAAkB,CAAC,OAAO,EAAE,MAAM;gBAClC,OAAO,EAAC,KAAY,EAAE,CAAC;oBACrB,EAAE,GAAG,QAAQ,EAAE,CAAC;wBACd,QAAQ,GAAG,IAAI;wBACf,MAAM,CAAC,KAAK;oBACd,CAAC;oBACD,KAAK;gBACP,CAAC;gBACD,eAAe,IAAG,CAAC;oBACjB,EAAE,GAAG,kBAAkB,EAAE,CAAC;wBACxB,SAAS;oBACX,CAAC;gBACH,CAAC;gBACD,aAAa,IAAG,CAAC;oBACf,SAAS;gBACX,CAAC;;QAEL,CAAC,EAAE,IAAI,CAAC,eAAe;;IAEzB,KAAK,CAAC,cAAc,aAAe,CAAC;QAClC,EAAE,EAAE,SAAQ,CAAC,eAAe,EAAE,CAAC;YAC7B,KAAK,CAAC,UAAU,IACd,OAA2B;gBACsB,CAAC;gBAClD,EAAE,EAAE,GAAG,CAAC,GAAG,IAAI,UAAU,EAAE,CAAC;oBAC1B,KAAK,CAAC,IAAI,GAv6BR,cAAc,CAu6BY,cAAc,eAx6BlC,MAAO,uBAy6BZ,UAAU;wBAAC,KAAK,EAAE,GAAG,CAAC,GAAG;;;wBAEnB,IAAI;wBAAE,IAAI;;gBACrB,CAAC;gBAED,EAAE,EAAE,GAAG,KAAK,KAAK,CAAC,MAAM,IAAI,KAAK,CAAC,SAAS,GAAG,CAAC;oBAC7C,KAAK,CAAC,GAAG,CAAC,KAAK,EACZ,sIAAsI;gBAE3I,CAAC;gBAED,KAAK,GAAG,GAAG,EAAE,WAAW,GAAE,SAAS,EAAE,iBAAiB,MACpD,iBAAiB,CAAC,OAAO,EAAE,GAAG,EAAE,SAAS;gBAE3C,KAAK,CAAC,IAAI,GAt7BN,cAAc,CAs7BU,cAAc,eAv7BhC,MAAO,uBAw7Bd,YAAY,sBAx7BL,MAAO,uBAy7BZ,WAAW;oBACV,SAAS,EAAE,iBAAiB;oBAC5B,MAAM,EAAE,MAAM;mBACV,KAAK;;oBAIN,IAAI;oBAAE,IAAI;;YACrB,CAAC;YACD,KAAK,CAAC,WAAW;mBAAQ,GAAG;gBAAE,UAAU;;YACxC,KAAK,CAAC,QAAQ,aAp5Bb,MAAqB,sBAq5BpB,SAAQ,EACR,WAAW;YAEb,EAA6D,AAA7D,2DAA6D;YAC7D,EAAE,MAz5BD,MAAqB,YAy5BR,GAAG,MAAM,KAAK,SAAS,IAAI;YAEzC,EAAE,GAAG,QAAQ,WAAW,QAAQ,CAAC,IAAI,MAAK,MAAQ,GAAE,CAAC;gBACnD,KAAK,CAAC,OAAO,IAAI,CAAC,MA55BnB,MAAqB,iBA65BlB,SAAQ,EACR,+FAA+F;gBACjG,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,OAAO;YACzB,CAAC;;gBAGC,UAAU,EA/8BK,cAAmC,SA+8B3B,EAAE,CAAC,QAAQ,CAAC,IAAI;gBACvC,eAAe,GAAG,SAAoB,iBAn9B5B,MAAO,uBAo9Bd,SAAQ;uBAAK,SAAS,EAAM,QAAQ;;gBAEvC,IAAI,EAAE,QAAQ,CAAC,IAAI;gBACnB,QAAQ,QAAQ,QAAQ,CAAC,WAAW;gBACpC,MAAM,EAAE,QAAQ,CAAC,MAAM;;QAE3B,CAAC,MAAM,CAAC;YACN,KAAK,CAAC,OAAO,GACX,GAAG,CAAC,GAAG,IAAI,UAAU,iBA59BX,MAAO,uBA69Bd,UAAU;gBAAC,KAAK,EAAE,GAAG,CAAC,GAAG;+BA79BlB,MAAO,uBA+9Bd,YAAY,sBA/9BL,MAAO,uBAg+BZ,GAAG;eAAK,KAAK;gBAAE,SAAS,EAAE,SAAS;gBAAE,MAAM,EAAE,MAAM;;YAG1D,KAAK,CAAC,UAAU,GAAG,kBAAkB,SAC3B,cAAc,CAAC,OAAO,IAj+Bf,cAAmC,SAk+BrC,EAAE,CAp+BX,cAAc,CAo+Ba,cAAc,CAAC,OAAO;;gBAGrD,UAAU;gBACV,eAAe,MAAS,SAAQ;;gBAChC,IAAI;gBACJ,QAAQ;gBACR,EAA0C,AAA1C,wCAA0C;gBAC1C,MAAM;2BA3+BI,OAAmB;;;QA6+BjC,CAAC;IACH,CAAC;IAED,KAAK,CAAC,cAAc,SAAS,cAAc;IAC3C,EAAE,GAAG,cAAc,EAAE,CAAC;eACb,IAAI;IACb,CAAC;IAED,KAAK,CAAC,iBAAiB,GAAG,GAAG,CAAC,GAAG;IACjC,KAAK,CAAC,cAAc,GAAG,GAAG,CAAC,GAAG;SAEzB,KAAK,CAAC,GAAG,IAAI,oBAAoB,CAAE,CAAC;QACvC,KAAK,CAAC,YAAY,GAAiB,qBAAqB,CAAC,GAAG;QAE5D,EAAE,EAAE,YAAY,EAAE,CAAC;YACjB,iBAAiB,CAAC,GAAG,CAAC,YAAY,CAAC,EAAE;YACrC,YAAY,CAAC,KAAK,CAAC,OAAO,EAAE,IAAI,GAAK,CAAC;gBACpC,cAAc,CAAC,GAAG,CAAC,IAAI;YACzB,CAAC;QACH,CAAC;IACH,CAAC;IAED,KAAK,CAAC,SAAS,GAAG,QAAQ,CAAC,MAAM;IAEjC,KAAK,CAAC,qBAAqB;;IAC3B,KAAK,GACH,WAAW,GACX,OAAO,GACP,YAAY,GACZ,aAAa,EAAb,cAAa,GACb,uBAAuB,GACvB,aAAa,EAAb,cAAa,GACb,MAAM,EAAN,OAAM,GACN,OAAO,EAAP,QAAO,GACP,aAAa,QACX,UAAU;IACd,KAAK,CAAC,SAAS;QACb,aAAa;YACX,KAAK;YACL,IAAI,EAAE,SAAQ;YACd,KAAK,EAAL,MAAK;YACL,OAAO;YACP,WAAW,EAAE,WAAW,UAAU,SAAS,GAAG,WAAW;YACzD,aAAa;YACb,UAAU,EAAE,UAAU,KAAK,IAAI,GAAG,IAAI,GAAG,SAAS;YAClD,UAAU,EAAE,YAAY,KAAK,IAAI,GAAG,IAAI,GAAG,SAAS;YACpD,UAAU,EAAV,WAAU;YACV,UAAU,EACR,iBAAiB,CAAC,IAAI,KAAK,CAAC,GACxB,SAAS,GACT,KAAK,CAAC,IAAI,CAAC,iBAAiB;YAClC,GAAG,EAAE,UAAU,CAAC,GAAG,GAAG,cAAc,CAAC,GAAG,EAAE,UAAU,CAAC,GAAG,IAAI,SAAS;YACrE,GAAG,IAAI,cAAc,GAAG,IAAI,GAAG,SAAS;YACxC,IAAI,IAAI,kBAAkB,GAAG,IAAI,GAAG,SAAS;YAC7C,YAAY;YACZ,GAAG,EAAE,sBAAsB,GAAG,IAAI,GAAG,SAAS;YAC9C,MAAM,GAAG,yBAAyB,GAAG,IAAI,GAAG,SAAS;YACrD,MAAM,EAAN,OAAM;YACN,OAAO,EAAP,QAAO;YACP,aAAa,EAAb,cAAa;YACb,aAAa,EAAb,cAAa;YACb,SAAS,EAAE,UAAS,KAAK,IAAI,GAAG,IAAI,GAAG,SAAS;;QAElD,aAAa,EAAE,qBAAqB;QACpC,qBAAqB;QACrB,eAAe,EAAE,MAAM,CAAC,MAAM;QAC9B,aAAa,GACV,UAAU,CAAC,OAAO,IAAK,GAAG,CAAS,oBAAoB,MACjD,UAAU,CAAC,aAAa,OAAO,CAAC,EAAE,UAAU,CAAC,MAAM,KACtD,UAAU,CAAC,aAAa;QAC9B,OAAO;QACP,SAAS;QACT,aAAa,IAAI,GAAG;QACpB,SAAS;QACT,cAAc,EAAE,KAAK,CAAC,IAAI,CAAC,cAAc;QACzC,WAAW;QACX,EAA2G,AAA3G,yGAA2G;QAC3G,kBAAkB,EAChB,OAAO,CAAC,GAAG,CAAC,QAAQ,MAAK,UAAY,IACjC,UAAU,CAAC,kBAAkB,GAC7B,SAAS;QACf,kBAAkB,EAAE,UAAU,CAAC,kBAAkB;QACjD,6BAA6B;QAC7B,YAAY;QACZ,MAAM,EAAN,OAAM;QACN,uBAAuB;QACvB,IAAI,EAAE,cAAc,CAAC,IAAI;QACzB,QAAQ,EAAE,cAAc,aAAd,cAAc,UAAd,CAAwB,QAAxB,CAAwB,GAAxB,cAAc,CAAE,QAAQ;QAClC,MAAM,EAAE,cAAc,CAAC,MAAM;;IAE/B,KAAK,CAAC,YAAY,GAxkCR,cAAc,CAwkCY,oBAAoB,eAzkCxC,MAAO,uBAmBO,WAA2B,iBAujCtC,QAAQ;QAAC,KAAK,EAAE,QAAQ;qBA1kC3B,MAAO,uBA+ClB,MAAqB,aA4hCT,QAAQ;QAAC,KAAK,EAAE,SAAS;OACnC,cAAc,CAAC,eAAe,CAAC,SAAS;IAK/C,EAAE,EAAE,OAAO,CAAC,GAAG,CAAC,QAAQ,MAAK,UAAY,GAAE,CAAC;QAC1C,KAAK,CAAC,qBAAqB;QAC3B,KAAK,CAAC,qBAAqB;aAAI,IAAM;aAAE,IAAM;aAAE,UAAY;aAAE,IAAM;;aAE9D,KAAK,CAAC,IAAI,IAAI,qBAAqB,CAAE,CAAC;YACzC,EAAE,GAAI,qBAAqB,CAAS,IAAI,GAAG,CAAC;gBAC1C,qBAAqB,CAAC,IAAI,CAAC,IAAI;YACjC,CAAC;QACH,CAAC;QACD,KAAK,CAAC,MAAM,GAAG,qBAAqB,CAAC,MAAM,KAAK,CAAC,IAAG,CAAG;QAEvD,EAAE,EAAE,qBAAqB,CAAC,MAAM,EAAE,CAAC;YACjC,KAAK,CAAC,oBAAoB,GAAG,qBAAqB,CAC/C,GAAG,EAAE,CAAC,IAAM,CAAC,EAAE,CAAC,CAAC,GAAG;cACpB,IAAI,EAAC,EAAI;gBA3lCG,IAAqB,QA6lCjC,mFAAmF,EAAE,MAAM,CAAC,GAAG,KAC7F,iBAAiB,EAAE,MAAM,CAAC,EAAE,EAAE,oBAAoB,CAAC,EAAE,KACtD,iFAAmF;QAEzF,CAAC;IACH,CAAC;IAED,GAAG,CAAC,OAAO;IACX,KAAK,CAAC,eAAe,GAAG,YAAY,CAAC,OAAO,CAhlCvC,WAAyB;IAilC9B,OAAO,CAAC,IAAI,CAvmCS,cAAmC,SAwmC3C,EAAE,EACX,eAAiB,IAAG,YAAY,CAAC,SAAS,CAAC,CAAC,EAAE,eAAe;IAGjE,EAAE,EAAE,SAAS,EAAE,CAAC;QACd,OAAO,CAAC,IAAI,CA7mCO,cAAmC,SA6mC9B,EAAE,EAAC,sBAAwB;IACrD,CAAC;IACD,OAAO,CAAC,IAAI,CAAC,cAAc,CAAC,UAAU;IACtC,OAAO,CAAC,IAAI,CAhnCS,cAAmC,SAinC3C,EAAE,CACX,YAAY,CAAC,SAAS,CAAC,eAAe,GA5lCrC,WAAyB,oBA4lCkC,MAAM;IAItE,KAAK,CAAC,cAAc,IAClB,kBAAkB;QAEZ,SAAS,UACE,IAAY,GAAK,CAAC;YACvB,IAAI,aArkCI,YAAgB,UAqkCC,IAAI,EAAE,UAAU,CAAC,kBAAkB;YAC5D,EAAE,GAAG,UAAU,CAAC,iBAAiB,IAAI,UAAU,CAAC,YAAY,EAAE,CAAC;sBACvD,UAAU,CAAC,YAAY,CAAC,IAAI,EAAE,SAAQ;YAC9C,CAAC;mBACM,IAAI;QACb,CAAC,GACD,IAAI;QACR,OAAO,CAAC,GAAG,CAAC,qBAAqB,IACjC,OAAO,CAAC,GAAG,CAAC,sBAAsB,UACvB,IAAY,GAAK,CAAC;6BAzmCf,YAA4B,UA2mClC,IAAI;gBACF,iBAAiB;;gBAEjB,aAAa,EAAE,UAAU,CAAC,aAAa;gBACvC,cAAc,EAAE,UAAU,CAAC,cAAc;;QAG/C,CAAC,GACD,IAAI;QACR,UAAU,CAAC,WAAW,UACX,IAAY,GAAK,CAAC;YACvB,EAA6D,AAA7D,2DAA6D;YAC7D,KAAK,CAAC,QAAQ,GAAG,OAAO,EAAC,QAAU;YACnC,KAAK,CAAC,YAAY,GAAG,GAAG,CAAC,QAAQ;gBAC/B,OAAO,EAAE,IAAI;gBACb,kBAAkB,EAAE,KAAK;gBACzB,IAAI,EAAE,UAAU,CAAC,OAAO;gBACxB,UAAU,KAAK,UAAU,CAAC,WAAW,CAAC,OAAO;gBAC7C,OAAO,GAAE,KAAO;gBAChB,KAAK,EAAE,KAAK;mBACT,UAAU,CAAC,WAAW;;yBAEd,YAAY,CAAC,OAAO,CAAC,IAAI;QACxC,CAAC,GACD,IAAI;QACR,SAAS,IAAI,SAAS,UACX,IAAY,GAAK,CAAC;mBAChB,IAAI,CAAC,OAAO,iBAAgB,MAAQ;QAC7C,CAAC,GACD,IAAI;YAGd,MAAM,CAAC,OAAO;IAEhB,EAAE,EAAE,cAAc,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;QAC9B,GAAG,CAAC,IAAI,aA5mCgD,OAAS,kBA4mChC,OAAO;aACnC,KAAK,CAAC,aAAa,IAAI,cAAc,CAAE,CAAC;YAC3C,EAAE,EAAE,aAAa,EAAE,CAAC;gBAClB,IAAI,SAAS,aAAa,CAAC,IAAI;YACjC,CAAC;QACH,CAAC;QACD,OAAO;YA/qCY,cAAmC,SA+qChC,EAAE,CAAC,IAAI;;IAC/B,CAAC;eAnnCyD,OAAS,eAqnC/C,OAAO;AAC7B,CAAC;SAEQ,eAAe,CAAC,MAAoB,EAAgB,CAAC;IAC5D,KAAK,CAAC,MAAM;IACZ,KAAK,CAAC,WAAW,GAAoD,GAAG,CAAC,GAAG;IAC5E,GAAG,CAAC,UAAU,GAEH,IAAI;IAEf,MAAM,CAAC,SAAS;QACd,IAAI,EAAC,KAAK,EAAE,CAAC;YACX,MAAM,CAAC,IAAI,CAAC,KAAK;YACjB,WAAW,CAAC,OAAO,EAAE,UAAU,GAAK,UAAU,CAAC,IAAI,CAAC,KAAK;;QAC3D,CAAC;QACD,KAAK,EAAC,KAAK,EAAE,CAAC;YACZ,EAAE,GAAG,UAAU,EAAE,CAAC;gBAChB,UAAU,IAAI,UAAU,GAAK,UAAU,CAAC,KAAK,CAAC,KAAK;;gBACnD,WAAW,CAAC,OAAO,CAAC,UAAU;gBAC9B,WAAW,CAAC,KAAK;YACnB,CAAC;QACH,CAAC;QACD,QAAQ,IAAG,CAAC;YACV,EAAE,GAAG,UAAU,EAAE,CAAC;gBAChB,UAAU,IAAI,UAAU,GAAK,UAAU,CAAC,QAAQ;;gBAChD,WAAW,CAAC,OAAO,CAAC,UAAU;gBAC9B,WAAW,CAAC,KAAK;YACnB,CAAC;QACH,CAAC;;WAGI,GAAG,CAjtCW,cAAmC,UAitCjC,QAAQ,GAAK,CAAC;aAC9B,KAAK,CAAC,KAAK,IAAI,MAAM,CAAE,CAAC;YAC3B,EAAE,EAAE,QAAQ,CAAC,MAAM,EAAE,CAAC;;YAEtB,CAAC;YACD,QAAQ,CAAC,IAAI,CAAC,KAAK;QACrB,CAAC;QAED,EAAE,EAAE,UAAU,EAAE,CAAC;YACf,UAAU,CAAC,QAAQ;;QAErB,CAAC;QAED,WAAW,CAAC,GAAG,CAAC,QAAQ;mBACX,CAAC;YACZ,WAAW,CAAC,MAAM,CAAC,QAAQ;QAC7B,CAAC;IACH,CAAC;AACH,CAAC;SAEQ,WAAW,CAAC,GAAU,EAAS,CAAC;IACvC,KAAK,GAAG,IAAI,GAAE,OAAO,GAAE,KAAK,MAAK,GAAG;;QAC3B,IAAI;QAAE,OAAO;QAAE,KAAK;;AAC/B,CAAC;SAEQ,cAAc,CACrB,GAAwB,EACxB,GAAU,EACuB,CAAC;IAClC,EAAE,EAAE,GAAG,EAAE,CAAC;eACD,WAAW,CAAC,GAAG;IACxB,CAAC;;QAGC,IAAI,GAAE,sBAAwB;QAC9B,OAAO,GAAE,4BAA8B;QACvC,UAAU,EAAE,GAAG;;AAEnB,CAAC"}