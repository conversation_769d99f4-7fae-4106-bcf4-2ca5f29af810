{"version": 3, "sources": ["../../client/with-router.tsx"], "sourcesContent": ["import React from 'react'\nimport { NextComponentType, NextPageContext } from '../shared/lib/utils'\nimport { NextRouter, useRouter } from './router'\n\nexport type WithRouterProps = {\n  router: NextRouter\n}\n\nexport type ExcludeRouterProps<P> = Pick<\n  P,\n  Exclude<keyof P, keyof WithRouterProps>\n>\n\nexport default function withRouter<\n  P extends WithRouterProps,\n  C = NextPageContext\n>(\n  ComposedComponent: NextComponentType<C, any, P>\n): React.ComponentType<ExcludeRouterProps<P>> {\n  function WithRouterWrapper(props: any): JSX.Element {\n    return <ComposedComponent router={useRouter()} {...props} />\n  }\n\n  WithRouterWrapper.getInitialProps = ComposedComponent.getInitialProps\n  // This is needed to allow checking for custom getInitialProps in _app\n  ;(WithRouterWrapper as any).origGetInitialProps = (\n    ComposedComponent as any\n  ).origGetInitialProps\n  if (process.env.NODE_ENV !== 'production') {\n    const name =\n      ComposedComponent.displayName || ComposedComponent.name || 'Unknown'\n    WithRouterWrapper.displayName = `withRouter(${name})`\n  }\n\n  return WithRouterWrapper\n}\n"], "names": [], "mappings": ";;;;kBAawB,UAAU;AAbhB,GAAO,CAAP,MAAO;AAEa,GAAU,CAAV,OAAU;;;;;;SAWxB,UAAU,CAIhC,iBAA+C,EACH,CAAC;aACpC,iBAAiB,CAAC,KAAU,EAAe,CAAC;6BAnBrC,MAAO,uBAoBb,iBAAiB;YAAC,MAAM,MAlBE,OAAU;WAkBO,KAAK;IAC1D,CAAC;IAED,iBAAiB,CAAC,eAAe,GAAG,iBAAiB,CAAC,eAAe;IAEnE,iBAAiB,CAAS,mBAAmB,GAC7C,iBAAiB,CACjB,mBAAmB;IACrB,EAAE,EAAE,OAAO,CAAC,GAAG,CAAC,QAAQ,MAAK,UAAY,GAAE,CAAC;QAC1C,KAAK,CAAC,IAAI,GACR,iBAAiB,CAAC,WAAW,IAAI,iBAAiB,CAAC,IAAI,KAAI,OAAS;QACtE,iBAAiB,CAAC,WAAW,IAAI,WAAW,EAAE,IAAI,CAAC,CAAC;IACtD,CAAC;WAEM,iBAAiB;AAC1B,CAAC"}