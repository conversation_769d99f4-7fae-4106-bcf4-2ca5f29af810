{"version": 3, "sources": ["../../server/require.ts"], "sourcesContent": ["import { promises } from 'fs'\nimport { join } from 'path'\nimport {\n  PAGES_MANIFEST,\n  SERVER_DIRECTORY,\n  SERVERLESS_DIRECTORY,\n  FONT_MANIFEST,\n} from '../shared/lib/constants'\nimport { normalizePagePath, denormalizePagePath } from './normalize-page-path'\nimport { PagesManifest } from '../build/webpack/plugins/pages-manifest-plugin'\nimport { normalizeLocalePath } from '../shared/lib/i18n/normalize-locale-path'\n\nexport function pageNotFoundError(page: string): Error {\n  const err: any = new Error(`Cannot find module for page: ${page}`)\n  err.code = 'ENOENT'\n  return err\n}\n\nexport function getPagePath(\n  page: string,\n  distDir: string,\n  serverless: boolean,\n  dev?: boolean,\n  locales?: string[]\n): string {\n  const serverBuildPath = join(\n    distDir,\n    serverless && !dev ? SERVERLESS_DIRECTORY : SERVER_DIRECTORY\n  )\n  const pagesManifest = require(join(\n    serverBuildPath,\n    PAGES_MANIFEST\n  )) as PagesManifest\n\n  try {\n    page = denormalizePagePath(normalizePagePath(page))\n  } catch (err) {\n    console.error(err)\n    throw pageNotFoundError(page)\n  }\n  let pagePath = pagesManifest[page]\n\n  if (!pagesManifest[page] && locales) {\n    const manifestNoLocales: typeof pagesManifest = {}\n\n    for (const key of Object.keys(pagesManifest)) {\n      manifestNoLocales[normalizeLocalePath(key, locales).pathname] =\n        pagesManifest[key]\n    }\n    pagePath = manifestNoLocales[page]\n  }\n\n  if (!pagePath) {\n    throw pageNotFoundError(page)\n  }\n  return join(serverBuildPath, pagePath)\n}\n\nexport function requirePage(\n  page: string,\n  distDir: string,\n  serverless: boolean\n): any {\n  const pagePath = getPagePath(page, distDir, serverless)\n  if (pagePath.endsWith('.html')) {\n    return promises.readFile(pagePath, 'utf8')\n  }\n  return require(pagePath)\n}\n\nexport function requireFontManifest(distDir: string, serverless: boolean) {\n  const serverBuildPath = join(\n    distDir,\n    serverless ? SERVERLESS_DIRECTORY : SERVER_DIRECTORY\n  )\n  const fontManifest = require(join(serverBuildPath, FONT_MANIFEST))\n  return fontManifest\n}\n"], "names": [], "mappings": ";;;;QAYgB,iBAAiB,GAAjB,iBAAiB;QAMjB,WAAW,GAAX,WAAW;QAwCX,WAAW,GAAX,WAAW;QAYX,mBAAmB,GAAnB,mBAAmB;AAtEV,GAAI,CAAJ,GAAI;AACR,GAAM,CAAN,KAAM;AAMpB,GAAyB,CAAzB,UAAyB;AACuB,GAAuB,CAAvB,kBAAuB;AAE1C,GAA0C,CAA1C,oBAA0C;SAE9D,iBAAiB,CAAC,IAAY,EAAS,CAAC;IACtD,KAAK,CAAC,GAAG,GAAQ,GAAG,CAAC,KAAK,EAAE,6BAA6B,EAAE,IAAI;IAC/D,GAAG,CAAC,IAAI,IAAG,MAAQ;WACZ,GAAG;AACZ,CAAC;SAEe,WAAW,CACzB,IAAY,EACZ,OAAe,EACf,UAAmB,EACnB,GAAa,EACb,OAAkB,EACV,CAAC;IACT,KAAK,CAAC,eAAe,OAxBF,KAAM,OAyBvB,OAAO,EACP,UAAU,KAAK,GAAG,GApBf,UAAyB,wBAAzB,UAAyB;IAsB9B,KAAK,CAAC,aAAa,GAAG,OAAO,KA5BV,KAAM,OA6BvB,eAAe,EAvBZ,UAAyB;QA2B1B,CAAC;QACH,IAAI,OA3B+C,kBAAuB,0BAAvB,kBAAuB,oBA2B7B,IAAI;IACnD,CAAC,QAAQ,GAAG,EAAE,CAAC;QACb,OAAO,CAAC,KAAK,CAAC,GAAG;QACjB,KAAK,CAAC,iBAAiB,CAAC,IAAI;IAC9B,CAAC;IACD,GAAG,CAAC,QAAQ,GAAG,aAAa,CAAC,IAAI;IAEjC,EAAE,GAAG,aAAa,CAAC,IAAI,KAAK,OAAO,EAAE,CAAC;QACpC,KAAK,CAAC,iBAAiB;;aAElB,KAAK,CAAC,GAAG,IAAI,MAAM,CAAC,IAAI,CAAC,aAAa,EAAG,CAAC;YAC7C,iBAAiB,KApCa,oBAA0C,sBAoClC,GAAG,EAAE,OAAO,EAAE,QAAQ,IAC1D,aAAa,CAAC,GAAG;QACrB,CAAC;QACD,QAAQ,GAAG,iBAAiB,CAAC,IAAI;IACnC,CAAC;IAED,EAAE,GAAG,QAAQ,EAAE,CAAC;QACd,KAAK,CAAC,iBAAiB,CAAC,IAAI;IAC9B,CAAC;eArDkB,KAAM,OAsDb,eAAe,EAAE,QAAQ;AACvC,CAAC;SAEe,WAAW,CACzB,IAAY,EACZ,OAAe,EACf,UAAmB,EACd,CAAC;IACN,KAAK,CAAC,QAAQ,GAAG,WAAW,CAAC,IAAI,EAAE,OAAO,EAAE,UAAU;IACtD,EAAE,EAAE,QAAQ,CAAC,QAAQ,EAAC,KAAO,IAAG,CAAC;eAhEV,GAAI,UAiET,QAAQ,CAAC,QAAQ,GAAE,IAAM;IAC3C,CAAC;WACM,OAAO,CAAC,QAAQ;AACzB,CAAC;SAEe,mBAAmB,CAAC,OAAe,EAAE,UAAmB,EAAE,CAAC;IACzE,KAAK,CAAC,eAAe,OAtEF,KAAM,OAuEvB,OAAO,EACP,UAAU,GAlEP,UAAyB,wBAAzB,UAAyB;IAoE9B,KAAK,CAAC,YAAY,GAAG,OAAO,KA1ET,KAAM,OA0ES,eAAe,EApE5C,UAAyB;WAqEvB,YAAY;AACrB,CAAC"}