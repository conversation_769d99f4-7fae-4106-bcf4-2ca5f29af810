{"version": 3, "sources": ["../../../../telemetry/trace/report/to-jaeger.ts"], "sourcesContent": ["import retry from 'next/dist/compiled/async-retry'\nimport { randomBytes } from 'crypto'\nimport fetch from 'node-fetch'\nimport * as Log from '../../../build/output/log'\n// <PERSON><PERSON><PERSON> uses <PERSON><PERSON><PERSON>'s reporting\nimport { batcher } from './to-zipkin'\n\nlet traceId: string\nlet batch: ReturnType<typeof batcher> | undefined\n\nconst localEndpoint = {\n  serviceName: 'nextjs',\n  ipv4: '127.0.0.1',\n  port: 9411,\n}\n// <PERSON><PERSON><PERSON> supports <PERSON><PERSON>kin's reporting API\nconst zipkinUrl = `http://${localEndpoint.ipv4}:${localEndpoint.port}`\nconst jaegerWebUiUrl = `http://${localEndpoint.ipv4}:16686`\nconst zipkinAPI = `${zipkinUrl}/api/v2/spans`\n\nfunction logWebUrl() {\n  Log.info(\n    `Jaeger trace will be available on ${jaegerWebUiUrl}/trace/${traceId}`\n  )\n}\n\nconst reportToLocalHost = (\n  name: string,\n  duration: number,\n  timestamp: number,\n  id: string,\n  parentId?: string,\n  attrs?: Object\n) => {\n  if (!traceId) {\n    traceId = process.env.TRACE_ID || randomBytes(8).toString('hex')\n    logWebUrl()\n  }\n\n  if (!batch) {\n    batch = batcher((events) => {\n      const eventsJson = JSON.stringify(events)\n      // Ensure ECONNRESET error is retried 3 times before erroring out\n      return retry(\n        () =>\n          // Send events to zipkin\n          fetch(zipkinAPI, {\n            method: 'POST',\n            headers: { 'Content-Type': 'application/json' },\n            body: eventsJson,\n          }),\n        { minTimeout: 500, retries: 3, factor: 1 }\n      )\n        .then(async (res: any) => {\n          if (res.status !== 202) {\n            console.log({\n              status: res.status,\n              body: await res.text(),\n              events: eventsJson,\n            })\n          }\n        })\n        .catch(console.log)\n    })\n  }\n\n  batch.report({\n    traceId,\n    parentId,\n    name,\n    id,\n    timestamp,\n    duration,\n    localEndpoint,\n    tags: attrs,\n  })\n}\n\nexport default {\n  flushAll: () =>\n    batch ? batch.flushAll().then(() => logWebUrl()) : undefined,\n  report: reportToLocalHost,\n}\n"], "names": [], "mappings": ";;;;;AAAkB,GAAgC,CAAhC,WAAgC;AACtB,GAAQ,CAAR,OAAQ;AAClB,GAAY,CAAZ,UAAY;AAClB,GAAG,CAAH,GAAG;AAES,GAAa,CAAb,SAAa;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAErC,GAAG,CAAC,OAAO;AACX,GAAG,CAAC,KAAK;AAET,KAAK,CAAC,aAAa;IACjB,WAAW,GAAE,MAAQ;IACrB,IAAI,GAAE,SAAW;IACjB,IAAI,EAAE,IAAI;;AAEZ,EAAyC,AAAzC,uCAAyC;AACzC,KAAK,CAAC,SAAS,IAAI,OAAO,EAAE,aAAa,CAAC,IAAI,CAAC,CAAC,EAAE,aAAa,CAAC,IAAI;AACpE,KAAK,CAAC,cAAc,IAAI,OAAO,EAAE,aAAa,CAAC,IAAI,CAAC,MAAM;AAC1D,KAAK,CAAC,SAAS,MAAM,SAAS,CAAC,aAAa;SAEnC,SAAS,GAAG,CAAC;IAjBV,GAAG,CAkBT,IAAI,EACL,kCAAkC,EAAE,cAAc,CAAC,OAAO,EAAE,OAAO;AAExE,CAAC;AAED,KAAK,CAAC,iBAAiB,IACrB,IAAY,EACZ,QAAgB,EAChB,SAAiB,EACjB,EAAU,EACV,QAAiB,EACjB,KAAc,GACX,CAAC;IACJ,EAAE,GAAG,OAAO,EAAE,CAAC;QACb,OAAO,GAAG,OAAO,CAAC,GAAG,CAAC,QAAQ,QAlCN,OAAQ,cAkCc,CAAC,EAAE,QAAQ,EAAC,GAAK;QAC/D,SAAS;IACX,CAAC;IAED,EAAE,GAAG,KAAK,EAAE,CAAC;QACX,KAAK,OAnCe,SAAa,WAmChB,MAAM,GAAK,CAAC;YAC3B,KAAK,CAAC,UAAU,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM;YACxC,EAAiE,AAAjE,+DAAiE;uBA1CrD,WAAgC,cA6CxC,EAAwB,AAAxB,sBAAwB;oBA3ChB,UAAY,UA4Cd,SAAS;oBACb,MAAM,GAAE,IAAM;oBACd,OAAO;yBAAI,YAAc,IAAE,gBAAkB;;oBAC7C,IAAI,EAAE,UAAU;;;gBAElB,UAAU,EAAE,GAAG;gBAAE,OAAO,EAAE,CAAC;gBAAE,MAAM,EAAE,CAAC;eAEvC,IAAI,QAAQ,GAAQ,GAAK,CAAC;gBACzB,EAAE,EAAE,GAAG,CAAC,MAAM,KAAK,GAAG,EAAE,CAAC;oBACvB,OAAO,CAAC,GAAG;wBACT,MAAM,EAAE,GAAG,CAAC,MAAM;wBAClB,IAAI,QAAQ,GAAG,CAAC,IAAI;wBACpB,MAAM,EAAE,UAAU;;gBAEtB,CAAC;YACH,CAAC,EACA,KAAK,CAAC,OAAO,CAAC,GAAG;QACtB,CAAC;IACH,CAAC;IAED,KAAK,CAAC,MAAM;QACV,OAAO;QACP,QAAQ;QACR,IAAI;QACJ,EAAE;QACF,SAAS;QACT,QAAQ;QACR,aAAa;QACb,IAAI,EAAE,KAAK;;AAEf,CAAC;;IAGC,QAAQ,MACN,KAAK,GAAG,KAAK,CAAC,QAAQ,GAAG,IAAI,KAAO,SAAS;YAAM,SAAS;;IAC9D,MAAM,EAAE,iBAAiB"}