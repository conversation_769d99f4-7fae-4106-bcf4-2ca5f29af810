{"version": 3, "sources": ["../../../client/dev/on-demand-entries-client.js"], "sourcesContent": ["import Router from 'next/router'\nimport { setupPing, currentPage, closePing } from './on-demand-entries-utils'\n\nexport default async ({ assetPrefix }) => {\n  Router.ready(() => {\n    Router.events.on(\n      'routeChangeComplete',\n      setupPing.bind(this, assetPrefix, () => Router.pathname)\n    )\n  })\n\n  setupPing(\n    assetPrefix,\n    () => Router.query.__NEXT_PAGE || Router.pathname,\n    currentPage\n  )\n\n  // prevent HMR connection from being closed when running tests\n  if (!process.env.__NEXT_TEST_MODE) {\n    document.addEventListener('visibilitychange', (_event) => {\n      const state = document.visibilityState\n      if (state === 'visible') {\n        setupPing(assetPrefix, () => Router.pathname, true)\n      } else {\n        closePing()\n      }\n    })\n\n    window.addEventListener('beforeunload', () => {\n      closePing()\n    })\n  }\n}\n"], "names": [], "mappings": ";;;;;AAAmB,GAAa,CAAb,OAAa;AACkB,GAA2B,CAA3B,qBAA2B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAErD,WAAW,KAAO,CAAC;IAHxB,OAAa,SAIvB,KAAK,KAAO,CAAC;QAJH,OAAa,SAKrB,MAAM,CAAC,EAAE,EACd,mBAAqB,GALuB,qBAA2B,WAM7D,IAAI,OAAO,WAAW,MAPnB,OAAa,SAOqB,QAAQ;;IAE3D,CAAC;QAR+C,qBAA2B,YAWzE,WAAW,MAZI,OAAa,SAaf,KAAK,CAAC,WAAW,IAbf,OAAa,SAaa,QAAQ;MAZH,qBAA2B;IAgB3E,EAA8D,AAA9D,4DAA8D;IAC9D,EAAE,GAAG,OAAO,CAAC,GAAG,CAAC,gBAAgB,EAAE,CAAC;QAClC,QAAQ,CAAC,gBAAgB,EAAC,gBAAkB,IAAG,MAAM,GAAK,CAAC;YACzD,KAAK,CAAC,KAAK,GAAG,QAAQ,CAAC,eAAe;YACtC,EAAE,EAAE,KAAK,MAAK,OAAS,GAAE,CAAC;oBApBkB,qBAA2B,YAqB3D,WAAW,MAtBV,OAAa,SAsBY,QAAQ;kBAAE,IAAI;YACpD,CAAC,MAAM,CAAC;oBAtBoC,qBAA2B;YAwBvE,CAAC;QACH,CAAC;QAED,MAAM,CAAC,gBAAgB,EAAC,YAAc,OAAQ,CAAC;gBA3BD,qBAA2B;QA6BzE,CAAC;IACH,CAAC;AACH,CAAC"}