{"version": 3, "sources": ["../../../../../build/webpack/plugins/wellknown-errors-plugin/webpackModuleError.ts"], "sourcesContent": ["import { readFileSync } from 'fs'\nimport * as path from 'path'\nimport { webpack } from 'next/dist/compiled/webpack/webpack'\nimport { getBabelError } from './parseBabel'\nimport { getCssError } from './parseCss'\nimport { getScssError } from './parseScss'\nimport { getNotFoundError } from './parseNotFoundError'\nimport { SimpleWebpackError } from './simpleWebpackError'\n\nfunction getFileData(\n  compilation: webpack.compilation.Compilation,\n  m: any\n): [string, string | null] {\n  let resolved: string\n  let ctx: string | null =\n    compilation.compiler?.context ?? compilation.context ?? null\n  if (ctx !== null && typeof m.resource === 'string') {\n    const res = path.relative(ctx, m.resource).replace(/\\\\/g, path.posix.sep)\n    resolved = res.startsWith('.') ? res : `.${path.posix.sep}${res}`\n  } else {\n    const requestShortener = compilation.requestShortener\n    if (typeof m?.readableIdentifier === 'function') {\n      resolved = m.readableIdentifier(requestShortener)\n    } else {\n      resolved = m.request ?? m.userRequest\n    }\n  }\n\n  if (resolved) {\n    let content: string | null = null\n    try {\n      content = readFileSync(\n        ctx ? path.resolve(ctx, resolved) : resolved,\n        'utf8'\n      )\n    } catch {}\n    return [resolved, content]\n  }\n\n  return ['<unknown>', null]\n}\n\nexport async function getModuleBuildError(\n  compilation: webpack.compilation.Compilation,\n  input: any\n): Promise<SimpleWebpackError | false> {\n  if (\n    !(\n      typeof input === 'object' &&\n      (input?.name === 'ModuleBuildError' ||\n        input?.name === 'ModuleNotFoundError') &&\n      Boolean(input.module) &&\n      input.error instanceof Error\n    )\n  ) {\n    return false\n  }\n\n  const err: Error = input.error\n  const [sourceFilename, sourceContent] = getFileData(compilation, input.module)\n\n  const notFoundError = await getNotFoundError(\n    compilation,\n    input,\n    sourceFilename\n  )\n  if (notFoundError !== false) {\n    return notFoundError\n  }\n\n  const babel = getBabelError(sourceFilename, err)\n  if (babel !== false) {\n    return babel\n  }\n\n  const css = getCssError(sourceFilename, err)\n  if (css !== false) {\n    return css\n  }\n\n  const scss = getScssError(sourceFilename, sourceContent, err)\n  if (scss !== false) {\n    return scss\n  }\n\n  return false\n}\n"], "names": [], "mappings": ";;;;QA0CsB,mBAAmB,GAAnB,mBAAmB;AA1CZ,GAAI,CAAJ,GAAI;AACrB,GAAI,CAAJ,IAAI;AAEc,GAAc,CAAd,WAAc;AAChB,GAAY,CAAZ,SAAY;AACX,GAAa,CAAb,UAAa;AACT,GAAsB,CAAtB,mBAAsB;;;;;;;;;;;;;;;;;;;;;;;;SAG9C,WAAW,CAClB,WAA4C,EAC5C,CAAM,EACmB,CAAC;QAGxB,GAAoB;IAFtB,GAAG,CAAC,QAAQ;QAEV,IAA6B,EAA7B,IAAoD;IADtD,GAAG,CAAC,GAAG,IACL,IAAoD,IAApD,IAA6B,IAA7B,GAAoB,GAApB,WAAW,CAAC,QAAQ,cAApB,GAAoB,UAApB,CAA6B,QAA7B,CAA6B,GAA7B,GAAoB,CAAE,OAAO,cAA7B,IAA6B,cAA7B,IAA6B,GAAI,WAAW,CAAC,OAAO,cAApD,IAAoD,cAApD,IAAoD,GAAI,IAAI;IAC9D,EAAE,EAAE,GAAG,KAAK,IAAI,WAAW,CAAC,CAAC,QAAQ,MAAK,MAAQ,GAAE,CAAC;QACnD,KAAK,CAAC,GAAG,GAhBD,IAAI,CAgBK,QAAQ,CAAC,GAAG,EAAE,CAAC,CAAC,QAAQ,EAAE,OAAO,QAhB1C,IAAI,CAgBmD,KAAK,CAAC,GAAG;QACxE,QAAQ,GAAG,GAAG,CAAC,UAAU,EAAC,CAAG,KAAI,GAAG,IAAI,CAAC,EAjBjC,IAAI,CAiBoC,KAAK,CAAC,GAAG,GAAG,GAAG;IACjE,CAAC,MAAM,CAAC;QACN,KAAK,CAAC,gBAAgB,GAAG,WAAW,CAAC,gBAAgB;QACrD,EAAE,UAAS,CAAC,aAAD,CAAC,UAAD,CAAqB,QAArB,CAAqB,GAArB,CAAC,CAAE,kBAAkB,OAAK,QAAU,GAAE,CAAC;YAChD,QAAQ,GAAG,CAAC,CAAC,kBAAkB,CAAC,gBAAgB;QAClD,CAAC,MAAM,CAAC;gBACK,QAAS;YAApB,QAAQ,IAAG,QAAS,GAAT,CAAC,CAAC,OAAO,cAAT,QAAS,cAAT,QAAS,GAAI,CAAC,CAAC,WAAW;QACvC,CAAC;IACH,CAAC;IAED,EAAE,EAAE,QAAQ,EAAE,CAAC;QACb,GAAG,CAAC,OAAO,GAAkB,IAAI;YAC7B,CAAC;YACH,OAAO,OA/BgB,GAAI,eAgCzB,GAAG,GA/BC,IAAI,CA+BG,OAAO,CAAC,GAAG,EAAE,QAAQ,IAAI,QAAQ,GAC5C,IAAM;QAEV,CAAC,QAAO,CAAC;QAAA,CAAC;;YACF,QAAQ;YAAE,OAAO;;IAC3B,CAAC;;SAEO,SAAW;QAAE,IAAI;;AAC3B,CAAC;eAEqB,mBAAmB,CACvC,WAA4C,EAC5C,KAAU,EAC2B,CAAC;IACtC,EAAE,WAES,KAAK,MAAK,MAAQ,OACxB,KAAK,aAAL,KAAK,UAAL,CAAW,QAAX,CAAW,GAAX,KAAK,CAAE,IAAI,OAAK,gBAAkB,MACjC,KAAK,aAAL,KAAK,UAAL,CAAW,QAAX,CAAW,GAAX,KAAK,CAAE,IAAI,OAAK,mBAAqB,MACvC,OAAO,CAAC,KAAK,CAAC,MAAM,KACpB,KAAK,CAAC,KAAK,YAAY,KAAK,GAE9B,CAAC;eACM,KAAK;IACd,CAAC;IAED,KAAK,CAAC,GAAG,GAAU,KAAK,CAAC,KAAK;IAC9B,KAAK,EAAE,cAAc,EAAE,aAAa,IAAI,WAAW,CAAC,WAAW,EAAE,KAAK,CAAC,MAAM;IAE7E,KAAK,CAAC,aAAa,aAvDY,mBAAsB,mBAwDnD,WAAW,EACX,KAAK,EACL,cAAc;IAEhB,EAAE,EAAE,aAAa,KAAK,KAAK,EAAE,CAAC;eACrB,aAAa;IACtB,CAAC;IAED,KAAK,CAAC,KAAK,OAnEiB,WAAc,gBAmEd,cAAc,EAAE,GAAG;IAC/C,EAAE,EAAE,KAAK,KAAK,KAAK,EAAE,CAAC;eACb,KAAK;IACd,CAAC;IAED,KAAK,CAAC,GAAG,OAvEiB,SAAY,cAuEd,cAAc,EAAE,GAAG;IAC3C,EAAE,EAAE,GAAG,KAAK,KAAK,EAAE,CAAC;eACX,GAAG;IACZ,CAAC;IAED,KAAK,CAAC,IAAI,OA3EiB,UAAa,eA2Ed,cAAc,EAAE,aAAa,EAAE,GAAG;IAC5D,EAAE,EAAE,IAAI,KAAK,KAAK,EAAE,CAAC;eACZ,IAAI;IACb,CAAC;WAEM,KAAK;AACd,CAAC"}