{"version": 3, "sources": ["../../../telemetry/trace/trace.ts"], "sourcesContent": ["import { randomBytes } from 'crypto'\nimport { SpanId } from './shared'\nimport { reporter } from './report'\n\nconst NUM_OF_MICROSEC_IN_SEC = BigInt('1000')\n\nconst getId = () => randomBytes(8).toString('hex')\n\n// eslint typescript has a bug with TS enums\n/* eslint-disable no-shadow */\nexport enum SpanStatus {\n  Started,\n  Stopped,\n}\n\nexport class Span {\n  name: string\n  id: SpanId\n  parentId?: SpanId\n  duration: number | null\n  attrs: { [key: string]: any }\n  status: SpanStatus\n\n  _start: bigint\n\n  constructor(name: string, parentId?: SpanId, attrs?: Object) {\n    this.name = name\n    this.parentId = parentId\n    this.duration = null\n    this.attrs = attrs ? { ...attrs } : {}\n    this.status = SpanStatus.Started\n    this.id = getId()\n    this._start = process.hrtime.bigint()\n  }\n\n  // Durations are reported as microseconds. This gives 1000x the precision\n  // of something like Date.now(), which reports in milliseconds.\n  // Additionally, ~285 years can be safely represented as microseconds as\n  // a float64 in both JSON and JavaScript.\n  stop() {\n    const end: bigint = process.hrtime.bigint()\n    const duration = (end - this._start) / NUM_OF_MICROSEC_IN_SEC\n    this.status = SpanStatus.Stopped\n    if (duration > Number.MAX_SAFE_INTEGER) {\n      throw new Error(`Duration is too long to express as float64: ${duration}`)\n    }\n    const timestamp = this._start / NUM_OF_MICROSEC_IN_SEC\n    reporter.report(\n      this.name,\n      Number(duration),\n      Number(timestamp),\n      this.id,\n      this.parentId,\n      this.attrs\n    )\n  }\n\n  traceChild(name: string, attrs?: Object) {\n    return new Span(name, this.id, attrs)\n  }\n\n  setAttribute(key: string, value: any) {\n    this.attrs[key] = String(value)\n  }\n\n  traceFn(fn: any) {\n    try {\n      return fn()\n    } finally {\n      this.stop()\n    }\n  }\n\n  async traceAsyncFn<T>(fn: () => T | Promise<T>): Promise<T> {\n    try {\n      return await fn()\n    } finally {\n      this.stop()\n    }\n  }\n}\n\nexport const trace = (name: string, parentId?: SpanId, attrs?: Object) => {\n  return new Span(name, parentId, attrs)\n}\n\nexport const flushAllTraces = () => reporter.flushAll()\n"], "names": [], "mappings": ";;;;;AAA4B,GAAQ,CAAR,OAAQ;AAEX,GAAU,CAAV,OAAU;AAEnC,KAAK,CAAC,sBAAsB,GAAG,MAAM,EAAC,IAAM;AAE5C,KAAK,CAAC,KAAK,WANiB,OAAQ,cAMJ,CAAC,EAAE,QAAQ,EAAC,GAAK;;;;UAIrC,WAAU;IAAV,WAAU,CAAV,WAAU,EACpB,OAAO,KAAP,CAAO,KAAP,OAAO;IADG,WAAU,CAAV,WAAU,EAEpB,OAAO,KAAP,CAAO,KAAP,OAAO;GAFG,UAAU,0BAAV,UAAU;;MAKT,IAAI;gBAUH,KAAY,EAAE,QAAiB,EAAE,MAAc,CAAE,CAAC;aACvD,IAAI,GAAG,KAAI;aACX,QAAQ,GAAG,QAAQ;aACnB,QAAQ,GAAG,IAAI;aACf,KAAK,GAAG,MAAK;eAAQ,MAAK;;;aAC1B,MAAM,GAAG,UAAU,CAAC,OAAO;aAC3B,EAAE,GAAG,KAAK;aACV,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC,MAAM;IACrC,CAAC;IAED,EAAyE,AAAzE,uEAAyE;IACzE,EAA+D,AAA/D,6DAA+D;IAC/D,EAAwE,AAAxE,sEAAwE;IACxE,EAAyC,AAAzC,uCAAyC;IACzC,IAAI,GAAG,CAAC;QACN,KAAK,CAAC,GAAG,GAAW,OAAO,CAAC,MAAM,CAAC,MAAM;QACzC,KAAK,CAAC,QAAQ,IAAI,GAAG,QAAQ,MAAM,IAAI,sBAAsB;aACxD,MAAM,GAAG,UAAU,CAAC,OAAO;QAChC,EAAE,EAAE,QAAQ,GAAG,MAAM,CAAC,gBAAgB,EAAE,CAAC;YACvC,KAAK,CAAC,GAAG,CAAC,KAAK,EAAE,4CAA4C,EAAE,QAAQ;QACzE,CAAC;QACD,KAAK,CAAC,SAAS,QAAQ,MAAM,GAAG,sBAAsB;QA5CjC,OAAU,UA6CtB,MAAM,MACR,IAAI,EACT,MAAM,CAAC,QAAQ,GACf,MAAM,CAAC,SAAS,QACX,EAAE,OACF,QAAQ,OACR,KAAK;IAEd,CAAC;IAED,UAAU,CAAC,IAAY,EAAE,KAAc,EAAE,CAAC;eACjC,GAAG,CAAC,IAAI,CAAC,IAAI,OAAO,EAAE,EAAE,KAAK;IACtC,CAAC;IAED,YAAY,CAAC,GAAW,EAAE,KAAU,EAAE,CAAC;aAChC,KAAK,CAAC,GAAG,IAAI,MAAM,CAAC,KAAK;IAChC,CAAC;IAED,OAAO,CAAC,EAAO,EAAE,CAAC;YACZ,CAAC;mBACI,EAAE;QACX,CAAC,QAAS,CAAC;iBACJ,IAAI;QACX,CAAC;IACH,CAAC;UAEK,YAAY,CAAI,EAAwB,EAAc,CAAC;YACvD,CAAC;yBACU,EAAE;QACjB,CAAC,QAAS,CAAC;iBACJ,IAAI;QACX,CAAC;IACH,CAAC;;QAhEU,IAAI,GAAJ,IAAI;AAmEV,KAAK,CAAC,KAAK,IAAI,KAAY,EAAE,SAAiB,EAAE,MAAc,GAAK,CAAC;WAClE,GAAG,CAAC,IAAI,CAAC,KAAI,EAAE,SAAQ,EAAE,MAAK;AACvC,CAAC;QAFY,KAAK,GAAL,KAAK;AAIX,KAAK,CAAC,cAAc,OApFF,OAAU,UAoFU,QAAQ;;QAAxC,cAAc,GAAd,cAAc"}