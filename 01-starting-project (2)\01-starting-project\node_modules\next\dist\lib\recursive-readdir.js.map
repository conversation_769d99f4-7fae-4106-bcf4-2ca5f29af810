{"version": 3, "sources": ["../../lib/recursive-readdir.ts"], "sourcesContent": ["import { Dirent, promises } from 'fs'\nimport { join } from 'path'\n\n/**\n * Recursively read directory\n * @param  {string} dir Directory to read\n * @param  {RegExp} filter Filter for the file name, only the name part is considered, not the full path\n * @param  {string[]=[]} arr This doesn't have to be provided, it's used for the recursion\n * @param  {string=dir`} rootDir Used to replace the initial path, only the relative path is left, it's faster than path.relative.\n * @returns Promise array holding all relative paths\n */\nexport async function recursiveReadDir(\n  dir: string,\n  filter: RegExp,\n  ignore?: RegExp,\n  arr: string[] = [],\n  rootDir: string = dir\n): Promise<string[]> {\n  const result = await promises.readdir(dir, { withFileTypes: true })\n\n  await Promise.all(\n    result.map(async (part: Dirent) => {\n      const absolutePath = join(dir, part.name)\n      if (ignore && ignore.test(part.name)) return\n\n      // readdir does not follow symbolic links\n      // if part is a symbolic link, follow it using stat\n      let isDirectory = part.isDirectory()\n      if (part.isSymbolicLink()) {\n        const stats = await promises.stat(absolutePath)\n        isDirectory = stats.isDirectory()\n      }\n\n      if (isDirectory) {\n        await recursiveReadDir(absolutePath, filter, ignore, arr, rootDir)\n        return\n      }\n\n      if (!filter.test(part.name)) {\n        return\n      }\n\n      arr.push(absolutePath.replace(rootDir, ''))\n    })\n  )\n\n  return arr.sort()\n}\n"], "names": [], "mappings": ";;;;QAWsB,gBAAgB,GAAhB,gBAAgB;AAXL,GAAI,CAAJ,GAAI;AAChB,GAAM,CAAN,KAAM;eAUL,gBAAgB,CACpC,GAAW,EACX,MAAc,EACd,MAAe,EACf,GAAa,OACb,OAAe,GAAG,GAAG,EACF,CAAC;IACpB,KAAK,CAAC,MAAM,SAlBmB,GAAI,UAkBL,OAAO,CAAC,GAAG;QAAI,aAAa,EAAE,IAAI;;UAE1D,OAAO,CAAC,GAAG,CACf,MAAM,CAAC,GAAG,QAAQ,IAAY,GAAK,CAAC;QAClC,KAAK,CAAC,YAAY,OArBH,KAAM,OAqBK,GAAG,EAAE,IAAI,CAAC,IAAI;QACxC,EAAE,EAAE,MAAM,IAAI,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI;QAEnC,EAAyC,AAAzC,uCAAyC;QACzC,EAAmD,AAAnD,iDAAmD;QACnD,GAAG,CAAC,WAAW,GAAG,IAAI,CAAC,WAAW;QAClC,EAAE,EAAE,IAAI,CAAC,cAAc,IAAI,CAAC;YAC1B,KAAK,CAAC,KAAK,SA7Bc,GAAI,UA6BA,IAAI,CAAC,YAAY;YAC9C,WAAW,GAAG,KAAK,CAAC,WAAW;QACjC,CAAC;QAED,EAAE,EAAE,WAAW,EAAE,CAAC;kBACV,gBAAgB,CAAC,YAAY,EAAE,MAAM,EAAE,MAAM,EAAE,GAAG,EAAE,OAAO;;QAEnE,CAAC;QAED,EAAE,GAAG,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,GAAG,CAAC;;QAE9B,CAAC;QAED,GAAG,CAAC,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,OAAO;IACvC,CAAC;WAGI,GAAG,CAAC,IAAI;AACjB,CAAC"}