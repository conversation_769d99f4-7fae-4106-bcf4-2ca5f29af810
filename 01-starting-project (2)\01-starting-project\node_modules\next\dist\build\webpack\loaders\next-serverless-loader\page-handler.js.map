{"version": 3, "sources": ["../../../../../build/webpack/loaders/next-serverless-loader/page-handler.ts"], "sourcesContent": ["import { IncomingMessage, ServerResponse } from 'http'\nimport { parse as parseUrl, format as formatUrl, UrlWithParsedQuery } from 'url'\nimport { DecodeError, isResSent } from '../../../../shared/lib/utils'\nimport { sendPayload } from '../../../../server/send-payload'\nimport { getUtils, vercelHeader, ServerlessHandlerCtx } from './utils'\n\nimport { renderToHTML } from '../../../../server/render'\nimport { tryGetPreviewData } from '../../../../server/api-utils'\nimport { denormalizePagePath } from '../../../../server/denormalize-page-path'\nimport { setLazyProp, getCookieParser } from '../../../../server/api-utils'\nimport { getRedirectStatus } from '../../../../lib/load-custom-routes'\nimport getRouteNoAssetPath from '../../../../shared/lib/router/utils/get-route-from-asset-path'\nimport { PERMANENT_REDIRECT_STATUS } from '../../../../shared/lib/constants'\nimport { resultsToString } from '../../../../server/utils'\n\nexport function getPageHandler(ctx: ServerlessHandlerCtx) {\n  const {\n    page,\n\n    pageComponent,\n    pageConfig,\n    pageGetStaticProps,\n    pageGetStaticPaths,\n    pageGetServerSideProps,\n\n    appModule,\n    documentModule,\n    errorModule,\n    notFoundModule,\n\n    encodedPreviewProps,\n    pageIsDynamic,\n    generateEtags,\n    poweredByHeader,\n\n    runtimeConfig,\n    buildManifest,\n    reactLoadableManifest,\n\n    i18n,\n    buildId,\n    basePath,\n    assetPrefix,\n    canonicalBase,\n    escapedBuildId,\n  } = ctx\n  const {\n    handleLocale,\n    handleRewrites,\n    handleBasePath,\n    defaultRouteRegex,\n    dynamicRouteMatcher,\n    interpolateDynamicPath,\n    getParamsFromRouteMatches,\n    normalizeDynamicRouteParams,\n    normalizeVercelUrl,\n  } = getUtils(ctx)\n\n  async function renderReqToHTML(\n    req: IncomingMessage,\n    res: ServerResponse,\n    renderMode?: 'export' | 'passthrough' | true,\n    _renderOpts?: any,\n    _params?: any\n  ) {\n    let Component\n    let App\n    let config\n    let Document\n    let Error\n    let notFoundMod\n    let getStaticProps\n    let getStaticPaths\n    let getServerSideProps\n    ;[\n      getStaticProps,\n      getServerSideProps,\n      getStaticPaths,\n      Component,\n      App,\n      config,\n      { default: Document },\n      { default: Error },\n      notFoundMod,\n    ] = await Promise.all([\n      pageGetStaticProps,\n      pageGetServerSideProps,\n      pageGetStaticPaths,\n      pageComponent,\n      appModule,\n      pageConfig,\n      documentModule,\n      errorModule,\n      notFoundModule,\n    ])\n\n    const fromExport = renderMode === 'export' || renderMode === true\n    const nextStartMode = renderMode === 'passthrough'\n\n    let hasValidParams = true\n\n    setLazyProp({ req: req as any }, 'cookies', getCookieParser(req.headers))\n\n    const options = {\n      App,\n      Document,\n      buildManifest,\n      getStaticProps,\n      getServerSideProps,\n      getStaticPaths,\n      reactLoadableManifest,\n      canonicalBase,\n      buildId,\n      assetPrefix,\n      runtimeConfig: (runtimeConfig || {}).publicRuntimeConfig || {},\n      previewProps: encodedPreviewProps,\n      env: process.env,\n      basePath,\n      requireStaticHTML: true, // Serverless target doesn't support streaming\n      ..._renderOpts,\n    }\n    let _nextData = false\n    let defaultLocale = i18n?.defaultLocale\n    let detectedLocale = i18n?.defaultLocale\n    let parsedUrl: UrlWithParsedQuery\n\n    try {\n      // We need to trust the dynamic route params from the proxy\n      // to ensure we are using the correct values\n      const trustQuery = !getStaticProps && req.headers[vercelHeader]\n      parsedUrl = parseUrl(req.url!, true)\n      let routeNoAssetPath = parsedUrl.pathname!\n\n      if (basePath) {\n        routeNoAssetPath =\n          routeNoAssetPath.replace(new RegExp(`^${basePath}`), '') || '/'\n      }\n      const origQuery = Object.assign({}, parsedUrl.query)\n\n      parsedUrl = handleRewrites(req, parsedUrl)\n      handleBasePath(req, parsedUrl)\n\n      // remove ?amp=1 from request URL if rendering for export\n      if (fromExport && parsedUrl.query.amp) {\n        const queryNoAmp = Object.assign({}, origQuery)\n        delete queryNoAmp.amp\n\n        req.url = formatUrl({\n          ...parsedUrl,\n          search: undefined,\n          query: queryNoAmp,\n        })\n      }\n\n      if (parsedUrl.pathname!.match(/_next\\/data/)) {\n        _nextData = page !== '/_error'\n        parsedUrl.pathname = getRouteNoAssetPath(\n          parsedUrl.pathname!.replace(\n            new RegExp(`/_next/data/${escapedBuildId}/`),\n            '/'\n          ),\n          '.json'\n        )\n        routeNoAssetPath = parsedUrl.pathname\n      }\n\n      const localeResult = handleLocale(\n        req,\n        res,\n        parsedUrl,\n        routeNoAssetPath,\n        fromExport || nextStartMode\n      )\n      defaultLocale = localeResult?.defaultLocale || defaultLocale\n      detectedLocale = localeResult?.detectedLocale || detectedLocale\n      routeNoAssetPath = localeResult?.routeNoAssetPath || routeNoAssetPath\n\n      if (parsedUrl.query.nextInternalLocale) {\n        detectedLocale = parsedUrl.query.nextInternalLocale as string\n        delete parsedUrl.query.nextInternalLocale\n      }\n\n      const renderOpts = Object.assign(\n        {\n          Component,\n          pageConfig: config,\n          nextExport: fromExport,\n          isDataReq: _nextData,\n          locales: i18n?.locales,\n          locale: detectedLocale,\n          defaultLocale,\n          domainLocales: i18n?.domains,\n        },\n        options\n      )\n\n      if (page === '/_error' && !res.statusCode) {\n        res.statusCode = 404\n      }\n\n      let params = {}\n\n      if (!fromExport && pageIsDynamic) {\n        const result = normalizeDynamicRouteParams(\n          trustQuery\n            ? parsedUrl.query\n            : (dynamicRouteMatcher!(parsedUrl.pathname) as Record<\n                string,\n                string | string[]\n              >)\n        )\n\n        hasValidParams = result.hasValidParams\n        params = result.params\n      }\n\n      let nowParams = null\n\n      if (\n        pageIsDynamic &&\n        !hasValidParams &&\n        req.headers?.['x-now-route-matches']\n      ) {\n        nowParams = getParamsFromRouteMatches(req, renderOpts, detectedLocale)\n      }\n\n      // make sure to set renderOpts to the correct params e.g. _params\n      // if provided from worker or params if we're parsing them here\n      renderOpts.params = _params || params\n\n      normalizeVercelUrl(req, !!trustQuery)\n\n      // normalize request URL/asPath for fallback/revalidate pages since the\n      // proxy sets the request URL to the output's path for fallback pages\n      if (pageIsDynamic && nowParams && defaultRouteRegex) {\n        const _parsedUrl = parseUrl(req.url!)\n\n        _parsedUrl.pathname = interpolateDynamicPath(\n          _parsedUrl.pathname!,\n          nowParams\n        )\n        parsedUrl.pathname = _parsedUrl.pathname\n        req.url = formatUrl(_parsedUrl)\n      }\n\n      // make sure to normalize asPath for revalidate and _next/data requests\n      // since the asPath should match what is shown on the client\n      if (!fromExport && (getStaticProps || getServerSideProps)) {\n        // don't include dynamic route params in query while normalizing\n        // asPath\n        if (pageIsDynamic && trustQuery && defaultRouteRegex) {\n          delete (parsedUrl as any).search\n\n          for (const param of Object.keys(defaultRouteRegex.groups)) {\n            delete origQuery[param]\n          }\n        }\n\n        parsedUrl.pathname = denormalizePagePath(parsedUrl.pathname!)\n        renderOpts.resolvedUrl = formatUrl({\n          ...parsedUrl,\n          query: origQuery,\n        })\n\n        // For getServerSideProps we need to ensure we use the original URL\n        // and not the resolved URL to prevent a hydration mismatch on asPath\n        renderOpts.resolvedAsPath = getServerSideProps\n          ? formatUrl({\n              ...parsedUrl,\n              pathname: routeNoAssetPath,\n              query: origQuery,\n            })\n          : renderOpts.resolvedUrl\n      }\n\n      const isFallback = parsedUrl.query.__nextFallback\n\n      const previewData = tryGetPreviewData(req, res, options.previewProps)\n      const isPreviewMode = previewData !== false\n\n      if (process.env.__NEXT_OPTIMIZE_FONTS) {\n        renderOpts.optimizeFonts = true\n        /**\n         * __webpack_require__.__NEXT_FONT_MANIFEST__ is added by\n         * font-stylesheet-gathering-plugin\n         */\n        // @ts-ignore\n        renderOpts.fontManifest = __webpack_require__.__NEXT_FONT_MANIFEST__\n      }\n\n      let result = await renderToHTML(\n        req,\n        res,\n        page,\n        Object.assign(\n          {},\n          getStaticProps\n            ? { ...(parsedUrl.query.amp ? { amp: '1' } : {}) }\n            : parsedUrl.query,\n          nowParams ? nowParams : params,\n          _params,\n          isFallback ? { __nextFallback: 'true' } : {}\n        ),\n        renderOpts\n      )\n\n      if (!renderMode) {\n        if (_nextData || getStaticProps || getServerSideProps) {\n          if (renderOpts.isNotFound) {\n            res.statusCode = 404\n\n            if (_nextData) {\n              res.end('{\"notFound\":true}')\n              return null\n            }\n\n            const NotFoundComponent = notFoundMod ? notFoundMod.default : Error\n            const errPathname = notFoundMod ? '/404' : '/_error'\n\n            const result2 = await renderToHTML(\n              req,\n              res,\n              errPathname,\n              parsedUrl.query,\n              Object.assign({}, options, {\n                getStaticProps: notFoundMod\n                  ? notFoundMod.getStaticProps\n                  : undefined,\n                getStaticPaths: undefined,\n                getServerSideProps: undefined,\n                Component: NotFoundComponent,\n                err: undefined,\n                locale: detectedLocale,\n                locales: i18n?.locales,\n                defaultLocale: i18n?.defaultLocale,\n              })\n            )\n            const html = result2 ? await resultsToString([result2]) : ''\n            sendPayload(\n              req,\n              res,\n              html,\n              'html',\n              {\n                generateEtags,\n                poweredByHeader,\n              },\n              {\n                private: isPreviewMode || page === '/404',\n                stateful: !!getServerSideProps,\n                revalidate: renderOpts.revalidate,\n              }\n            )\n            return null\n          } else if (renderOpts.isRedirect && !_nextData) {\n            const redirect = {\n              destination: renderOpts.pageData.pageProps.__N_REDIRECT,\n              statusCode: renderOpts.pageData.pageProps.__N_REDIRECT_STATUS,\n              basePath: renderOpts.pageData.pageProps.__N_REDIRECT_BASE_PATH,\n            }\n            const statusCode = getRedirectStatus(redirect)\n\n            if (\n              basePath &&\n              redirect.basePath !== false &&\n              redirect.destination.startsWith('/')\n            ) {\n              redirect.destination = `${basePath}${redirect.destination}`\n            }\n\n            if (statusCode === PERMANENT_REDIRECT_STATUS) {\n              res.setHeader('Refresh', `0;url=${redirect.destination}`)\n            }\n\n            res.statusCode = statusCode\n            res.setHeader('Location', redirect.destination)\n            res.end()\n            return null\n          } else {\n            sendPayload(\n              req,\n              res,\n              _nextData ? JSON.stringify(renderOpts.pageData) : result,\n              _nextData ? 'json' : 'html',\n              {\n                generateEtags,\n                poweredByHeader,\n              },\n              {\n                private: isPreviewMode || renderOpts.is404Page,\n                stateful: !!getServerSideProps,\n                revalidate: renderOpts.revalidate,\n              }\n            )\n            return null\n          }\n        }\n      } else if (isPreviewMode) {\n        res.setHeader(\n          'Cache-Control',\n          'private, no-cache, no-store, max-age=0, must-revalidate'\n        )\n      }\n\n      if (renderMode) return { html: result, renderOpts }\n      return result ? await resultsToString([result]) : null\n    } catch (err) {\n      if (!parsedUrl!) {\n        parsedUrl = parseUrl(req.url!, true)\n      }\n\n      if (err.code === 'ENOENT') {\n        res.statusCode = 404\n      } else if (err instanceof DecodeError) {\n        res.statusCode = 400\n      } else {\n        console.error('Unhandled error during request:', err)\n\n        // Backwards compat (call getInitialProps in custom error):\n        try {\n          await renderToHTML(\n            req,\n            res,\n            '/_error',\n            parsedUrl!.query,\n            Object.assign({}, options, {\n              getStaticProps: undefined,\n              getStaticPaths: undefined,\n              getServerSideProps: undefined,\n              Component: Error,\n              err: err,\n              // Short-circuit rendering:\n              isDataReq: true,\n            })\n          )\n        } catch (underErrorErr) {\n          console.error(\n            'Failed call /_error subroutine, continuing to crash function:',\n            underErrorErr\n          )\n        }\n\n        // Throw the error to crash the serverless function\n        if (isResSent(res)) {\n          console.error('!!! WARNING !!!')\n          console.error(\n            'Your function crashed, but closed the response before allowing the function to exit.\\\\n' +\n              'This may cause unexpected behavior for the next request.'\n          )\n          console.error('!!! WARNING !!!')\n        }\n        throw err\n      }\n\n      const result2 = await renderToHTML(\n        req,\n        res,\n        '/_error',\n        parsedUrl!.query,\n        Object.assign({}, options, {\n          getStaticProps: undefined,\n          getStaticPaths: undefined,\n          getServerSideProps: undefined,\n          Component: Error,\n          err: res.statusCode === 404 ? undefined : err,\n        })\n      )\n      return result2 ? await resultsToString([result2]) : null\n    }\n  }\n\n  return {\n    renderReqToHTML,\n    render: async function render(req: IncomingMessage, res: ServerResponse) {\n      try {\n        const html = await renderReqToHTML(req, res)\n        if (html) {\n          sendPayload(req, res, html, 'html', {\n            generateEtags,\n            poweredByHeader,\n          })\n        }\n      } catch (err) {\n        console.error(err)\n        // Throw the error to crash the serverless function\n        throw err\n      }\n    },\n  }\n}\n"], "names": [], "mappings": ";;;;QAegB,cAAc,GAAd,cAAc;AAd6C,GAAK,CAAL,IAAK;AACzC,GAA8B,CAA9B,MAA8B;AACzC,GAAiC,CAAjC,YAAiC;AACA,GAAS,CAAT,OAAS;AAEzC,GAA2B,CAA3B,OAA2B;AACtB,GAA8B,CAA9B,SAA8B;AAC5B,GAA0C,CAA1C,oBAA0C;AAE5C,GAAoC,CAApC,iBAAoC;AACtC,GAA+D,CAA/D,sBAA+D;AACrD,GAAkC,CAAlC,UAAkC;AAC5C,GAA0B,CAA1B,OAA0B;;;;;;SAE1C,cAAc,CAAC,GAAyB,EAAE,CAAC;IACzD,KAAK,GACH,IAAI,GAEJ,aAAa,GACb,UAAU,GACV,kBAAkB,GAClB,kBAAkB,GAClB,sBAAsB,GAEtB,SAAS,GACT,cAAc,GACd,WAAW,GACX,cAAc,GAEd,mBAAmB,GACnB,aAAa,GACb,aAAa,GACb,eAAe,GAEf,aAAa,GACb,aAAa,GACb,qBAAqB,GAErB,IAAI,GACJ,OAAO,GACP,QAAQ,GACR,WAAW,GACX,aAAa,GACb,cAAc,QACZ,GAAG;IACP,KAAK,GACH,YAAY,GACZ,cAAc,GACd,cAAc,GACd,iBAAiB,GACjB,mBAAmB,GACnB,sBAAsB,GACtB,yBAAyB,GACzB,2BAA2B,GAC3B,kBAAkB,YAnDuC,OAAS,WAoDvD,GAAG;mBAED,eAAe,CAC5B,GAAoB,EACpB,GAAmB,EACnB,UAA4C,EAC5C,WAAiB,EACjB,OAAa,EACb,CAAC;QACD,GAAG,CAAC,SAAS;QACb,GAAG,CAAC,GAAG;QACP,GAAG,CAAC,MAAM;QACV,GAAG,CAAC,SAAQ;QACZ,GAAG,CAAC,MAAK;QACT,GAAG,CAAC,WAAW;QACf,GAAG,CAAC,cAAc;QAClB,GAAG,CAAC,cAAc;QAClB,GAAG,CAAC,kBAAkB;SAEpB,cAAc,EACd,kBAAkB,EAClB,cAAc,EACd,SAAS,EACT,GAAG,EACH,MAAM,IACJ,OAAO,EAAE,SAAQ,OACjB,OAAO,EAAE,MAAK,KAChB,WAAW,YACH,OAAO,CAAC,GAAG;YACnB,kBAAkB;YAClB,sBAAsB;YACtB,kBAAkB;YAClB,aAAa;YACb,SAAS;YACT,UAAU;YACV,cAAc;YACd,WAAW;YACX,cAAc;;QAGhB,KAAK,CAAC,UAAU,GAAG,UAAU,MAAK,MAAQ,KAAI,UAAU,KAAK,IAAI;QACjE,KAAK,CAAC,aAAa,GAAG,UAAU,MAAK,WAAa;QAElD,GAAG,CAAC,cAAc,GAAG,IAAI;YA5FK,SAA8B;YA8F9C,GAAG,EAAE,GAAG;YAAW,OAAS,OA9FZ,SAA8B,kBA8FA,GAAG,CAAC,OAAO;QAEvE,KAAK,CAAC,OAAO;YACX,GAAG;YACH,QAAQ,EAAR,SAAQ;YACR,aAAa;YACb,cAAc;YACd,kBAAkB;YAClB,cAAc;YACd,qBAAqB;YACrB,aAAa;YACb,OAAO;YACP,WAAW;YACX,aAAa,GAAG,aAAa;eAAQ,mBAAmB;;YACxD,YAAY,EAAE,mBAAmB;YACjC,GAAG,EAAE,OAAO,CAAC,GAAG;YAChB,QAAQ;YACR,iBAAiB,EAAE,IAAI;eACpB,WAAW;;QAEhB,GAAG,CAAC,SAAS,GAAG,KAAK;QACrB,GAAG,CAAC,aAAa,GAAG,IAAI,aAAJ,IAAI,UAAJ,CAAmB,QAAnB,CAAmB,GAAnB,IAAI,CAAE,aAAa;QACvC,GAAG,CAAC,cAAc,GAAG,IAAI,aAAJ,IAAI,UAAJ,CAAmB,QAAnB,CAAmB,GAAnB,IAAI,CAAE,aAAa;QACxC,GAAG,CAAC,SAAS;YAET,CAAC;gBA+FD,GAAW;YA9Fb,EAA2D,AAA3D,yDAA2D;YAC3D,EAA4C,AAA5C,0CAA4C;YAC5C,KAAK,CAAC,UAAU,IAAI,cAAc,IAAI,GAAG,CAAC,OAAO,CA7HM,OAAS;YA8HhE,SAAS,OAjI4D,IAAK,QAiIrD,GAAG,CAAC,GAAG,EAAG,IAAI;YACnC,GAAG,CAAC,gBAAgB,GAAG,SAAS,CAAC,QAAQ;YAEzC,EAAE,EAAE,QAAQ,EAAE,CAAC;gBACb,gBAAgB,GACd,gBAAgB,CAAC,OAAO,CAAC,GAAG,CAAC,MAAM,EAAE,CAAC,EAAE,QAAQ,aAAY,CAAG;YACnE,CAAC;YACD,KAAK,CAAC,SAAS,GAAG,MAAM,CAAC,MAAM;eAAK,SAAS,CAAC,KAAK;YAEnD,SAAS,GAAG,cAAc,CAAC,GAAG,EAAE,SAAS;YACzC,cAAc,CAAC,GAAG,EAAE,SAAS;YAE7B,EAAyD,AAAzD,uDAAyD;YACzD,EAAE,EAAE,UAAU,IAAI,SAAS,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC;gBACtC,KAAK,CAAC,UAAU,GAAG,MAAM,CAAC,MAAM;mBAAK,SAAS;uBACvC,UAAU,CAAC,GAAG;gBAErB,GAAG,CAAC,GAAG,OAlJ4D,IAAK;uBAmJnE,SAAS;oBACZ,MAAM,EAAE,SAAS;oBACjB,KAAK,EAAE,UAAU;;YAErB,CAAC;YAED,EAAE,EAAE,SAAS,CAAC,QAAQ,CAAE,KAAK,iBAAiB,CAAC;gBAC7C,SAAS,GAAG,IAAI,MAAK,OAAS;gBAC9B,SAAS,CAAC,QAAQ,OAjJM,sBAA+D,UAkJrF,SAAS,CAAC,QAAQ,CAAE,OAAO,CACzB,GAAG,CAAC,MAAM,EAAE,YAAY,EAAE,cAAc,CAAC,CAAC,KAC1C,CAAG,KAEL,KAAO;gBAET,gBAAgB,GAAG,SAAS,CAAC,QAAQ;YACvC,CAAC;YAED,KAAK,CAAC,YAAY,GAAG,YAAY,CAC/B,GAAG,EACH,GAAG,EACH,SAAS,EACT,gBAAgB,EAChB,UAAU,IAAI,aAAa;YAE7B,aAAa,IAAG,YAAY,aAAZ,YAAY,UAAZ,CAA2B,QAA3B,CAA2B,GAA3B,YAAY,CAAE,aAAa,KAAI,aAAa;YAC5D,cAAc,IAAG,YAAY,aAAZ,YAAY,UAAZ,CAA4B,QAA5B,CAA4B,GAA5B,YAAY,CAAE,cAAc,KAAI,cAAc;YAC/D,gBAAgB,IAAG,YAAY,aAAZ,YAAY,UAAZ,CAA8B,QAA9B,CAA8B,GAA9B,YAAY,CAAE,gBAAgB,KAAI,gBAAgB;YAErE,EAAE,EAAE,SAAS,CAAC,KAAK,CAAC,kBAAkB,EAAE,CAAC;gBACvC,cAAc,GAAG,SAAS,CAAC,KAAK,CAAC,kBAAkB;uBAC5C,SAAS,CAAC,KAAK,CAAC,kBAAkB;YAC3C,CAAC;YAED,KAAK,CAAC,UAAU,GAAG,MAAM,CAAC,MAAM;gBAE5B,SAAS;gBACT,UAAU,EAAE,MAAM;gBAClB,UAAU,EAAE,UAAU;gBACtB,SAAS,EAAE,SAAS;gBACpB,OAAO,EAAE,IAAI,aAAJ,IAAI,UAAJ,CAAa,QAAb,CAAa,GAAb,IAAI,CAAE,OAAO;gBACtB,MAAM,EAAE,cAAc;gBACtB,aAAa;gBACb,aAAa,EAAE,IAAI,aAAJ,IAAI,UAAJ,CAAa,QAAb,CAAa,GAAb,IAAI,CAAE,OAAO;eAE9B,OAAO;YAGT,EAAE,EAAE,IAAI,MAAK,OAAS,MAAK,GAAG,CAAC,UAAU,EAAE,CAAC;gBAC1C,GAAG,CAAC,UAAU,GAAG,GAAG;YACtB,CAAC;YAED,GAAG,CAAC,MAAM;;YAEV,EAAE,GAAG,UAAU,IAAI,aAAa,EAAE,CAAC;gBACjC,KAAK,CAAC,MAAM,GAAG,2BAA2B,CACxC,UAAU,GACN,SAAS,CAAC,KAAK,GACd,mBAAmB,CAAE,SAAS,CAAC,QAAQ;gBAM9C,cAAc,GAAG,MAAM,CAAC,cAAc;gBACtC,MAAM,GAAG,MAAM,CAAC,MAAM;YACxB,CAAC;YAED,GAAG,CAAC,SAAS,GAAG,IAAI;YAEpB,EAAE,EACA,aAAa,KACZ,cAAc,MACf,GAAW,GAAX,GAAG,CAAC,OAAO,cAAX,GAAW,UAAX,CAAoC,QAApC,CAAoC,GAApC,GAAW,EAAG,mBAAqB,KACnC,CAAC;gBACD,SAAS,GAAG,yBAAyB,CAAC,GAAG,EAAE,UAAU,EAAE,cAAc;YACvE,CAAC;YAED,EAAiE,AAAjE,+DAAiE;YACjE,EAA+D,AAA/D,6DAA+D;YAC/D,UAAU,CAAC,MAAM,GAAG,OAAO,IAAI,MAAM;YAErC,kBAAkB,CAAC,GAAG,IAAI,UAAU;YAEpC,EAAuE,AAAvE,qEAAuE;YACvE,EAAqE,AAArE,mEAAqE;YACrE,EAAE,EAAE,aAAa,IAAI,SAAS,IAAI,iBAAiB,EAAE,CAAC;gBACpD,KAAK,CAAC,UAAU,OA1OmD,IAAK,QA0O5C,GAAG,CAAC,GAAG;gBAEnC,UAAU,CAAC,QAAQ,GAAG,sBAAsB,CAC1C,UAAU,CAAC,QAAQ,EACnB,SAAS;gBAEX,SAAS,CAAC,QAAQ,GAAG,UAAU,CAAC,QAAQ;gBACxC,GAAG,CAAC,GAAG,OAjP4D,IAAK,SAiPpD,UAAU;YAChC,CAAC;YAED,EAAuE,AAAvE,qEAAuE;YACvE,EAA4D,AAA5D,0DAA4D;YAC5D,EAAE,GAAG,UAAU,KAAK,cAAc,IAAI,kBAAkB,GAAG,CAAC;gBAC1D,EAAgE,AAAhE,8DAAgE;gBAChE,EAAS,AAAT,OAAS;gBACT,EAAE,EAAE,aAAa,IAAI,UAAU,IAAI,iBAAiB,EAAE,CAAC;2BAC7C,SAAS,CAAS,MAAM;yBAE3B,KAAK,CAAC,KAAK,IAAI,MAAM,CAAC,IAAI,CAAC,iBAAiB,CAAC,MAAM,EAAG,CAAC;+BACnD,SAAS,CAAC,KAAK;oBACxB,CAAC;gBACH,CAAC;gBAED,SAAS,CAAC,QAAQ,OA1PU,oBAA0C,sBA0P7B,SAAS,CAAC,QAAQ;gBAC3D,UAAU,CAAC,WAAW,OAlQ6C,IAAK;uBAmQnE,SAAS;oBACZ,KAAK,EAAE,SAAS;;gBAGlB,EAAmE,AAAnE,iEAAmE;gBACnE,EAAqE,AAArE,mEAAqE;gBACrE,UAAU,CAAC,cAAc,GAAG,kBAAkB,OAzQqB,IAAK;uBA2Q/D,SAAS;oBACZ,QAAQ,EAAE,gBAAgB;oBAC1B,KAAK,EAAE,SAAS;qBAElB,UAAU,CAAC,WAAW;YAC5B,CAAC;YAED,KAAK,CAAC,UAAU,GAAG,SAAS,CAAC,KAAK,CAAC,cAAc;YAEjD,KAAK,CAAC,WAAW,OA9QW,SAA8B,oBA8QpB,GAAG,EAAE,GAAG,EAAE,OAAO,CAAC,YAAY;YACpE,KAAK,CAAC,aAAa,GAAG,WAAW,KAAK,KAAK;YAE3C,EAAE,EAAE,OAAO,CAAC,GAAG,CAAC,qBAAqB,EAAE,CAAC;gBACtC,UAAU,CAAC,aAAa,GAAG,IAAI;gBAC/B,EAGG,AAHH;;;SAGG,AAHH,EAGG,CACH,EAAa,AAAb,WAAa;gBACb,UAAU,CAAC,YAAY,GAAG,mBAAmB,CAAC,sBAAsB;YACtE,CAAC;YAED,GAAG,CAAC,MAAM,aA5Ra,OAA2B,eA6RhD,GAAG,EACH,GAAG,EACH,IAAI,EACJ,MAAM,CAAC,MAAM;eAEX,cAAc;mBACJ,SAAS,CAAC,KAAK,CAAC,GAAG;oBAAK,GAAG,GAAE,CAAG;;;gBACtC,SAAS,CAAC,KAAK,EACnB,SAAS,GAAG,SAAS,GAAG,MAAM,EAC9B,OAAO,EACP,UAAU;gBAAK,cAAc,GAAE,IAAM;;gBAEvC,UAAU;YAGZ,EAAE,GAAG,UAAU,EAAE,CAAC;gBAChB,EAAE,EAAE,SAAS,IAAI,cAAc,IAAI,kBAAkB,EAAE,CAAC;oBACtD,EAAE,EAAE,UAAU,CAAC,UAAU,EAAE,CAAC;wBAC1B,GAAG,CAAC,UAAU,GAAG,GAAG;wBAEpB,EAAE,EAAE,SAAS,EAAE,CAAC;4BACd,GAAG,CAAC,GAAG,EAAC,iBAAmB;mCACpB,IAAI;wBACb,CAAC;wBAED,KAAK,CAAC,iBAAiB,GAAG,WAAW,GAAG,WAAW,CAAC,OAAO,GAAG,MAAK;wBACnE,KAAK,CAAC,WAAW,GAAG,WAAW,IAAG,IAAM,KAAG,OAAS;wBAEpD,KAAK,CAAC,OAAO,aAzTI,OAA2B,eA0T1C,GAAG,EACH,GAAG,EACH,WAAW,EACX,SAAS,CAAC,KAAK,EACf,MAAM,CAAC,MAAM;2BAAK,OAAO;4BACvB,cAAc,EAAE,WAAW,GACvB,WAAW,CAAC,cAAc,GAC1B,SAAS;4BACb,cAAc,EAAE,SAAS;4BACzB,kBAAkB,EAAE,SAAS;4BAC7B,SAAS,EAAE,iBAAiB;4BAC5B,GAAG,EAAE,SAAS;4BACd,MAAM,EAAE,cAAc;4BACtB,OAAO,EAAE,IAAI,aAAJ,IAAI,UAAJ,CAAa,QAAb,CAAa,GAAb,IAAI,CAAE,OAAO;4BACtB,aAAa,EAAE,IAAI,aAAJ,IAAI,UAAJ,CAAmB,QAAnB,CAAmB,GAAnB,IAAI,CAAE,aAAa;;wBAGtC,KAAK,CAAC,IAAI,GAAG,OAAO,aApUA,OAA0B;4BAoUA,OAAO;;4BA9UrC,YAAiC,cAgV/C,GAAG,EACH,GAAG,EACH,IAAI,GACJ,IAAM;4BAEJ,aAAa;4BACb,eAAe;;4BAGf,OAAO,EAAE,aAAa,IAAI,IAAI,MAAK,IAAM;4BACzC,QAAQ,IAAI,kBAAkB;4BAC9B,UAAU,EAAE,UAAU,CAAC,UAAU;;+BAG9B,IAAI;oBACb,CAAC,MAAM,EAAE,EAAE,UAAU,CAAC,UAAU,KAAK,SAAS,EAAE,CAAC;wBAC/C,KAAK,CAAC,QAAQ;4BACZ,WAAW,EAAE,UAAU,CAAC,QAAQ,CAAC,SAAS,CAAC,YAAY;4BACvD,UAAU,EAAE,UAAU,CAAC,QAAQ,CAAC,SAAS,CAAC,mBAAmB;4BAC7D,QAAQ,EAAE,UAAU,CAAC,QAAQ,CAAC,SAAS,CAAC,sBAAsB;;wBAEhE,KAAK,CAAC,UAAU,OA9VM,iBAAoC,oBA8VrB,QAAQ;wBAE7C,EAAE,EACA,QAAQ,IACR,QAAQ,CAAC,QAAQ,KAAK,KAAK,IAC3B,QAAQ,CAAC,WAAW,CAAC,UAAU,EAAC,CAAG,IACnC,CAAC;4BACD,QAAQ,CAAC,WAAW,MAAM,QAAQ,GAAG,QAAQ,CAAC,WAAW;wBAC3D,CAAC;wBAED,EAAE,EAAE,UAAU,KAtWgB,UAAkC,4BAsWlB,CAAC;4BAC7C,GAAG,CAAC,SAAS,EAAC,OAAS,IAAG,MAAM,EAAE,QAAQ,CAAC,WAAW;wBACxD,CAAC;wBAED,GAAG,CAAC,UAAU,GAAG,UAAU;wBAC3B,GAAG,CAAC,SAAS,EAAC,QAAU,GAAE,QAAQ,CAAC,WAAW;wBAC9C,GAAG,CAAC,GAAG;+BACA,IAAI;oBACb,CAAC,MAAM,CAAC;4BAvXU,YAAiC,cAyX/C,GAAG,EACH,GAAG,EACH,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,QAAQ,IAAI,MAAM,EACxD,SAAS,IAAG,IAAM,KAAG,IAAM;4BAEzB,aAAa;4BACb,eAAe;;4BAGf,OAAO,EAAE,aAAa,IAAI,UAAU,CAAC,SAAS;4BAC9C,QAAQ,IAAI,kBAAkB;4BAC9B,UAAU,EAAE,UAAU,CAAC,UAAU;;+BAG9B,IAAI;oBACb,CAAC;gBACH,CAAC;YACH,CAAC,MAAM,EAAE,EAAE,aAAa,EAAE,CAAC;gBACzB,GAAG,CAAC,SAAS,EACX,aAAe,IACf,uDAAyD;YAE7D,CAAC;YAED,EAAE,EAAE,UAAU;gBAAW,IAAI,EAAE,MAAM;gBAAE,UAAU;;mBAC1C,MAAM,aAxYa,OAA0B;gBAwYb,MAAM;iBAAK,IAAI;QACxD,CAAC,QAAQ,GAAG,EAAE,CAAC;YACb,EAAE,GAAG,SAAS,EAAG,CAAC;gBAChB,SAAS,OAvZ0D,IAAK,QAuZnD,GAAG,CAAC,GAAG,EAAG,IAAI;YACrC,CAAC;YAED,EAAE,EAAE,GAAG,CAAC,IAAI,MAAK,MAAQ,GAAE,CAAC;gBAC1B,GAAG,CAAC,UAAU,GAAG,GAAG;YACtB,CAAC,MAAM,EAAE,EAAE,GAAG,YA3ZmB,MAA8B,cA2ZxB,CAAC;gBACtC,GAAG,CAAC,UAAU,GAAG,GAAG;YACtB,CAAC,MAAM,CAAC;gBACN,OAAO,CAAC,KAAK,EAAC,+BAAiC,GAAE,GAAG;gBAEpD,EAA2D,AAA3D,yDAA2D;oBACvD,CAAC;8BA7ZgB,OAA2B,eA+Z5C,GAAG,EACH,GAAG,GACH,OAAS,GACT,SAAS,CAAE,KAAK,EAChB,MAAM,CAAC,MAAM;uBAAK,OAAO;wBACvB,cAAc,EAAE,SAAS;wBACzB,cAAc,EAAE,SAAS;wBACzB,kBAAkB,EAAE,SAAS;wBAC7B,SAAS,EAAE,MAAK;wBAChB,GAAG,EAAE,GAAG;wBACR,EAA2B,AAA3B,yBAA2B;wBAC3B,SAAS,EAAE,IAAI;;gBAGrB,CAAC,QAAQ,aAAa,EAAE,CAAC;oBACvB,OAAO,CAAC,KAAK,EACX,6DAA+D,GAC/D,aAAa;gBAEjB,CAAC;gBAED,EAAmD,AAAnD,iDAAmD;gBACnD,EAAE,MAzb6B,MAA8B,YAyb/C,GAAG,GAAG,CAAC;oBACnB,OAAO,CAAC,KAAK,EAAC,eAAiB;oBAC/B,OAAO,CAAC,KAAK,EACX,uFAAyF,KACvF,wDAA0D;oBAE9D,OAAO,CAAC,KAAK,EAAC,eAAiB;gBACjC,CAAC;gBACD,KAAK,CAAC,GAAG;YACX,CAAC;YAED,KAAK,CAAC,OAAO,aAhcU,OAA2B,eAichD,GAAG,EACH,GAAG,GACH,OAAS,GACT,SAAS,CAAE,KAAK,EAChB,MAAM,CAAC,MAAM;eAAK,OAAO;gBACvB,cAAc,EAAE,SAAS;gBACzB,cAAc,EAAE,SAAS;gBACzB,kBAAkB,EAAE,SAAS;gBAC7B,SAAS,EAAE,MAAK;gBAChB,GAAG,EAAE,GAAG,CAAC,UAAU,KAAK,GAAG,GAAG,SAAS,GAAG,GAAG;;mBAG1C,OAAO,aAtcY,OAA0B;gBAscZ,OAAO;iBAAK,IAAI;QAC1D,CAAC;IACH,CAAC;;QAGC,eAAe;QACf,MAAM,iBAAiB,MAAM,CAAC,GAAoB,EAAE,GAAmB,EAAE,CAAC;gBACpE,CAAC;gBACH,KAAK,CAAC,IAAI,SAAS,eAAe,CAAC,GAAG,EAAE,GAAG;gBAC3C,EAAE,EAAE,IAAI,EAAE,CAAC;wBAzdS,YAAiC,cA0dvC,GAAG,EAAE,GAAG,EAAE,IAAI,GAAE,IAAM;wBAChC,aAAa;wBACb,eAAe;;gBAEnB,CAAC;YACH,CAAC,QAAQ,GAAG,EAAE,CAAC;gBACb,OAAO,CAAC,KAAK,CAAC,GAAG;gBACjB,EAAmD,AAAnD,iDAAmD;gBACnD,KAAK,CAAC,GAAG;YACX,CAAC;QACH,CAAC;;AAEL,CAAC"}