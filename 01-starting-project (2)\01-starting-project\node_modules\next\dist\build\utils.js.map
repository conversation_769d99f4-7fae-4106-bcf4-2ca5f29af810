{"version": 3, "sources": ["../../build/utils.ts"], "sourcesContent": ["import '../server/node-polyfill-fetch'\nimport chalk from 'chalk'\nimport getGzipSize from 'next/dist/compiled/gzip-size'\nimport textTable from 'next/dist/compiled/text-table'\nimport path from 'path'\nimport { promises as fs } from 'fs'\nimport { isValidElementType } from 'react-is'\nimport stripAnsi from 'next/dist/compiled/strip-ansi'\nimport {\n  Redirect,\n  Rewrite,\n  Header,\n  CustomRoutes,\n} from '../lib/load-custom-routes'\nimport {\n  SSG_GET_INITIAL_PROPS_CONFLICT,\n  SERVER_PROPS_GET_INIT_PROPS_CONFLICT,\n  SERVER_PROPS_SSG_CONFLICT,\n} from '../lib/constants'\nimport prettyBytes from '../lib/pretty-bytes'\nimport { recursiveReadDir } from '../lib/recursive-readdir'\nimport { getRouteMatcher, getRouteRegex } from '../shared/lib/router/utils'\nimport { isDynamicRoute } from '../shared/lib/router/utils/is-dynamic'\nimport escapePathDelimiters from '../shared/lib/router/utils/escape-path-delimiters'\nimport { findPageFile } from '../server/lib/find-page-file'\nimport { GetStaticPaths, PageConfig } from 'next/types'\nimport { denormalizePagePath } from '../server/normalize-page-path'\nimport { BuildManifest } from '../server/get-page-files'\nimport { removePathTrailingSlash } from '../client/normalize-trailing-slash'\nimport { UnwrapPromise } from '../lib/coalesced-function'\nimport { normalizeLocalePath } from '../shared/lib/i18n/normalize-locale-path'\nimport * as Log from './output/log'\nimport { loadComponents } from '../server/load-components'\nimport { trace } from '../telemetry/trace'\nimport { setHttpAgentOptions } from '../server/config'\nimport { NextConfigComplete } from '../server/config-shared'\n\nconst fileGzipStats: { [k: string]: Promise<number> | undefined } = {}\nconst fsStatGzip = (file: string) => {\n  const cached = fileGzipStats[file]\n  if (cached) return cached\n  return (fileGzipStats[file] = getGzipSize.file(file))\n}\n\nconst fileSize = async (file: string) => (await fs.stat(file)).size\n\nconst fileStats: { [k: string]: Promise<number> | undefined } = {}\nconst fsStat = (file: string) => {\n  const cached = fileStats[file]\n  if (cached) return cached\n  return (fileStats[file] = fileSize(file))\n}\n\nexport function collectPages(\n  directory: string,\n  pageExtensions: string[]\n): Promise<string[]> {\n  return recursiveReadDir(\n    directory,\n    new RegExp(`\\\\.(?:${pageExtensions.join('|')})$`)\n  )\n}\n\nexport interface PageInfo {\n  isHybridAmp?: boolean\n  size: number\n  totalSize: number\n  static: boolean\n  isSsg: boolean\n  ssgPageRoutes: string[] | null\n  initialRevalidateSeconds: number | false\n  pageDuration: number | undefined\n  ssgPageDurations: number[] | undefined\n}\n\nexport async function printTreeView(\n  list: readonly string[],\n  pageInfos: Map<string, PageInfo>,\n  serverless: boolean,\n  {\n    distPath,\n    buildId,\n    pagesDir,\n    pageExtensions,\n    buildManifest,\n    useStatic404,\n    gzipSize = true,\n  }: {\n    distPath: string\n    buildId: string\n    pagesDir: string\n    pageExtensions: string[]\n    buildManifest: BuildManifest\n    useStatic404: boolean\n    gzipSize?: boolean\n  }\n) {\n  const getPrettySize = (_size: number): string => {\n    const size = prettyBytes(_size)\n    // green for 0-130kb\n    if (_size < 130 * 1000) return chalk.green(size)\n    // yellow for 130-170kb\n    if (_size < 170 * 1000) return chalk.yellow(size)\n    // red for >= 170kb\n    return chalk.red.bold(size)\n  }\n\n  const MIN_DURATION = 300\n  const getPrettyDuration = (_duration: number): string => {\n    const duration = `${_duration} ms`\n    // green for 300-1000ms\n    if (_duration < 1000) return chalk.green(duration)\n    // yellow for 1000-2000ms\n    if (_duration < 2000) return chalk.yellow(duration)\n    // red for >= 2000ms\n    return chalk.red.bold(duration)\n  }\n\n  const getCleanName = (fileName: string) =>\n    fileName\n      // Trim off `static/`\n      .replace(/^static\\//, '')\n      // Re-add `static/` for root files\n      .replace(/^<buildId>/, 'static')\n      // Remove file hash\n      .replace(/(?:^|[.-])([0-9a-z]{6})[0-9a-z]{14}(?=\\.)/, '.$1')\n\n  const messages: [string, string, string][] = [\n    ['Page', 'Size', 'First Load JS'].map((entry) =>\n      chalk.underline(entry)\n    ) as [string, string, string],\n  ]\n\n  const hasCustomApp = await findPageFile(pagesDir, '/_app', pageExtensions)\n\n  pageInfos.set('/404', {\n    ...(pageInfos.get('/404') || pageInfos.get('/_error')),\n    static: useStatic404,\n  } as any)\n\n  if (!list.includes('/404')) {\n    list = [...list, '/404']\n  }\n\n  const sizeData = await computeFromManifest(\n    buildManifest,\n    distPath,\n    gzipSize,\n    pageInfos\n  )\n\n  const pageList = list\n    .slice()\n    .filter(\n      (e) =>\n        !(\n          e === '/_document' ||\n          e === '/_error' ||\n          (!hasCustomApp && e === '/_app')\n        )\n    )\n    .sort((a, b) => a.localeCompare(b))\n\n  pageList.forEach((item, i, arr) => {\n    const symbol =\n      i === 0\n        ? arr.length === 1\n          ? '─'\n          : '┌'\n        : i === arr.length - 1\n        ? '└'\n        : '├'\n\n    const pageInfo = pageInfos.get(item)\n    const ampFirst = buildManifest.ampFirstPages.includes(item)\n\n    const totalDuration =\n      (pageInfo?.pageDuration || 0) +\n      (pageInfo?.ssgPageDurations?.reduce((a, b) => a + (b || 0), 0) || 0)\n\n    messages.push([\n      `${symbol} ${\n        item === '/_app'\n          ? ' '\n          : pageInfo?.static\n          ? '○'\n          : pageInfo?.isSsg\n          ? '●'\n          : 'λ'\n      } ${\n        pageInfo?.initialRevalidateSeconds\n          ? `${item} (ISR: ${pageInfo?.initialRevalidateSeconds} Seconds)`\n          : item\n      }${\n        totalDuration > MIN_DURATION\n          ? ` (${getPrettyDuration(totalDuration)})`\n          : ''\n      }`,\n      pageInfo\n        ? ampFirst\n          ? chalk.cyan('AMP')\n          : pageInfo.size >= 0\n          ? prettyBytes(pageInfo.size)\n          : ''\n        : '',\n      pageInfo\n        ? ampFirst\n          ? chalk.cyan('AMP')\n          : pageInfo.size >= 0\n          ? getPrettySize(pageInfo.totalSize)\n          : ''\n        : '',\n    ])\n\n    const uniqueCssFiles =\n      buildManifest.pages[item]?.filter(\n        (file) => file.endsWith('.css') && sizeData.uniqueFiles.includes(file)\n      ) || []\n\n    if (uniqueCssFiles.length > 0) {\n      const contSymbol = i === arr.length - 1 ? ' ' : '├'\n\n      uniqueCssFiles.forEach((file, index, { length }) => {\n        const innerSymbol = index === length - 1 ? '└' : '├'\n        messages.push([\n          `${contSymbol}   ${innerSymbol} ${getCleanName(file)}`,\n          prettyBytes(sizeData.sizeUniqueFiles[file]),\n          '',\n        ])\n      })\n    }\n\n    if (pageInfo?.ssgPageRoutes?.length) {\n      const totalRoutes = pageInfo.ssgPageRoutes.length\n      const contSymbol = i === arr.length - 1 ? ' ' : '├'\n\n      let routes: { route: string; duration: number; avgDuration?: number }[]\n      if (\n        pageInfo.ssgPageDurations &&\n        pageInfo.ssgPageDurations.some((d) => d > MIN_DURATION)\n      ) {\n        const previewPages = totalRoutes === 8 ? 8 : Math.min(totalRoutes, 7)\n        const routesWithDuration = pageInfo.ssgPageRoutes\n          .map((route, idx) => ({\n            route,\n            duration: pageInfo.ssgPageDurations![idx] || 0,\n          }))\n          .sort(({ duration: a }, { duration: b }) =>\n            // Sort by duration\n            // keep too small durations in original order at the end\n            a <= MIN_DURATION && b <= MIN_DURATION ? 0 : b - a\n          )\n        routes = routesWithDuration.slice(0, previewPages)\n        const remainingRoutes = routesWithDuration.slice(previewPages)\n        if (remainingRoutes.length) {\n          const remaining = remainingRoutes.length\n          const avgDuration = Math.round(\n            remainingRoutes.reduce(\n              (total, { duration }) => total + duration,\n              0\n            ) / remainingRoutes.length\n          )\n          routes.push({\n            route: `[+${remaining} more paths]`,\n            duration: 0,\n            avgDuration,\n          })\n        }\n      } else {\n        const previewPages = totalRoutes === 4 ? 4 : Math.min(totalRoutes, 3)\n        routes = pageInfo.ssgPageRoutes\n          .slice(0, previewPages)\n          .map((route) => ({ route, duration: 0 }))\n        if (totalRoutes > previewPages) {\n          const remaining = totalRoutes - previewPages\n          routes.push({ route: `[+${remaining} more paths]`, duration: 0 })\n        }\n      }\n\n      routes.forEach(({ route, duration, avgDuration }, index, { length }) => {\n        const innerSymbol = index === length - 1 ? '└' : '├'\n        messages.push([\n          `${contSymbol}   ${innerSymbol} ${route}${\n            duration > MIN_DURATION ? ` (${getPrettyDuration(duration)})` : ''\n          }${\n            avgDuration && avgDuration > MIN_DURATION\n              ? ` (avg ${getPrettyDuration(avgDuration)})`\n              : ''\n          }`,\n          '',\n          '',\n        ])\n      })\n    }\n  })\n\n  const sharedFilesSize = sizeData.sizeCommonFiles\n  const sharedFiles = sizeData.sizeCommonFile\n\n  messages.push([\n    '+ First Load JS shared by all',\n    getPrettySize(sharedFilesSize),\n    '',\n  ])\n  const sharedFileKeys = Object.keys(sharedFiles)\n  const sharedCssFiles: string[] = []\n  ;[\n    ...sharedFileKeys\n      .filter((file) => {\n        if (file.endsWith('.css')) {\n          sharedCssFiles.push(file)\n          return false\n        }\n        return true\n      })\n      .map((e) => e.replace(buildId, '<buildId>'))\n      .sort(),\n    ...sharedCssFiles.map((e) => e.replace(buildId, '<buildId>')).sort(),\n  ].forEach((fileName, index, { length }) => {\n    const innerSymbol = index === length - 1 ? '└' : '├'\n\n    const originalName = fileName.replace('<buildId>', buildId)\n    const cleanName = getCleanName(fileName)\n\n    messages.push([\n      `  ${innerSymbol} ${cleanName}`,\n      prettyBytes(sharedFiles[originalName]),\n      '',\n    ])\n  })\n\n  console.log(\n    textTable(messages, {\n      align: ['l', 'l', 'r'],\n      stringLength: (str) => stripAnsi(str).length,\n    })\n  )\n\n  console.log()\n  console.log(\n    textTable(\n      [\n        [\n          'λ',\n          serverless ? '(Lambda)' : '(Server)',\n          `server-side renders at runtime (uses ${chalk.cyan(\n            'getInitialProps'\n          )} or ${chalk.cyan('getServerSideProps')})`,\n        ],\n        [\n          '○',\n          '(Static)',\n          'automatically rendered as static HTML (uses no initial props)',\n        ],\n        [\n          '●',\n          '(SSG)',\n          `automatically generated as static HTML + JSON (uses ${chalk.cyan(\n            'getStaticProps'\n          )})`,\n        ],\n        [\n          '',\n          '(ISR)',\n          `incremental static regeneration (uses revalidate in ${chalk.cyan(\n            'getStaticProps'\n          )})`,\n        ],\n      ] as [string, string, string][],\n      {\n        align: ['l', 'l', 'l'],\n        stringLength: (str) => stripAnsi(str).length,\n      }\n    )\n  )\n\n  console.log()\n}\n\nexport function printCustomRoutes({\n  redirects,\n  rewrites,\n  headers,\n}: CustomRoutes) {\n  const printRoutes = (\n    routes: Redirect[] | Rewrite[] | Header[],\n    type: 'Redirects' | 'Rewrites' | 'Headers'\n  ) => {\n    const isRedirects = type === 'Redirects'\n    const isHeaders = type === 'Headers'\n    console.log(chalk.underline(type))\n    console.log()\n\n    /*\n        ┌ source\n        ├ permanent/statusCode\n        └ destination\n     */\n    const routesStr = (routes as any[])\n      .map((route: { source: string }) => {\n        let routeStr = `┌ source: ${route.source}\\n`\n\n        if (!isHeaders) {\n          const r = route as Rewrite\n          routeStr += `${isRedirects ? '├' : '└'} destination: ${\n            r.destination\n          }\\n`\n        }\n        if (isRedirects) {\n          const r = route as Redirect\n          routeStr += `└ ${\n            r.statusCode\n              ? `status: ${r.statusCode}`\n              : `permanent: ${r.permanent}`\n          }\\n`\n        }\n\n        if (isHeaders) {\n          const r = route as Header\n          routeStr += `└ headers:\\n`\n\n          for (let i = 0; i < r.headers.length; i++) {\n            const header = r.headers[i]\n            const last = i === headers.length - 1\n\n            routeStr += `  ${last ? '└' : '├'} ${header.key}: ${header.value}\\n`\n          }\n        }\n\n        return routeStr\n      })\n      .join('\\n')\n\n    console.log(routesStr, '\\n')\n  }\n\n  if (redirects.length) {\n    printRoutes(redirects, 'Redirects')\n  }\n  if (headers.length) {\n    printRoutes(headers, 'Headers')\n  }\n\n  const combinedRewrites = [\n    ...rewrites.beforeFiles,\n    ...rewrites.afterFiles,\n    ...rewrites.fallback,\n  ]\n  if (combinedRewrites.length) {\n    printRoutes(combinedRewrites, 'Rewrites')\n  }\n}\n\ntype ComputeManifestShape = {\n  commonFiles: string[]\n  uniqueFiles: string[]\n  sizeUniqueFiles: { [file: string]: number }\n  sizeCommonFile: { [file: string]: number }\n  sizeCommonFiles: number\n}\n\nlet cachedBuildManifest: BuildManifest | undefined\n\nlet lastCompute: ComputeManifestShape | undefined\nlet lastComputePageInfo: boolean | undefined\n\nexport async function computeFromManifest(\n  manifest: BuildManifest,\n  distPath: string,\n  gzipSize: boolean = true,\n  pageInfos?: Map<string, PageInfo>\n): Promise<ComputeManifestShape> {\n  if (\n    Object.is(cachedBuildManifest, manifest) &&\n    lastComputePageInfo === !!pageInfos\n  ) {\n    return lastCompute!\n  }\n\n  let expected = 0\n  const files = new Map<string, number>()\n  Object.keys(manifest.pages).forEach((key) => {\n    if (pageInfos) {\n      const pageInfo = pageInfos.get(key)\n      // don't include AMP pages since they don't rely on shared bundles\n      // AMP First pages are not under the pageInfos key\n      if (pageInfo?.isHybridAmp) {\n        return\n      }\n    }\n\n    ++expected\n    manifest.pages[key].forEach((file) => {\n      if (key === '/_app') {\n        files.set(file, Infinity)\n      } else if (files.has(file)) {\n        files.set(file, files.get(file)! + 1)\n      } else {\n        files.set(file, 1)\n      }\n    })\n  })\n\n  const getSize = gzipSize ? fsStatGzip : fsStat\n\n  const commonFiles = [...files.entries()]\n    .filter(([, len]) => len === expected || len === Infinity)\n    .map(([f]) => f)\n  const uniqueFiles = [...files.entries()]\n    .filter(([, len]) => len === 1)\n    .map(([f]) => f)\n\n  let stats: [string, number][]\n  try {\n    stats = await Promise.all(\n      commonFiles.map(\n        async (f) =>\n          [f, await getSize(path.join(distPath, f))] as [string, number]\n      )\n    )\n  } catch (_) {\n    stats = []\n  }\n\n  let uniqueStats: [string, number][]\n  try {\n    uniqueStats = await Promise.all(\n      uniqueFiles.map(\n        async (f) =>\n          [f, await getSize(path.join(distPath, f))] as [string, number]\n      )\n    )\n  } catch (_) {\n    uniqueStats = []\n  }\n\n  lastCompute = {\n    commonFiles,\n    uniqueFiles,\n    sizeUniqueFiles: uniqueStats.reduce(\n      (obj, n) => Object.assign(obj, { [n[0]]: n[1] }),\n      {}\n    ),\n    sizeCommonFile: stats.reduce(\n      (obj, n) => Object.assign(obj, { [n[0]]: n[1] }),\n      {}\n    ),\n    sizeCommonFiles: stats.reduce((size, [f, stat]) => {\n      if (f.endsWith('.css')) return size\n      return size + stat\n    }, 0),\n  }\n\n  cachedBuildManifest = manifest\n  lastComputePageInfo = !!pageInfos\n  return lastCompute!\n}\n\nexport function difference<T>(main: T[] | Set<T>, sub: T[] | Set<T>): T[] {\n  const a = new Set(main)\n  const b = new Set(sub)\n  return [...a].filter((x) => !b.has(x))\n}\n\nfunction intersect<T>(main: T[], sub: T[]): T[] {\n  const a = new Set(main)\n  const b = new Set(sub)\n  return [...new Set([...a].filter((x) => b.has(x)))]\n}\n\nfunction sum(a: number[]): number {\n  return a.reduce((size, stat) => size + stat, 0)\n}\n\nexport async function getJsPageSizeInKb(\n  page: string,\n  distPath: string,\n  buildManifest: BuildManifest,\n  gzipSize: boolean = true,\n  computedManifestData?: ComputeManifestShape\n): Promise<[number, number]> {\n  const data =\n    computedManifestData ||\n    (await computeFromManifest(buildManifest, distPath, gzipSize))\n\n  const fnFilterJs = (entry: string) => entry.endsWith('.js')\n\n  const pageFiles = (\n    buildManifest.pages[denormalizePagePath(page)] || []\n  ).filter(fnFilterJs)\n  const appFiles = (buildManifest.pages['/_app'] || []).filter(fnFilterJs)\n\n  const fnMapRealPath = (dep: string) => `${distPath}/${dep}`\n\n  const allFilesReal = [...new Set([...pageFiles, ...appFiles])].map(\n    fnMapRealPath\n  )\n  const selfFilesReal = difference(\n    intersect(pageFiles, data.uniqueFiles),\n    data.commonFiles\n  ).map(fnMapRealPath)\n\n  const getSize = gzipSize ? fsStatGzip : fsStat\n\n  try {\n    // Doesn't use `Promise.all`, as we'd double compute duplicate files. This\n    // function is memoized, so the second one will instantly resolve.\n    const allFilesSize = sum(await Promise.all(allFilesReal.map(getSize)))\n    const selfFilesSize = sum(await Promise.all(selfFilesReal.map(getSize)))\n\n    return [selfFilesSize, allFilesSize]\n  } catch (_) {}\n  return [-1, -1]\n}\n\nexport async function buildStaticPaths(\n  page: string,\n  getStaticPaths: GetStaticPaths,\n  locales?: string[],\n  defaultLocale?: string\n): Promise<\n  Omit<UnwrapPromise<ReturnType<GetStaticPaths>>, 'paths'> & {\n    paths: string[]\n    encodedPaths: string[]\n  }\n> {\n  const prerenderPaths = new Set<string>()\n  const encodedPrerenderPaths = new Set<string>()\n  const _routeRegex = getRouteRegex(page)\n  const _routeMatcher = getRouteMatcher(_routeRegex)\n\n  // Get the default list of allowed params.\n  const _validParamKeys = Object.keys(_routeMatcher(page))\n\n  const staticPathsResult = await getStaticPaths({ locales, defaultLocale })\n\n  const expectedReturnVal =\n    `Expected: { paths: [], fallback: boolean }\\n` +\n    `See here for more info: https://nextjs.org/docs/messages/invalid-getstaticpaths-value`\n\n  if (\n    !staticPathsResult ||\n    typeof staticPathsResult !== 'object' ||\n    Array.isArray(staticPathsResult)\n  ) {\n    throw new Error(\n      `Invalid value returned from getStaticPaths in ${page}. Received ${typeof staticPathsResult} ${expectedReturnVal}`\n    )\n  }\n\n  const invalidStaticPathKeys = Object.keys(staticPathsResult).filter(\n    (key) => !(key === 'paths' || key === 'fallback')\n  )\n\n  if (invalidStaticPathKeys.length > 0) {\n    throw new Error(\n      `Extra keys returned from getStaticPaths in ${page} (${invalidStaticPathKeys.join(\n        ', '\n      )}) ${expectedReturnVal}`\n    )\n  }\n\n  if (\n    !(\n      typeof staticPathsResult.fallback === 'boolean' ||\n      staticPathsResult.fallback === 'blocking'\n    )\n  ) {\n    throw new Error(\n      `The \\`fallback\\` key must be returned from getStaticPaths in ${page}.\\n` +\n        expectedReturnVal\n    )\n  }\n\n  const toPrerender = staticPathsResult.paths\n\n  if (!Array.isArray(toPrerender)) {\n    throw new Error(\n      `Invalid \\`paths\\` value returned from getStaticPaths in ${page}.\\n` +\n        `\\`paths\\` must be an array of strings or objects of shape { params: [key: string]: string }`\n    )\n  }\n\n  toPrerender.forEach((entry) => {\n    // For a string-provided path, we must make sure it matches the dynamic\n    // route.\n    if (typeof entry === 'string') {\n      entry = removePathTrailingSlash(entry)\n\n      const localePathResult = normalizeLocalePath(entry, locales)\n      let cleanedEntry = entry\n\n      if (localePathResult.detectedLocale) {\n        cleanedEntry = entry.substr(localePathResult.detectedLocale.length + 1)\n      } else if (defaultLocale) {\n        entry = `/${defaultLocale}${entry}`\n      }\n\n      const result = _routeMatcher(cleanedEntry)\n      if (!result) {\n        throw new Error(\n          `The provided path \\`${cleanedEntry}\\` does not match the page: \\`${page}\\`.`\n        )\n      }\n\n      // If leveraging the string paths variant the entry should already be\n      // encoded so we decode the segments ensuring we only escape path\n      // delimiters\n      prerenderPaths.add(\n        entry\n          .split('/')\n          .map((segment) =>\n            escapePathDelimiters(decodeURIComponent(segment), true)\n          )\n          .join('/')\n      )\n      encodedPrerenderPaths.add(entry)\n    }\n    // For the object-provided path, we must make sure it specifies all\n    // required keys.\n    else {\n      const invalidKeys = Object.keys(entry).filter(\n        (key) => key !== 'params' && key !== 'locale'\n      )\n\n      if (invalidKeys.length) {\n        throw new Error(\n          `Additional keys were returned from \\`getStaticPaths\\` in page \"${page}\". ` +\n            `URL Parameters intended for this dynamic route must be nested under the \\`params\\` key, i.e.:` +\n            `\\n\\n\\treturn { params: { ${_validParamKeys\n              .map((k) => `${k}: ...`)\n              .join(', ')} } }` +\n            `\\n\\nKeys that need to be moved: ${invalidKeys.join(', ')}.\\n`\n        )\n      }\n\n      const { params = {} } = entry\n      let builtPage = page\n      let encodedBuiltPage = page\n\n      _validParamKeys.forEach((validParamKey) => {\n        const { repeat, optional } = _routeRegex.groups[validParamKey]\n        let paramValue = params[validParamKey]\n        if (\n          optional &&\n          params.hasOwnProperty(validParamKey) &&\n          (paramValue === null ||\n            paramValue === undefined ||\n            (paramValue as any) === false)\n        ) {\n          paramValue = []\n        }\n        if (\n          (repeat && !Array.isArray(paramValue)) ||\n          (!repeat && typeof paramValue !== 'string')\n        ) {\n          throw new Error(\n            `A required parameter (${validParamKey}) was not provided as ${\n              repeat ? 'an array' : 'a string'\n            } in getStaticPaths for ${page}`\n          )\n        }\n        let replaced = `[${repeat ? '...' : ''}${validParamKey}]`\n        if (optional) {\n          replaced = `[${replaced}]`\n        }\n        builtPage = builtPage\n          .replace(\n            replaced,\n            repeat\n              ? (paramValue as string[])\n                  .map((segment) => escapePathDelimiters(segment, true))\n                  .join('/')\n              : escapePathDelimiters(paramValue as string, true)\n          )\n          .replace(/(?!^)\\/$/, '')\n\n        encodedBuiltPage = encodedBuiltPage\n          .replace(\n            replaced,\n            repeat\n              ? (paramValue as string[]).map(encodeURIComponent).join('/')\n              : encodeURIComponent(paramValue as string)\n          )\n          .replace(/(?!^)\\/$/, '')\n      })\n\n      if (entry.locale && !locales?.includes(entry.locale)) {\n        throw new Error(\n          `Invalid locale returned from getStaticPaths for ${page}, the locale ${entry.locale} is not specified in next.config.js`\n        )\n      }\n      const curLocale = entry.locale || defaultLocale || ''\n\n      prerenderPaths.add(\n        `${curLocale ? `/${curLocale}` : ''}${\n          curLocale && builtPage === '/' ? '' : builtPage\n        }`\n      )\n      encodedPrerenderPaths.add(\n        `${curLocale ? `/${curLocale}` : ''}${\n          curLocale && encodedBuiltPage === '/' ? '' : encodedBuiltPage\n        }`\n      )\n    }\n  })\n\n  return {\n    paths: [...prerenderPaths],\n    fallback: staticPathsResult.fallback,\n    encodedPaths: [...encodedPrerenderPaths],\n  }\n}\n\nexport async function isPageStatic(\n  page: string,\n  distDir: string,\n  serverless: boolean,\n  runtimeEnvConfig: any,\n  httpAgentOptions: NextConfigComplete['httpAgentOptions'],\n  locales?: string[],\n  defaultLocale?: string,\n  parentId?: any\n): Promise<{\n  isStatic?: boolean\n  isAmpOnly?: boolean\n  isHybridAmp?: boolean\n  hasServerProps?: boolean\n  hasStaticProps?: boolean\n  prerenderRoutes?: string[]\n  encodedPrerenderRoutes?: string[]\n  prerenderFallback?: boolean | 'blocking'\n  isNextImageImported?: boolean\n  traceIncludes?: string[]\n  traceExcludes?: string[]\n}> {\n  const isPageStaticSpan = trace('is-page-static-utils', parentId)\n  return isPageStaticSpan.traceAsyncFn(async () => {\n    try {\n      require('../shared/lib/runtime-config').setConfig(runtimeEnvConfig)\n      setHttpAgentOptions(httpAgentOptions)\n      const components = await loadComponents(distDir, page, serverless)\n      const mod = components.ComponentMod\n      const Comp = mod.default || mod\n\n      if (!Comp || !isValidElementType(Comp) || typeof Comp === 'string') {\n        throw new Error('INVALID_DEFAULT_EXPORT')\n      }\n\n      const hasGetInitialProps = !!(Comp as any).getInitialProps\n      const hasStaticProps = !!(await mod.getStaticProps)\n      const hasStaticPaths = !!(await mod.getStaticPaths)\n      const hasServerProps = !!(await mod.getServerSideProps)\n      const hasLegacyServerProps = !!(await mod.unstable_getServerProps)\n      const hasLegacyStaticProps = !!(await mod.unstable_getStaticProps)\n      const hasLegacyStaticPaths = !!(await mod.unstable_getStaticPaths)\n      const hasLegacyStaticParams = !!(await mod.unstable_getStaticParams)\n\n      if (hasLegacyStaticParams) {\n        throw new Error(\n          `unstable_getStaticParams was replaced with getStaticPaths. Please update your code.`\n        )\n      }\n\n      if (hasLegacyStaticPaths) {\n        throw new Error(\n          `unstable_getStaticPaths was replaced with getStaticPaths. Please update your code.`\n        )\n      }\n\n      if (hasLegacyStaticProps) {\n        throw new Error(\n          `unstable_getStaticProps was replaced with getStaticProps. Please update your code.`\n        )\n      }\n\n      if (hasLegacyServerProps) {\n        throw new Error(\n          `unstable_getServerProps was replaced with getServerSideProps. Please update your code.`\n        )\n      }\n\n      // A page cannot be prerendered _and_ define a data requirement. That's\n      // contradictory!\n      if (hasGetInitialProps && hasStaticProps) {\n        throw new Error(SSG_GET_INITIAL_PROPS_CONFLICT)\n      }\n\n      if (hasGetInitialProps && hasServerProps) {\n        throw new Error(SERVER_PROPS_GET_INIT_PROPS_CONFLICT)\n      }\n\n      if (hasStaticProps && hasServerProps) {\n        throw new Error(SERVER_PROPS_SSG_CONFLICT)\n      }\n\n      const pageIsDynamic = isDynamicRoute(page)\n      // A page cannot have static parameters if it is not a dynamic page.\n      if (hasStaticProps && hasStaticPaths && !pageIsDynamic) {\n        throw new Error(\n          `getStaticPaths can only be used with dynamic pages, not '${page}'.` +\n            `\\nLearn more: https://nextjs.org/docs/routing/dynamic-routes`\n        )\n      }\n\n      if (hasStaticProps && pageIsDynamic && !hasStaticPaths) {\n        throw new Error(\n          `getStaticPaths is required for dynamic SSG pages and is missing for '${page}'.` +\n            `\\nRead more: https://nextjs.org/docs/messages/invalid-getstaticpaths-value`\n        )\n      }\n\n      let prerenderRoutes: Array<string> | undefined\n      let encodedPrerenderRoutes: Array<string> | undefined\n      let prerenderFallback: boolean | 'blocking' | undefined\n      if (hasStaticProps && hasStaticPaths) {\n        ;({\n          paths: prerenderRoutes,\n          fallback: prerenderFallback,\n          encodedPaths: encodedPrerenderRoutes,\n        } = await buildStaticPaths(\n          page,\n          mod.getStaticPaths,\n          locales,\n          defaultLocale\n        ))\n      }\n\n      const isNextImageImported = (global as any).__NEXT_IMAGE_IMPORTED\n      const config: PageConfig = mod.config || {}\n      return {\n        isStatic: !hasStaticProps && !hasGetInitialProps && !hasServerProps,\n        isHybridAmp: config.amp === 'hybrid',\n        isAmpOnly: config.amp === true,\n        prerenderRoutes,\n        prerenderFallback,\n        encodedPrerenderRoutes,\n        hasStaticProps,\n        hasServerProps,\n        isNextImageImported,\n        traceIncludes: config.unstable_includeFiles || [],\n        traceExcludes: config.unstable_excludeFiles || [],\n      }\n    } catch (err) {\n      if (err.code === 'MODULE_NOT_FOUND') return {}\n      throw err\n    }\n  })\n}\n\nexport async function hasCustomGetInitialProps(\n  page: string,\n  distDir: string,\n  isLikeServerless: boolean,\n  runtimeEnvConfig: any,\n  checkingApp: boolean\n): Promise<boolean> {\n  require('../shared/lib/runtime-config').setConfig(runtimeEnvConfig)\n\n  const components = await loadComponents(distDir, page, isLikeServerless)\n  let mod = components.ComponentMod\n\n  if (checkingApp) {\n    mod = (await mod._app) || mod.default || mod\n  } else {\n    mod = mod.default || mod\n  }\n  mod = await mod\n  return mod.getInitialProps !== mod.origGetInitialProps\n}\n\nexport async function getNamedExports(\n  page: string,\n  distDir: string,\n  isLikeServerless: boolean,\n  runtimeEnvConfig: any\n): Promise<Array<string>> {\n  require('../shared/lib/runtime-config').setConfig(runtimeEnvConfig)\n  const components = await loadComponents(distDir, page, isLikeServerless)\n  let mod = components.ComponentMod\n\n  return Object.keys(mod)\n}\n\nexport function detectConflictingPaths(\n  combinedPages: string[],\n  ssgPages: Set<string>,\n  additionalSsgPaths: Map<string, string[]>\n) {\n  const conflictingPaths = new Map<\n    string,\n    Array<{\n      path: string\n      page: string\n    }>\n  >()\n\n  const dynamicSsgPages = [...ssgPages].filter((page) => isDynamicRoute(page))\n\n  additionalSsgPaths.forEach((paths, pathsPage) => {\n    paths.forEach((curPath) => {\n      const lowerPath = curPath.toLowerCase()\n      let conflictingPage = combinedPages.find(\n        (page) => page.toLowerCase() === lowerPath\n      )\n\n      if (conflictingPage) {\n        conflictingPaths.set(lowerPath, [\n          { path: curPath, page: pathsPage },\n          { path: conflictingPage, page: conflictingPage },\n        ])\n      } else {\n        let conflictingPath: string | undefined\n\n        conflictingPage = dynamicSsgPages.find((page) => {\n          if (page === pathsPage) return false\n\n          conflictingPath = additionalSsgPaths\n            .get(page)\n            ?.find((compPath) => compPath.toLowerCase() === lowerPath)\n          return conflictingPath\n        })\n\n        if (conflictingPage && conflictingPath) {\n          conflictingPaths.set(lowerPath, [\n            { path: curPath, page: pathsPage },\n            { path: conflictingPath, page: conflictingPage },\n          ])\n        }\n      }\n    })\n  })\n\n  if (conflictingPaths.size > 0) {\n    let conflictingPathsOutput = ''\n\n    conflictingPaths.forEach((pathItems) => {\n      pathItems.forEach((pathItem, idx) => {\n        const isDynamic = pathItem.page !== pathItem.path\n\n        if (idx > 0) {\n          conflictingPathsOutput += 'conflicts with '\n        }\n\n        conflictingPathsOutput += `path: \"${pathItem.path}\"${\n          isDynamic ? ` from page: \"${pathItem.page}\" ` : ' '\n        }`\n      })\n      conflictingPathsOutput += '\\n'\n    })\n\n    Log.error(\n      'Conflicting paths returned from getStaticPaths, paths must unique per page.\\n' +\n        'See more info here: https://nextjs.org/docs/messages/conflicting-ssg-paths\\n\\n' +\n        conflictingPathsOutput\n    )\n    process.exit(1)\n  }\n}\n\nexport function getCssFilePaths(buildManifest: BuildManifest): string[] {\n  const cssFiles = new Set<string>()\n  Object.values(buildManifest.pages).forEach((files) => {\n    files.forEach((file) => {\n      if (file.endsWith('.css')) {\n        cssFiles.add(file)\n      }\n    })\n  })\n\n  return [...cssFiles]\n}\n"], "names": [], "mappings": ";;;;QAqDgB,YAAY,GAAZ,YAAY;QAsBN,aAAa,GAAb,aAAa;QAgTnB,iBAAiB,GAAjB,iBAAiB;QAuFX,mBAAmB,GAAnB,mBAAmB;QA4FzB,UAAU,GAAV,UAAU;QAgBJ,iBAAiB,GAAjB,iBAAiB;QAyCjB,gBAAgB,GAAhB,gBAAgB;QAuMhB,YAAY,GAAZ,YAAY;QAwIZ,wBAAwB,GAAxB,wBAAwB;QAqBxB,eAAe,GAAf,eAAe;QAarB,sBAAsB,GAAtB,sBAAsB;QA4EtB,eAAe,GAAf,eAAe;;AAniCb,GAAO,CAAP,MAAO;AACD,GAA8B,CAA9B,SAA8B;AAChC,GAA+B,CAA/B,UAA+B;AACpC,GAAM,CAAN,KAAM;AACQ,GAAI,CAAJ,GAAI;AACA,GAAU,CAAV,QAAU;AACvB,GAA+B,CAA/B,UAA+B;AAW9C,GAAkB,CAAlB,UAAkB;AACD,GAAqB,CAArB,YAAqB;AACZ,GAA0B,CAA1B,iBAA0B;AACZ,GAA4B,CAA5B,MAA4B;AAC5C,GAAuC,CAAvC,UAAuC;AACrC,GAAmD,CAAnD,qBAAmD;AACvD,GAA8B,CAA9B,aAA8B;AAEvB,GAA+B,CAA/B,kBAA+B;AAE3B,GAAoC,CAApC,uBAAoC;AAExC,GAA0C,CAA1C,oBAA0C;AAClE,GAAG,CAAH,GAAG;AACgB,GAA2B,CAA3B,eAA2B;AACpC,GAAoB,CAApB,MAAoB;AACN,GAAkB,CAAlB,OAAkB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAGtD,KAAK,CAAC,aAAa;;AACnB,KAAK,CAAC,UAAU,IAAI,IAAY,GAAK,CAAC;IACpC,KAAK,CAAC,MAAM,GAAG,aAAa,CAAC,IAAI;IACjC,EAAE,EAAE,MAAM,SAAS,MAAM;WACjB,aAAa,CAAC,IAAI,IAvCJ,SAA8B,SAuCV,IAAI,CAAC,IAAI;AACrD,CAAC;AAED,KAAK,CAAC,QAAQ,UAAU,IAAY,UAvCL,GAAI,UAuCgB,IAAI,CAAC,IAAI,GAAG,IAAI;;AAEnE,KAAK,CAAC,SAAS;;AACf,KAAK,CAAC,MAAM,IAAI,IAAY,GAAK,CAAC;IAChC,KAAK,CAAC,MAAM,GAAG,SAAS,CAAC,IAAI;IAC7B,EAAE,EAAE,MAAM,SAAS,MAAM;WACjB,SAAS,CAAC,IAAI,IAAI,QAAQ,CAAC,IAAI;AACzC,CAAC;SAEe,YAAY,CAC1B,SAAiB,EACjB,cAAwB,EACL,CAAC;eApCW,iBAA0B,mBAsCvD,SAAS,EACT,GAAG,CAAC,MAAM,EAAE,MAAM,EAAE,cAAc,CAAC,IAAI,EAAC,CAAG,GAAE,EAAE;AAEnD,CAAC;eAcqB,aAAa,CACjC,IAAuB,EACvB,SAAgC,EAChC,UAAmB,IAEjB,QAAQ,GACR,OAAO,GACP,QAAQ,GACR,cAAc,GACd,aAAa,GACb,YAAY,GACZ,QAAQ,EAAG,IAAI,KAUjB,CAAC;IACD,KAAK,CAAC,aAAa,IAAI,KAAa,GAAa,CAAC;QAChD,KAAK,CAAC,IAAI,OA/EU,YAAqB,UA+EhB,KAAK;QAC9B,EAAoB,AAApB,kBAAoB;QACpB,EAAE,EAAE,KAAK,GAAG,GAAG,GAAG,IAAI,SAnGR,MAAO,SAmGgB,KAAK,CAAC,IAAI;QAC/C,EAAuB,AAAvB,qBAAuB;QACvB,EAAE,EAAE,KAAK,GAAG,GAAG,GAAG,IAAI,SArGR,MAAO,SAqGgB,MAAM,CAAC,IAAI;QAChD,EAAmB,AAAnB,iBAAmB;eAtGL,MAAO,SAuGR,GAAG,CAAC,IAAI,CAAC,IAAI;IAC5B,CAAC;IAED,KAAK,CAAC,YAAY,GAAG,GAAG;IACxB,KAAK,CAAC,iBAAiB,IAAI,SAAiB,GAAa,CAAC;QACxD,KAAK,CAAC,QAAQ,MAAM,SAAS,CAAC,GAAG;QACjC,EAAuB,AAAvB,qBAAuB;QACvB,EAAE,EAAE,SAAS,GAAG,IAAI,SA9GN,MAAO,SA8Gc,KAAK,CAAC,QAAQ;QACjD,EAAyB,AAAzB,uBAAyB;QACzB,EAAE,EAAE,SAAS,GAAG,IAAI,SAhHN,MAAO,SAgHc,MAAM,CAAC,QAAQ;QAClD,EAAoB,AAApB,kBAAoB;eAjHN,MAAO,SAkHR,GAAG,CAAC,IAAI,CAAC,QAAQ;IAChC,CAAC;IAED,KAAK,CAAC,YAAY,IAAI,QAAgB,GACpC,QAAQ,AACN,EAAqB,AAArB,mBAAqB;SACpB,OAAO,iBACR,EAAkC,AAAlC,gCAAkC;SACjC,OAAO,gBAAe,MAAQ,EAC/B,EAAmB,AAAnB,iBAAmB;SAClB,OAAO,+CAA8C,GAAK;;IAE/D,KAAK,CAAC,QAAQ;;aACX,IAAM;aAAE,IAAM;aAAE,aAAe;UAAE,GAAG,EAAE,KAAK,GA/H9B,MAAO,SAgIb,SAAS,CAAC,KAAK;;;IAIzB,KAAK,CAAC,YAAY,aA7GS,aAA8B,eA6GjB,QAAQ,GAAE,KAAO,GAAE,cAAc;IAEzE,SAAS,CAAC,GAAG,EAAC,IAAM;WACd,SAAS,CAAC,GAAG,EAAC,IAAM,MAAK,SAAS,CAAC,GAAG,EAAC,OAAS;QACpD,MAAM,EAAE,YAAY;;IAGtB,EAAE,GAAG,IAAI,CAAC,QAAQ,EAAC,IAAM,IAAG,CAAC;QAC3B,IAAI;eAAO,IAAI;aAAE,IAAM;;IACzB,CAAC;IAED,KAAK,CAAC,QAAQ,SAAS,mBAAmB,CACxC,aAAa,EACb,QAAQ,EACR,QAAQ,EACR,SAAS;IAGX,KAAK,CAAC,QAAQ,GAAG,IAAI,CAClB,KAAK,GACL,MAAM,EACJ,CAAC,KAEE,CAAC,MAAK,UAAY,KAClB,CAAC,MAAK,OAAS,MACb,YAAY,IAAI,CAAC,MAAK,KAAO;MAGpC,IAAI,EAAE,CAAC,EAAE,CAAC,GAAK,CAAC,CAAC,aAAa,CAAC,CAAC;;IAEnC,QAAQ,CAAC,OAAO,EAAE,IAAI,EAAE,CAAC,EAAE,GAAG,GAAK,CAAC;YAe/B,GAA0B,EAqC3B,IAAyB,EAiBvB,IAAuB;QApE3B,KAAK,CAAC,MAAM,GACV,CAAC,KAAK,CAAC,GACH,GAAG,CAAC,MAAM,KAAK,CAAC,IACd,GAAK,KACL,GAAK,IACP,CAAC,KAAK,GAAG,CAAC,MAAM,GAAG,CAAC,IACpB,GAAK,KACL,GAAK;QAEX,KAAK,CAAC,QAAQ,GAAG,SAAS,CAAC,GAAG,CAAC,IAAI;QACnC,KAAK,CAAC,QAAQ,GAAG,aAAa,CAAC,aAAa,CAAC,QAAQ,CAAC,IAAI;QAE1D,KAAK,CAAC,aAAa,KAChB,QAAQ,aAAR,QAAQ,UAAR,CAAsB,QAAtB,CAAsB,GAAtB,QAAQ,CAAE,YAAY,KAAI,CAAC,MAC3B,QAAQ,aAAR,QAAQ,UAAR,CAA0B,QAA1B,CAA0B,IAA1B,GAA0B,GAA1B,QAAQ,CAAE,gBAAgB,cAA1B,GAA0B,UAA1B,CAA0B,QAA1B,CAA0B,GAA1B,GAA0B,CAAE,MAAM,EAAE,CAAC,EAAE,CAAC,GAAK,CAAC,IAAI,CAAC,IAAI,CAAC;UAAG,CAAC,MAAK,CAAC;QAErE,QAAQ,CAAC,IAAI;eACR,MAAM,CAAC,CAAC,EACT,IAAI,MAAK,KAAO,KACZ,CAAG,KACH,QAAQ,aAAR,QAAQ,UAAR,CAAgB,QAAhB,CAAgB,GAAhB,QAAQ,CAAE,MAAM,KAChB,GAAK,KACL,QAAQ,aAAR,QAAQ,UAAR,CAAe,QAAf,CAAe,GAAf,QAAQ,CAAE,KAAK,KACf,GAAK,KACL,EAAI,EACT,CAAC,GACA,QAAQ,aAAR,QAAQ,UAAR,CAAkC,QAAlC,CAAkC,GAAlC,QAAQ,CAAE,wBAAwB,OAC3B,IAAI,CAAC,OAAO,EAAE,QAAQ,aAAR,QAAQ,UAAR,CAAkC,QAAlC,CAAkC,GAAlC,QAAQ,CAAE,wBAAwB,CAAC,SAAS,IAC7D,IAAI,GAER,aAAa,GAAG,YAAY,IACvB,EAAE,EAAE,iBAAiB,CAAC,aAAa,EAAE,CAAC;YAG7C,QAAQ,GACJ,QAAQ,GAtMA,MAAO,SAuMP,IAAI,EAAC,GAAK,KAChB,QAAQ,CAAC,IAAI,IAAI,CAAC,OAtLN,YAAqB,UAuLrB,QAAQ,CAAC,IAAI;YAG/B,QAAQ,GACJ,QAAQ,GA7MA,MAAO,SA8MP,IAAI,EAAC,GAAK,KAChB,QAAQ,CAAC,IAAI,IAAI,CAAC,GAClB,aAAa,CAAC,QAAQ,CAAC,SAAS;;QAKxC,KAAK,CAAC,cAAc,KAClB,IAAyB,GAAzB,aAAa,CAAC,KAAK,CAAC,IAAI,eAAxB,IAAyB,UAAzB,CAAiC,QAAjC,CAAiC,GAAjC,IAAyB,CAAE,MAAM,EAC9B,IAAI,GAAK,IAAI,CAAC,QAAQ,EAAC,IAAM,MAAK,QAAQ,CAAC,WAAW,CAAC,QAAQ,CAAC,IAAI;;QAGzE,EAAE,EAAE,cAAc,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC9B,KAAK,CAAC,UAAU,GAAG,CAAC,KAAK,GAAG,CAAC,MAAM,GAAG,CAAC,IAAG,CAAG,KAAG,GAAK;YAErD,cAAc,CAAC,OAAO,EAAE,IAAI,EAAE,KAAK,IAAI,MAAM,MAAO,CAAC;gBACnD,KAAK,CAAC,WAAW,GAAG,KAAK,KAAK,MAAM,GAAG,CAAC,IAAG,GAAK,KAAG,GAAK;gBACxD,QAAQ,CAAC,IAAI;uBACR,UAAU,CAAC,GAAG,EAAE,WAAW,CAAC,CAAC,EAAE,YAAY,CAAC,IAAI;wBA9MrC,YAAqB,UA+MvB,QAAQ,CAAC,eAAe,CAAC,IAAI;;;YAG7C,CAAC;QACH,CAAC;QAED,EAAE,EAAE,QAAQ,aAAR,QAAQ,UAAR,CAAuB,QAAvB,CAAuB,IAAvB,IAAuB,GAAvB,QAAQ,CAAE,aAAa,cAAvB,IAAuB,UAAvB,CAAuB,QAAvB,CAAuB,GAAvB,IAAuB,CAAE,MAAM,EAAE,CAAC;YACpC,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,aAAa,CAAC,MAAM;YACjD,KAAK,CAAC,UAAU,GAAG,CAAC,KAAK,GAAG,CAAC,MAAM,GAAG,CAAC,IAAG,CAAG,KAAG,GAAK;YAErD,GAAG,CAAC,MAAM;YACV,EAAE,EACA,QAAQ,CAAC,gBAAgB,IACzB,QAAQ,CAAC,gBAAgB,CAAC,IAAI,EAAE,CAAC,GAAK,CAAC,GAAG,YAAY;eACtD,CAAC;gBACD,KAAK,CAAC,YAAY,GAAG,WAAW,KAAK,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,WAAW,EAAE,CAAC;gBACpE,KAAK,CAAC,kBAAkB,GAAG,QAAQ,CAAC,aAAa,CAC9C,GAAG,EAAE,KAAK,EAAE,GAAG;wBACd,KAAK;wBACL,QAAQ,EAAE,QAAQ,CAAC,gBAAgB,CAAE,GAAG,KAAK,CAAC;;kBAE/C,IAAI,IAAI,QAAQ,EAAE,CAAC,OAAM,QAAQ,EAAE,CAAC,MACnC,EAAmB,AAAnB,iBAAmB;oBACnB,EAAwD,AAAxD,sDAAwD;oBACxD,CAAC,IAAI,YAAY,IAAI,CAAC,IAAI,YAAY,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC;;gBAEtD,MAAM,GAAG,kBAAkB,CAAC,KAAK,CAAC,CAAC,EAAE,YAAY;gBACjD,KAAK,CAAC,eAAe,GAAG,kBAAkB,CAAC,KAAK,CAAC,YAAY;gBAC7D,EAAE,EAAE,eAAe,CAAC,MAAM,EAAE,CAAC;oBAC3B,KAAK,CAAC,SAAS,GAAG,eAAe,CAAC,MAAM;oBACxC,KAAK,CAAC,WAAW,GAAG,IAAI,CAAC,KAAK,CAC5B,eAAe,CAAC,MAAM,EACnB,KAAK,IAAI,QAAQ,MAAO,KAAK,GAAG,QAAQ;sBACzC,CAAC,IACC,eAAe,CAAC,MAAM;oBAE5B,MAAM,CAAC,IAAI;wBACT,KAAK,GAAG,EAAE,EAAE,SAAS,CAAC,YAAY;wBAClC,QAAQ,EAAE,CAAC;wBACX,WAAW;;gBAEf,CAAC;YACH,CAAC,MAAM,CAAC;gBACN,KAAK,CAAC,YAAY,GAAG,WAAW,KAAK,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,WAAW,EAAE,CAAC;gBACpE,MAAM,GAAG,QAAQ,CAAC,aAAa,CAC5B,KAAK,CAAC,CAAC,EAAE,YAAY,EACrB,GAAG,EAAE,KAAK;wBAAQ,KAAK;wBAAE,QAAQ,EAAE,CAAC;;;gBACvC,EAAE,EAAE,WAAW,GAAG,YAAY,EAAE,CAAC;oBAC/B,KAAK,CAAC,SAAS,GAAG,WAAW,GAAG,YAAY;oBAC5C,MAAM,CAAC,IAAI;wBAAG,KAAK,GAAG,EAAE,EAAE,SAAS,CAAC,YAAY;wBAAG,QAAQ,EAAE,CAAC;;gBAChE,CAAC;YACH,CAAC;YAED,MAAM,CAAC,OAAO,IAAI,KAAK,GAAE,QAAQ,GAAE,WAAW,KAAI,KAAK,IAAI,MAAM,MAAO,CAAC;gBACvE,KAAK,CAAC,WAAW,GAAG,KAAK,KAAK,MAAM,GAAG,CAAC,IAAG,GAAK,KAAG,GAAK;gBACxD,QAAQ,CAAC,IAAI;uBACR,UAAU,CAAC,GAAG,EAAE,WAAW,CAAC,CAAC,EAAE,KAAK,GACrC,QAAQ,GAAG,YAAY,IAAI,EAAE,EAAE,iBAAiB,CAAC,QAAQ,EAAE,CAAC,SAE5D,WAAW,IAAI,WAAW,GAAG,YAAY,IACpC,MAAM,EAAE,iBAAiB,CAAC,WAAW,EAAE,CAAC;;;;YAMnD,CAAC;QACH,CAAC;IACH,CAAC;IAED,KAAK,CAAC,eAAe,GAAG,QAAQ,CAAC,eAAe;IAChD,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,cAAc;IAE3C,QAAQ,CAAC,IAAI;SACX,6BAA+B;QAC/B,aAAa,CAAC,eAAe;;;IAG/B,KAAK,CAAC,cAAc,GAAG,MAAM,CAAC,IAAI,CAAC,WAAW;IAC9C,KAAK,CAAC,cAAc;;WAEf,cAAc,CACd,MAAM,EAAE,IAAI,GAAK,CAAC;YACjB,EAAE,EAAE,IAAI,CAAC,QAAQ,EAAC,IAAM,IAAG,CAAC;gBAC1B,cAAc,CAAC,IAAI,CAAC,IAAI;uBACjB,KAAK;YACd,CAAC;mBACM,IAAI;QACb,CAAC,EACA,GAAG,EAAE,CAAC,GAAK,CAAC,CAAC,OAAO,CAAC,OAAO,GAAE,SAAW;UACzC,IAAI;WACJ,cAAc,CAAC,GAAG,EAAE,CAAC,GAAK,CAAC,CAAC,OAAO,CAAC,OAAO,GAAE,SAAW;UAAG,IAAI;MAClE,OAAO,EAAE,QAAQ,EAAE,KAAK,IAAI,MAAM,MAAO,CAAC;QAC1C,KAAK,CAAC,WAAW,GAAG,KAAK,KAAK,MAAM,GAAG,CAAC,IAAG,GAAK,KAAG,GAAK;QAExD,KAAK,CAAC,YAAY,GAAG,QAAQ,CAAC,OAAO,EAAC,SAAW,GAAE,OAAO;QAC1D,KAAK,CAAC,SAAS,GAAG,YAAY,CAAC,QAAQ;QAEvC,QAAQ,CAAC,IAAI;aACV,EAAE,EAAE,WAAW,CAAC,CAAC,EAAE,SAAS;gBAlTX,YAAqB,UAmT3B,WAAW,CAAC,YAAY;;;IAGxC,CAAC;IAED,OAAO,CAAC,GAAG,KAxUS,UAA+B,UAyUvC,QAAQ;QAChB,KAAK;aAAG,CAAG;aAAE,CAAG;aAAE,CAAG;;QACrB,YAAY,GAAG,GAAG,OAvUF,UAA+B,UAuUd,GAAG,EAAE,MAAM;;IAIhD,OAAO,CAAC,GAAG;IACX,OAAO,CAAC,GAAG,KAhVS,UAA+B;;aAoV3C,EAAI;YACJ,UAAU,IAAG,QAAU,KAAG,QAAU;aACnC,qCAAqC,EAxV9B,MAAO,SAwV+B,IAAI,EAChD,eAAiB,GACjB,IAAI,EA1VE,MAAO,SA0VD,IAAI,EAAC,kBAAoB,GAAE,CAAC;;;aAG1C,GAAK;aACL,QAAU;aACV,6DAA+D;;;aAG/D,GAAK;aACL,KAAO;aACN,oDAAoD,EApW7C,MAAO,SAoW8C,IAAI,EAC/D,cAAgB,GAChB,CAAC;;;;aAIH,KAAO;aACN,oDAAoD,EA3W7C,MAAO,SA2W8C,IAAI,EAC/D,cAAgB,GAChB,CAAC;;;QAIL,KAAK;aAAG,CAAG;aAAE,CAAG;aAAE,CAAG;;QACrB,YAAY,GAAG,GAAG,OA5WJ,UAA+B,UA4WZ,GAAG,EAAE,MAAM;;IAKlD,OAAO,CAAC,GAAG;AACb,CAAC;SAEe,iBAAiB,GAC/B,SAAS,GACT,QAAQ,GACR,OAAO,KACQ,CAAC;IAChB,KAAK,CAAC,WAAW,IACf,MAAyC,EACzC,IAA0C,GACvC,CAAC;QACJ,KAAK,CAAC,WAAW,GAAG,IAAI,MAAK,SAAW;QACxC,KAAK,CAAC,SAAS,GAAG,IAAI,MAAK,OAAS;QACpC,OAAO,CAAC,GAAG,CArYG,MAAO,SAqYH,SAAS,CAAC,IAAI;QAChC,OAAO,CAAC,GAAG;QAEX,EAIG,AAJH;;;;KAIG,AAJH,EAIG,CACH,KAAK,CAAC,SAAS,GAAI,MAAM,CACtB,GAAG,EAAE,KAAyB,GAAK,CAAC;YACnC,GAAG,CAAC,QAAQ,IAAI,YAAU,EAAI,KAAK,CAAC,MAAM,CAAC,EAAE;YAE3C,EAAA,GAAG,SAAS,EAAE,CAAC;gBACf,KAAK,CAAC,CAAC,GAAG,KAAK;gBACf,QAAQ,OAAO,WAAW,IAAG,GAAG,KAAK,GAAG,EAAG,cAAc,EACnD,CAAH,CAAC,WAAW,CACd,EAAE;YACL,CAAC;YACD,EAAE,EAAE,WAAW,EAAE,CAAC;gBAChB,KAAK,CAAC,CAAC,GAAG,KAAK;gBACf,QAAQ,KAAK,IAAE,EACX,CAAD,CAAC,UAAU,IACP,QAAQ,EAAE,CAAC,CAAC,UAAU,MACtB,WAAW,EAAE,CAAC,CAAC,SAAS,GAC9B,EAAE;YACL,CAAC;YAED,EAAE,EAAE,SAAS,EAAE,CAAC;gBACd,KAAK,CAAC,CAAC,GAAG,KAAK;gBACf,QAAQ,KAAK,cAAY;oBAElB,GAAC,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC,GAAI,CAAC;oBAC1C,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,OAAO,CAAC,CAAC;oBAC1B,KAAK,CAAC,IAAI,GAAG,CAAC,KAAK,OAAO,CAAC,MAAM,GAAG,CAAC;oBAErC,QAAQ,KAAK,EAAE,EAAE,IAAI,IAAG,GAAG,KAAK,GAAG,EAAC,CAAC,EAAE,MAAM,CAAC,GAAG,CAAC,EAAE,EAAE,MAAM,CAAC,KAAK,CAAC,EAAE;gBACrE,CAAC;YACH,CAAC;mBAEM,QAAQ;QACjB,CAAC,EACA,IAAI,EAAC,EAAI;QAEZ,OAAO,CAAC,GAAG,CAAC,SAAS,GAAE,EAAI;IAC7B,CAAC;IAED,EAAE,EAAE,SAAS,CAAC,MAAM,EAAE,CAAC;QACrB,WAAW,CAAC,SAAS,GAAE,SAAW;IACpC,CAAC;IACD,EAAE,EAAE,OAAO,CAAC,MAAM,EAAE,CAAC;QACnB,WAAW,CAAC,OAAO,GAAE,OAAS;IAChC,CAAC;IAED,KAAK,CAAC,gBAAgB;WACjB,QAAQ,CAAC,WAAW;WACpB,QAAQ,CAAC,UAAU;WACnB,QAAQ,CAAC,QAAQ;;IAEtB,EAAE,EAAE,gBAAgB,CAAC,MAAM,EAAE,CAAC;QAC5B,WAAW,CAAC,gBAAgB,GAAE,QAAU;IAC1C,CAAC;AACH,CAAC;AAUD,GAAG,CAAC,mBAAmB;AAEvB,GAAG,CAAC,WAAW;AACf,GAAG,CAAC,mBAAmB;eAED,mBAAmB,CACvC,QAAuB,EACvB,QAAgB,EAChB,QAAiB,GAAG,IAAI,EACxB,SAAiC,EACF,CAAC;IAChC,EAAE,EACA,MAAM,CAAC,EAAE,CAAC,mBAAmB,EAAE,QAAQ,KACvC,mBAAmB,OAAO,SAAS,EACnC,CAAC;eACM,WAAW;IACpB,CAAC;IAED,GAAG,CAAC,QAAQ,GAAG,CAAC;IAChB,KAAK,CAAC,KAAK,GAAG,GAAG,CAAC,GAAG;IACrB,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,OAAO,EAAE,GAAG,GAAK,CAAC;QAC5C,EAAE,EAAE,SAAS,EAAE,CAAC;YACd,KAAK,CAAC,QAAQ,GAAG,SAAS,CAAC,GAAG,CAAC,GAAG;YAClC,EAAkE,AAAlE,gEAAkE;YAClE,EAAkD,AAAlD,gDAAkD;YAClD,EAAE,EAAE,QAAQ,aAAR,QAAQ,UAAR,CAAqB,QAArB,CAAqB,GAArB,QAAQ,CAAE,WAAW,EAAE,CAAC;;YAE5B,CAAC;QACH,CAAC;UAEC,QAAQ;QACV,QAAQ,CAAC,KAAK,CAAC,GAAG,EAAE,OAAO,EAAE,IAAI,GAAK,CAAC;YACrC,EAAE,EAAE,GAAG,MAAK,KAAO,GAAE,CAAC;gBACpB,KAAK,CAAC,GAAG,CAAC,IAAI,EAAE,QAAQ;YAC1B,CAAC,MAAM,EAAE,EAAE,KAAK,CAAC,GAAG,CAAC,IAAI,GAAG,CAAC;gBAC3B,KAAK,CAAC,GAAG,CAAC,IAAI,EAAE,KAAK,CAAC,GAAG,CAAC,IAAI,IAAK,CAAC;YACtC,CAAC,MAAM,CAAC;gBACN,KAAK,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC;YACnB,CAAC;QACH,CAAC;IACH,CAAC;IAED,KAAK,CAAC,OAAO,GAAG,QAAQ,GAAG,UAAU,GAAG,MAAM;IAE9C,KAAK,CAAC,WAAW;WAAO,KAAK,CAAC,OAAO;MAClC,MAAM,KAAK,GAAG,IAAM,GAAG,KAAK,QAAQ,IAAI,GAAG,KAAK,QAAQ;MACxD,GAAG,GAAG,CAAC,IAAM,CAAC;;IACjB,KAAK,CAAC,WAAW;WAAO,KAAK,CAAC,OAAO;MAClC,MAAM,KAAK,GAAG,IAAM,GAAG,KAAK,CAAC;MAC7B,GAAG,GAAG,CAAC,IAAM,CAAC;;IAEjB,GAAG,CAAC,KAAK;QACL,CAAC;QACH,KAAK,SAAS,OAAO,CAAC,GAAG,CACvB,WAAW,CAAC,GAAG,QACN,CAAC;gBACL,CAAC;sBAAQ,OAAO,CAjgBZ,KAAM,SAigBY,IAAI,CAAC,QAAQ,EAAE,CAAC;;;IAG/C,CAAC,QAAQ,CAAC,EAAE,CAAC;QACX,KAAK;IACP,CAAC;IAED,GAAG,CAAC,WAAW;QACX,CAAC;QACH,WAAW,SAAS,OAAO,CAAC,GAAG,CAC7B,WAAW,CAAC,GAAG,QACN,CAAC;gBACL,CAAC;sBAAQ,OAAO,CA7gBZ,KAAM,SA6gBY,IAAI,CAAC,QAAQ,EAAE,CAAC;;;IAG/C,CAAC,QAAQ,CAAC,EAAE,CAAC;QACX,WAAW;IACb,CAAC;IAED,WAAW;QACT,WAAW;QACX,WAAW;QACX,eAAe,EAAE,WAAW,CAAC,MAAM,EAChC,GAAG,EAAE,CAAC,GAAK,MAAM,CAAC,MAAM,CAAC,GAAG;iBAAK,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;;;;QAG9C,cAAc,EAAE,KAAK,CAAC,MAAM,EACzB,GAAG,EAAE,CAAC,GAAK,MAAM,CAAC,MAAM,CAAC,GAAG;iBAAK,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;;;;QAG9C,eAAe,EAAE,KAAK,CAAC,MAAM,EAAE,IAAI,GAAG,CAAC,EAAE,IAAI,IAAM,CAAC;YAClD,EAAE,EAAE,CAAC,CAAC,QAAQ,EAAC,IAAM,WAAU,IAAI;mBAC5B,IAAI,GAAG,IAAI;QACpB,CAAC,EAAE,CAAC;;IAGN,mBAAmB,GAAG,QAAQ;IAC9B,mBAAmB,KAAK,SAAS;WAC1B,WAAW;AACpB,CAAC;SAEe,UAAU,CAAI,IAAkB,EAAE,GAAiB,EAAO,CAAC;IACzE,KAAK,CAAC,CAAC,GAAG,GAAG,CAAC,GAAG,CAAC,IAAI;IACtB,KAAK,CAAC,CAAC,GAAG,GAAG,CAAC,GAAG,CAAC,GAAG;;WACV,CAAC;MAAE,MAAM,EAAE,CAAC,IAAM,CAAC,CAAC,GAAG,CAAC,CAAC;;AACtC,CAAC;SAEQ,SAAS,CAAI,IAAS,EAAE,GAAQ,EAAO,CAAC;IAC/C,KAAK,CAAC,CAAC,GAAG,GAAG,CAAC,GAAG,CAAC,IAAI;IACtB,KAAK,CAAC,CAAC,GAAG,GAAG,CAAC,GAAG,CAAC,GAAG;;WACV,GAAG,CAAC,GAAG;eAAK,CAAC;UAAE,MAAM,EAAE,CAAC,GAAK,CAAC,CAAC,GAAG,CAAC,CAAC;;;AACjD,CAAC;SAEQ,GAAG,CAAC,CAAW,EAAU,CAAC;WAC1B,CAAC,CAAC,MAAM,EAAE,IAAI,EAAE,IAAI,GAAK,IAAI,GAAG,IAAI;MAAE,CAAC;AAChD,CAAC;eAEqB,iBAAiB,CACrC,IAAY,EACZ,QAAgB,EAChB,aAA4B,EAC5B,QAAiB,GAAG,IAAI,EACxB,oBAA2C,EAChB,CAAC;IAC5B,KAAK,CAAC,IAAI,GACR,oBAAoB,UACb,mBAAmB,CAAC,aAAa,EAAE,QAAQ,EAAE,QAAQ;IAE9D,KAAK,CAAC,UAAU,IAAI,KAAa,GAAK,KAAK,CAAC,QAAQ,EAAC,GAAK;;IAE1D,KAAK,CAAC,SAAS,IACb,aAAa,CAAC,KAAK,KAljBW,kBAA+B,sBAkjBrB,IAAI,UAC5C,MAAM,CAAC,UAAU;IACnB,KAAK,CAAC,QAAQ,IAAI,aAAa,CAAC,KAAK,EAAC,KAAO,UAAS,MAAM,CAAC,UAAU;IAEvE,KAAK,CAAC,aAAa,IAAI,GAAW,MAAQ,QAAQ,CAAC,CAAC,EAAE,GAAG;;IAEzD,KAAK,CAAC,YAAY;WAAO,GAAG,CAAC,GAAG;eAAK,SAAS;eAAK,QAAQ;;MAAI,GAAG,CAChE,aAAa;IAEf,KAAK,CAAC,aAAa,GAAG,UAAU,CAC9B,SAAS,CAAC,SAAS,EAAE,IAAI,CAAC,WAAW,GACrC,IAAI,CAAC,WAAW,EAChB,GAAG,CAAC,aAAa;IAEnB,KAAK,CAAC,OAAO,GAAG,QAAQ,GAAG,UAAU,GAAG,MAAM;QAE1C,CAAC;QACH,EAA0E,AAA1E,wEAA0E;QAC1E,EAAkE,AAAlE,gEAAkE;QAClE,KAAK,CAAC,YAAY,GAAG,GAAG,OAAO,OAAO,CAAC,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,OAAO;QACnE,KAAK,CAAC,aAAa,GAAG,GAAG,OAAO,OAAO,CAAC,GAAG,CAAC,aAAa,CAAC,GAAG,CAAC,OAAO;;YAE7D,aAAa;YAAE,YAAY;;IACrC,CAAC,QAAQ,CAAC,EAAE,CAAC;IAAA,CAAC;;SACL,CAAC;SAAG,CAAC;;AAChB,CAAC;eAEqB,gBAAgB,CACpC,IAAY,EACZ,cAA8B,EAC9B,OAAkB,EAClB,aAAsB,EAMtB,CAAC;IACD,KAAK,CAAC,cAAc,GAAG,GAAG,CAAC,GAAG;IAC9B,KAAK,CAAC,qBAAqB,GAAG,GAAG,CAAC,GAAG;IACrC,KAAK,CAAC,WAAW,OA/lB0B,MAA4B,gBA+lBrC,IAAI;IACtC,KAAK,CAAC,aAAa,OAhmBwB,MAA4B,kBAgmBjC,WAAW;IAEjD,EAA0C,AAA1C,wCAA0C;IAC1C,KAAK,CAAC,eAAe,GAAG,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,IAAI;IAEtD,KAAK,CAAC,iBAAiB,SAAS,cAAc;QAAG,OAAO;QAAE,aAAa;;IAEvE,KAAK,CAAC,iBAAiB,IACpB,4CAA4C,KAC5C,qFAAqF;IAExF,EAAE,GACC,iBAAiB,WACX,iBAAiB,MAAK,MAAQ,KACrC,KAAK,CAAC,OAAO,CAAC,iBAAiB,GAC/B,CAAC;QACD,KAAK,CAAC,GAAG,CAAC,KAAK,EACZ,8CAA8C,EAAE,IAAI,CAAC,WAAW,SAAS,iBAAiB,CAAC,CAAC,EAAE,iBAAiB;IAEpH,CAAC;IAED,KAAK,CAAC,qBAAqB,GAAG,MAAM,CAAC,IAAI,CAAC,iBAAiB,EAAE,MAAM,EAChE,GAAG,KAAO,GAAG,MAAK,KAAO,KAAI,GAAG,MAAK,QAAU;;IAGlD,EAAE,EAAE,qBAAqB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;QACrC,KAAK,CAAC,GAAG,CAAC,KAAK,EACZ,2CAA2C,EAAE,IAAI,CAAC,EAAE,EAAE,qBAAqB,CAAC,IAAI,EAC/E,EAAI,GACJ,EAAE,EAAE,iBAAiB;IAE3B,CAAC;IAED,EAAE,WAES,iBAAiB,CAAC,QAAQ,MAAK,OAAS,KAC/C,iBAAiB,CAAC,QAAQ,MAAK,QAAU,IAE3C,CAAC;QACD,KAAK,CAAC,GAAG,CAAC,KAAK,EACZ,6DAA6D,EAAE,IAAI,CAAC,GAAG,IACtE,iBAAiB;IAEvB,CAAC;IAED,KAAK,CAAC,WAAW,GAAG,iBAAiB,CAAC,KAAK;IAE3C,EAAE,GAAG,KAAK,CAAC,OAAO,CAAC,WAAW,GAAG,CAAC;QAChC,KAAK,CAAC,GAAG,CAAC,KAAK,EACZ,wDAAwD,EAAE,IAAI,CAAC,GAAG,KAChE,2FAA2F;IAElG,CAAC;IAED,WAAW,CAAC,OAAO,EAAE,KAAK,GAAK,CAAC;QAC9B,EAAuE,AAAvE,qEAAuE;QACvE,EAAS,AAAT,OAAS;QACT,EAAE,SAAS,KAAK,MAAK,MAAQ,GAAE,CAAC;YAC9B,KAAK,OAnpB2B,uBAAoC,0BAmpBpC,KAAK;YAErC,KAAK,CAAC,gBAAgB,OAnpBM,oBAA0C,sBAmpBzB,KAAK,EAAE,OAAO;YAC3D,GAAG,CAAC,YAAY,GAAG,KAAK;YAExB,EAAE,EAAE,gBAAgB,CAAC,cAAc,EAAE,CAAC;gBACpC,YAAY,GAAG,KAAK,CAAC,MAAM,CAAC,gBAAgB,CAAC,cAAc,CAAC,MAAM,GAAG,CAAC;YACxE,CAAC,MAAM,EAAE,EAAE,aAAa,EAAE,CAAC;gBACzB,KAAK,IAAI,CAAC,EAAE,aAAa,GAAG,KAAK;YACnC,CAAC;YAED,KAAK,CAAC,MAAM,GAAG,aAAa,CAAC,YAAY;YACzC,EAAE,GAAG,MAAM,EAAE,CAAC;gBACZ,KAAK,CAAC,GAAG,CAAC,KAAK,EACZ,oBAAoB,EAAE,YAAY,CAAC,8BAA8B,EAAE,IAAI,CAAC,GAAG;YAEhF,CAAC;YAED,EAAqE,AAArE,mEAAqE;YACrE,EAAiE,AAAjE,+DAAiE;YACjE,EAAa,AAAb,WAAa;YACb,cAAc,CAAC,GAAG,CAChB,KAAK,CACF,KAAK,EAAC,CAAG,GACT,GAAG,EAAE,OAAO,OAhrBQ,qBAAmD,UAirBjD,kBAAkB,CAAC,OAAO,GAAG,IAAI;cAEvD,IAAI,EAAC,CAAG;YAEb,qBAAqB,CAAC,GAAG,CAAC,KAAK;QACjC,CAAC,MAGI,CAAC;YACJ,KAAK,CAAC,WAAW,GAAG,MAAM,CAAC,IAAI,CAAC,KAAK,EAAE,MAAM,EAC1C,GAAG,GAAK,GAAG,MAAK,MAAQ,KAAI,GAAG,MAAK,MAAQ;;YAG/C,EAAE,EAAE,WAAW,CAAC,MAAM,EAAE,CAAC;gBACvB,KAAK,CAAC,GAAG,CAAC,KAAK,EACZ,+DAA+D,EAAE,IAAI,CAAC,GAAG,KACvE,6FAA6F,KAC7F,yBAAyB,EAAE,eAAe,CACxC,GAAG,EAAE,CAAC,MAAQ,CAAC,CAAC,KAAK;kBACrB,IAAI,EAAC,EAAI,GAAE,IAAI,KACjB,gCAAgC,EAAE,WAAW,CAAC,IAAI,EAAC,EAAI,GAAE,GAAG;YAEnE,CAAC;YAED,KAAK,GAAG,MAAM;mBAAU,KAAK;YAC7B,GAAG,CAAC,SAAS,GAAG,IAAI;YACpB,GAAG,CAAC,gBAAgB,GAAG,IAAI;YAE3B,eAAe,CAAC,OAAO,EAAE,aAAa,GAAK,CAAC;gBAC1C,KAAK,GAAG,MAAM,GAAE,QAAQ,MAAK,WAAW,CAAC,MAAM,CAAC,aAAa;gBAC7D,GAAG,CAAC,UAAU,GAAG,MAAM,CAAC,aAAa;gBACrC,EAAE,EACA,QAAQ,IACR,MAAM,CAAC,cAAc,CAAC,aAAa,MAClC,UAAU,KAAK,IAAI,IAClB,UAAU,KAAK,SAAS,IACvB,UAAU,KAAa,KAAK,GAC/B,CAAC;oBACD,UAAU;gBACZ,CAAC;gBACD,EAAE,EACC,MAAM,KAAK,KAAK,CAAC,OAAO,CAAC,UAAU,MAClC,MAAM,WAAW,UAAU,MAAK,MAAQ,GAC1C,CAAC;oBACD,KAAK,CAAC,GAAG,CAAC,KAAK,EACZ,sBAAsB,EAAE,aAAa,CAAC,sBAAsB,EAC3D,MAAM,IAAG,QAAU,KAAG,QAAU,EACjC,uBAAuB,EAAE,IAAI;gBAElC,CAAC;gBACD,GAAG,CAAC,QAAQ,IAAI,CAAC,EAAE,MAAM,IAAG,GAAK,SAAQ,aAAa,CAAC,CAAC;gBACxD,EAAE,EAAE,QAAQ,EAAE,CAAC;oBACb,QAAQ,IAAI,CAAC,EAAE,QAAQ,CAAC,CAAC;gBAC3B,CAAC;gBACD,SAAS,GAAG,SAAS,CAClB,OAAO,CACN,QAAQ,EACR,MAAM,GACD,UAAU,CACR,GAAG,EAAE,OAAO,OA5uBA,qBAAmD,UA4uBzB,OAAO,EAAE,IAAI;kBACnD,IAAI,EAAC,CAAG,SA7uBI,qBAAmD,UA8uB7C,UAAU,EAAY,IAAI,GAEpD,OAAO;gBAEV,gBAAgB,GAAG,gBAAgB,CAChC,OAAO,CACN,QAAQ,EACR,MAAM,GACD,UAAU,CAAc,GAAG,CAAC,kBAAkB,EAAE,IAAI,EAAC,CAAG,KACzD,kBAAkB,CAAC,UAAU,GAElC,OAAO;YACZ,CAAC;YAED,EAAE,EAAE,KAAK,CAAC,MAAM,MAAK,OAAO,aAAP,OAAO,UAAP,CAAiB,QAAjB,CAAiB,GAAjB,OAAO,CAAE,QAAQ,CAAC,KAAK,CAAC,MAAM,IAAG,CAAC;gBACrD,KAAK,CAAC,GAAG,CAAC,KAAK,EACZ,gDAAgD,EAAE,IAAI,CAAC,aAAa,EAAE,KAAK,CAAC,MAAM,CAAC,mCAAmC;YAE3H,CAAC;YACD,KAAK,CAAC,SAAS,GAAG,KAAK,CAAC,MAAM,IAAI,aAAa;YAE/C,cAAc,CAAC,GAAG,IACb,SAAS,IAAI,CAAC,EAAE,SAAS,UAC1B,SAAS,IAAI,SAAS,MAAK,CAAG,SAAQ,SAAS;YAGnD,qBAAqB,CAAC,GAAG,IACpB,SAAS,IAAI,CAAC,EAAE,SAAS,UAC1B,SAAS,IAAI,gBAAgB,MAAK,CAAG,SAAQ,gBAAgB;QAGnE,CAAC;IACH,CAAC;;QAGC,KAAK;eAAM,cAAc;;QACzB,QAAQ,EAAE,iBAAiB,CAAC,QAAQ;QACpC,YAAY;eAAM,qBAAqB;;;AAE3C,CAAC;eAEqB,YAAY,CAChC,IAAY,EACZ,OAAe,EACf,UAAmB,EACnB,gBAAqB,EACrB,gBAAwD,EACxD,OAAkB,EAClB,aAAsB,EACtB,QAAc,EAab,CAAC;IACF,KAAK,CAAC,gBAAgB,OAnyBJ,MAAoB,SAmyBP,oBAAsB,GAAE,QAAQ;WACxD,gBAAgB,CAAC,YAAY,WAAa,CAAC;YAC5C,CAAC;YACH,OAAO,EAAC,4BAA8B,GAAE,SAAS,CAAC,gBAAgB;gBAryBtC,OAAkB,sBAsyB1B,gBAAgB;YACpC,KAAK,CAAC,UAAU,aAzyBO,eAA2B,iBAyyBV,OAAO,EAAE,IAAI,EAAE,UAAU;YACjE,KAAK,CAAC,GAAG,GAAG,UAAU,CAAC,YAAY;YACnC,KAAK,CAAC,IAAI,GAAG,GAAG,CAAC,OAAO,IAAI,GAAG;YAE/B,EAAE,GAAG,IAAI,SAv0BkB,QAAU,qBAu0BJ,IAAI,YAAY,IAAI,MAAK,MAAQ,GAAE,CAAC;gBACnE,KAAK,CAAC,GAAG,CAAC,KAAK,EAAC,sBAAwB;YAC1C,CAAC;YAED,KAAK,CAAC,kBAAkB,KAAM,IAAI,CAAS,eAAe;YAC1D,KAAK,CAAC,cAAc,WAAY,GAAG,CAAC,cAAc;YAClD,KAAK,CAAC,cAAc,WAAY,GAAG,CAAC,cAAc;YAClD,KAAK,CAAC,cAAc,WAAY,GAAG,CAAC,kBAAkB;YACtD,KAAK,CAAC,oBAAoB,WAAY,GAAG,CAAC,uBAAuB;YACjE,KAAK,CAAC,oBAAoB,WAAY,GAAG,CAAC,uBAAuB;YACjE,KAAK,CAAC,oBAAoB,WAAY,GAAG,CAAC,uBAAuB;YACjE,KAAK,CAAC,qBAAqB,WAAY,GAAG,CAAC,wBAAwB;YAEnE,EAAE,EAAE,qBAAqB,EAAE,CAAC;gBAC1B,KAAK,CAAC,GAAG,CAAC,KAAK,EACZ,mFAAmF;YAExF,CAAC;YAED,EAAE,EAAE,oBAAoB,EAAE,CAAC;gBACzB,KAAK,CAAC,GAAG,CAAC,KAAK,EACZ,kFAAkF;YAEvF,CAAC;YAED,EAAE,EAAE,oBAAoB,EAAE,CAAC;gBACzB,KAAK,CAAC,GAAG,CAAC,KAAK,EACZ,kFAAkF;YAEvF,CAAC;YAED,EAAE,EAAE,oBAAoB,EAAE,CAAC;gBACzB,KAAK,CAAC,GAAG,CAAC,KAAK,EACZ,sFAAsF;YAE3F,CAAC;YAED,EAAuE,AAAvE,qEAAuE;YACvE,EAAiB,AAAjB,eAAiB;YACjB,EAAE,EAAE,kBAAkB,IAAI,cAAc,EAAE,CAAC;gBACzC,KAAK,CAAC,GAAG,CAAC,KAAK,CAn2BlB,UAAkB;YAo2BjB,CAAC;YAED,EAAE,EAAE,kBAAkB,IAAI,cAAc,EAAE,CAAC;gBACzC,KAAK,CAAC,GAAG,CAAC,KAAK,CAv2BlB,UAAkB;YAw2BjB,CAAC;YAED,EAAE,EAAE,cAAc,IAAI,cAAc,EAAE,CAAC;gBACrC,KAAK,CAAC,GAAG,CAAC,KAAK,CA32BlB,UAAkB;YA42BjB,CAAC;YAED,KAAK,CAAC,aAAa,OA12BI,UAAuC,iBA02BzB,IAAI;YACzC,EAAoE,AAApE,kEAAoE;YACpE,EAAE,EAAE,cAAc,IAAI,cAAc,KAAK,aAAa,EAAE,CAAC;gBACvD,KAAK,CAAC,GAAG,CAAC,KAAK,EACZ,yDAAyD,EAAE,IAAI,CAAC,EAAE,KAChE,4DAA4D;YAEnE,CAAC;YAED,EAAE,EAAE,cAAc,IAAI,aAAa,KAAK,cAAc,EAAE,CAAC;gBACvD,KAAK,CAAC,GAAG,CAAC,KAAK,EACZ,qEAAqE,EAAE,IAAI,CAAC,EAAE,KAC5E,0EAA0E;YAEjF,CAAC;YAED,GAAG,CAAC,eAAe;YACnB,GAAG,CAAC,sBAAsB;YAC1B,GAAG,CAAC,iBAAiB;YACrB,EAAE,EAAE,cAAc,IAAI,cAAc,EAAE,CAAC;mBAEnC,KAAK,EAAE,eAAe,GACtB,QAAQ,EAAE,iBAAiB,GAC3B,YAAY,EAAE,sBAAsB,cAC5B,gBAAgB,CACxB,IAAI,EACJ,GAAG,CAAC,cAAc,EAClB,OAAO,EACP,aAAa;YAEjB,CAAC;YAED,KAAK,CAAC,mBAAmB,GAAI,MAAM,CAAS,qBAAqB;YACjE,KAAK,CAAC,MAAM,GAAe,GAAG,CAAC,MAAM;;;gBAEnC,QAAQ,GAAG,cAAc,KAAK,kBAAkB,KAAK,cAAc;gBACnE,WAAW,EAAE,MAAM,CAAC,GAAG,MAAK,MAAQ;gBACpC,SAAS,EAAE,MAAM,CAAC,GAAG,KAAK,IAAI;gBAC9B,eAAe;gBACf,iBAAiB;gBACjB,sBAAsB;gBACtB,cAAc;gBACd,cAAc;gBACd,mBAAmB;gBACnB,aAAa,EAAE,MAAM,CAAC,qBAAqB;gBAC3C,aAAa,EAAE,MAAM,CAAC,qBAAqB;;QAE/C,CAAC,QAAQ,GAAG,EAAE,CAAC;YACb,EAAE,EAAE,GAAG,CAAC,IAAI,MAAK,gBAAkB;;YACnC,KAAK,CAAC,GAAG;QACX,CAAC;IACH,CAAC;AACH,CAAC;eAEqB,wBAAwB,CAC5C,IAAY,EACZ,OAAe,EACf,gBAAyB,EACzB,gBAAqB,EACrB,WAAoB,EACF,CAAC;IACnB,OAAO,EAAC,4BAA8B,GAAE,SAAS,CAAC,gBAAgB;IAElE,KAAK,CAAC,UAAU,aA/5BW,eAA2B,iBA+5Bd,OAAO,EAAE,IAAI,EAAE,gBAAgB;IACvE,GAAG,CAAC,GAAG,GAAG,UAAU,CAAC,YAAY;IAEjC,EAAE,EAAE,WAAW,EAAE,CAAC;QAChB,GAAG,SAAU,GAAG,CAAC,IAAI,IAAK,GAAG,CAAC,OAAO,IAAI,GAAG;IAC9C,CAAC,MAAM,CAAC;QACN,GAAG,GAAG,GAAG,CAAC,OAAO,IAAI,GAAG;IAC1B,CAAC;IACD,GAAG,SAAS,GAAG;WACR,GAAG,CAAC,eAAe,KAAK,GAAG,CAAC,mBAAmB;AACxD,CAAC;eAEqB,eAAe,CACnC,IAAY,EACZ,OAAe,EACf,gBAAyB,EACzB,gBAAqB,EACG,CAAC;IACzB,OAAO,EAAC,4BAA8B,GAAE,SAAS,CAAC,gBAAgB;IAClE,KAAK,CAAC,UAAU,aAl7BW,eAA2B,iBAk7Bd,OAAO,EAAE,IAAI,EAAE,gBAAgB;IACvE,GAAG,CAAC,GAAG,GAAG,UAAU,CAAC,YAAY;WAE1B,MAAM,CAAC,IAAI,CAAC,GAAG;AACxB,CAAC;SAEe,sBAAsB,CACpC,aAAuB,EACvB,QAAqB,EACrB,kBAAyC,EACzC,CAAC;IACD,KAAK,CAAC,gBAAgB,GAAG,GAAG,CAAC,GAAG;IAQhC,KAAK,CAAC,eAAe;WAAO,QAAQ;MAAE,MAAM,EAAE,IAAI,OA/8BvB,UAAuC,iBA+8BI,IAAI;;IAE1E,kBAAkB,CAAC,OAAO,EAAE,KAAK,EAAE,SAAS,GAAK,CAAC;QAChD,KAAK,CAAC,OAAO,EAAE,OAAO,GAAK,CAAC;YAC1B,KAAK,CAAC,SAAS,GAAG,OAAO,CAAC,WAAW;YACrC,GAAG,CAAC,eAAe,GAAG,aAAa,CAAC,IAAI,EACrC,IAAI,GAAK,IAAI,CAAC,WAAW,OAAO,SAAS;;YAG5C,EAAE,EAAE,eAAe,EAAE,CAAC;gBACpB,gBAAgB,CAAC,GAAG,CAAC,SAAS;;wBAC1B,IAAI,EAAE,OAAO;wBAAE,IAAI,EAAE,SAAS;;;wBAC9B,IAAI,EAAE,eAAe;wBAAE,IAAI,EAAE,eAAe;;;YAElD,CAAC,MAAM,CAAC;gBACN,GAAG,CAAC,eAAe;gBAEnB,eAAe,GAAG,eAAe,CAAC,IAAI,EAAE,IAAI,GAAK,CAAC;wBAG9B,IACN;oBAHZ,EAAE,EAAE,IAAI,KAAK,SAAS,SAAS,KAAK;oBAEpC,eAAe,IAAG,IACN,GADM,kBAAkB,CACjC,GAAG,CAAC,IAAI,eADO,IACN,UADM,CAEV,QAFU,CAEV,GAFU,IACN,CACR,IAAI,EAAE,QAAQ,GAAK,QAAQ,CAAC,WAAW,OAAO,SAAS;;2BACpD,eAAe;gBACxB,CAAC;gBAED,EAAE,EAAE,eAAe,IAAI,eAAe,EAAE,CAAC;oBACvC,gBAAgB,CAAC,GAAG,CAAC,SAAS;;4BAC1B,IAAI,EAAE,OAAO;4BAAE,IAAI,EAAE,SAAS;;;4BAC9B,IAAI,EAAE,eAAe;4BAAE,IAAI,EAAE,eAAe;;;gBAElD,CAAC;YACH,CAAC;QACH,CAAC;IACH,CAAC;IAED,EAAE,EAAE,gBAAgB,CAAC,IAAI,GAAG,CAAC,EAAE,CAAC;QAC9B,GAAG,CAAC,sBAAsB;QAE1B,gBAAgB,CAAC,OAAO,EAAE,SAAS,GAAK,CAAC;YACvC,SAAS,CAAC,OAAO,EAAE,QAAQ,EAAE,GAAG,GAAK,CAAC;gBACpC,KAAK,CAAC,SAAS,GAAG,QAAQ,CAAC,IAAI,KAAK,QAAQ,CAAC,IAAI;gBAEjD,EAAE,EAAE,GAAG,GAAG,CAAC,EAAE,CAAC;oBACZ,sBAAsB,KAAI,eAAiB;gBAC7C,CAAC;gBAED,sBAAsB,KAAK,OAAO,EAAE,QAAQ,CAAC,IAAI,CAAC,CAAC,EACjD,SAAS,IAAI,aAAa,EAAE,QAAQ,CAAC,IAAI,CAAC,EAAE,KAAI,CAAG;YAEvD,CAAC;YACD,sBAAsB,KAAI,EAAI;QAChC,CAAC;QA1/BK,GAAG,CA4/BL,KAAK,EACP,6EAA+E,KAC7E,8EAAgF,IAChF,sBAAsB;QAE1B,OAAO,CAAC,IAAI,CAAC,CAAC;IAChB,CAAC;AACH,CAAC;SAEe,eAAe,CAAC,aAA4B,EAAY,CAAC;IACvE,KAAK,CAAC,QAAQ,GAAG,GAAG,CAAC,GAAG;IACxB,MAAM,CAAC,MAAM,CAAC,aAAa,CAAC,KAAK,EAAE,OAAO,EAAE,KAAK,GAAK,CAAC;QACrD,KAAK,CAAC,OAAO,EAAE,IAAI,GAAK,CAAC;YACvB,EAAE,EAAE,IAAI,CAAC,QAAQ,EAAC,IAAM,IAAG,CAAC;gBAC1B,QAAQ,CAAC,GAAG,CAAC,IAAI;YACnB,CAAC;QACH,CAAC;IACH,CAAC;;WAEU,QAAQ;;AACrB,CAAC"}