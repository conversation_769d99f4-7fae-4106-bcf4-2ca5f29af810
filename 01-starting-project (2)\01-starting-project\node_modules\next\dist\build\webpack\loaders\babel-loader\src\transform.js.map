{"version": 3, "sources": ["../../../../../../build/webpack/loaders/babel-loader/src/transform.js"], "sourcesContent": ["import { transform as _transform } from 'next/dist/compiled/babel/core'\nimport { promisify } from 'util'\nimport LoaderError from './Error'\n\nconst transform = promisify(_transform)\n\nexport default async function (source, options) {\n  let result\n  try {\n    result = await transform(source, options)\n  } catch (err) {\n    throw err.message && err.codeFrame ? new LoaderError(err) : err\n  }\n\n  if (!result) return null\n\n  // We don't return the full result here because some entries are not\n  // really serializable. For a full list of properties see here:\n  // https://github.com/babel/babel/blob/main/packages/babel-core/src/transformation/index.js\n  // For discussion on this topic see here:\n  // https://github.com/babel/babel-loader/pull/629\n  const { ast, code, map, metadata, sourceType } = result\n\n  if (map && (!map.sourcesContent || !map.sourcesContent.length)) {\n    map.sourcesContent = [source]\n  }\n\n  return { ast, code, map, metadata, sourceType }\n}\n"], "names": [], "mappings": ";;;;;AAAwC,GAA+B,CAA/B,KAA+B;AAC7C,GAAM,CAAN,KAAM;AACR,GAAS,CAAT,MAAS;;;;;;AAEjC,KAAK,CAAC,SAAS,OAHW,KAAM,YADQ,KAA+B;wBAMxC,MAAM,EAAE,OAAO,EAAE,CAAC;IAC/C,GAAG,CAAC,MAAM;QACN,CAAC;QACH,MAAM,SAAS,SAAS,CAAC,MAAM,EAAE,OAAO;IAC1C,CAAC,QAAQ,GAAG,EAAE,CAAC;QACb,KAAK,CAAC,GAAG,CAAC,OAAO,IAAI,GAAG,CAAC,SAAS,GAAG,GAAG,CATpB,MAAS,SASwB,GAAG,IAAI,GAAG;IACjE,CAAC;IAED,EAAE,GAAG,MAAM,SAAS,IAAI;IAExB,EAAoE,AAApE,kEAAoE;IACpE,EAA+D,AAA/D,6DAA+D;IAC/D,EAA2F,AAA3F,yFAA2F;IAC3F,EAAyC,AAAzC,uCAAyC;IACzC,EAAiD,AAAjD,+CAAiD;IACjD,KAAK,GAAG,GAAG,GAAE,IAAI,GAAE,GAAG,GAAE,QAAQ,GAAE,UAAU,MAAK,MAAM;IAEvD,EAAE,EAAE,GAAG,MAAM,GAAG,CAAC,cAAc,KAAK,GAAG,CAAC,cAAc,CAAC,MAAM,GAAG,CAAC;QAC/D,GAAG,CAAC,cAAc;YAAI,MAAM;;IAC9B,CAAC;;QAEQ,GAAG;QAAE,IAAI;QAAE,GAAG;QAAE,QAAQ;QAAE,UAAU;;AAC/C,CAAC"}