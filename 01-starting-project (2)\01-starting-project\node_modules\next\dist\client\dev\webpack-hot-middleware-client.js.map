{"version": 3, "sources": ["../../../client/dev/webpack-hot-middleware-client.js"], "sourcesContent": ["import connect from './error-overlay/hot-dev-client'\n\nexport default () => {\n  const devClient = connect()\n\n  devClient.subscribeToHmrEvent((obj) => {\n    if (obj.action === 'reloadPage') {\n      return window.location.reload()\n    }\n    if (obj.action === 'removedPage') {\n      const [page] = obj.data\n      if (page === window.next.router.pathname) {\n        return window.location.reload()\n      }\n      return\n    }\n    if (obj.action === 'addedPage') {\n      const [page] = obj.data\n      if (\n        page === window.next.router.pathname &&\n        typeof window.next.router.components[page] === 'undefined'\n      ) {\n        return window.location.reload()\n      }\n      return\n    }\n    throw new Error('Unexpected action ' + obj.action)\n  })\n\n  return devClient\n}\n"], "names": [], "mappings": ";;;;;AAAoB,GAAgC,CAAhC,aAAgC;;;;;;mBAE/B,CAAC;IACpB,KAAK,CAAC,SAAS,OAHG,aAAgC;IAKlD,SAAS,CAAC,mBAAmB,EAAE,GAAG,GAAK,CAAC;QACtC,EAAE,EAAE,GAAG,CAAC,MAAM,MAAK,UAAY,GAAE,CAAC;mBACzB,MAAM,CAAC,QAAQ,CAAC,MAAM;QAC/B,CAAC;QACD,EAAE,EAAE,GAAG,CAAC,MAAM,MAAK,WAAa,GAAE,CAAC;YACjC,KAAK,EAAE,IAAI,IAAI,GAAG,CAAC,IAAI;YACvB,EAAE,EAAE,IAAI,KAAK,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC;uBAClC,MAAM,CAAC,QAAQ,CAAC,MAAM;YAC/B,CAAC;;QAEH,CAAC;QACD,EAAE,EAAE,GAAG,CAAC,MAAM,MAAK,SAAW,GAAE,CAAC;YAC/B,KAAK,EAAE,IAAI,IAAI,GAAG,CAAC,IAAI;YACvB,EAAE,EACA,IAAI,KAAK,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,WAC7B,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,IAAI,OAAM,SAAW,GAC1D,CAAC;uBACM,MAAM,CAAC,QAAQ,CAAC,MAAM;YAC/B,CAAC;;QAEH,CAAC;QACD,KAAK,CAAC,GAAG,CAAC,KAAK,EAAC,kBAAoB,IAAG,GAAG,CAAC,MAAM;IACnD,CAAC;WAEM,SAAS;AAClB,CAAC"}