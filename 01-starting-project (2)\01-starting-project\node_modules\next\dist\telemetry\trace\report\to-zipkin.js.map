{"version": 3, "sources": ["../../../../telemetry/trace/report/to-zipkin.ts"], "sourcesContent": ["import retry from 'next/dist/compiled/async-retry'\nimport { randomBytes } from 'crypto'\nimport fetch from 'node-fetch'\nimport * as Log from '../../../build/output/log'\n\nlet traceId: string\nlet batch: ReturnType<typeof batcher> | undefined\n\nconst localEndpoint = {\n  serviceName: 'nextjs',\n  ipv4: '127.0.0.1',\n  port: 9411,\n}\nconst zipkinUrl = `http://${localEndpoint.ipv4}:${localEndpoint.port}`\nconst zipkinAPI = `${zipkinUrl}/api/v2/spans`\n\ntype Event = {\n  traceId: string\n  parentId?: string\n  name: string\n  id: string\n  timestamp: number\n  duration: number\n  localEndpoint?: typeof localEndpoint\n  tags?: Object\n}\n\n// Batch events as zipkin allows for multiple events to be sent in one go\nexport function batcher(reportEvents: (evts: Event[]) => Promise<void>) {\n  const events: Event[] = []\n  // Promise queue to ensure events are always sent on flushAll\n  const queue = new Set()\n  return {\n    flushAll: async () => {\n      await Promise.all(queue)\n      if (events.length > 0) {\n        await reportEvents(events)\n        events.length = 0\n      }\n    },\n    report: (event: Event) => {\n      events.push(event)\n\n      if (events.length > 100) {\n        const evts = events.slice()\n        events.length = 0\n        const report = reportEvents(evts)\n        queue.add(report)\n        report.then(() => queue.delete(report))\n      }\n    },\n  }\n}\n\nconst reportToLocalHost = (\n  name: string,\n  duration: number,\n  timestamp: number,\n  id: string,\n  parentId?: string,\n  attrs?: Object\n) => {\n  if (!traceId) {\n    traceId = process.env.TRACE_ID || randomBytes(8).toString('hex')\n    Log.info(\n      `Zipkin trace will be available on ${zipkinUrl}/zipkin/traces/${traceId}`\n    )\n  }\n\n  if (!batch) {\n    batch = batcher((events) => {\n      // Ensure ECONNRESET error is retried 3 times before erroring out\n      return retry(\n        () =>\n          // Send events to zipkin\n          fetch(zipkinAPI, {\n            method: 'POST',\n            headers: { 'Content-Type': 'application/json' },\n            body: JSON.stringify(events),\n          }),\n        { minTimeout: 500, retries: 3, factor: 1 }\n      ).catch(console.log)\n    })\n  }\n\n  batch.report({\n    traceId,\n    parentId,\n    name,\n    id,\n    timestamp,\n    duration,\n    localEndpoint,\n    tags: attrs,\n  })\n}\n\nexport default {\n  flushAll: () => (batch ? batch.flushAll() : undefined),\n  report: reportToLocalHost,\n}\n"], "names": [], "mappings": ";;;;QA4BgB,OAAO,GAAP,OAAO;;AA5BL,GAAgC,CAAhC,WAAgC;AACtB,GAAQ,CAAR,OAAQ;AAClB,GAAY,CAAZ,UAAY;AAClB,GAAG,CAAH,GAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEf,GAAG,CAAC,OAAO;AACX,GAAG,CAAC,KAAK;AAET,KAAK,CAAC,aAAa;IACjB,WAAW,GAAE,MAAQ;IACrB,IAAI,GAAE,SAAW;IACjB,IAAI,EAAE,IAAI;;AAEZ,KAAK,CAAC,SAAS,IAAI,OAAO,EAAE,aAAa,CAAC,IAAI,CAAC,CAAC,EAAE,aAAa,CAAC,IAAI;AACpE,KAAK,CAAC,SAAS,MAAM,SAAS,CAAC,aAAa;SAc5B,OAAO,CAAC,YAA8C,EAAE,CAAC;IACvE,KAAK,CAAC,MAAM;IACZ,EAA6D,AAA7D,2DAA6D;IAC7D,KAAK,CAAC,KAAK,GAAG,GAAG,CAAC,GAAG;;QAEnB,QAAQ,YAAc,CAAC;kBACf,OAAO,CAAC,GAAG,CAAC,KAAK;YACvB,EAAE,EAAE,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;sBAChB,YAAY,CAAC,MAAM;gBACzB,MAAM,CAAC,MAAM,GAAG,CAAC;YACnB,CAAC;QACH,CAAC;QACD,MAAM,GAAG,KAAY,GAAK,CAAC;YACzB,MAAM,CAAC,IAAI,CAAC,KAAK;YAEjB,EAAE,EAAE,MAAM,CAAC,MAAM,GAAG,GAAG,EAAE,CAAC;gBACxB,KAAK,CAAC,IAAI,GAAG,MAAM,CAAC,KAAK;gBACzB,MAAM,CAAC,MAAM,GAAG,CAAC;gBACjB,KAAK,CAAC,MAAM,GAAG,YAAY,CAAC,IAAI;gBAChC,KAAK,CAAC,GAAG,CAAC,MAAM;gBAChB,MAAM,CAAC,IAAI,KAAO,KAAK,CAAC,MAAM,CAAC,MAAM;;YACvC,CAAC;QACH,CAAC;;AAEL,CAAC;AAED,KAAK,CAAC,iBAAiB,IACrB,IAAY,EACZ,QAAgB,EAChB,SAAiB,EACjB,EAAU,EACV,QAAiB,EACjB,KAAc,GACX,CAAC;IACJ,EAAE,GAAG,OAAO,EAAE,CAAC;QACb,OAAO,GAAG,OAAO,CAAC,GAAG,CAAC,QAAQ,QA9DN,OAAQ,cA8Dc,CAAC,EAAE,QAAQ,EAAC,GAAK;QA5DvD,GAAG,CA6DP,IAAI,EACL,kCAAkC,EAAE,SAAS,CAAC,eAAe,EAAE,OAAO;IAE3E,CAAC;IAED,EAAE,GAAG,KAAK,EAAE,CAAC;QACX,KAAK,GAAG,OAAO,EAAE,MAAM,GAAK,CAAC;YAC3B,EAAiE,AAAjE,+DAAiE;uBAvErD,WAAgC,cA0ExC,EAAwB,AAAxB,sBAAwB;oBAxEhB,UAAY,UAyEd,SAAS;oBACb,MAAM,GAAE,IAAM;oBACd,OAAO;yBAAI,YAAc,IAAE,gBAAkB;;oBAC7C,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,MAAM;;;gBAE7B,UAAU,EAAE,GAAG;gBAAE,OAAO,EAAE,CAAC;gBAAE,MAAM,EAAE,CAAC;eACxC,KAAK,CAAC,OAAO,CAAC,GAAG;QACrB,CAAC;IACH,CAAC;IAED,KAAK,CAAC,MAAM;QACV,OAAO;QACP,QAAQ;QACR,IAAI;QACJ,EAAE;QACF,SAAS;QACT,QAAQ;QACR,aAAa;QACb,IAAI,EAAE,KAAK;;AAEf,CAAC;;IAGC,QAAQ,MAAS,KAAK,GAAG,KAAK,CAAC,QAAQ,KAAK,SAAS;;IACrD,MAAM,EAAE,iBAAiB"}