{"version": 3, "sources": ["../../../../../shared/lib/router/utils/path-match.ts"], "sourcesContent": ["import * as pathToRegexp from 'next/dist/compiled/path-to-regexp'\n\nexport { pathToRegexp }\n\nexport const matcherOptions: pathToRegexp.TokensToRegexpOptions &\n  pathToRegexp.ParseOptions = {\n  sensitive: false,\n  delimiter: '/',\n}\n\nexport const customRouteMatcherOptions: pathToRegexp.TokensToRegexpOptions &\n  pathToRegexp.ParseOptions = {\n  ...matcherOptions,\n  strict: true,\n}\n\nexport default (customRoute = false) => {\n  return (path: string, regexModifier?: (regex: string) => string) => {\n    const keys: pathToRegexp.Key[] = []\n    let matcherRegex = pathToRegexp.pathToRegexp(\n      path,\n      keys,\n      customRoute ? customRouteMatcherOptions : matcherOptions\n    )\n\n    if (regexModifier) {\n      const regexSource = regexModifier(matcherRegex.source)\n      matcherRegex = new RegExp(regexSource, matcherRegex.flags)\n    }\n\n    const matcher = pathToRegexp.regexpToFunction(matcherRegex, keys)\n\n    return (pathname: string | null | undefined, params?: any) => {\n      const res = pathname == null ? false : matcher(pathname)\n      if (!res) {\n        return false\n      }\n\n      if (customRoute) {\n        for (const key of keys) {\n          // unnamed params should be removed as they\n          // are not allowed to be used in the destination\n          if (typeof key.name === 'number') {\n            delete (res.params as any)[key.name]\n          }\n        }\n      }\n\n      return { ...params, ...res.params }\n    }\n  }\n}\n"], "names": [], "mappings": ";;;;;AAAY,GAAY,CAAZ,YAAY;;;;;;;;;;;;;;;;;;;;;;;;QAEf,YAAY,GAFT,YAAY;AAIjB,KAAK,CAAC,cAAc;IAEzB,SAAS,EAAE,KAAK;IAChB,SAAS,GAAE,CAAG;;QAHH,cAAc,GAAd,cAAc;AAMpB,KAAK,CAAC,yBAAyB;OAEjC,cAAc;IACjB,MAAM,EAAE,IAAI;;QAHD,yBAAyB,GAAzB,yBAAyB;gBAMtB,WAAW,GAAG,KAAK,GAAK,CAAC;YAC/B,IAAY,EAAE,aAAyC,GAAK,CAAC;QACnE,KAAK,CAAC,IAAI;QACV,GAAG,CAAC,YAAY,GAnBR,YAAY,CAmBY,YAAY,CAC1C,IAAI,EACJ,IAAI,EACJ,WAAW,GAAG,yBAAyB,GAAG,cAAc;QAG1D,EAAE,EAAE,aAAa,EAAE,CAAC;YAClB,KAAK,CAAC,WAAW,GAAG,aAAa,CAAC,YAAY,CAAC,MAAM;YACrD,YAAY,GAAG,GAAG,CAAC,MAAM,CAAC,WAAW,EAAE,YAAY,CAAC,KAAK;QAC3D,CAAC;QAED,KAAK,CAAC,OAAO,GA9BL,YAAY,CA8BS,gBAAgB,CAAC,YAAY,EAAE,IAAI;gBAExD,QAAmC,EAAE,MAAY,GAAK,CAAC;YAC7D,KAAK,CAAC,GAAG,GAAG,QAAQ,IAAI,IAAI,GAAG,KAAK,GAAG,OAAO,CAAC,QAAQ;YACvD,EAAE,GAAG,GAAG,EAAE,CAAC;uBACF,KAAK;YACd,CAAC;YAED,EAAE,EAAE,WAAW,EAAE,CAAC;qBACX,KAAK,CAAC,GAAG,IAAI,IAAI,CAAE,CAAC;oBACvB,EAA2C,AAA3C,yCAA2C;oBAC3C,EAAgD,AAAhD,8CAAgD;oBAChD,EAAE,SAAS,GAAG,CAAC,IAAI,MAAK,MAAQ,GAAE,CAAC;+BACzB,GAAG,CAAC,MAAM,CAAS,GAAG,CAAC,IAAI;oBACrC,CAAC;gBACH,CAAC;YACH,CAAC;;mBAEW,MAAM;mBAAK,GAAG,CAAC,MAAM;;QACnC,CAAC;IACH,CAAC;AACH,CAAC"}