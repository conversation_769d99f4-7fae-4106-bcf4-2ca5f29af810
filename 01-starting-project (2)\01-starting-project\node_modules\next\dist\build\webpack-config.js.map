{"version": 3, "sources": ["../../build/webpack-config.ts"], "sourcesContent": ["import ReactRefreshWebpackPlugin from '@next/react-refresh-utils/ReactRefreshWebpackPlugin'\nimport chalk from 'chalk'\nimport crypto from 'crypto'\nimport { readFileSync } from 'fs'\nimport { codeFrameColumns } from 'next/dist/compiled/babel/code-frame'\nimport semver from 'next/dist/compiled/semver'\nimport { isWebpack5, webpack } from 'next/dist/compiled/webpack/webpack'\nimport path, { join as pathJoin, relative as relativePath } from 'path'\nimport {\n  DOT_NEXT_ALIAS,\n  NEXT_PROJECT_ROOT,\n  NEXT_PROJECT_ROOT_DIST_CLIENT,\n  PAGES_DIR_ALIAS,\n} from '../lib/constants'\nimport { fileExists } from '../lib/file-exists'\nimport { getPackageVersion } from '../lib/get-package-version'\nimport { CustomRoutes } from '../lib/load-custom-routes.js'\nimport { getTypeScriptConfiguration } from '../lib/typescript/getTypeScriptConfiguration'\nimport {\n  CLIENT_STATIC_FILES_RUNTIME_AMP,\n  CLIENT_STATIC_FILES_RUNTIME_MAIN,\n  CLIENT_STATIC_FILES_RUNTIME_POLYFILLS_SYMBOL,\n  CLIENT_STATIC_FILES_RUNTIME_REACT_REFRESH,\n  CLIENT_STATIC_FILES_RUNTIME_WEBPACK,\n  REACT_LOADABLE_MANIFEST,\n  SERVERLESS_DIRECTORY,\n  SERVER_DIRECTORY,\n} from '../shared/lib/constants'\nimport { execOnce } from '../shared/lib/utils'\nimport { NextConfigComplete } from '../server/config-shared'\nimport { WebpackEntrypoints } from './entries'\nimport * as Log from './output/log'\nimport { build as buildConfiguration } from './webpack/config'\nimport { __overrideCssConfiguration } from './webpack/config/blocks/css/overrideCssConfiguration'\nimport BuildManifestPlugin from './webpack/plugins/build-manifest-plugin'\nimport BuildStatsPlugin from './webpack/plugins/build-stats-plugin'\nimport ChunkNamesPlugin from './webpack/plugins/chunk-names-plugin'\nimport { JsConfigPathsPlugin } from './webpack/plugins/jsconfig-paths-plugin'\nimport { DropClientPage } from './webpack/plugins/next-drop-client-page-plugin'\nimport { TraceEntryPointsPlugin } from './webpack/plugins/next-trace-entrypoints-plugin'\nimport NextJsSsrImportPlugin from './webpack/plugins/nextjs-ssr-import'\nimport NextJsSSRModuleCachePlugin from './webpack/plugins/nextjs-ssr-module-cache'\nimport PagesManifestPlugin from './webpack/plugins/pages-manifest-plugin'\nimport { ProfilingPlugin } from './webpack/plugins/profiling-plugin'\nimport { ReactLoadablePlugin } from './webpack/plugins/react-loadable-plugin'\nimport { ServerlessPlugin } from './webpack/plugins/serverless-plugin'\nimport WebpackConformancePlugin, {\n  DuplicatePolyfillsConformanceCheck,\n  GranularChunksConformanceCheck,\n  MinificationConformanceCheck,\n  ReactSyncScriptsConformanceCheck,\n} from './webpack/plugins/webpack-conformance-plugin'\nimport { WellKnownErrorsPlugin } from './webpack/plugins/wellknown-errors-plugin'\nimport { regexLikeCss } from './webpack/config/blocks/css'\nimport { CopyFilePlugin } from './webpack/plugins/copy-file-plugin'\nimport type { Span } from '../telemetry/trace'\n\ntype ExcludesFalse = <T>(x: T | false) => x is T\n\nconst devtoolRevertWarning = execOnce(\n  (devtool: webpack.Configuration['devtool']) => {\n    console.warn(\n      chalk.yellow.bold('Warning: ') +\n        chalk.bold(`Reverting webpack devtool to '${devtool}'.\\n`) +\n        'Changing the webpack devtool in development mode will cause severe performance regressions.\\n' +\n        'Read more: https://nextjs.org/docs/messages/improper-devtool'\n    )\n  }\n)\n\nfunction parseJsonFile(filePath: string) {\n  const JSON5 = require('next/dist/compiled/json5')\n  const contents = readFileSync(filePath, 'utf8')\n\n  // Special case an empty file\n  if (contents.trim() === '') {\n    return {}\n  }\n\n  try {\n    return JSON5.parse(contents)\n  } catch (err) {\n    const codeFrame = codeFrameColumns(\n      String(contents),\n      { start: { line: err.lineNumber, column: err.columnNumber } },\n      { message: err.message, highlightCode: true }\n    )\n    throw new Error(`Failed to parse \"${filePath}\":\\n${codeFrame}`)\n  }\n}\n\nfunction getOptimizedAliases(isServer: boolean): { [pkg: string]: string } {\n  if (isServer) {\n    return {}\n  }\n\n  const stubWindowFetch = path.join(__dirname, 'polyfills', 'fetch', 'index.js')\n  const stubObjectAssign = path.join(__dirname, 'polyfills', 'object-assign.js')\n\n  const shimAssign = path.join(__dirname, 'polyfills', 'object.assign')\n  return Object.assign(\n    {},\n    {\n      unfetch$: stubWindowFetch,\n      'isomorphic-unfetch$': stubWindowFetch,\n      'whatwg-fetch$': path.join(\n        __dirname,\n        'polyfills',\n        'fetch',\n        'whatwg-fetch.js'\n      ),\n    },\n    {\n      'object-assign$': stubObjectAssign,\n\n      // Stub Package: object.assign\n      'object.assign/auto': path.join(shimAssign, 'auto.js'),\n      'object.assign/implementation': path.join(\n        shimAssign,\n        'implementation.js'\n      ),\n      'object.assign$': path.join(shimAssign, 'index.js'),\n      'object.assign/polyfill': path.join(shimAssign, 'polyfill.js'),\n      'object.assign/shim': path.join(shimAssign, 'shim.js'),\n\n      // Replace: full URL polyfill with platform-based polyfill\n      url: require.resolve('native-url'),\n    }\n  )\n}\n\ntype ClientEntries = {\n  [key: string]: string | string[]\n}\n\nexport function attachReactRefresh(\n  webpackConfig: webpack.Configuration,\n  targetLoader: webpack.RuleSetUseItem\n) {\n  let injections = 0\n  const reactRefreshLoaderName = '@next/react-refresh-utils/loader'\n  const reactRefreshLoader = require.resolve(reactRefreshLoaderName)\n  webpackConfig.module?.rules.forEach((rule) => {\n    const curr = rule.use\n    // When the user has configured `defaultLoaders.babel` for a input file:\n    if (curr === targetLoader) {\n      ++injections\n      rule.use = [reactRefreshLoader, curr as webpack.RuleSetUseItem]\n    } else if (\n      Array.isArray(curr) &&\n      curr.some((r) => r === targetLoader) &&\n      // Check if loader already exists:\n      !curr.some(\n        (r) => r === reactRefreshLoader || r === reactRefreshLoaderName\n      )\n    ) {\n      ++injections\n      const idx = curr.findIndex((r) => r === targetLoader)\n      // Clone to not mutate user input\n      rule.use = [...curr]\n\n      // inject / input: [other, babel] output: [other, refresh, babel]:\n      rule.use.splice(idx, 0, reactRefreshLoader)\n    }\n  })\n\n  if (injections) {\n    Log.info(\n      `automatically enabled Fast Refresh for ${injections} custom loader${\n        injections > 1 ? 's' : ''\n      }`\n    )\n  }\n}\n\nconst WEBPACK_RESOLVE_OPTIONS = {\n  // This always uses commonjs resolving, assuming API is identical\n  // between ESM and CJS in a package\n  // Otherwise combined ESM+CJS packages will never be external\n  // as resolving mismatch would lead to opt-out from being external.\n  dependencyType: 'commonjs',\n  symlinks: true,\n}\n\nconst WEBPACK_ESM_RESOLVE_OPTIONS = {\n  dependencyType: 'esm',\n  symlinks: true,\n}\n\nconst NODE_RESOLVE_OPTIONS = {\n  dependencyType: 'commonjs',\n  modules: ['node_modules'],\n  alias: false,\n  fallback: false,\n  exportsFields: ['exports'],\n  importsFields: ['imports'],\n  conditionNames: ['node', 'require', 'module'],\n  descriptionFiles: ['package.json'],\n  extensions: ['.js', '.json', '.node'],\n  enforceExtensions: false,\n  symlinks: true,\n  mainFields: ['main'],\n  mainFiles: ['index'],\n  roots: [],\n  fullySpecified: false,\n  preferRelative: false,\n  preferAbsolute: false,\n  restrictions: [],\n}\n\nconst NODE_ESM_RESOLVE_OPTIONS = {\n  ...NODE_RESOLVE_OPTIONS,\n  dependencyType: 'esm',\n  conditionNames: ['node', 'import', 'module'],\n  fullySpecified: true,\n}\n\nexport default async function getBaseWebpackConfig(\n  dir: string,\n  {\n    buildId,\n    config,\n    dev = false,\n    isServer = false,\n    pagesDir,\n    target = 'server',\n    reactProductionProfiling = false,\n    entrypoints,\n    rewrites,\n    isDevFallback = false,\n    runWebpackSpan,\n  }: {\n    buildId: string\n    config: NextConfigComplete\n    dev?: boolean\n    isServer?: boolean\n    pagesDir: string\n    target?: string\n    reactProductionProfiling?: boolean\n    entrypoints: WebpackEntrypoints\n    rewrites: CustomRoutes['rewrites']\n    isDevFallback?: boolean\n    runWebpackSpan: Span\n  }\n): Promise<webpack.Configuration> {\n  const hasRewrites =\n    rewrites.beforeFiles.length > 0 ||\n    rewrites.afterFiles.length > 0 ||\n    rewrites.fallback.length > 0\n  const hasReactRefresh: boolean = dev && !isServer\n  const reactDomVersion = await getPackageVersion({\n    cwd: dir,\n    name: 'react-dom',\n  })\n  const hasReact18: boolean =\n    Boolean(reactDomVersion) &&\n    (semver.gte(reactDomVersion!, '18.0.0') ||\n      semver.coerce(reactDomVersion)?.version === '18.0.0')\n  const hasReactPrerelease =\n    Boolean(reactDomVersion) && semver.prerelease(reactDomVersion!) != null\n  const hasReactRoot: boolean = config.experimental.reactRoot || hasReact18\n\n  // Only inform during one of the builds\n  if (!isServer) {\n    if (hasReactRoot) {\n      Log.info('Using the createRoot API for React')\n    }\n    if (hasReactPrerelease) {\n      Log.warn(\n        `You are using an unsupported prerelease of 'react-dom' which may cause ` +\n          `unexpected or broken application behavior. Continue at your own risk.`\n      )\n    }\n  }\n\n  const babelConfigFile = await [\n    '.babelrc',\n    '.babelrc.json',\n    '.babelrc.js',\n    '.babelrc.mjs',\n    '.babelrc.cjs',\n    'babel.config.js',\n    'babel.config.json',\n    'babel.config.mjs',\n    'babel.config.cjs',\n  ].reduce(async (memo: Promise<string | undefined>, filename) => {\n    const configFilePath = path.join(dir, filename)\n    return (\n      (await memo) ||\n      ((await fileExists(configFilePath)) ? configFilePath : undefined)\n    )\n  }, Promise.resolve(undefined))\n\n  const distDir = path.join(dir, config.distDir)\n\n  // Webpack 5 can use the faster babel loader, webpack 5 has built-in caching for loaders\n  // For webpack 4 the old loader is used as it has external caching\n  const babelLoader = isWebpack5\n    ? require.resolve('./babel/loader/index')\n    : 'next-babel-loader'\n\n  const useSWCLoader = config.experimental.swcLoader && isWebpack5\n  if (useSWCLoader && babelConfigFile) {\n    Log.warn(\n      `experimental.swcLoader enabled. The custom Babel configuration will not be used.`\n    )\n  }\n  const defaultLoaders = {\n    babel: useSWCLoader\n      ? {\n          loader: 'next-swc-loader',\n          options: {\n            isServer,\n          },\n        }\n      : {\n          loader: babelLoader,\n          options: {\n            configFile: babelConfigFile,\n            isServer,\n            distDir,\n            pagesDir,\n            cwd: dir,\n            // Webpack 5 has a built-in loader cache\n            cache: !isWebpack5,\n            development: dev,\n            hasReactRefresh,\n            hasJsxRuntime: true,\n          },\n        },\n    // Backwards compat\n    hotSelfAccept: {\n      loader: 'noop-loader',\n    },\n  }\n\n  const babelIncludeRegexes: RegExp[] = [\n    /next[\\\\/]dist[\\\\/]shared[\\\\/]lib/,\n    /next[\\\\/]dist[\\\\/]client/,\n    /next[\\\\/]dist[\\\\/]pages/,\n    /[\\\\/](strip-ansi|ansi-regex)[\\\\/]/,\n  ]\n\n  // Support for NODE_PATH\n  const nodePathList = (process.env.NODE_PATH || '')\n    .split(process.platform === 'win32' ? ';' : ':')\n    .filter((p) => !!p)\n\n  const isServerless = target === 'serverless'\n  const isServerlessTrace = target === 'experimental-serverless-trace'\n  // Intentionally not using isTargetLikeServerless helper\n  const isLikeServerless = isServerless || isServerlessTrace\n\n  const outputDir = isLikeServerless ? SERVERLESS_DIRECTORY : SERVER_DIRECTORY\n  const outputPath = path.join(distDir, isServer ? outputDir : '')\n  const totalPages = Object.keys(entrypoints).length\n  const clientEntries = !isServer\n    ? ({\n        // Backwards compatibility\n        'main.js': [],\n        ...(dev\n          ? {\n              [CLIENT_STATIC_FILES_RUNTIME_REACT_REFRESH]: require.resolve(\n                `@next/react-refresh-utils/runtime`\n              ),\n              [CLIENT_STATIC_FILES_RUNTIME_AMP]:\n                `./` +\n                relativePath(\n                  dir,\n                  pathJoin(NEXT_PROJECT_ROOT_DIST_CLIENT, 'dev', 'amp-dev')\n                ).replace(/\\\\/g, '/'),\n            }\n          : {}),\n        [CLIENT_STATIC_FILES_RUNTIME_MAIN]:\n          `./` +\n          path\n            .relative(\n              dir,\n              path.join(\n                NEXT_PROJECT_ROOT_DIST_CLIENT,\n                dev ? `next-dev.js` : 'next.js'\n              )\n            )\n            .replace(/\\\\/g, '/'),\n      } as ClientEntries)\n    : undefined\n\n  let typeScriptPath: string | undefined\n  try {\n    typeScriptPath = require.resolve('typescript', { paths: [dir] })\n  } catch (_) {}\n  const tsConfigPath = path.join(dir, 'tsconfig.json')\n  const useTypeScript = Boolean(\n    typeScriptPath && (await fileExists(tsConfigPath))\n  )\n\n  let jsConfig\n  // jsconfig is a subset of tsconfig\n  if (useTypeScript) {\n    const ts = (await import(typeScriptPath!)) as typeof import('typescript')\n    const tsConfig = await getTypeScriptConfiguration(ts, tsConfigPath, true)\n    jsConfig = { compilerOptions: tsConfig.options }\n  }\n\n  const jsConfigPath = path.join(dir, 'jsconfig.json')\n  if (!useTypeScript && (await fileExists(jsConfigPath))) {\n    jsConfig = parseJsonFile(jsConfigPath)\n  }\n\n  let resolvedBaseUrl\n  if (jsConfig?.compilerOptions?.baseUrl) {\n    resolvedBaseUrl = path.resolve(dir, jsConfig.compilerOptions.baseUrl)\n  }\n\n  function getReactProfilingInProduction() {\n    if (reactProductionProfiling) {\n      return {\n        'react-dom$': 'react-dom/profiling',\n        'scheduler/tracing': 'scheduler/tracing-profiling',\n      }\n    }\n  }\n\n  // tell webpack where to look for _app and _document\n  // using aliases to allow falling back to the default\n  // version when removed or not present\n  const clientResolveRewrites = require.resolve(\n    '../shared/lib/router/utils/resolve-rewrites'\n  )\n  const clientResolveRewritesNoop = require.resolve(\n    '../shared/lib/router/utils/resolve-rewrites-noop'\n  )\n\n  const customAppAliases: { [key: string]: string[] } = {}\n  const customErrorAlias: { [key: string]: string[] } = {}\n  const customDocumentAliases: { [key: string]: string[] } = {}\n\n  if (dev && isWebpack5) {\n    customAppAliases[`${PAGES_DIR_ALIAS}/_app`] = [\n      ...config.pageExtensions.reduce((prev, ext) => {\n        prev.push(path.join(pagesDir, `_app.${ext}`))\n        return prev\n      }, [] as string[]),\n      'next/dist/pages/_app.js',\n    ]\n    customAppAliases[`${PAGES_DIR_ALIAS}/_error`] = [\n      ...config.pageExtensions.reduce((prev, ext) => {\n        prev.push(path.join(pagesDir, `_error.${ext}`))\n        return prev\n      }, [] as string[]),\n      'next/dist/pages/_error.js',\n    ]\n    customDocumentAliases[`${PAGES_DIR_ALIAS}/_document`] = [\n      ...config.pageExtensions.reduce((prev, ext) => {\n        prev.push(path.join(pagesDir, `_document.${ext}`))\n        return prev\n      }, [] as string[]),\n      'next/dist/pages/_document.js',\n    ]\n  }\n\n  const resolveConfig = {\n    // Disable .mjs for node_modules bundling\n    extensions: isServer\n      ? [\n          '.js',\n          '.mjs',\n          ...(useTypeScript ? ['.tsx', '.ts'] : []),\n          '.jsx',\n          '.json',\n          '.wasm',\n        ]\n      : [\n          '.mjs',\n          '.js',\n          ...(useTypeScript ? ['.tsx', '.ts'] : []),\n          '.jsx',\n          '.json',\n          '.wasm',\n        ],\n    modules: [\n      'node_modules',\n      ...nodePathList, // Support for NODE_PATH environment variable\n    ],\n    alias: {\n      next: NEXT_PROJECT_ROOT,\n\n      ...customAppAliases,\n      ...customErrorAlias,\n      ...customDocumentAliases,\n\n      [PAGES_DIR_ALIAS]: pagesDir,\n      [DOT_NEXT_ALIAS]: distDir,\n      ...getOptimizedAliases(isServer),\n      ...getReactProfilingInProduction(),\n      [clientResolveRewrites]: hasRewrites\n        ? clientResolveRewrites\n        : // With webpack 5 an alias can be pointed to false to noop\n        isWebpack5\n        ? false\n        : clientResolveRewritesNoop,\n    },\n    ...(isWebpack5 && !isServer\n      ? {\n          // Full list of old polyfills is accessible here:\n          // https://github.com/webpack/webpack/blob/2a0536cf510768111a3a6dceeb14cb79b9f59273/lib/ModuleNotFoundError.js#L13-L42\n          fallback: {\n            assert: require.resolve('assert/'),\n            buffer: require.resolve('buffer/'),\n            constants: require.resolve('constants-browserify'),\n            crypto: require.resolve('crypto-browserify'),\n            domain: require.resolve('domain-browser'),\n            http: require.resolve('stream-http'),\n            https: require.resolve('https-browserify'),\n            os: require.resolve('os-browserify/browser'),\n            path: require.resolve('path-browserify'),\n            punycode: require.resolve('punycode'),\n            process: require.resolve('process/browser'),\n            // Handled in separate alias\n            querystring: require.resolve('querystring-es3'),\n            stream: require.resolve('stream-browserify'),\n            string_decoder: require.resolve('string_decoder'),\n            sys: require.resolve('util/'),\n            timers: require.resolve('timers-browserify'),\n            tty: require.resolve('tty-browserify'),\n            // Handled in separate alias\n            // url: require.resolve('url/'),\n            util: require.resolve('util/'),\n            vm: require.resolve('vm-browserify'),\n            zlib: require.resolve('browserify-zlib'),\n          },\n        }\n      : undefined),\n    mainFields: isServer ? ['main', 'module'] : ['browser', 'module', 'main'],\n    plugins: isWebpack5\n      ? // webpack 5+ has the PnP resolver built-in by default:\n        []\n      : [require('pnp-webpack-plugin')],\n  }\n\n  const terserOptions: any = {\n    parse: {\n      ecma: 8,\n    },\n    compress: {\n      ecma: 5,\n      warnings: false,\n      // The following two options are known to break valid JavaScript code\n      comparisons: false,\n      inline: 2, // https://github.com/vercel/next.js/issues/7178#issuecomment-493048965\n    },\n    mangle: { safari10: true },\n    output: {\n      ecma: 5,\n      safari10: true,\n      comments: false,\n      // Fixes usage of Emoji and certain Regex\n      ascii_only: true,\n    },\n  }\n\n  const isModuleCSS = (module: { type: string }): boolean => {\n    return (\n      // mini-css-extract-plugin\n      module.type === `css/mini-extract` ||\n      // extract-css-chunks-webpack-plugin (old)\n      module.type === `css/extract-chunks` ||\n      // extract-css-chunks-webpack-plugin (new)\n      module.type === `css/extract-css-chunks`\n    )\n  }\n\n  // Contains various versions of the Webpack SplitChunksPlugin used in different build types\n  const splitChunksConfigs: {\n    [propName: string]: webpack.Options.SplitChunksOptions | false\n  } = {\n    dev: {\n      cacheGroups: {\n        default: false,\n        vendors: false,\n      },\n    },\n    prodGranular: {\n      // Keep main and _app chunks unsplitted in webpack 5\n      // as we don't need a separate vendor chunk from that\n      // and all other chunk depend on them so there is no\n      // duplication that need to be pulled out.\n      chunks: isWebpack5\n        ? (chunk) => !/^(polyfills|main|pages\\/_app)$/.test(chunk.name)\n        : 'all',\n      cacheGroups: {\n        framework: {\n          chunks: 'all',\n          name: 'framework',\n          // This regex ignores nested copies of framework libraries so they're\n          // bundled with their issuer.\n          // https://github.com/vercel/next.js/pull/9012\n          test: /(?<!node_modules.*)[\\\\/]node_modules[\\\\/](react|react-dom|scheduler|prop-types|use-subscription)[\\\\/]/,\n          priority: 40,\n          // Don't let webpack eliminate this chunk (prevents this chunk from\n          // becoming a part of the commons chunk)\n          enforce: true,\n        },\n        lib: {\n          test(module: {\n            size: Function\n            nameForCondition: Function\n          }): boolean {\n            return (\n              module.size() > 160000 &&\n              /node_modules[/\\\\]/.test(module.nameForCondition() || '')\n            )\n          },\n          name(module: {\n            type: string\n            libIdent?: Function\n            updateHash: (hash: crypto.Hash) => void\n          }): string {\n            const hash = crypto.createHash('sha1')\n            if (isModuleCSS(module)) {\n              module.updateHash(hash)\n            } else {\n              if (!module.libIdent) {\n                throw new Error(\n                  `Encountered unknown module type: ${module.type}. Please open an issue.`\n                )\n              }\n\n              hash.update(module.libIdent({ context: dir }))\n            }\n\n            return hash.digest('hex').substring(0, 8)\n          },\n          priority: 30,\n          minChunks: 1,\n          reuseExistingChunk: true,\n        },\n        commons: {\n          name: 'commons',\n          minChunks: totalPages,\n          priority: 20,\n        },\n        ...(isWebpack5\n          ? undefined\n          : {\n              default: false,\n              vendors: false,\n              shared: {\n                name(module, chunks) {\n                  return (\n                    crypto\n                      .createHash('sha1')\n                      .update(\n                        chunks.reduce(\n                          (acc: string, chunk: webpack.compilation.Chunk) => {\n                            return acc + chunk.name\n                          },\n                          ''\n                        )\n                      )\n                      .digest('hex') + (isModuleCSS(module) ? '_CSS' : '')\n                  )\n                },\n                priority: 10,\n                minChunks: 2,\n                reuseExistingChunk: true,\n              },\n            }),\n      },\n      maxInitialRequests: 25,\n      minSize: 20000,\n    },\n  }\n\n  // Select appropriate SplitChunksPlugin config for this build\n  let splitChunksConfig: webpack.Options.SplitChunksOptions | false\n  if (dev) {\n    splitChunksConfig = isWebpack5 ? false : splitChunksConfigs.dev\n  } else {\n    splitChunksConfig = splitChunksConfigs.prodGranular\n  }\n\n  const crossOrigin = config.crossOrigin\n\n  const conformanceConfig = Object.assign(\n    {\n      ReactSyncScriptsConformanceCheck: {\n        enabled: true,\n      },\n      MinificationConformanceCheck: {\n        enabled: true,\n      },\n      DuplicatePolyfillsConformanceCheck: {\n        enabled: true,\n        BlockedAPIToBePolyfilled: Object.assign(\n          [],\n          ['fetch'],\n          config.conformance?.DuplicatePolyfillsConformanceCheck\n            ?.BlockedAPIToBePolyfilled || []\n        ),\n      },\n      GranularChunksConformanceCheck: {\n        enabled: true,\n      },\n    },\n    config.conformance\n  )\n\n  const esmExternals = !!config.experimental?.esmExternals\n  const looseEsmExternals = config.experimental?.esmExternals === 'loose'\n\n  async function handleExternals(\n    context: string,\n    request: string,\n    dependencyType: string,\n    getResolve: (\n      options: any\n    ) => (\n      resolveContext: string,\n      resolveRequest: string\n    ) => Promise<[string | null, boolean]>\n  ) {\n    // We need to externalize internal requests for files intended to\n    // not be bundled.\n\n    const isLocal: boolean =\n      request.startsWith('.') ||\n      // Always check for unix-style path, as webpack sometimes\n      // normalizes as posix.\n      path.posix.isAbsolute(request) ||\n      // When on Windows, we also want to check for Windows-specific\n      // absolute paths.\n      (process.platform === 'win32' && path.win32.isAbsolute(request))\n\n    // Relative requires don't need custom resolution, because they\n    // are relative to requests we've already resolved here.\n    // Absolute requires (require('/foo')) are extremely uncommon, but\n    // also have no need for customization as they're already resolved.\n    if (!isLocal) {\n      if (/^(?:next$|react(?:$|\\/))/.test(request)) {\n        return `commonjs ${request}`\n      }\n\n      const notExternalModules =\n        /^(?:private-next-pages\\/|next\\/(?:dist\\/pages\\/|(?:app|document|link|image|constants|dynamic)$)|string-hash$)/\n      if (notExternalModules.test(request)) {\n        return\n      }\n    }\n\n    // When in esm externals mode, and using import, we resolve with\n    // ESM resolving options.\n    const isEsmRequested = dependencyType === 'esm'\n    const preferEsm = esmExternals && isEsmRequested\n\n    const resolve = getResolve(\n      preferEsm ? WEBPACK_ESM_RESOLVE_OPTIONS : WEBPACK_RESOLVE_OPTIONS\n    )\n\n    // Resolve the import with the webpack provided context, this\n    // ensures we're resolving the correct version when multiple\n    // exist.\n    let res: string | null\n    let isEsm: boolean = false\n    try {\n      ;[res, isEsm] = await resolve(context, request)\n    } catch (err) {\n      res = null\n    }\n\n    // If resolving fails, and we can use an alternative way\n    // try the alternative resolving options.\n    if (!res && (isEsmRequested || looseEsmExternals)) {\n      const resolveAlternative = getResolve(\n        preferEsm ? WEBPACK_RESOLVE_OPTIONS : WEBPACK_ESM_RESOLVE_OPTIONS\n      )\n      try {\n        ;[res, isEsm] = await resolveAlternative(context, request)\n      } catch (err) {\n        res = null\n      }\n    }\n\n    // If the request cannot be resolved we need to have\n    // webpack \"bundle\" it so it surfaces the not found error.\n    if (!res) {\n      return\n    }\n\n    // ESM externals can only be imported (and not required).\n    // Make an exception in loose mode.\n    if (!isEsmRequested && isEsm && !looseEsmExternals) {\n      throw new Error(\n        `ESM packages (${request}) need to be imported. Use 'import' to reference the package instead. https://nextjs.org/docs/messages/import-esm-externals`\n      )\n    }\n\n    if (isLocal) {\n      // Makes sure dist/shared and dist/server are not bundled\n      // we need to process shared `router/router` and `dynamic`,\n      // so that the DefinePlugin can inject process.env values\n      const isNextExternal =\n        /next[/\\\\]dist[/\\\\](shared|server)[/\\\\](?!lib[/\\\\](router[/\\\\]router|dynamic))/.test(\n          res\n        )\n\n      if (isNextExternal) {\n        // Generate Next.js external import\n        const externalRequest = path.posix.join(\n          'next',\n          'dist',\n          path\n            .relative(\n              // Root of Next.js package:\n              path.join(__dirname, '..'),\n              res\n            )\n            // Windows path normalization\n            .replace(/\\\\/g, '/')\n        )\n        return `commonjs ${externalRequest}`\n      } else {\n        return\n      }\n    }\n\n    // Bundled Node.js code is relocated without its node_modules tree.\n    // This means we need to make sure its request resolves to the same\n    // package that'll be available at runtime. If it's not identical,\n    // we need to bundle the code (even if it _should_ be external).\n    let baseRes: string | null\n    let baseIsEsm: boolean\n    try {\n      const baseResolve = getResolve(\n        isEsm ? NODE_ESM_RESOLVE_OPTIONS : NODE_RESOLVE_OPTIONS\n      )\n      ;[baseRes, baseIsEsm] = await baseResolve(dir, request)\n    } catch (err) {\n      baseRes = null\n      baseIsEsm = false\n    }\n\n    // Same as above: if the package, when required from the root,\n    // would be different from what the real resolution would use, we\n    // cannot externalize it.\n    // if request is pointing to a symlink it could point to the the same file,\n    // the resolver will resolve symlinks so this is handled\n    if (baseRes !== res || isEsm !== baseIsEsm) {\n      return\n    }\n\n    const externalType = isEsm ? 'module' : 'commonjs'\n\n    if (\n      res.match(/next[/\\\\]dist[/\\\\]shared[/\\\\](?!lib[/\\\\]router[/\\\\]router)/)\n    ) {\n      return `${externalType} ${request}`\n    }\n\n    // Default pages have to be transpiled\n    if (\n      res.match(/[/\\\\]next[/\\\\]dist[/\\\\]/) ||\n      // This is the @babel/plugin-transform-runtime \"helpers: true\" option\n      res.match(/node_modules[/\\\\]@babel[/\\\\]runtime[/\\\\]/)\n    ) {\n      return\n    }\n\n    // Webpack itself has to be compiled because it doesn't always use module relative paths\n    if (\n      res.match(/node_modules[/\\\\]webpack/) ||\n      res.match(/node_modules[/\\\\]css-loader/)\n    ) {\n      return\n    }\n\n    // Anything else that is standard JavaScript within `node_modules`\n    // can be externalized.\n    if (/node_modules[/\\\\].*\\.c?js$/.test(res)) {\n      return `${externalType} ${request}`\n    }\n\n    // Default behavior: bundle the code!\n  }\n\n  const emacsLockfilePattern = '**/.#*'\n\n  let webpackConfig: webpack.Configuration = {\n    parallelism: Number(process.env.NEXT_WEBPACK_PARALLELISM) || undefined,\n    externals: !isServer\n      ? // make sure importing \"next\" is handled gracefully for client\n        // bundles in case a user imported types and it wasn't removed\n        // TODO: should we warn/error for this instead?\n        ['next']\n      : !isServerless\n      ? [\n          isWebpack5\n            ? ({\n                context,\n                request,\n                dependencyType,\n                getResolve,\n              }: {\n                context: string\n                request: string\n                dependencyType: string\n                getResolve: (\n                  options: any\n                ) => (\n                  resolveContext: string,\n                  resolveRequest: string,\n                  callback: (\n                    err?: Error,\n                    result?: string,\n                    resolveData?: { descriptionFileData?: { type?: any } }\n                  ) => void\n                ) => void\n              }) =>\n                handleExternals(context, request, dependencyType, (options) => {\n                  const resolveFunction = getResolve(options)\n                  return (resolveContext: string, requestToResolve: string) =>\n                    new Promise((resolve, reject) => {\n                      resolveFunction(\n                        resolveContext,\n                        requestToResolve,\n                        (err, result, resolveData) => {\n                          if (err) return reject(err)\n                          if (!result) return resolve([null, false])\n                          const isEsm = /\\.js$/i.test(result)\n                            ? resolveData?.descriptionFileData?.type ===\n                              'module'\n                            : /\\.mjs$/i.test(result)\n                          resolve([result, isEsm])\n                        }\n                      )\n                    })\n                })\n            : (\n                context: string,\n                request: string,\n                callback: (err?: Error, result?: string | undefined) => void\n              ) =>\n                handleExternals(\n                  context,\n                  request,\n                  'commonjs',\n                  () => (resolveContext: string, requestToResolve: string) =>\n                    new Promise((resolve) =>\n                      resolve([\n                        require.resolve(requestToResolve, {\n                          paths: [resolveContext],\n                        }),\n                        false,\n                      ])\n                    )\n                ).then((result) => callback(undefined, result), callback),\n        ]\n      : [\n          // When the 'serverless' target is used all node_modules will be compiled into the output bundles\n          // So that the 'serverless' bundles have 0 runtime dependencies\n          'next/dist/compiled/@ampproject/toolbox-optimizer', // except this one\n\n          // Mark this as external if not enabled so it doesn't cause a\n          // webpack error from being missing\n          ...(config.experimental.optimizeCss ? [] : ['critters']),\n        ],\n    optimization: {\n      // Webpack 5 uses a new property for the same functionality\n      ...(isWebpack5 ? { emitOnErrors: !dev } : { noEmitOnErrors: dev }),\n      checkWasmTypes: false,\n      nodeEnv: false,\n      splitChunks: isServer\n        ? isWebpack5 && !dev\n          ? ({\n              filename: '[name].js',\n              // allow to split entrypoints\n              chunks: 'all',\n              // size of files is not so relevant for server build\n              // we want to prefer deduplication to load less code\n              minSize: 1000,\n            } as any)\n          : false\n        : splitChunksConfig,\n      runtimeChunk: isServer\n        ? isWebpack5 && !isLikeServerless\n          ? { name: 'webpack-runtime' }\n          : undefined\n        : { name: CLIENT_STATIC_FILES_RUNTIME_WEBPACK },\n      minimize: !(dev || isServer),\n      minimizer: [\n        // Minify JavaScript\n        (compiler: webpack.Compiler) => {\n          // @ts-ignore No typings yet\n          const {\n            TerserPlugin,\n          } = require('./webpack/plugins/terser-webpack-plugin/src/index.js')\n          new TerserPlugin({\n            cacheDir: path.join(distDir, 'cache', 'next-minifier'),\n            parallel: config.experimental.cpus,\n            swcMinify: config.experimental.swcMinify,\n            terserOptions,\n          }).apply(compiler)\n        },\n        // Minify CSS\n        (compiler: webpack.Compiler) => {\n          const {\n            CssMinimizerPlugin,\n          } = require('./webpack/plugins/css-minimizer-plugin')\n          new CssMinimizerPlugin({\n            postcssOptions: {\n              map: {\n                // `inline: false` generates the source map in a separate file.\n                // Otherwise, the CSS file is needlessly large.\n                inline: false,\n                // `annotation: false` skips appending the `sourceMappingURL`\n                // to the end of the CSS file. Webpack already handles this.\n                annotation: false,\n              },\n            },\n          }).apply(compiler)\n        },\n      ],\n    },\n    context: dir,\n    node: {\n      setImmediate: false,\n    },\n    // Kept as function to be backwards compatible\n    // @ts-ignore TODO webpack 5 typings needed\n    entry: async () => {\n      return {\n        ...(clientEntries ? clientEntries : {}),\n        ...entrypoints,\n      }\n    },\n    watchOptions: {\n      aggregateTimeout: 5,\n      ignored: [\n        '**/.git/**',\n        '**/node_modules/**',\n        '**/.next/**',\n        // can be removed after https://github.com/paulmillr/chokidar/issues/955 is released\n        emacsLockfilePattern,\n      ],\n    },\n    output: {\n      // we must set publicPath to an empty value to override the default of\n      // auto which doesn't work in IE11\n      publicPath: `${config.assetPrefix || ''}/_next/`,\n      path:\n        isServer && isWebpack5 && !dev\n          ? path.join(outputPath, 'chunks')\n          : outputPath,\n      // On the server we don't use hashes\n      filename: isServer\n        ? isWebpack5 && !dev\n          ? '../[name].js'\n          : '[name].js'\n        : `static/chunks/${isDevFallback ? 'fallback/' : ''}[name]${\n            dev ? '' : isWebpack5 ? '-[contenthash]' : '-[chunkhash]'\n          }.js`,\n      library: isServer ? undefined : '_N_E',\n      libraryTarget: isServer ? 'commonjs2' : 'assign',\n      hotUpdateChunkFilename: isWebpack5\n        ? 'static/webpack/[id].[fullhash].hot-update.js'\n        : 'static/webpack/[id].[hash].hot-update.js',\n      hotUpdateMainFilename: isWebpack5\n        ? 'static/webpack/[fullhash].[runtime].hot-update.json'\n        : 'static/webpack/[hash].hot-update.json',\n      // This saves chunks with the name given via `import()`\n      chunkFilename: isServer\n        ? '[name].js'\n        : `static/chunks/${isDevFallback ? 'fallback/' : ''}${\n            dev ? '[name]' : '[name].[contenthash]'\n          }.js`,\n      strictModuleExceptionHandling: true,\n      crossOriginLoading: crossOrigin,\n      futureEmitAssets: !dev,\n      webassemblyModuleFilename: 'static/wasm/[modulehash].wasm',\n    },\n    performance: false,\n    resolve: resolveConfig,\n    resolveLoader: {\n      // The loaders Next.js provides\n      alias: [\n        'emit-file-loader',\n        'error-loader',\n        'next-babel-loader',\n        'next-swc-loader',\n        'next-client-pages-loader',\n        'next-image-loader',\n        'next-serverless-loader',\n        'noop-loader',\n        'next-style-loader',\n      ].reduce((alias, loader) => {\n        // using multiple aliases to replace `resolveLoader.modules`\n        alias[loader] = path.join(__dirname, 'webpack', 'loaders', loader)\n\n        return alias\n      }, {} as Record<string, string>),\n      modules: [\n        'node_modules',\n        ...nodePathList, // Support for NODE_PATH environment variable\n      ],\n      plugins: isWebpack5 ? [] : [require('pnp-webpack-plugin')],\n    },\n    module: {\n      rules: [\n        ...(isWebpack5\n          ? [\n              // TODO: FIXME: do NOT webpack 5 support with this\n              // x-ref: https://github.com/webpack/webpack/issues/11467\n              {\n                test: /\\.m?js/,\n                resolve: {\n                  fullySpecified: false,\n                },\n              } as any,\n            ]\n          : []),\n        {\n          test: /\\.(tsx|ts|js|mjs|jsx)$/,\n          ...(config.experimental.externalDir\n            ? // Allowing importing TS/TSX files from outside of the root dir.\n              {}\n            : { include: [dir, ...babelIncludeRegexes] }),\n          exclude: (excludePath: string) => {\n            if (babelIncludeRegexes.some((r) => r.test(excludePath))) {\n              return false\n            }\n            return /node_modules/.test(excludePath)\n          },\n          use: hasReactRefresh\n            ? [\n                require.resolve('@next/react-refresh-utils/loader'),\n                defaultLoaders.babel,\n              ]\n            : defaultLoaders.babel,\n        },\n        ...(!config.images.disableStaticImages && isWebpack5\n          ? [\n              {\n                test: /\\.(png|jpg|jpeg|gif|webp|ico|bmp|svg)$/i,\n                loader: 'next-image-loader',\n                issuer: { not: regexLikeCss },\n                dependency: { not: ['url'] },\n                options: {\n                  isServer,\n                  isDev: dev,\n                  assetPrefix: config.assetPrefix,\n                },\n              },\n            ]\n          : []),\n      ].filter(Boolean),\n    },\n    plugins: [\n      hasReactRefresh && new ReactRefreshWebpackPlugin(webpack),\n      // Makes sure `Buffer` and `process` are polyfilled in client-side bundles (same behavior as webpack 4)\n      isWebpack5 &&\n        !isServer &&\n        new webpack.ProvidePlugin({\n          Buffer: [require.resolve('buffer'), 'Buffer'],\n          process: [require.resolve('process')],\n        }),\n      // This plugin makes sure `output.filename` is used for entry chunks\n      !isWebpack5 && new ChunkNamesPlugin(),\n      new webpack.DefinePlugin({\n        ...Object.keys(process.env).reduce(\n          (prev: { [key: string]: string }, key: string) => {\n            if (key.startsWith('NEXT_PUBLIC_')) {\n              prev[`process.env.${key}`] = JSON.stringify(process.env[key]!)\n            }\n            return prev\n          },\n          {}\n        ),\n        ...Object.keys(config.env).reduce((acc, key) => {\n          if (/^(?:NODE_.+)|^(?:__.+)$/i.test(key)) {\n            throw new Error(\n              `The key \"${key}\" under \"env\" in next.config.js is not allowed. https://nextjs.org/docs/messages/env-key-not-allowed`\n            )\n          }\n\n          return {\n            ...acc,\n            [`process.env.${key}`]: JSON.stringify(config.env[key]),\n          }\n        }, {}),\n        // TODO: enforce `NODE_ENV` on `process.env`, and add a test:\n        'process.env.NODE_ENV': JSON.stringify(\n          dev ? 'development' : 'production'\n        ),\n        'process.env.__NEXT_CROSS_ORIGIN': JSON.stringify(crossOrigin),\n        'process.browser': JSON.stringify(!isServer),\n        'process.env.__NEXT_TEST_MODE': JSON.stringify(\n          process.env.__NEXT_TEST_MODE\n        ),\n        // This is used in client/dev-error-overlay/hot-dev-client.js to replace the dist directory\n        ...(dev && !isServer\n          ? {\n              'process.env.__NEXT_DIST_DIR': JSON.stringify(distDir),\n            }\n          : {}),\n        'process.env.__NEXT_TRAILING_SLASH': JSON.stringify(\n          config.trailingSlash\n        ),\n        'process.env.__NEXT_BUILD_INDICATOR': JSON.stringify(\n          config.devIndicators.buildActivity\n        ),\n        'process.env.__NEXT_PLUGINS': JSON.stringify(\n          config.experimental.plugins\n        ),\n        'process.env.__NEXT_STRICT_MODE': JSON.stringify(\n          config.reactStrictMode\n        ),\n        'process.env.__NEXT_REACT_ROOT': JSON.stringify(hasReactRoot),\n        'process.env.__NEXT_CONCURRENT_FEATURES': JSON.stringify(\n          config.experimental.concurrentFeatures && hasReactRoot\n        ),\n        'process.env.__NEXT_OPTIMIZE_FONTS': JSON.stringify(\n          config.optimizeFonts && !dev\n        ),\n        'process.env.__NEXT_OPTIMIZE_IMAGES': JSON.stringify(\n          config.experimental.optimizeImages\n        ),\n        'process.env.__NEXT_OPTIMIZE_CSS': JSON.stringify(\n          config.experimental.optimizeCss && !dev\n        ),\n        'process.env.__NEXT_SCROLL_RESTORATION': JSON.stringify(\n          config.experimental.scrollRestoration\n        ),\n        'process.env.__NEXT_IMAGE_OPTS': JSON.stringify({\n          deviceSizes: config.images.deviceSizes,\n          imageSizes: config.images.imageSizes,\n          path: config.images.path,\n          loader: config.images.loader,\n          ...(dev\n            ? {\n                // pass domains in development to allow validating on the client\n                domains: config.images.domains,\n              }\n            : {}),\n        }),\n        'process.env.__NEXT_ROUTER_BASEPATH': JSON.stringify(config.basePath),\n        'process.env.__NEXT_HAS_REWRITES': JSON.stringify(hasRewrites),\n        'process.env.__NEXT_I18N_SUPPORT': JSON.stringify(!!config.i18n),\n        'process.env.__NEXT_I18N_DOMAINS': JSON.stringify(config.i18n?.domains),\n        'process.env.__NEXT_ANALYTICS_ID': JSON.stringify(config.analyticsId),\n        ...(isServer\n          ? {\n              // Fix bad-actors in the npm ecosystem (e.g. `node-formidable`)\n              // This is typically found in unmaintained modules from the\n              // pre-webpack era (common in server-side code)\n              'global.GENTLY': JSON.stringify(false),\n            }\n          : undefined),\n        // stub process.env with proxy to warn a missing value is\n        // being accessed in development mode\n        ...(config.experimental.pageEnv && dev\n          ? {\n              'process.env': `\n            new Proxy(${isServer ? 'process.env' : '{}'}, {\n              get(target, prop) {\n                if (typeof target[prop] === 'undefined') {\n                  console.warn(\\`An environment variable (\\${prop}) that was not provided in the environment was accessed.\\nSee more info here: https://nextjs.org/docs/messages/missing-env-value\\`)\n                }\n                return target[prop]\n              }\n            })\n          `,\n            }\n          : {}),\n      }),\n      !isServer &&\n        new ReactLoadablePlugin({\n          filename: REACT_LOADABLE_MANIFEST,\n          pagesDir,\n        }),\n      !isServer && new DropClientPage(),\n      config.experimental.nftTracing &&\n        !isLikeServerless &&\n        isServer &&\n        !dev &&\n        isWebpack5 &&\n        new TraceEntryPointsPlugin({ appDir: dir }),\n      // Moment.js is an extremely popular library that bundles large locale files\n      // by default due to how Webpack interprets its code. This is a practical\n      // solution that requires the user to opt into importing specific locales.\n      // https://github.com/jmblog/how-to-optimize-momentjs-with-webpack\n      config.excludeDefaultMomentLocales &&\n        new webpack.IgnorePlugin({\n          resourceRegExp: /^\\.\\/locale$/,\n          contextRegExp: /moment$/,\n        }),\n      ...(dev\n        ? (() => {\n            // Even though require.cache is server only we have to clear assets from both compilations\n            // This is because the client compilation generates the build manifest that's used on the server side\n            const {\n              NextJsRequireCacheHotReloader,\n            } = require('./webpack/plugins/nextjs-require-cache-hot-reloader')\n            const devPlugins = [new NextJsRequireCacheHotReloader()]\n\n            if (!isServer) {\n              devPlugins.push(new webpack.HotModuleReplacementPlugin())\n            }\n\n            return devPlugins\n          })()\n        : []),\n      // Webpack 5 no longer requires this plugin in production:\n      !isWebpack5 && !dev && new webpack.HashedModuleIdsPlugin(),\n      !dev &&\n        new webpack.IgnorePlugin({\n          resourceRegExp: /react-is/,\n          contextRegExp: /next[\\\\/]dist[\\\\/]/,\n        }),\n      isServerless && isServer && new ServerlessPlugin(),\n      isServer &&\n        new PagesManifestPlugin({ serverless: isLikeServerless, dev }),\n      !isWebpack5 &&\n        target === 'server' &&\n        isServer &&\n        new NextJsSSRModuleCachePlugin({ outputPath }),\n      isServer && new NextJsSsrImportPlugin(),\n      !isServer &&\n        new BuildManifestPlugin({\n          buildId,\n          rewrites,\n          isDevFallback,\n        }),\n      !dev &&\n        !isServer &&\n        config.experimental.stats &&\n        new BuildStatsPlugin({\n          distDir,\n        }),\n      new ProfilingPlugin({ runWebpackSpan }),\n      config.optimizeFonts &&\n        !dev &&\n        isServer &&\n        (function () {\n          const { FontStylesheetGatheringPlugin } =\n            require('./webpack/plugins/font-stylesheet-gathering-plugin') as {\n              FontStylesheetGatheringPlugin: typeof import('./webpack/plugins/font-stylesheet-gathering-plugin').FontStylesheetGatheringPlugin\n            }\n          return new FontStylesheetGatheringPlugin({\n            isLikeServerless,\n          })\n        })(),\n      config.experimental.conformance &&\n        !isWebpack5 &&\n        !dev &&\n        new WebpackConformancePlugin({\n          tests: [\n            !isServer &&\n              conformanceConfig.MinificationConformanceCheck.enabled &&\n              new MinificationConformanceCheck(),\n            conformanceConfig.ReactSyncScriptsConformanceCheck.enabled &&\n              new ReactSyncScriptsConformanceCheck({\n                AllowedSources:\n                  conformanceConfig.ReactSyncScriptsConformanceCheck\n                    .allowedSources || [],\n              }),\n            !isServer &&\n              conformanceConfig.DuplicatePolyfillsConformanceCheck.enabled &&\n              new DuplicatePolyfillsConformanceCheck({\n                BlockedAPIToBePolyfilled:\n                  conformanceConfig.DuplicatePolyfillsConformanceCheck\n                    .BlockedAPIToBePolyfilled,\n              }),\n            !isServer &&\n              conformanceConfig.GranularChunksConformanceCheck.enabled &&\n              new GranularChunksConformanceCheck(\n                splitChunksConfigs.prodGranular\n              ),\n          ].filter(Boolean),\n        }),\n      new WellKnownErrorsPlugin(),\n      !isServer &&\n        new CopyFilePlugin({\n          filePath: require.resolve('./polyfills/polyfill-nomodule'),\n          cacheKey: process.env.__NEXT_VERSION as string,\n          name: `static/chunks/polyfills${dev ? '' : '-[hash]'}.js`,\n          minimize: false,\n          info: {\n            [CLIENT_STATIC_FILES_RUNTIME_POLYFILLS_SYMBOL]: 1,\n            // This file is already minified\n            minimized: true,\n          },\n        }),\n    ].filter(Boolean as any as ExcludesFalse),\n  }\n\n  // Support tsconfig and jsconfig baseUrl\n  if (resolvedBaseUrl) {\n    webpackConfig.resolve?.modules?.push(resolvedBaseUrl)\n  }\n\n  if (jsConfig?.compilerOptions?.paths && resolvedBaseUrl) {\n    webpackConfig.resolve?.plugins?.unshift(\n      new JsConfigPathsPlugin(jsConfig.compilerOptions.paths, resolvedBaseUrl)\n    )\n  }\n\n  if (isWebpack5) {\n    // futureEmitAssets is on by default in webpack 5\n    delete webpackConfig.output?.futureEmitAssets\n\n    if (isServer && dev) {\n      // Enable building of client compilation before server compilation in development\n      // @ts-ignore dependencies exists\n      webpackConfig.dependencies = ['client']\n    }\n    // webpack 5 no longer polyfills Node.js modules:\n    if (webpackConfig.node) delete webpackConfig.node.setImmediate\n\n    // Due to bundling of webpack the default values can't be correctly detected\n    // This restores the webpack defaults\n    // @ts-ignore webpack 5\n    webpackConfig.snapshot = {}\n    if (process.versions.pnp === '3') {\n      const match =\n        /^(.+?)[\\\\/]cache[\\\\/]jest-worker-npm-[^\\\\/]+\\.zip[\\\\/]node_modules[\\\\/]/.exec(\n          require.resolve('jest-worker')\n        )\n      if (match) {\n        // @ts-ignore webpack 5\n        webpackConfig.snapshot.managedPaths = [\n          path.resolve(match[1], 'unplugged'),\n        ]\n      }\n    } else {\n      const match = /^(.+?[\\\\/]node_modules)[\\\\/]/.exec(\n        require.resolve('jest-worker')\n      )\n      if (match) {\n        // @ts-ignore webpack 5\n        webpackConfig.snapshot.managedPaths = [match[1]]\n      }\n    }\n    if (process.versions.pnp === '1') {\n      const match =\n        /^(.+?[\\\\/]v4)[\\\\/]npm-jest-worker-[^\\\\/]+-[\\da-f]{40}[\\\\/]node_modules[\\\\/]/.exec(\n          require.resolve('jest-worker')\n        )\n      if (match) {\n        // @ts-ignore webpack 5\n        webpackConfig.snapshot.immutablePaths = [match[1]]\n      }\n    } else if (process.versions.pnp === '3') {\n      const match =\n        /^(.+?)[\\\\/]jest-worker-npm-[^\\\\/]+\\.zip[\\\\/]node_modules[\\\\/]/.exec(\n          require.resolve('jest-worker')\n        )\n      if (match) {\n        // @ts-ignore webpack 5\n        webpackConfig.snapshot.immutablePaths = [match[1]]\n      }\n    }\n\n    if (dev) {\n      if (!webpackConfig.optimization) {\n        webpackConfig.optimization = {}\n      }\n      webpackConfig.optimization.providedExports = false\n      webpackConfig.optimization.usedExports = false\n    }\n\n    const configVars = JSON.stringify({\n      crossOrigin: config.crossOrigin,\n      pageExtensions: config.pageExtensions,\n      trailingSlash: config.trailingSlash,\n      buildActivity: config.devIndicators.buildActivity,\n      productionBrowserSourceMaps: !!config.productionBrowserSourceMaps,\n      plugins: config.experimental.plugins,\n      reactStrictMode: config.reactStrictMode,\n      reactMode: config.experimental.reactMode,\n      optimizeFonts: config.optimizeFonts,\n      optimizeImages: config.experimental.optimizeImages,\n      optimizeCss: config.experimental.optimizeCss,\n      scrollRestoration: config.experimental.scrollRestoration,\n      basePath: config.basePath,\n      pageEnv: config.experimental.pageEnv,\n      excludeDefaultMomentLocales: config.excludeDefaultMomentLocales,\n      assetPrefix: config.assetPrefix,\n      disableOptimizedLoading: config.experimental.disableOptimizedLoading,\n      target,\n      reactProductionProfiling,\n      webpack: !!config.webpack,\n      hasRewrites,\n      reactRoot: config.experimental.reactRoot,\n      concurrentFeatures: config.experimental.concurrentFeatures,\n    })\n\n    const cache: any = {\n      type: 'filesystem',\n      // Includes:\n      //  - Next.js version\n      //  - next.config.js keys that affect compilation\n      version: `${process.env.__NEXT_VERSION}|${configVars}`,\n      cacheDirectory: path.join(distDir, 'cache', 'webpack'),\n    }\n\n    // Adds `next.config.js` as a buildDependency when custom webpack config is provided\n    if (config.webpack && config.configFile) {\n      cache.buildDependencies = {\n        config: [config.configFile],\n      }\n    }\n\n    webpackConfig.cache = cache\n\n    if (process.env.NEXT_WEBPACK_LOGGING) {\n      const logInfra =\n        process.env.NEXT_WEBPACK_LOGGING.includes('infrastructure')\n      const logProfileClient =\n        process.env.NEXT_WEBPACK_LOGGING.includes('profile-client')\n      const logProfileServer =\n        process.env.NEXT_WEBPACK_LOGGING.includes('profile-server')\n      const logDefault = !logInfra && !logProfileClient && !logProfileServer\n\n      if (logDefault || logInfra) {\n        // @ts-ignore TODO: remove ignore when webpack 5 is stable\n        webpackConfig.infrastructureLogging = {\n          level: 'verbose',\n          debug: /FileSystemInfo/,\n        }\n      }\n\n      if (\n        logDefault ||\n        (logProfileClient && !isServer) ||\n        (logProfileServer && isServer)\n      ) {\n        webpackConfig.plugins!.push((compiler: webpack.Compiler) => {\n          compiler.hooks.done.tap('next-webpack-logging', (stats) => {\n            console.log(\n              stats.toString({\n                colors: true,\n                // @ts-ignore TODO: remove ignore when webpack 5 is stable\n                logging: logDefault ? 'log' : 'verbose',\n              })\n            )\n          })\n        })\n      }\n\n      if ((logProfileClient && !isServer) || (logProfileServer && isServer)) {\n        webpackConfig.plugins!.push(\n          new webpack.ProgressPlugin({\n            // @ts-ignore TODO: remove ignore when webpack 5 is stable\n            profile: true,\n          })\n        )\n        webpackConfig.profile = true\n      }\n    }\n  }\n\n  webpackConfig = await buildConfiguration(webpackConfig, {\n    rootDirectory: dir,\n    customAppFile: new RegExp(\n      path.join(pagesDir, `_app`).replace(/\\\\/g, '(/|\\\\\\\\)')\n    ),\n    isDevelopment: dev,\n    isServer,\n    assetPrefix: config.assetPrefix || '',\n    sassOptions: config.sassOptions,\n    productionBrowserSourceMaps: config.productionBrowserSourceMaps,\n    future: config.future,\n    isCraCompat: config.experimental.craCompat,\n  })\n\n  let originalDevtool = webpackConfig.devtool\n  if (typeof config.webpack === 'function') {\n    webpackConfig = config.webpack(webpackConfig, {\n      dir,\n      dev,\n      isServer,\n      buildId,\n      config,\n      defaultLoaders,\n      totalPages,\n      webpack,\n    })\n\n    if (!webpackConfig) {\n      throw new Error(\n        'Webpack config is undefined. You may have forgot to return properly from within the \"webpack\" method of your next.config.js.\\n' +\n          'See more info here https://nextjs.org/docs/messages/undefined-webpack-config'\n      )\n    }\n\n    if (dev && originalDevtool !== webpackConfig.devtool) {\n      webpackConfig.devtool = originalDevtool\n      devtoolRevertWarning(originalDevtool)\n    }\n\n    if (typeof (webpackConfig as any).then === 'function') {\n      console.warn(\n        '> Promise returned in next config. https://nextjs.org/docs/messages/promise-in-next-config'\n      )\n    }\n  }\n\n  if (!config.images.disableStaticImages && isWebpack5) {\n    const rules = webpackConfig.module?.rules || []\n    const hasCustomSvg = rules.some(\n      (rule) =>\n        rule.loader !== 'next-image-loader' &&\n        'test' in rule &&\n        rule.test instanceof RegExp &&\n        rule.test.test('.svg')\n    )\n    const nextImageRule = rules.find(\n      (rule) => rule.loader === 'next-image-loader'\n    )\n    if (hasCustomSvg && nextImageRule) {\n      // Exclude svg if the user already defined it in custom\n      // webpack config such as `@svgr/webpack` plugin or\n      // the `babel-plugin-inline-react-svg` plugin.\n      nextImageRule.test = /\\.(png|jpg|jpeg|gif|webp|ico|bmp)$/i\n    }\n  }\n\n  if (\n    config.experimental.craCompat &&\n    webpackConfig.module?.rules &&\n    webpackConfig.plugins\n  ) {\n    // CRA prevents loading all locales by default\n    // https://github.com/facebook/create-react-app/blob/fddce8a9e21bf68f37054586deb0c8636a45f50b/packages/react-scripts/config/webpack.config.js#L721\n    webpackConfig.plugins.push(\n      new webpack.IgnorePlugin(/^\\.\\/locale$/, /moment$/)\n    )\n\n    // CRA allows importing non-webpack handled files with file-loader\n    // these need to be the last rule to prevent catching other items\n    // https://github.com/facebook/create-react-app/blob/fddce8a9e21bf68f37054586deb0c8636a45f50b/packages/react-scripts/config/webpack.config.js#L594\n    const fileLoaderExclude = [/\\.(js|mjs|jsx|ts|tsx|json)$/]\n    const fileLoader = isWebpack5\n      ? {\n          exclude: fileLoaderExclude,\n          issuer: fileLoaderExclude,\n          type: 'asset/resource',\n          generator: {\n            publicPath: '/_next/',\n            filename: 'static/media/[name].[hash:8].[ext]',\n          },\n        }\n      : {\n          loader: require.resolve('next/dist/compiled/file-loader'),\n          // Exclude `js` files to keep \"css\" loader working as it injects\n          // its runtime that would otherwise be processed through \"file\" loader.\n          // Also exclude `html` and `json` extensions so they get processed\n          // by webpacks internal loaders.\n          exclude: fileLoaderExclude,\n          issuer: fileLoaderExclude,\n          options: {\n            publicPath: '/_next/static/media',\n            outputPath: 'static/media',\n            name: '[name].[hash:8].[ext]',\n          },\n        }\n\n    const topRules = []\n    const innerRules = []\n\n    for (const rule of webpackConfig.module.rules) {\n      if (rule.resolve) {\n        topRules.push(rule)\n      } else {\n        if (\n          rule.oneOf &&\n          !(rule.test || rule.exclude || rule.resource || rule.issuer)\n        ) {\n          rule.oneOf.forEach((r) => innerRules.push(r))\n        } else {\n          innerRules.push(rule)\n        }\n      }\n    }\n\n    webpackConfig.module.rules = [\n      ...(topRules as any),\n      {\n        oneOf: [...innerRules, fileLoader],\n      },\n    ]\n  }\n\n  // Backwards compat with webpack-dev-middleware options object\n  if (typeof config.webpackDevMiddleware === 'function') {\n    const options = config.webpackDevMiddleware({\n      watchOptions: webpackConfig.watchOptions,\n    })\n    if (options.watchOptions) {\n      webpackConfig.watchOptions = options.watchOptions\n    }\n  }\n\n  function canMatchCss(rule: webpack.RuleSetCondition | undefined): boolean {\n    if (!rule) {\n      return false\n    }\n\n    const fileNames = [\n      '/tmp/test.css',\n      '/tmp/test.scss',\n      '/tmp/test.sass',\n      '/tmp/test.less',\n      '/tmp/test.styl',\n    ]\n\n    if (rule instanceof RegExp && fileNames.some((input) => rule.test(input))) {\n      return true\n    }\n\n    if (typeof rule === 'function') {\n      if (\n        fileNames.some((input) => {\n          try {\n            if (rule(input)) {\n              return true\n            }\n          } catch (_) {}\n          return false\n        })\n      ) {\n        return true\n      }\n    }\n\n    if (Array.isArray(rule) && rule.some(canMatchCss)) {\n      return true\n    }\n\n    return false\n  }\n\n  const hasUserCssConfig =\n    webpackConfig.module?.rules.some(\n      (rule) => canMatchCss(rule.test) || canMatchCss(rule.include)\n    ) ?? false\n\n  if (hasUserCssConfig) {\n    // only show warning for one build\n    if (isServer) {\n      console.warn(\n        chalk.yellow.bold('Warning: ') +\n          chalk.bold(\n            'Built-in CSS support is being disabled due to custom CSS configuration being detected.\\n'\n          ) +\n          'See here for more info: https://nextjs.org/docs/messages/built-in-css-disabled\\n'\n      )\n    }\n\n    if (webpackConfig.module?.rules.length) {\n      // Remove default CSS Loader\n      webpackConfig.module.rules = webpackConfig.module.rules.filter(\n        (r) =>\n          !(\n            typeof r.oneOf?.[0]?.options === 'object' &&\n            r.oneOf[0].options.__next_css_remove === true\n          )\n      )\n    }\n    if (webpackConfig.plugins?.length) {\n      // Disable CSS Extraction Plugin\n      webpackConfig.plugins = webpackConfig.plugins.filter(\n        (p) => (p as any).__next_css_remove !== true\n      )\n    }\n    if (webpackConfig.optimization?.minimizer?.length) {\n      // Disable CSS Minifier\n      webpackConfig.optimization.minimizer =\n        webpackConfig.optimization.minimizer.filter(\n          (e) => (e as any).__next_css_remove !== true\n        )\n    }\n  } else if (!config.future.strictPostcssConfiguration) {\n    await __overrideCssConfiguration(dir, !dev, webpackConfig)\n  }\n\n  // Inject missing React Refresh loaders so that development mode is fast:\n  if (hasReactRefresh) {\n    attachReactRefresh(webpackConfig, defaultLoaders.babel)\n  }\n\n  // check if using @zeit/next-typescript and show warning\n  if (\n    isServer &&\n    webpackConfig.module &&\n    Array.isArray(webpackConfig.module.rules)\n  ) {\n    let foundTsRule = false\n\n    webpackConfig.module.rules = webpackConfig.module.rules.filter(\n      (rule): boolean => {\n        if (!(rule.test instanceof RegExp)) return true\n        if ('noop.ts'.match(rule.test) && !'noop.js'.match(rule.test)) {\n          // remove if it matches @zeit/next-typescript\n          foundTsRule = rule.use === defaultLoaders.babel\n          return !foundTsRule\n        }\n        return true\n      }\n    )\n\n    if (foundTsRule) {\n      console.warn(\n        '\\n@zeit/next-typescript is no longer needed since Next.js has built-in support for TypeScript now. Please remove it from your next.config.js and your .babelrc\\n'\n      )\n    }\n  }\n\n  // Patch `@zeit/next-sass`, `@zeit/next-less`, `@zeit/next-stylus` for compatibility\n  if (webpackConfig.module && Array.isArray(webpackConfig.module.rules)) {\n    ;[].forEach.call(\n      webpackConfig.module.rules,\n      function (rule: webpack.RuleSetRule) {\n        if (!(rule.test instanceof RegExp && Array.isArray(rule.use))) {\n          return\n        }\n\n        const isSass =\n          rule.test.source === '\\\\.scss$' || rule.test.source === '\\\\.sass$'\n        const isLess = rule.test.source === '\\\\.less$'\n        const isCss = rule.test.source === '\\\\.css$'\n        const isStylus = rule.test.source === '\\\\.styl$'\n\n        // Check if the rule we're iterating over applies to Sass, Less, or CSS\n        if (!(isSass || isLess || isCss || isStylus)) {\n          return\n        }\n\n        ;[].forEach.call(rule.use, function (use: webpack.RuleSetUseItem) {\n          if (\n            !(\n              use &&\n              typeof use === 'object' &&\n              // Identify use statements only pertaining to `css-loader`\n              (use.loader === 'css-loader' ||\n                use.loader === 'css-loader/locals') &&\n              use.options &&\n              typeof use.options === 'object' &&\n              // The `minimize` property is a good heuristic that we need to\n              // perform this hack. The `minimize` property was only valid on\n              // old `css-loader` versions. Custom setups (that aren't next-sass,\n              // next-less or next-stylus) likely have the newer version.\n              // We still handle this gracefully below.\n              (Object.prototype.hasOwnProperty.call(use.options, 'minimize') ||\n                Object.prototype.hasOwnProperty.call(\n                  use.options,\n                  'exportOnlyLocals'\n                ))\n            )\n          ) {\n            return\n          }\n\n          // Try to monkey patch within a try-catch. We shouldn't fail the build\n          // if we cannot pull this off.\n          // The user may not even be using the `next-sass` or `next-less` or\n          // `next-stylus` plugins.\n          // If it does work, great!\n          try {\n            // Resolve the version of `@zeit/next-css` as depended on by the Sass,\n            // Less or Stylus plugin.\n            const correctNextCss = require.resolve('@zeit/next-css', {\n              paths: [\n                isCss\n                  ? // Resolve `@zeit/next-css` from the base directory\n                    dir\n                  : // Else, resolve it from the specific plugins\n                    require.resolve(\n                      isSass\n                        ? '@zeit/next-sass'\n                        : isLess\n                        ? '@zeit/next-less'\n                        : isStylus\n                        ? '@zeit/next-stylus'\n                        : 'next'\n                    ),\n              ],\n            })\n\n            // If we found `@zeit/next-css` ...\n            if (correctNextCss) {\n              // ... resolve the version of `css-loader` shipped with that\n              // package instead of whichever was hoisted highest in your\n              // `node_modules` tree.\n              const correctCssLoader = require.resolve(use.loader, {\n                paths: [correctNextCss],\n              })\n              if (correctCssLoader) {\n                // We saved the user from a failed build!\n                use.loader = correctCssLoader\n              }\n            }\n          } catch (_) {\n            // The error is not required to be handled.\n          }\n        })\n      }\n    )\n  }\n\n  // Backwards compat for `main.js` entry key\n  // and setup of dependencies between entries\n  // we can't do that in the initial entry for\n  // backward-compat reasons\n  const originalEntry: any = webpackConfig.entry\n  if (typeof originalEntry !== 'undefined') {\n    const updatedEntry = async () => {\n      const entry: WebpackEntrypoints =\n        typeof originalEntry === 'function'\n          ? await originalEntry()\n          : originalEntry\n      // Server compilation doesn't have main.js\n      if (\n        clientEntries &&\n        Array.isArray(entry['main.js']) &&\n        entry['main.js'].length > 0\n      ) {\n        const originalFile = clientEntries[\n          CLIENT_STATIC_FILES_RUNTIME_MAIN\n        ] as string\n        entry[CLIENT_STATIC_FILES_RUNTIME_MAIN] = [\n          ...entry['main.js'],\n          originalFile,\n        ]\n      }\n      delete entry['main.js']\n\n      if (isWebpack5 && !isServer) {\n        for (const name of Object.keys(entry)) {\n          if (\n            name === 'polyfills' ||\n            name === 'main' ||\n            name === 'amp' ||\n            name === 'react-refresh'\n          )\n            continue\n          const dependOn =\n            name.startsWith('pages/') && name !== 'pages/_app'\n              ? 'pages/_app'\n              : 'main'\n          const old = entry[name]\n          if (typeof old === 'object' && !Array.isArray(old)) {\n            entry[name] = {\n              dependOn,\n              ...old,\n            }\n          } else {\n            entry[name] = {\n              import: old,\n              dependOn,\n            }\n          }\n        }\n      }\n\n      return entry\n    }\n    // @ts-ignore webpack 5 typings needed\n    webpackConfig.entry = updatedEntry\n  }\n\n  if (!dev) {\n    // entry is always a function\n    webpackConfig.entry = await (webpackConfig.entry as webpack.EntryFunc)()\n  }\n\n  return webpackConfig\n}\n"], "names": [], "mappings": ";;;;QAuIgB,kBAAkB,GAAlB,kBAAkB;kBAkFJ,oBAAoB;AAzNZ,GAAqD,CAArD,0BAAqD;AACzE,GAAO,CAAP,MAAO;AACN,GAAQ,CAAR,OAAQ;AACE,GAAI,CAAJ,GAAI;AACA,GAAqC,CAArC,UAAqC;AACnD,GAA2B,CAA3B,OAA2B;AACV,GAAoC,CAApC,QAAoC;AACP,GAAM,CAAN,KAAM;AAMhE,GAAkB,CAAlB,UAAkB;AACE,GAAoB,CAApB,WAAoB;AACb,GAA4B,CAA5B,kBAA4B;AAEnB,GAA8C,CAA9C,2BAA8C;AAUlF,GAAyB,CAAzB,WAAyB;AACP,GAAqB,CAArB,MAAqB;AAGlC,GAAG,CAAH,GAAG;AAC6B,GAAkB,CAAlB,OAAkB;AACnB,GAAsD,CAAtD,yBAAsD;AACjE,GAAyC,CAAzC,oBAAyC;AAC5C,GAAsC,CAAtC,iBAAsC;AACtC,GAAsC,CAAtC,iBAAsC;AAC/B,GAAyC,CAAzC,oBAAyC;AAC9C,GAAgD,CAAhD,yBAAgD;AACxC,GAAiD,CAAjD,2BAAiD;AACtD,GAAqC,CAArC,gBAAqC;AAChC,GAA2C,CAA3C,qBAA2C;AAClD,GAAyC,CAAzC,oBAAyC;AACzC,GAAoC,CAApC,gBAAoC;AAChC,GAAyC,CAAzC,oBAAyC;AAC5C,GAAqC,CAArC,iBAAqC;AAM/D,GAA8C,CAA9C,yBAA8C;AACf,GAA2C,CAA3C,sBAA2C;AACpD,GAA6B,CAA7B,IAA6B;AAC3B,GAAoC,CAApC,eAAoC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAKnE,KAAK,CAAC,oBAAoB,OA/BD,MAAqB,YAgC3C,OAAyC,GAAK,CAAC;IAC9C,OAAO,CAAC,IAAI,CA5DE,MAAO,SA6Db,MAAM,CAAC,IAAI,EAAC,SAAW,KA7DjB,MAAO,SA8DX,IAAI,EAAE,8BAA8B,EAAE,OAAO,CAAC,IAAI,MACxD,6FAA+F,KAC/F,4DAA8D;AAEpE,CAAC;SAGM,aAAa,CAAC,QAAgB,EAAE,CAAC;IACxC,KAAK,CAAC,KAAK,GAAG,OAAO,EAAC,wBAA0B;IAChD,KAAK,CAAC,QAAQ,OArEa,GAAI,eAqED,QAAQ,GAAE,IAAM;IAE9C,EAA6B,AAA7B,2BAA6B;IAC7B,EAAE,EAAE,QAAQ,CAAC,IAAI,WAAW,CAAC;;;IAE7B,CAAC;QAEG,CAAC;eACI,KAAK,CAAC,KAAK,CAAC,QAAQ;IAC7B,CAAC,QAAQ,GAAG,EAAE,CAAC;QACb,KAAK,CAAC,SAAS,OA9Ec,UAAqC,mBA+EhE,MAAM,CAAC,QAAQ;YACb,KAAK;gBAAI,IAAI,EAAE,GAAG,CAAC,UAAU;gBAAE,MAAM,EAAE,GAAG,CAAC,YAAY;;;YACvD,OAAO,EAAE,GAAG,CAAC,OAAO;YAAE,aAAa,EAAE,IAAI;;QAE7C,KAAK,CAAC,GAAG,CAAC,KAAK,EAAE,iBAAiB,EAAE,QAAQ,CAAC,IAAI,EAAE,SAAS;IAC9D,CAAC;AACH,CAAC;SAEQ,mBAAmB,CAAC,QAAiB,EAA6B,CAAC;IAC1E,EAAE,EAAE,QAAQ,EAAE,CAAC;;;IAEf,CAAC;IAED,KAAK,CAAC,eAAe,GAzF0C,KAAM,SAyFxC,IAAI,CAAC,SAAS,GAAE,SAAW,IAAE,KAAO,IAAE,QAAU;IAC7E,KAAK,CAAC,gBAAgB,GA1FyC,KAAM,SA0FvC,IAAI,CAAC,SAAS,GAAE,SAAW,IAAE,gBAAkB;IAE7E,KAAK,CAAC,UAAU,GA5F+C,KAAM,SA4F7C,IAAI,CAAC,SAAS,GAAE,SAAW,IAAE,aAAe;WAC7D,MAAM,CAAC,MAAM;;QAGhB,QAAQ,EAAE,eAAe;SACzB,mBAAqB,GAAE,eAAe;SACtC,aAAe,GAlG4C,KAAM,SAkG3C,IAAI,CACxB,SAAS,GACT,SAAW,IACX,KAAO,IACP,eAAiB;;SAInB,cAAgB,GAAE,gBAAgB;QAElC,EAA8B,AAA9B,4BAA8B;SAC9B,kBAAoB,GA7GuC,KAAM,SA6GtC,IAAI,CAAC,UAAU,GAAE,OAAS;SACrD,4BAA8B,GA9G6B,KAAM,SA8G5B,IAAI,CACvC,UAAU,GACV,iBAAmB;SAErB,cAAgB,GAlH2C,KAAM,SAkH1C,IAAI,CAAC,UAAU,GAAE,QAAU;SAClD,sBAAwB,GAnHmC,KAAM,SAmHlC,IAAI,CAAC,UAAU,GAAE,WAAa;SAC7D,kBAAoB,GApHuC,KAAM,SAoHtC,IAAI,CAAC,UAAU,GAAE,OAAS;QAErD,EAA0D,AAA1D,wDAA0D;QAC1D,GAAG,EAAE,OAAO,CAAC,OAAO,EAAC,UAAY;;AAGvC,CAAC;SAMe,kBAAkB,CAChC,aAAoC,EACpC,YAAoC,EACpC,CAAC;QAID,GAAoB;IAHpB,GAAG,CAAC,UAAU,GAAG,CAAC;IAClB,KAAK,CAAC,sBAAsB,IAAG,gCAAkC;IACjE,KAAK,CAAC,kBAAkB,GAAG,OAAO,CAAC,OAAO,CAAC,sBAAsB;KACjE,GAAoB,GAApB,aAAa,CAAC,MAAM,cAApB,GAAoB,UAApB,CAA2B,QAA3B,CAA2B,GAA3B,GAAoB,CAAE,KAAK,CAAC,OAAO,EAAE,IAAI,GAAK,CAAC;QAC7C,KAAK,CAAC,IAAI,GAAG,IAAI,CAAC,GAAG;QACrB,EAAwE,AAAxE,sEAAwE;QACxE,EAAE,EAAE,IAAI,KAAK,YAAY,EAAE,CAAC;cACxB,UAAU;YACZ,IAAI,CAAC,GAAG;gBAAI,kBAAkB;gBAAE,IAAI;;QACtC,CAAC,MAAM,EAAE,EACP,KAAK,CAAC,OAAO,CAAC,IAAI,KAClB,IAAI,CAAC,IAAI,EAAE,CAAC,GAAK,CAAC,KAAK,YAAY;aACnC,EAAkC,AAAlC,gCAAkC;SACjC,IAAI,CAAC,IAAI,EACP,CAAC,GAAK,CAAC,KAAK,kBAAkB,IAAI,CAAC,KAAK,sBAAsB;WAEjE,CAAC;cACC,UAAU;YACZ,KAAK,CAAC,GAAG,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC,GAAK,CAAC,KAAK,YAAY;;YACpD,EAAiC,AAAjC,+BAAiC;YACjC,IAAI,CAAC,GAAG;mBAAO,IAAI;;YAEnB,EAAkE,AAAlE,gEAAkE;YAClE,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,GAAG,EAAE,CAAC,EAAE,kBAAkB;QAC5C,CAAC;IACH,CAAC;IAED,EAAE,EAAE,UAAU,EAAE,CAAC;QAvIP,GAAG,CAwIP,IAAI,EACL,uCAAuC,EAAE,UAAU,CAAC,cAAc,EACjE,UAAU,GAAG,CAAC,IAAG,CAAG;IAG1B,CAAC;AACH,CAAC;AAED,KAAK,CAAC,uBAAuB;IAC3B,EAAiE,AAAjE,+DAAiE;IACjE,EAAmC,AAAnC,iCAAmC;IACnC,EAA6D,AAA7D,2DAA6D;IAC7D,EAAmE,AAAnE,iEAAmE;IACnE,cAAc,GAAE,QAAU;IAC1B,QAAQ,EAAE,IAAI;;AAGhB,KAAK,CAAC,2BAA2B;IAC/B,cAAc,GAAE,GAAK;IACrB,QAAQ,EAAE,IAAI;;AAGhB,KAAK,CAAC,oBAAoB;IACxB,cAAc,GAAE,QAAU;IAC1B,OAAO;SAAG,YAAc;;IACxB,KAAK,EAAE,KAAK;IACZ,QAAQ,EAAE,KAAK;IACf,aAAa;SAAG,OAAS;;IACzB,aAAa;SAAG,OAAS;;IACzB,cAAc;SAAG,IAAM;SAAE,OAAS;SAAE,MAAQ;;IAC5C,gBAAgB;SAAG,YAAc;;IACjC,UAAU;SAAG,GAAK;SAAE,KAAO;SAAE,KAAO;;IACpC,iBAAiB,EAAE,KAAK;IACxB,QAAQ,EAAE,IAAI;IACd,UAAU;SAAG,IAAM;;IACnB,SAAS;SAAG,KAAO;;IACnB,KAAK;IACL,cAAc,EAAE,KAAK;IACrB,cAAc,EAAE,KAAK;IACrB,cAAc,EAAE,KAAK;IACrB,YAAY;;AAGd,KAAK,CAAC,wBAAwB;OACzB,oBAAoB;IACvB,cAAc,GAAE,GAAK;IACrB,cAAc;SAAG,IAAM;SAAE,MAAQ;SAAE,MAAQ;;IAC3C,cAAc,EAAE,IAAI;;eAGQ,oBAAoB,CAChD,GAAW,IAET,OAAO,GACP,MAAM,GACN,GAAG,EAAG,KAAK,GACX,QAAQ,EAAG,KAAK,GAChB,QAAQ,GACR,MAAM,GAAG,MAAQ,IACjB,wBAAwB,EAAG,KAAK,GAChC,WAAW,GACX,QAAQ,GACR,aAAa,EAAG,KAAK,GACrB,cAAc,KAcgB,CAAC;QAa7B,GAA8B,EAyJ9B,IAAyB,EA+RrB,IAAkB,QAWH,IAAmB,EAChB,IAAmB,EA0hBW,IAAW,EAyJ/D,IAAyB,EAqO3B,IAAoB,EAkHpB,IAAoB;IA19CtB,KAAK,CAAC,WAAW,GACf,QAAQ,CAAC,WAAW,CAAC,MAAM,GAAG,CAAC,IAC/B,QAAQ,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,IAC9B,QAAQ,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC;IAC9B,KAAK,CAAC,eAAe,GAAY,GAAG,KAAK,QAAQ;IACjD,KAAK,CAAC,eAAe,aA3OW,kBAA4B;QA4O1D,GAAG,EAAE,GAAG;QACR,IAAI,GAAE,SAAW;;IAEnB,KAAK,CAAC,UAAU,GACd,OAAO,CAAC,eAAe,MA1PR,OAA2B,SA2PlC,GAAG,CAAC,eAAe,GAAG,MAAQ,QACpC,GAA8B,GA5PjB,OAA2B,SA4PjC,MAAM,CAAC,eAAe,eAA7B,GAA8B,UAA9B,CAAuC,QAAvC,CAAuC,GAAvC,GAA8B,CAAE,OAAO,OAAK,MAAQ;IACxD,KAAK,CAAC,kBAAkB,GACtB,OAAO,CAAC,eAAe,KA9PR,OAA2B,SA8PP,UAAU,CAAC,eAAe,KAAM,IAAI;IACzE,KAAK,CAAC,YAAY,GAAY,MAAM,CAAC,YAAY,CAAC,SAAS,IAAI,UAAU;IAEzE,EAAuC,AAAvC,qCAAuC;IACvC,EAAE,GAAG,QAAQ,EAAE,CAAC;QACd,EAAE,EAAE,YAAY,EAAE,CAAC;YAzOX,GAAG,CA0OL,IAAI,EAAC,kCAAoC;QAC/C,CAAC;QACD,EAAE,EAAE,kBAAkB,EAAE,CAAC;YA5OjB,GAAG,CA6OL,IAAI,EACL,uEAAuE,KACrE,qEAAqE;QAE5E,CAAC;IACH,CAAC;IAED,KAAK,CAAC,eAAe;SACnB,QAAU;SACV,aAAe;SACf,WAAa;SACb,YAAc;SACd,YAAc;SACd,eAAiB;SACjB,iBAAmB;SACnB,gBAAkB;SAClB,gBAAkB;MAClB,MAAM,QAAQ,IAAiC,EAAE,QAAQ,GAAK,CAAC;QAC/D,KAAK,CAAC,cAAc,GAvRyC,KAAM,SAuRvC,IAAI,CAAC,GAAG,EAAE,QAAQ;qBAErC,IAAI,eAlRU,WAAoB,aAmRtB,cAAc,IAAK,cAAc,GAAG,SAAS;IAEpE,CAAC,EAAE,OAAO,CAAC,OAAO,CAAC,SAAS;IAE5B,KAAK,CAAC,OAAO,GA9RkD,KAAM,SA8RhD,IAAI,CAAC,GAAG,EAAE,MAAM,CAAC,OAAO;IAE7C,EAAwF,AAAxF,sFAAwF;IACxF,EAAkE,AAAlE,gEAAkE;IAClE,KAAK,CAAC,WAAW,GAnSiB,QAAoC,cAoSlE,OAAO,CAAC,OAAO,EAAC,oBAAsB,MACtC,iBAAmB;IAEvB,KAAK,CAAC,YAAY,GAAG,MAAM,CAAC,YAAY,CAAC,SAAS,IAvShB,QAAoC;IAwStE,EAAE,EAAE,YAAY,IAAI,eAAe,EAAE,CAAC;QA/Q5B,GAAG,CAgRP,IAAI,EACL,gFAAgF;IAErF,CAAC;IACD,KAAK,CAAC,cAAc;QAClB,KAAK,EAAE,YAAY;YAEb,MAAM,GAAE,eAAiB;YACzB,OAAO;gBACL,QAAQ;;;YAIV,MAAM,EAAE,WAAW;YACnB,OAAO;gBACL,UAAU,EAAE,eAAe;gBAC3B,QAAQ;gBACR,OAAO;gBACP,QAAQ;gBACR,GAAG,EAAE,GAAG;gBACR,EAAwC,AAAxC,sCAAwC;gBACxC,KAAK,GA9TmB,QAAoC;gBA+T5D,WAAW,EAAE,GAAG;gBAChB,eAAe;gBACf,aAAa,EAAE,IAAI;;;QAG3B,EAAmB,AAAnB,iBAAmB;QACnB,aAAa;YACX,MAAM,GAAE,WAAa;;;IAIzB,KAAK,CAAC,mBAAmB;;;;;;IAOzB,EAAwB,AAAxB,sBAAwB;IACxB,KAAK,CAAC,YAAY,IAAI,OAAO,CAAC,GAAG,CAAC,SAAS,QACxC,KAAK,CAAC,OAAO,CAAC,QAAQ,MAAK,KAAO,KAAG,CAAG,KAAG,CAAG,GAC9C,MAAM,EAAE,CAAC,KAAO,CAAC;;IAEpB,KAAK,CAAC,YAAY,GAAG,MAAM,MAAK,UAAY;IAC5C,KAAK,CAAC,iBAAiB,GAAG,MAAM,MAAK,6BAA+B;IACpE,EAAwD,AAAxD,sDAAwD;IACxD,KAAK,CAAC,gBAAgB,GAAG,YAAY,IAAI,iBAAiB;IAE1D,KAAK,CAAC,SAAS,GAAG,gBAAgB,GAtU7B,WAAyB,wBAAzB,WAAyB;IAuU9B,KAAK,CAAC,UAAU,GA3V+C,KAAM,SA2V7C,IAAI,CAAC,OAAO,EAAE,QAAQ,GAAG,SAAS;IAC1D,KAAK,CAAC,UAAU,GAAG,MAAM,CAAC,IAAI,CAAC,WAAW,EAAE,MAAM;IAClD,KAAK,CAAC,aAAa,IAAI,QAAQ;QAEzB,EAA0B,AAA1B,wBAA0B;SAC1B,OAAS;WACL,GAAG;aA7UR,WAAyB,6CA+U2B,OAAO,CAAC,OAAO,EACzD,iCAAiC;aAhV3C,WAAyB,oCAmVf,EAAE,QAvW8C,KAAM,WAyWrD,GAAG,MAzW4C,KAAM,OAMhE,UAAkB,iCAoWiC,GAAK,IAAE,OAAS,IACxD,OAAO,SAAQ,CAAG;;;SAvV7B,WAAyB,qCA2VrB,EAAE,IA/WoD,KAAM,SAiX1D,QAAQ,CACP,GAAG,EAlXgD,KAAM,SAmXpD,IAAI,CA7WhB,UAAkB,gCA+WT,GAAG,IAAI,WAAW,KAAI,OAAS,IAGlC,OAAO,SAAQ,CAAG;QAEzB,SAAS;IAEb,GAAG,CAAC,cAAc;QACd,CAAC;QACH,cAAc,GAAG,OAAO,CAAC,OAAO,EAAC,UAAY;YAAI,KAAK;gBAAG,GAAG;;;IAC9D,CAAC,QAAQ,CAAC,EAAE,CAAC;IAAA,CAAC;IACd,KAAK,CAAC,YAAY,GAhY6C,KAAM,SAgY3C,IAAI,CAAC,GAAG,GAAE,aAAe;IACnD,KAAK,CAAC,aAAa,GAAG,OAAO,CAC3B,cAAc,cA3XS,WAAoB,aA2XP,YAAY;IAGlD,GAAG,CAAC,QAAQ;IACZ,EAAmC,AAAnC,iCAAmC;IACnC,EAAE,EAAE,aAAa,EAAE,CAAC;QAClB,KAAK,CAAC,EAAE;mDAAiB,cAAc;;QACvC,KAAK,CAAC,QAAQ,aA/XyB,2BAA8C,6BA+XnC,EAAE,EAAE,YAAY,EAAE,IAAI;QACxE,QAAQ;YAAK,eAAe,EAAE,QAAQ,CAAC,OAAO;;IAChD,CAAC;IAED,KAAK,CAAC,YAAY,GA7Y6C,KAAM,SA6Y3C,IAAI,CAAC,GAAG,GAAE,aAAe;IACnD,EAAE,GAAG,aAAa,cAvYO,WAAoB,aAuYL,YAAY,GAAI,CAAC;QACvD,QAAQ,GAAG,aAAa,CAAC,YAAY;IACvC,CAAC;IAED,GAAG,CAAC,eAAe;IACnB,EAAE,EAAE,QAAQ,aAAR,QAAQ,UAAR,CAAyB,QAAzB,CAAyB,IAAzB,IAAyB,GAAzB,QAAQ,CAAE,eAAe,cAAzB,IAAyB,UAAzB,CAAyB,QAAzB,CAAyB,GAAzB,IAAyB,CAAE,OAAO,EAAE,CAAC;QACvC,eAAe,GApZ8C,KAAM,SAoZ5C,OAAO,CAAC,GAAG,EAAE,QAAQ,CAAC,eAAe,CAAC,OAAO;IACtE,CAAC;aAEQ,6BAA6B,GAAG,CAAC;QACxC,EAAE,EAAE,wBAAwB,EAAE,CAAC;;iBAE3B,UAAY,IAAE,mBAAqB;iBACnC,iBAAmB,IAAE,2BAA6B;;QAEtD,CAAC;IACH,CAAC;IAED,EAAoD,AAApD,kDAAoD;IACpD,EAAqD,AAArD,mDAAqD;IACrD,EAAsC,AAAtC,oCAAsC;IACtC,KAAK,CAAC,qBAAqB,GAAG,OAAO,CAAC,OAAO,EAC3C,2CAA6C;IAE/C,KAAK,CAAC,yBAAyB,GAAG,OAAO,CAAC,OAAO,EAC/C,gDAAkD;IAGpD,KAAK,CAAC,gBAAgB;;IACtB,KAAK,CAAC,gBAAgB;;IACtB,KAAK,CAAC,qBAAqB;;IAE3B,EAAE,EAAE,GAAG,IA/a2B,QAAoC,aA+a/C,CAAC;QACtB,gBAAgB,IAzab,UAAkB,iBAyae,KAAK;eACpC,MAAM,CAAC,cAAc,CAAC,MAAM,EAAE,IAAI,EAAE,GAAG,GAAK,CAAC;gBAC9C,IAAI,CAAC,IAAI,CAjbgD,KAAM,SAibhD,IAAI,CAAC,QAAQ,GAAG,KAAK,EAAE,GAAG;uBAClC,IAAI;YACb,CAAC;aACD,uBAAyB;;QAE3B,gBAAgB,IAhbb,UAAkB,iBAgbe,OAAO;eACtC,MAAM,CAAC,cAAc,CAAC,MAAM,EAAE,IAAI,EAAE,GAAG,GAAK,CAAC;gBAC9C,IAAI,CAAC,IAAI,CAxbgD,KAAM,SAwbhD,IAAI,CAAC,QAAQ,GAAG,OAAO,EAAE,GAAG;uBACpC,IAAI;YACb,CAAC;aACD,yBAA2B;;QAE7B,qBAAqB,IAvblB,UAAkB,iBAuboB,UAAU;eAC9C,MAAM,CAAC,cAAc,CAAC,MAAM,EAAE,IAAI,EAAE,GAAG,GAAK,CAAC;gBAC9C,IAAI,CAAC,IAAI,CA/bgD,KAAM,SA+bhD,IAAI,CAAC,QAAQ,GAAG,UAAU,EAAE,GAAG;uBACvC,IAAI;YACb,CAAC;aACD,4BAA8B;;IAElC,CAAC;IAED,KAAK,CAAC,aAAa;QACjB,EAAyC,AAAzC,uCAAyC;QACzC,UAAU,EAAE,QAAQ;aAEd,GAAK;aACL,IAAM;eACF,aAAa;iBAAI,IAAM;iBAAE,GAAK;;aAClC,IAAM;aACN,KAAO;aACP,KAAO;;aAGP,IAAM;aACN,GAAK;eACD,aAAa;iBAAI,IAAM;iBAAE,GAAK;;aAClC,IAAM;aACN,KAAO;aACP,KAAO;;QAEb,OAAO;aACL,YAAc;eACX,YAAY;;QAEjB,KAAK;YACH,IAAI,EAxdH,UAAkB;eA0dhB,gBAAgB;eAChB,gBAAgB;eAChB,qBAAqB;aA5dvB,UAAkB,mBA8dA,QAAQ;aA9d1B,UAAkB,kBA+dD,OAAO;eACtB,mBAAmB,CAAC,QAAQ;eAC5B,6BAA6B;aAC/B,qBAAqB,GAAG,WAAW,GAChC,qBAAqB,GA1eK,QAAoC,cA6e9D,KAAK,GACL,yBAAyB;;WA9eC,QAAoC,gBAgfjD,QAAQ;YAErB,EAAiD,AAAjD,+CAAiD;YACjD,EAAsH,AAAtH,oHAAsH;YACtH,QAAQ;gBACN,MAAM,EAAE,OAAO,CAAC,OAAO,EAAC,OAAS;gBACjC,MAAM,EAAE,OAAO,CAAC,OAAO,EAAC,OAAS;gBACjC,SAAS,EAAE,OAAO,CAAC,OAAO,EAAC,oBAAsB;gBACjD,MAAM,EAAE,OAAO,CAAC,OAAO,EAAC,iBAAmB;gBAC3C,MAAM,EAAE,OAAO,CAAC,OAAO,EAAC,cAAgB;gBACxC,IAAI,EAAE,OAAO,CAAC,OAAO,EAAC,WAAa;gBACnC,KAAK,EAAE,OAAO,CAAC,OAAO,EAAC,gBAAkB;gBACzC,EAAE,EAAE,OAAO,CAAC,OAAO,EAAC,qBAAuB;gBAC3C,IAAI,EAAE,OAAO,CAAC,OAAO,EAAC,eAAiB;gBACvC,QAAQ,EAAE,OAAO,CAAC,OAAO,EAAC,QAAU;gBACpC,OAAO,EAAE,OAAO,CAAC,OAAO,EAAC,eAAiB;gBAC1C,EAA4B,AAA5B,0BAA4B;gBAC5B,WAAW,EAAE,OAAO,CAAC,OAAO,EAAC,eAAiB;gBAC9C,MAAM,EAAE,OAAO,CAAC,OAAO,EAAC,iBAAmB;gBAC3C,cAAc,EAAE,OAAO,CAAC,OAAO,EAAC,cAAgB;gBAChD,GAAG,EAAE,OAAO,CAAC,OAAO,EAAC,KAAO;gBAC5B,MAAM,EAAE,OAAO,CAAC,OAAO,EAAC,iBAAmB;gBAC3C,GAAG,EAAE,OAAO,CAAC,OAAO,EAAC,cAAgB;gBACrC,EAA4B,AAA5B,0BAA4B;gBAC5B,EAAgC,AAAhC,8BAAgC;gBAChC,IAAI,EAAE,OAAO,CAAC,OAAO,EAAC,KAAO;gBAC7B,EAAE,EAAE,OAAO,CAAC,OAAO,EAAC,aAAe;gBACnC,IAAI,EAAE,OAAO,CAAC,OAAO,EAAC,eAAiB;;YAG3C,SAAS;QACb,UAAU,EAAE,QAAQ;aAAI,IAAM;aAAE,MAAQ;;aAAK,OAAS;aAAE,MAAQ;aAAE,IAAM;;QACxE,OAAO,EAhhByB,QAAoC;YAmhB/D,OAAO,EAAC,kBAAoB;;;IAGnC,KAAK,CAAC,aAAa;QACjB,KAAK;YACH,IAAI,EAAE,CAAC;;QAET,QAAQ;YACN,IAAI,EAAE,CAAC;YACP,QAAQ,EAAE,KAAK;YACf,EAAqE,AAArE,mEAAqE;YACrE,WAAW,EAAE,KAAK;YAClB,MAAM,EAAE,CAAC;;QAEX,MAAM;YAAI,QAAQ,EAAE,IAAI;;QACxB,MAAM;YACJ,IAAI,EAAE,CAAC;YACP,QAAQ,EAAE,IAAI;YACd,QAAQ,EAAE,KAAK;YACf,EAAyC,AAAzC,uCAAyC;YACzC,UAAU,EAAE,IAAI;;;IAIpB,KAAK,CAAC,WAAW,IAAI,MAAwB,GAAc,CAAC;eAExD,EAA0B,AAA1B,wBAA0B;QAC1B,MAAM,CAAC,IAAI,MAAM,gBAAgB,KACjC,EAA0C,AAA1C,wCAA0C;QAC1C,MAAM,CAAC,IAAI,MAAM,kBAAkB,KACnC,EAA0C,AAA1C,wCAA0C;QAC1C,MAAM,CAAC,IAAI,MAAM,sBAAsB;IAE3C,CAAC;IAED,EAA2F,AAA3F,yFAA2F;IAC3F,KAAK,CAAC,kBAAkB;QAGtB,GAAG;YACD,WAAW;gBACT,OAAO,EAAE,KAAK;gBACd,OAAO,EAAE,KAAK;;;QAGlB,YAAY;YACV,EAAoD,AAApD,kDAAoD;YACpD,EAAqD,AAArD,mDAAqD;YACrD,EAAoD,AAApD,kDAAoD;YACpD,EAA0C,AAA1C,wCAA0C;YAC1C,MAAM,EArkBwB,QAAoC,eAskB7D,KAAK,qCAAuC,IAAI,CAAC,KAAK,CAAC,IAAI;gBAC5D,GAAK;YACT,WAAW;gBACT,SAAS;oBACP,MAAM,GAAE,GAAK;oBACb,IAAI,GAAE,SAAW;oBACjB,EAAqE,AAArE,mEAAqE;oBACrE,EAA6B,AAA7B,2BAA6B;oBAC7B,EAA8C,AAA9C,4CAA8C;oBAC9C,IAAI;oBACJ,QAAQ,EAAE,EAAE;oBACZ,EAAmE,AAAnE,iEAAmE;oBACnE,EAAwC,AAAxC,sCAAwC;oBACxC,OAAO,EAAE,IAAI;;gBAEf,GAAG;oBACD,IAAI,EAAC,MAGJ,EAAW,CAAC;+BAET,MAAM,CAAC,IAAI,KAAK,MAAM,wBACF,IAAI,CAAC,MAAM,CAAC,gBAAgB;oBAEpD,CAAC;oBACD,IAAI,EAAC,MAIJ,EAAU,CAAC;wBACV,KAAK,CAAC,IAAI,GAxmBH,OAAQ,SAwmBK,UAAU,EAAC,IAAM;wBACrC,EAAE,EAAE,WAAW,CAAC,MAAM,GAAG,CAAC;4BACxB,MAAM,CAAC,UAAU,CAAC,IAAI;wBACxB,CAAC,MAAM,CAAC;4BACN,EAAE,GAAG,MAAM,CAAC,QAAQ,EAAE,CAAC;gCACrB,KAAK,CAAC,GAAG,CAAC,KAAK,EACZ,iCAAiC,EAAE,MAAM,CAAC,IAAI,CAAC,uBAAuB;4BAE3E,CAAC;4BAED,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,QAAQ;gCAAG,OAAO,EAAE,GAAG;;wBAC5C,CAAC;+BAEM,IAAI,CAAC,MAAM,EAAC,GAAK,GAAE,SAAS,CAAC,CAAC,EAAE,CAAC;oBAC1C,CAAC;oBACD,QAAQ,EAAE,EAAE;oBACZ,SAAS,EAAE,CAAC;oBACZ,kBAAkB,EAAE,IAAI;;gBAE1B,OAAO;oBACL,IAAI,GAAE,OAAS;oBACf,SAAS,EAAE,UAAU;oBACrB,QAAQ,EAAE,EAAE;;mBA1nBc,QAAoC,cA6nB5D,SAAS;oBAEP,OAAO,EAAE,KAAK;oBACd,OAAO,EAAE,KAAK;oBACd,MAAM;wBACJ,IAAI,EAAC,MAAM,EAAE,MAAM,EAAE,CAAC;mCAtoBnB,OAAQ,SAyoBJ,UAAU,EAAC,IAAM,GACjB,MAAM,CACL,MAAM,CAAC,MAAM,EACV,GAAW,EAAE,KAAgC,GAAK,CAAC;uCAC3C,GAAG,GAAG,KAAK,CAAC,IAAI;4BACzB,CAAC,OAIJ,MAAM,EAAC,GAAK,MAAK,WAAW,CAAC,MAAM,KAAI,IAAM;wBAEpD,CAAC;wBACD,QAAQ,EAAE,EAAE;wBACZ,SAAS,EAAE,CAAC;wBACZ,kBAAkB,EAAE,IAAI;;;;YAIlC,kBAAkB,EAAE,EAAE;YACtB,OAAO,EAAE,KAAK;;;IAIlB,EAA6D,AAA7D,2DAA6D;IAC7D,GAAG,CAAC,iBAAiB;IACrB,EAAE,EAAE,GAAG,EAAE,CAAC;QACR,iBAAiB,GA/pBe,QAAoC,cA+pBnC,KAAK,GAAG,kBAAkB,CAAC,GAAG;IACjE,CAAC,MAAM,CAAC;QACN,iBAAiB,GAAG,kBAAkB,CAAC,YAAY;IACrD,CAAC;IAED,KAAK,CAAC,WAAW,GAAG,MAAM,CAAC,WAAW;IAEtC,KAAK,CAAC,iBAAiB,GAAG,MAAM,CAAC,MAAM;QAEnC,gCAAgC;YAC9B,OAAO,EAAE,IAAI;;QAEf,4BAA4B;YAC1B,OAAO,EAAE,IAAI;;QAEf,kCAAkC;YAChC,OAAO,EAAE,IAAI;YACb,wBAAwB,EAAE,MAAM,CAAC,MAAM;iBAEpC,KAAO;iBACR,IAAkB,GAAlB,MAAM,CAAC,WAAW,cAAlB,IAAkB,UAAlB,CAAsD,QAAtD,CAAsD,WAAtD,IAAkB,CAAE,kCAAkC,4BAAtD,CAAsD,QAAtD,CAAsD,QAClD,wBAAwB;;QAGhC,8BAA8B;YAC5B,OAAO,EAAE,IAAI;;OAGjB,MAAM,CAAC,WAAW;IAGpB,KAAK,CAAC,YAAY,OAAK,IAAmB,GAAnB,MAAM,CAAC,YAAY,cAAnB,IAAmB,UAAnB,CAAiC,QAAjC,CAAiC,GAAjC,IAAmB,CAAE,YAAY;IACxD,KAAK,CAAC,iBAAiB,KAAG,IAAmB,GAAnB,MAAM,CAAC,YAAY,cAAnB,IAAmB,UAAnB,CAAiC,QAAjC,CAAiC,GAAjC,IAAmB,CAAE,YAAY,OAAK,KAAO;mBAExD,eAAe,CAC5B,OAAe,EACf,OAAe,EACf,cAAsB,EACtB,UAKsC,EACtC,CAAC;QACD,EAAiE,AAAjE,+DAAiE;QACjE,EAAkB,AAAlB,gBAAkB;QAElB,KAAK,CAAC,OAAO,GACX,OAAO,CAAC,UAAU,EAAC,CAAG,MACtB,EAAyD,AAAzD,uDAAyD;QACzD,EAAuB,AAAvB,qBAAuB;QAjtBoC,KAAM,SAktB5D,KAAK,CAAC,UAAU,CAAC,OAAO,KAC7B,EAA8D,AAA9D,4DAA8D;QAC9D,EAAkB,AAAlB,gBAAkB;SACjB,OAAO,CAAC,QAAQ,MAAK,KAAO,KArtB8B,KAAM,SAqtB3B,KAAK,CAAC,UAAU,CAAC,OAAO;QAEhE,EAA+D,AAA/D,6DAA+D;QAC/D,EAAwD,AAAxD,sDAAwD;QACxD,EAAkE,AAAlE,gEAAkE;QAClE,EAAmE,AAAnE,iEAAmE;QACnE,EAAE,GAAG,OAAO,EAAE,CAAC;YACb,EAAE,6BAA6B,IAAI,CAAC,OAAO,GAAG,CAAC;wBACrC,SAAS,EAAE,OAAO;YAC5B,CAAC;YAED,KAAK,CAAC,kBAAkB;YAExB,EAAE,EAAE,kBAAkB,CAAC,IAAI,CAAC,OAAO,GAAG,CAAC;;YAEvC,CAAC;QACH,CAAC;QAED,EAAgE,AAAhE,8DAAgE;QAChE,EAAyB,AAAzB,uBAAyB;QACzB,KAAK,CAAC,cAAc,GAAG,cAAc,MAAK,GAAK;QAC/C,KAAK,CAAC,SAAS,GAAG,YAAY,IAAI,cAAc;QAEhD,KAAK,CAAC,OAAO,GAAG,UAAU,CACxB,SAAS,GAAG,2BAA2B,GAAG,uBAAuB;QAGnE,EAA6D,AAA7D,2DAA6D;QAC7D,EAA4D,AAA5D,0DAA4D;QAC5D,EAAS,AAAT,OAAS;QACT,GAAG,CAAC,GAAG;QACP,GAAG,CAAC,KAAK,GAAY,KAAK;YACtB,CAAC;aACD,GAAG,EAAE,KAAK,UAAU,OAAO,CAAC,OAAO,EAAE,OAAO;QAChD,CAAC,QAAQ,GAAG,EAAE,CAAC;YACb,GAAG,GAAG,IAAI;QACZ,CAAC;QAED,EAAwD,AAAxD,sDAAwD;QACxD,EAAyC,AAAzC,uCAAyC;QACzC,EAAE,GAAG,GAAG,KAAK,cAAc,IAAI,iBAAiB,GAAG,CAAC;YAClD,KAAK,CAAC,kBAAkB,GAAG,UAAU,CACnC,SAAS,GAAG,uBAAuB,GAAG,2BAA2B;gBAE/D,CAAC;iBACD,GAAG,EAAE,KAAK,UAAU,kBAAkB,CAAC,OAAO,EAAE,OAAO;YAC3D,CAAC,QAAQ,GAAG,EAAE,CAAC;gBACb,GAAG,GAAG,IAAI;YACZ,CAAC;QACH,CAAC;QAED,EAAoD,AAApD,kDAAoD;QACpD,EAA0D,AAA1D,wDAA0D;QAC1D,EAAE,GAAG,GAAG,EAAE,CAAC;;QAEX,CAAC;QAED,EAAyD,AAAzD,uDAAyD;QACzD,EAAmC,AAAnC,iCAAmC;QACnC,EAAE,GAAG,cAAc,IAAI,KAAK,KAAK,iBAAiB,EAAE,CAAC;YACnD,KAAK,CAAC,GAAG,CAAC,KAAK,EACZ,cAAc,EAAE,OAAO,CAAC,2HAA2H;QAExJ,CAAC;QAED,EAAE,EAAE,OAAO,EAAE,CAAC;YACZ,EAAyD,AAAzD,uDAAyD;YACzD,EAA2D,AAA3D,yDAA2D;YAC3D,EAAyD,AAAzD,uDAAyD;YACzD,KAAK,CAAC,cAAc,mFAC8D,IAAI,CAClF,GAAG;YAGP,EAAE,EAAE,cAAc,EAAE,CAAC;gBACnB,EAAmC,AAAnC,iCAAmC;gBACnC,KAAK,CAAC,eAAe,GAjyBoC,KAAM,SAiyBlC,KAAK,CAAC,IAAI,EACrC,IAAM,IACN,IAAM,GAnyBiD,KAAM,SAqyB1D,QAAQ,CACP,EAA2B,AAA3B,yBAA2B;gBAtyBwB,KAAM,SAuyBpD,IAAI,CAAC,SAAS,GAAE,EAAI,IACzB,GAAG,CAEL,EAA6B,AAA7B,2BAA6B;iBAC5B,OAAO,SAAQ,CAAG;wBAEf,SAAS,EAAE,eAAe;YACpC,CAAC,MAAM,CAAC;;YAER,CAAC;QACH,CAAC;QAED,EAAmE,AAAnE,iEAAmE;QACnE,EAAmE,AAAnE,iEAAmE;QACnE,EAAkE,AAAlE,gEAAkE;QAClE,EAAgE,AAAhE,8DAAgE;QAChE,GAAG,CAAC,OAAO;QACX,GAAG,CAAC,SAAS;YACT,CAAC;YACH,KAAK,CAAC,WAAW,GAAG,UAAU,CAC5B,KAAK,GAAG,wBAAwB,GAAG,oBAAoB;aAEvD,OAAO,EAAE,SAAS,UAAU,WAAW,CAAC,GAAG,EAAE,OAAO;QACxD,CAAC,QAAQ,GAAG,EAAE,CAAC;YACb,OAAO,GAAG,IAAI;YACd,SAAS,GAAG,KAAK;QACnB,CAAC;QAED,EAA8D,AAA9D,4DAA8D;QAC9D,EAAiE,AAAjE,+DAAiE;QACjE,EAAyB,AAAzB,uBAAyB;QACzB,EAA2E,AAA3E,yEAA2E;QAC3E,EAAwD,AAAxD,sDAAwD;QACxD,EAAE,EAAE,OAAO,KAAK,GAAG,IAAI,KAAK,KAAK,SAAS,EAAE,CAAC;;QAE7C,CAAC;QAED,KAAK,CAAC,YAAY,GAAG,KAAK,IAAG,MAAQ,KAAG,QAAU;QAElD,EAAE,EACA,GAAG,CAAC,KAAK,gEACT,CAAC;sBACS,YAAY,CAAC,CAAC,EAAE,OAAO;QACnC,CAAC;QAED,EAAsC,AAAtC,oCAAsC;QACtC,EAAE,EACA,GAAG,CAAC,KAAK,+BACT,EAAqE,AAArE,mEAAqE;QACrE,GAAG,CAAC,KAAK,8CACT,CAAC;;QAEH,CAAC;QAED,EAAwF,AAAxF,sFAAwF;QACxF,EAAE,EACA,GAAG,CAAC,KAAK,gCACT,GAAG,CAAC,KAAK,iCACT,CAAC;;QAEH,CAAC;QAED,EAAkE,AAAlE,gEAAkE;QAClE,EAAuB,AAAvB,qBAAuB;QACvB,EAAE,+BAA+B,IAAI,CAAC,GAAG,GAAG,CAAC;sBACjC,YAAY,CAAC,CAAC,EAAE,OAAO;QACnC,CAAC;IAED,EAAqC,AAArC,mCAAqC;IACvC,CAAC;IAED,KAAK,CAAC,oBAAoB,IAAG,MAAQ;IAErC,GAAG,CAAC,aAAa;QACf,WAAW,EAAE,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC,wBAAwB,KAAK,SAAS;QACtE,SAAS,GAAG,QAAQ,GAEhB,EAA8D,AAA9D,4DAA8D;QAC9D,EAA+C,AAA/C,6CAA+C;;aAC9C,IAAM;aACN,YAAY;YAx3Be,QAAoC,iBA43BxD,OAAO,GACP,OAAO,GACP,cAAc,GACd,UAAU;uBAiBV,eAAe,CAAC,OAAO,EAAE,OAAO,EAAE,cAAc,GAAG,OAAO,GAAK,CAAC;oBAC9D,KAAK,CAAC,eAAe,GAAG,UAAU,CAAC,OAAO;4BAClC,cAAsB,EAAE,gBAAwB;+BACtD,GAAG,CAAC,OAAO,EAAE,OAAO,EAAE,MAAM,GAAK,CAAC;4BAChC,eAAe,CACb,cAAc,EACd,gBAAgB,GACf,GAAG,EAAE,MAAM,EAAE,WAAW,GAAK,CAAC;oCAIzB,KAAgC;gCAHpC,EAAE,EAAE,GAAG,SAAS,MAAM,CAAC,GAAG;gCAC1B,EAAE,GAAG,MAAM,SAAS,OAAO;oCAAE,IAAI;oCAAE,KAAK;;gCACxC,KAAK,CAAC,KAAK,YAAY,IAAI,CAAC,MAAM,KAC9B,WAAW,aAAX,WAAW,UAAX,CAAgC,QAAhC,CAAgC,IAAhC,KAAgC,GAAhC,WAAW,CAAE,mBAAmB,cAAhC,KAAgC,UAAhC,CAAgC,QAAhC,CAAgC,GAAhC,KAAgC,CAAE,IAAI,OACtC,MAAQ,cACE,IAAI,CAAC,MAAM;gCACzB,OAAO;oCAAE,MAAM;oCAAE,KAAK;;4BACxB,CAAC;wBAEL,CAAC;;gBACL,CAAC;iBAED,OAAe,EACf,OAAe,EACf,QAA4D,GAE5D,eAAe,CACb,OAAO,EACP,OAAO,GACP,QAAU,QACH,cAAsB,EAAE,gBAAwB,GACrD,GAAG,CAAC,OAAO,EAAE,OAAO,GAClB,OAAO;gCACL,OAAO,CAAC,OAAO,CAAC,gBAAgB;oCAC9B,KAAK;wCAAG,cAAc;;;gCAExB,KAAK;;;kBAGX,IAAI,EAAE,MAAM,GAAK,QAAQ,CAAC,SAAS,EAAE,MAAM;kBAAG,QAAQ;;;YAG9D,EAAiG,AAAjG,+FAAiG;YACjG,EAA+D,AAA/D,6DAA+D;aAC/D,gDAAkD;YAElD,EAA6D,AAA7D,2DAA6D;YAC7D,EAAmC,AAAnC,iCAAmC;eAC/B,MAAM,CAAC,YAAY,CAAC,WAAW;iBAAS,QAAU;;;QAE5D,YAAY;YACV,EAA2D,AAA3D,yDAA2D;eAj8B7B,QAAoC;gBAk8B/C,YAAY,GAAG,GAAG;;gBAAO,cAAc,EAAE,GAAG;;YAC/D,cAAc,EAAE,KAAK;YACrB,OAAO,EAAE,KAAK;YACd,WAAW,EAAE,QAAQ,GAr8BS,QAAoC,gBAs8B/C,GAAG;gBAEd,QAAQ,GAAE,SAAW;gBACrB,EAA6B,AAA7B,2BAA6B;gBAC7B,MAAM,GAAE,GAAK;gBACb,EAAoD,AAApD,kDAAoD;gBACpD,EAAoD,AAApD,kDAAoD;gBACpD,OAAO,EAAE,IAAI;gBAEf,KAAK,GACP,iBAAiB;YACrB,YAAY,EAAE,QAAQ,GAj9BQ,QAAoC,gBAk9B/C,gBAAgB;gBAC3B,IAAI,GAAE,eAAiB;gBACzB,SAAS;gBACT,IAAI,EAh8BT,WAAyB;;YAi8B1B,QAAQ,IAAI,GAAG,IAAI,QAAQ;YAC3B,SAAS;gBACP,EAAoB,AAApB,kBAAoB;iBACnB,QAA0B,GAAK,CAAC;oBAC/B,EAA4B,AAA5B,0BAA4B;oBAC5B,KAAK,GACH,YAAY,QACV,OAAO,EAAC,oDAAsD;oBAClE,GAAG,CAAC,YAAY;wBACd,QAAQ,EA99B6C,KAAM,SA89B5C,IAAI,CAAC,OAAO,GAAE,KAAO,IAAE,aAAe;wBACrD,QAAQ,EAAE,MAAM,CAAC,YAAY,CAAC,IAAI;wBAClC,SAAS,EAAE,MAAM,CAAC,YAAY,CAAC,SAAS;wBACxC,aAAa;uBACZ,KAAK,CAAC,QAAQ;gBACnB,CAAC;gBACD,EAAa,AAAb,WAAa;iBACZ,QAA0B,GAAK,CAAC;oBAC/B,KAAK,GACH,kBAAkB,QAChB,OAAO,EAAC,sCAAwC;oBACpD,GAAG,CAAC,kBAAkB;wBACpB,cAAc;4BACZ,GAAG;gCACD,EAA+D,AAA/D,6DAA+D;gCAC/D,EAA+C,AAA/C,6CAA+C;gCAC/C,MAAM,EAAE,KAAK;gCACb,EAA6D,AAA7D,2DAA6D;gCAC7D,EAA4D,AAA5D,0DAA4D;gCAC5D,UAAU,EAAE,KAAK;;;uBAGpB,KAAK,CAAC,QAAQ;gBACnB,CAAC;;;QAGL,OAAO,EAAE,GAAG;QACZ,IAAI;YACF,YAAY,EAAE,KAAK;;QAErB,EAA8C,AAA9C,4CAA8C;QAC9C,EAA2C,AAA3C,yCAA2C;QAC3C,KAAK,YAAc,CAAC;;mBAEZ,aAAa,GAAG,aAAa;;mBAC9B,WAAW;;QAElB,CAAC;QACD,YAAY;YACV,gBAAgB,EAAE,CAAC;YACnB,OAAO;iBACL,UAAY;iBACZ,kBAAoB;iBACpB,WAAa;gBACb,EAAoF,AAApF,kFAAoF;gBACpF,oBAAoB;;;QAGxB,MAAM;YACJ,EAAsE,AAAtE,oEAAsE;YACtE,EAAkC,AAAlC,gCAAkC;YAClC,UAAU,KAAK,MAAM,CAAC,WAAW,OAAO,OAAO;YAC/C,IAAI,EACF,QAAQ,IAphCoB,QAAoC,gBAohCrC,GAAG,GAnhC2B,KAAM,SAohCtD,IAAI,CAAC,UAAU,GAAE,MAAQ,KAC9B,UAAU;YAChB,EAAoC,AAApC,kCAAoC;YACpC,QAAQ,EAAE,QAAQ,GAxhCY,QAAoC,gBAyhC/C,GAAG,IAChB,YAAc,KACd,SAAW,KACZ,cAAc,EAAE,aAAa,IAAG,SAAW,OAAM,MAAM,EACtD,GAAG,QA7hCqB,QAAoC,eA6hCpC,cAAgB,KAAG,YAAc,EAC1D,GAAG;YACR,OAAO,EAAE,QAAQ,GAAG,SAAS,IAAG,IAAM;YACtC,aAAa,EAAE,QAAQ,IAAG,SAAW,KAAG,MAAQ;YAChD,sBAAsB,EAjiCQ,QAAoC,eAkiC9D,4CAA8C,KAC9C,wCAA0C;YAC9C,qBAAqB,EApiCS,QAAoC,eAqiC9D,mDAAqD,KACrD,qCAAuC;YAC3C,EAAuD,AAAvD,qDAAuD;YACvD,aAAa,EAAE,QAAQ,IACnB,SAAW,KACV,cAAc,EAAE,aAAa,IAAG,SAAW,SAC1C,GAAG,IAAG,MAAQ,KAAG,oBAAsB,EACxC,GAAG;YACR,6BAA6B,EAAE,IAAI;YACnC,kBAAkB,EAAE,WAAW;YAC/B,gBAAgB,GAAG,GAAG;YACtB,yBAAyB,GAAE,6BAA+B;;QAE5D,WAAW,EAAE,KAAK;QAClB,OAAO,EAAE,aAAa;QACtB,aAAa;YACX,EAA+B,AAA/B,6BAA+B;YAC/B,KAAK;iBACH,gBAAkB;iBAClB,YAAc;iBACd,iBAAmB;iBACnB,eAAiB;iBACjB,wBAA0B;iBAC1B,iBAAmB;iBACnB,sBAAwB;iBACxB,WAAa;iBACb,iBAAmB;cACnB,MAAM,EAAE,KAAK,EAAE,MAAM,GAAK,CAAC;gBAC3B,EAA4D,AAA5D,0DAA4D;gBAC5D,KAAK,CAAC,MAAM,IAjkC6C,KAAM,SAikC1C,IAAI,CAAC,SAAS,GAAE,OAAS,IAAE,OAAS,GAAE,MAAM;uBAE1D,KAAK;YACd,CAAC;;YACD,OAAO;iBACL,YAAc;mBACX,YAAY;;YAEjB,OAAO,EA1kCuB,QAAoC;gBA0kCtC,OAAO,EAAC,kBAAoB;;;QAE1D,MAAM;YACJ,KAAK;mBA7kCyB,QAAoC;oBAglC1D,EAAkD,AAAlD,gDAAkD;oBAClD,EAAyD,AAAzD,uDAAyD;;wBAEvD,IAAI;wBACJ,OAAO;4BACL,cAAc,EAAE,KAAK;;;;;oBAM7B,IAAI;uBACA,MAAM,CAAC,YAAY,CAAC,WAAW;;wBAG7B,OAAO;4BAAG,GAAG;+BAAK,mBAAmB;;;oBAC3C,OAAO,GAAG,WAAmB,GAAK,CAAC;wBACjC,EAAE,EAAE,mBAAmB,CAAC,IAAI,EAAE,CAAC,GAAK,CAAC,CAAC,IAAI,CAAC,WAAW;2BAAI,CAAC;mCAClD,KAAK;wBACd,CAAC;8CACqB,IAAI,CAAC,WAAW;oBACxC,CAAC;oBACD,GAAG,EAAE,eAAe;wBAEd,OAAO,CAAC,OAAO,EAAC,gCAAkC;wBAClD,cAAc,CAAC,KAAK;wBAEtB,cAAc,CAAC,KAAK;;oBAErB,MAAM,CAAC,MAAM,CAAC,mBAAmB,IA7mCV,QAAoC;;wBAgnCxD,IAAI;wBACJ,MAAM,GAAE,iBAAmB;wBAC3B,MAAM;4BAAI,GAAG,EAnkCA,IAA6B;;wBAokC1C,UAAU;4BAAI,GAAG;iCAAG,GAAK;;;wBACzB,OAAO;4BACL,QAAQ;4BACR,KAAK,EAAE,GAAG;4BACV,WAAW,EAAE,MAAM,CAAC,WAAW;;;;cAKzC,MAAM,CAAC,OAAO;;QAElB,OAAO;YACL,eAAe,IAAI,GAAG,CAroCU,0BAAqD,SAMvD,QAAoC;YAgoClE,EAAuG,AAAvG,qGAAuG;YAhoCzE,QAAoC,gBAkoC/D,QAAQ,IACT,GAAG,CAnoCyB,QAAoC,SAmoCpD,aAAa;gBACvB,MAAM;oBAAG,OAAO,CAAC,OAAO,EAAC,MAAQ;qBAAG,MAAQ;;gBAC5C,OAAO;oBAAG,OAAO,CAAC,OAAO,EAAC,OAAS;;;YAEvC,EAAoE,AAApE,kEAAoE;aAvoCtC,QAAoC,eAwoCnD,GAAG,CA1mCK,iBAAsC;YA2mC7D,GAAG,CAzoC2B,QAAoC,SAyoCtD,YAAY;mBACnB,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,MAAM,EAC/B,IAA+B,EAAE,GAAW,GAAK,CAAC;oBACjD,EAAE,EAAE,GAAG,CAAC,UAAU,EAAC,YAAc,IAAG,CAAC;wBACnC,IAAI,EAAE,YAAY,EAAE,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG;oBAC7D,CAAC;2BACM,IAAI;gBACb,CAAC;;mBAGA,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,EAAE,MAAM,EAAE,GAAG,EAAE,GAAG,GAAK,CAAC;oBAC/C,EAAE,6BAA6B,IAAI,CAAC,GAAG,GAAG,CAAC;wBACzC,KAAK,CAAC,GAAG,CAAC,KAAK,EACZ,SAAS,EAAE,GAAG,CAAC,oGAAoG;oBAExH,CAAC;;2BAGI,GAAG;0BACJ,YAAY,EAAE,GAAG,KAAK,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,GAAG,CAAC,GAAG;;gBAEzD,CAAC;;gBACD,EAA6D,AAA7D,2DAA6D;iBAC7D,oBAAsB,GAAE,IAAI,CAAC,SAAS,CACpC,GAAG,IAAG,WAAa,KAAG,UAAY;iBAEpC,+BAAiC,GAAE,IAAI,CAAC,SAAS,CAAC,WAAW;iBAC7D,eAAiB,GAAE,IAAI,CAAC,SAAS,EAAE,QAAQ;iBAC3C,4BAA8B,GAAE,IAAI,CAAC,SAAS,CAC5C,OAAO,CAAC,GAAG,CAAC,gBAAgB;gBAE9B,EAA2F,AAA3F,yFAA2F;mBACvF,GAAG,KAAK,QAAQ;qBAEd,2BAA6B,GAAE,IAAI,CAAC,SAAS,CAAC,OAAO;;;iBAG3D,iCAAmC,GAAE,IAAI,CAAC,SAAS,CACjD,MAAM,CAAC,aAAa;iBAEtB,kCAAoC,GAAE,IAAI,CAAC,SAAS,CAClD,MAAM,CAAC,aAAa,CAAC,aAAa;iBAEpC,0BAA4B,GAAE,IAAI,CAAC,SAAS,CAC1C,MAAM,CAAC,YAAY,CAAC,OAAO;iBAE7B,8BAAgC,GAAE,IAAI,CAAC,SAAS,CAC9C,MAAM,CAAC,eAAe;iBAExB,6BAA+B,GAAE,IAAI,CAAC,SAAS,CAAC,YAAY;iBAC5D,sCAAwC,GAAE,IAAI,CAAC,SAAS,CACtD,MAAM,CAAC,YAAY,CAAC,kBAAkB,IAAI,YAAY;iBAExD,iCAAmC,GAAE,IAAI,CAAC,SAAS,CACjD,MAAM,CAAC,aAAa,KAAK,GAAG;iBAE9B,kCAAoC,GAAE,IAAI,CAAC,SAAS,CAClD,MAAM,CAAC,YAAY,CAAC,cAAc;iBAEpC,+BAAiC,GAAE,IAAI,CAAC,SAAS,CAC/C,MAAM,CAAC,YAAY,CAAC,WAAW,KAAK,GAAG;iBAEzC,qCAAuC,GAAE,IAAI,CAAC,SAAS,CACrD,MAAM,CAAC,YAAY,CAAC,iBAAiB;iBAEvC,6BAA+B,GAAE,IAAI,CAAC,SAAS;oBAC7C,WAAW,EAAE,MAAM,CAAC,MAAM,CAAC,WAAW;oBACtC,UAAU,EAAE,MAAM,CAAC,MAAM,CAAC,UAAU;oBACpC,IAAI,EAAE,MAAM,CAAC,MAAM,CAAC,IAAI;oBACxB,MAAM,EAAE,MAAM,CAAC,MAAM,CAAC,MAAM;uBACxB,GAAG;wBAED,EAAgE,AAAhE,8DAAgE;wBAChE,OAAO,EAAE,MAAM,CAAC,MAAM,CAAC,OAAO;;;;iBAItC,kCAAoC,GAAE,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,QAAQ;iBACpE,+BAAiC,GAAE,IAAI,CAAC,SAAS,CAAC,WAAW;iBAC7D,+BAAiC,GAAE,IAAI,CAAC,SAAS,GAAG,MAAM,CAAC,IAAI;iBAC/D,+BAAiC,GAAE,IAAI,CAAC,SAAS,EAAC,IAAW,GAAX,MAAM,CAAC,IAAI,cAAX,IAAW,UAAX,CAAoB,QAApB,CAAoB,GAApB,IAAW,CAAE,OAAO;iBACtE,+BAAiC,GAAE,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,WAAW;mBAChE,QAAQ;oBAEN,EAA+D,AAA/D,6DAA+D;oBAC/D,EAA2D,AAA3D,yDAA2D;oBAC3D,EAA+C,AAA/C,6CAA+C;qBAC/C,aAAe,GAAE,IAAI,CAAC,SAAS,CAAC,KAAK;oBAEvC,SAAS;gBACb,EAAyD,AAAzD,uDAAyD;gBACzD,EAAqC,AAArC,mCAAqC;mBACjC,MAAM,CAAC,YAAY,CAAC,OAAO,IAAI,GAAG;qBAEhC,WAAa,IAAG,uBACR,EAAE,QAAQ,IAAG,WAAa,KAAG,EAAI,EAAC,sYAQ9C;;;;aAIH,QAAQ,IACP,GAAG,CA/sCyB,oBAAyC;gBAgtCnE,QAAQ,EAjuCX,WAAyB;gBAkuCtB,QAAQ;;aAEX,QAAQ,IAAI,GAAG,CAztCS,yBAAgD;YA0tCzE,MAAM,CAAC,YAAY,CAAC,UAAU,KAC3B,gBAAgB,IACjB,QAAQ,KACP,GAAG,IA7vCwB,QAAoC,eA+vChE,GAAG,CA9tC4B,2BAAiD;gBA8tCnD,MAAM,EAAE,GAAG;;YAC1C,EAA4E,AAA5E,0EAA4E;YAC5E,EAAyE,AAAzE,uEAAyE;YACzE,EAA0E,AAA1E,wEAA0E;YAC1E,EAAkE,AAAlE,gEAAkE;YAClE,MAAM,CAAC,2BAA2B,IAChC,GAAG,CArwCyB,QAAoC,SAqwCpD,YAAY;gBACtB,cAAc;gBACd,aAAa;;eAEb,GAAG,QACI,CAAC;gBACN,EAA0F,AAA1F,wFAA0F;gBAC1F,EAAqG,AAArG,mGAAqG;gBACrG,KAAK,GACH,6BAA6B,QAC3B,OAAO,EAAC,mDAAqD;gBACjE,KAAK,CAAC,UAAU;oBAAI,GAAG,CAAC,6BAA6B;;gBAErD,EAAE,GAAG,QAAQ,EAAE,CAAC;oBACd,UAAU,CAAC,IAAI,CAAC,GAAG,CAnxCG,QAAoC,SAmxC9B,0BAA0B;gBACxD,CAAC;uBAEM,UAAU;YACnB,CAAC;YAEL,EAA0D,AAA1D,wDAA0D;aAzxC5B,QAAoC,gBA0xClD,GAAG,IAAI,GAAG,CA1xCI,QAAoC,SA0xC/B,qBAAqB;aACvD,GAAG,IACF,GAAG,CA5xCyB,QAAoC,SA4xCpD,YAAY;gBACtB,cAAc;gBACd,aAAa;;YAEjB,YAAY,IAAI,QAAQ,IAAI,GAAG,CAzvCJ,iBAAqC;YA0vChE,QAAQ,IACN,GAAG,CA9vCqB,oBAAyC;gBA8vCvC,UAAU,EAAE,gBAAgB;gBAAE,GAAG;;aAlyC/B,QAAoC,eAoyChE,MAAM,MAAK,MAAQ,KACnB,QAAQ,IACR,GAAG,CAnwC4B,qBAA2C;gBAmwCzC,UAAU;;YAC7C,QAAQ,IAAI,GAAG,CArwCa,gBAAqC;aAswChE,QAAQ,IACP,GAAG,CA7wCqB,oBAAyC;gBA8wC/D,OAAO;gBACP,QAAQ;gBACR,aAAa;;aAEhB,GAAG,KACD,QAAQ,IACT,MAAM,CAAC,YAAY,CAAC,KAAK,IACzB,GAAG,CApxCkB,iBAAsC;gBAqxCzD,OAAO;;YAEX,GAAG,CA/wCuB,gBAAoC;gBA+wCxC,cAAc;;YACpC,MAAM,CAAC,aAAa,KACjB,GAAG,IACJ,QAAQ,eACK,CAAC;gBACZ,KAAK,GAAG,6BAA6B,MACnC,OAAO,EAAC,kDAAoD;uBAGvD,GAAG,CAAC,6BAA6B;oBACtC,gBAAgB;;YAEpB,CAAC;YACH,MAAM,CAAC,YAAY,CAAC,WAAW,KAj0CD,QAAoC,gBAm0C/D,GAAG,IACJ,GAAG,CAvxCJ,yBAA8C;gBAwxC3C,KAAK;qBACF,QAAQ,IACP,iBAAiB,CAAC,4BAA4B,CAAC,OAAO,IACtD,GAAG,CA3xCV,yBAA8C;oBA4xCzC,iBAAiB,CAAC,gCAAgC,CAAC,OAAO,IACxD,GAAG,CA7xCV,yBAA8C;wBA8xCrC,cAAc,EACZ,iBAAiB,CAAC,gCAAgC,CAC/C,cAAc;;qBAEtB,QAAQ,IACP,iBAAiB,CAAC,kCAAkC,CAAC,OAAO,IAC5D,GAAG,CApyCV,yBAA8C;wBAqyCrC,wBAAwB,EACtB,iBAAiB,CAAC,kCAAkC,CACjD,wBAAwB;;qBAEhC,QAAQ,IACP,iBAAiB,CAAC,8BAA8B,CAAC,OAAO,IACxD,GAAG,CA3yCV,yBAA8C,gCA4yCrC,kBAAkB,CAAC,YAAY;kBAEnC,MAAM,CAAC,OAAO;;YAEpB,GAAG,CA/yC6B,sBAA2C;aAgzC1E,QAAQ,IACP,GAAG,CA/yCoB,eAAoC;gBAgzCzD,QAAQ,EAAE,OAAO,CAAC,OAAO,EAAC,6BAA+B;gBACzD,QAAQ,EAAE,OAAO,CAAC,GAAG,CAAC,cAAc;gBACpC,IAAI,GAAG,uBAAuB,EAAE,GAAG,SAAQ,OAAS,EAAC,GAAG;gBACxD,QAAQ,EAAE,KAAK;gBACf,IAAI;qBA/0CP,WAAyB,gDAg1C4B,CAAC;oBACjD,EAAgC,AAAhC,8BAAgC;oBAChC,SAAS,EAAE,IAAI;;;UAGrB,MAAM,CAAC,OAAO;;IAGlB,EAAwC,AAAxC,sCAAwC;IACxC,EAAE,EAAE,eAAe,EAAE,CAAC;YACpB,KAAqB;SAArB,KAAqB,GAArB,aAAa,CAAC,OAAO,cAArB,KAAqB,UAArB,CAA8B,QAA9B,CAA8B,YAA9B,KAAqB,CAAE,OAAO,6BAA9B,CAA8B,QAA9B,CAA8B,SAAE,IAAI,CAAC,eAAe;IACtD,CAAC;IAED,EAAE,GAAE,QAAQ,aAAR,QAAQ,UAAR,CAAyB,QAAzB,CAAyB,IAAzB,IAAyB,GAAzB,QAAQ,CAAE,eAAe,cAAzB,IAAyB,UAAzB,CAAyB,QAAzB,CAAyB,GAAzB,IAAyB,CAAE,KAAK,KAAI,eAAe,EAAE,CAAC;YACxD,KAAqB;SAArB,KAAqB,GAArB,aAAa,CAAC,OAAO,cAArB,KAAqB,UAArB,CAA8B,QAA9B,CAA8B,YAA9B,KAAqB,CAAE,OAAO,6BAA9B,CAA8B,QAA9B,CAA8B,SAAE,OAAO,CACrC,GAAG,CAr1C2B,oBAAyC,qBAq1C/C,QAAQ,CAAC,eAAe,CAAC,KAAK,EAAE,eAAe;IAE3E,CAAC;IAED,EAAE,EAx3CgC,QAAoC,aAw3CtD,CAAC;YAER,KAAoB;QAD3B,EAAiD,AAAjD,+CAAiD;SAC1C,KAAoB,GAApB,aAAa,CAAC,MAAM,cAApB,KAAoB,UAApB,CAAsC,QAAtC,CAAsC,UAAtC,KAAoB,CAAE,gBAAgB;QAE7C,EAAE,EAAE,QAAQ,IAAI,GAAG,EAAE,CAAC;YACpB,EAAiF,AAAjF,+EAAiF;YACjF,EAAiC,AAAjC,+BAAiC;YACjC,aAAa,CAAC,YAAY;iBAAI,MAAQ;;QACxC,CAAC;QACD,EAAiD,AAAjD,+CAAiD;QACjD,EAAE,EAAE,aAAa,CAAC,IAAI,SAAS,aAAa,CAAC,IAAI,CAAC,YAAY;QAE9D,EAA4E,AAA5E,0EAA4E;QAC5E,EAAqC,AAArC,mCAAqC;QACrC,EAAuB,AAAvB,qBAAuB;QACvB,aAAa,CAAC,QAAQ;;QACtB,EAAE,EAAE,OAAO,CAAC,QAAQ,CAAC,GAAG,MAAK,CAAG,GAAE,CAAC;YACjC,KAAK,CAAC,KAAK,6EACiE,IAAI,CAC5E,OAAO,CAAC,OAAO,EAAC,WAAa;YAEjC,EAAE,EAAE,KAAK,EAAE,CAAC;gBACV,EAAuB,AAAvB,qBAAuB;gBACvB,aAAa,CAAC,QAAQ,CAAC,YAAY;oBA94CsB,KAAM,SA+4CxD,OAAO,CAAC,KAAK,CAAC,CAAC,IAAG,SAAW;;YAEtC,CAAC;QACH,CAAC,MAAM,CAAC;YACN,KAAK,CAAC,KAAK,kCAAkC,IAAI,CAC/C,OAAO,CAAC,OAAO,EAAC,WAAa;YAE/B,EAAE,EAAE,KAAK,EAAE,CAAC;gBACV,EAAuB,AAAvB,qBAAuB;gBACvB,aAAa,CAAC,QAAQ,CAAC,YAAY;oBAAI,KAAK,CAAC,CAAC;;YAChD,CAAC;QACH,CAAC;QACD,EAAE,EAAE,OAAO,CAAC,QAAQ,CAAC,GAAG,MAAK,CAAG,GAAE,CAAC;YACjC,KAAK,CAAC,KAAK,iFACqE,IAAI,CAChF,OAAO,CAAC,OAAO,EAAC,WAAa;YAEjC,EAAE,EAAE,KAAK,EAAE,CAAC;gBACV,EAAuB,AAAvB,qBAAuB;gBACvB,aAAa,CAAC,QAAQ,CAAC,cAAc;oBAAI,KAAK,CAAC,CAAC;;YAClD,CAAC;QACH,CAAC,MAAM,EAAE,EAAE,OAAO,CAAC,QAAQ,CAAC,GAAG,MAAK,CAAG,GAAE,CAAC;YACxC,KAAK,CAAC,KAAK,mEACuD,IAAI,CAClE,OAAO,CAAC,OAAO,EAAC,WAAa;YAEjC,EAAE,EAAE,KAAK,EAAE,CAAC;gBACV,EAAuB,AAAvB,qBAAuB;gBACvB,aAAa,CAAC,QAAQ,CAAC,cAAc;oBAAI,KAAK,CAAC,CAAC;;YAClD,CAAC;QACH,CAAC;QAED,EAAE,EAAE,GAAG,EAAE,CAAC;YACR,EAAE,GAAG,aAAa,CAAC,YAAY,EAAE,CAAC;gBAChC,aAAa,CAAC,YAAY;;YAC5B,CAAC;YACD,aAAa,CAAC,YAAY,CAAC,eAAe,GAAG,KAAK;YAClD,aAAa,CAAC,YAAY,CAAC,WAAW,GAAG,KAAK;QAChD,CAAC;QAED,KAAK,CAAC,UAAU,GAAG,IAAI,CAAC,SAAS;YAC/B,WAAW,EAAE,MAAM,CAAC,WAAW;YAC/B,cAAc,EAAE,MAAM,CAAC,cAAc;YACrC,aAAa,EAAE,MAAM,CAAC,aAAa;YACnC,aAAa,EAAE,MAAM,CAAC,aAAa,CAAC,aAAa;YACjD,2BAA2B,IAAI,MAAM,CAAC,2BAA2B;YACjE,OAAO,EAAE,MAAM,CAAC,YAAY,CAAC,OAAO;YACpC,eAAe,EAAE,MAAM,CAAC,eAAe;YACvC,SAAS,EAAE,MAAM,CAAC,YAAY,CAAC,SAAS;YACxC,aAAa,EAAE,MAAM,CAAC,aAAa;YACnC,cAAc,EAAE,MAAM,CAAC,YAAY,CAAC,cAAc;YAClD,WAAW,EAAE,MAAM,CAAC,YAAY,CAAC,WAAW;YAC5C,iBAAiB,EAAE,MAAM,CAAC,YAAY,CAAC,iBAAiB;YACxD,QAAQ,EAAE,MAAM,CAAC,QAAQ;YACzB,OAAO,EAAE,MAAM,CAAC,YAAY,CAAC,OAAO;YACpC,2BAA2B,EAAE,MAAM,CAAC,2BAA2B;YAC/D,WAAW,EAAE,MAAM,CAAC,WAAW;YAC/B,uBAAuB,EAAE,MAAM,CAAC,YAAY,CAAC,uBAAuB;YACpE,MAAM;YACN,wBAAwB;YACxB,OAAO,IAAI,MAAM,CAAC,OAAO;YACzB,WAAW;YACX,SAAS,EAAE,MAAM,CAAC,YAAY,CAAC,SAAS;YACxC,kBAAkB,EAAE,MAAM,CAAC,YAAY,CAAC,kBAAkB;;QAG5D,KAAK,CAAC,KAAK;YACT,IAAI,GAAE,UAAY;YAClB,EAAY,AAAZ,UAAY;YACZ,EAAqB,AAArB,mBAAqB;YACrB,EAAiD,AAAjD,+CAAiD;YACjD,OAAO,KAAK,OAAO,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC,EAAE,UAAU;YACpD,cAAc,EAv9C6C,KAAM,SAu9C5C,IAAI,CAAC,OAAO,GAAE,KAAO,IAAE,OAAS;;QAGvD,EAAoF,AAApF,kFAAoF;QACpF,EAAE,EAAE,MAAM,CAAC,OAAO,IAAI,MAAM,CAAC,UAAU,EAAE,CAAC;YACxC,KAAK,CAAC,iBAAiB;gBACrB,MAAM;oBAAG,MAAM,CAAC,UAAU;;;QAE9B,CAAC;QAED,aAAa,CAAC,KAAK,GAAG,KAAK;QAE3B,EAAE,EAAE,OAAO,CAAC,GAAG,CAAC,oBAAoB,EAAE,CAAC;YACrC,KAAK,CAAC,QAAQ,GACZ,OAAO,CAAC,GAAG,CAAC,oBAAoB,CAAC,QAAQ,EAAC,cAAgB;YAC5D,KAAK,CAAC,gBAAgB,GACpB,OAAO,CAAC,GAAG,CAAC,oBAAoB,CAAC,QAAQ,EAAC,cAAgB;YAC5D,KAAK,CAAC,gBAAgB,GACpB,OAAO,CAAC,GAAG,CAAC,oBAAoB,CAAC,QAAQ,EAAC,cAAgB;YAC5D,KAAK,CAAC,UAAU,IAAI,QAAQ,KAAK,gBAAgB,KAAK,gBAAgB;YAEtE,EAAE,EAAE,UAAU,IAAI,QAAQ,EAAE,CAAC;gBAC3B,EAA0D,AAA1D,wDAA0D;gBAC1D,aAAa,CAAC,qBAAqB;oBACjC,KAAK,GAAE,OAAS;oBAChB,KAAK;;YAET,CAAC;YAED,EAAE,EACA,UAAU,IACT,gBAAgB,KAAK,QAAQ,IAC7B,gBAAgB,IAAI,QAAQ,EAC7B,CAAC;gBACD,aAAa,CAAC,OAAO,CAAE,IAAI,EAAE,QAA0B,GAAK,CAAC;oBAC3D,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,EAAC,oBAAsB,IAAG,KAAK,GAAK,CAAC;wBAC1D,OAAO,CAAC,GAAG,CACT,KAAK,CAAC,QAAQ;4BACZ,MAAM,EAAE,IAAI;4BACZ,EAA0D,AAA1D,wDAA0D;4BAC1D,OAAO,EAAE,UAAU,IAAG,GAAK,KAAG,OAAS;;oBAG7C,CAAC;gBACH,CAAC;YACH,CAAC;YAED,EAAE,EAAG,gBAAgB,KAAK,QAAQ,IAAM,gBAAgB,IAAI,QAAQ,EAAG,CAAC;gBACtE,aAAa,CAAC,OAAO,CAAE,IAAI,CACzB,GAAG,CAzgDuB,QAAoC,SAygDlD,cAAc;oBACxB,EAA0D,AAA1D,wDAA0D;oBAC1D,OAAO,EAAE,IAAI;;gBAGjB,aAAa,CAAC,OAAO,GAAG,IAAI;YAC9B,CAAC;QACH,CAAC;IACH,CAAC;IAED,aAAa,aAz/C6B,OAAkB,QAy/CnB,aAAa;QACpD,aAAa,EAAE,GAAG;QAClB,aAAa,EAAE,GAAG,CAAC,MAAM,CAphDoC,KAAM,SAqhD5D,IAAI,CAAC,QAAQ,GAAG,IAAI,GAAG,OAAO,SAAQ,QAAU;QAEvD,aAAa,EAAE,GAAG;QAClB,QAAQ;QACR,WAAW,EAAE,MAAM,CAAC,WAAW;QAC/B,WAAW,EAAE,MAAM,CAAC,WAAW;QAC/B,2BAA2B,EAAE,MAAM,CAAC,2BAA2B;QAC/D,MAAM,EAAE,MAAM,CAAC,MAAM;QACrB,WAAW,EAAE,MAAM,CAAC,YAAY,CAAC,SAAS;;IAG5C,GAAG,CAAC,eAAe,GAAG,aAAa,CAAC,OAAO;IAC3C,EAAE,SAAS,MAAM,CAAC,OAAO,MAAK,QAAU,GAAE,CAAC;QACzC,aAAa,GAAG,MAAM,CAAC,OAAO,CAAC,aAAa;YAC1C,GAAG;YACH,GAAG;YACH,QAAQ;YACR,OAAO;YACP,MAAM;YACN,cAAc;YACd,UAAU;YACV,OAAO,EA3iDuB,QAAoC;;QA8iDpE,EAAE,GAAG,aAAa,EAAE,CAAC;YACnB,KAAK,CAAC,GAAG,CAAC,KAAK,EACb,8HAAgI,KAC9H,4EAA8E;QAEpF,CAAC;QAED,EAAE,EAAE,GAAG,IAAI,eAAe,KAAK,aAAa,CAAC,OAAO,EAAE,CAAC;YACrD,aAAa,CAAC,OAAO,GAAG,eAAe;YACvC,oBAAoB,CAAC,eAAe;QACtC,CAAC;QAED,EAAE,SAAU,aAAa,CAAS,IAAI,MAAK,QAAU,GAAE,CAAC;YACtD,OAAO,CAAC,IAAI,EACV,0FAA4F;QAEhG,CAAC;IACH,CAAC;IAED,EAAE,GAAG,MAAM,CAAC,MAAM,CAAC,mBAAmB,IAjkDJ,QAAoC,aAikDhB,CAAC;YACvC,KAAoB;QAAlC,KAAK,CAAC,KAAK,KAAG,KAAoB,GAApB,aAAa,CAAC,MAAM,cAApB,KAAoB,UAApB,CAA2B,QAA3B,CAA2B,GAA3B,KAAoB,CAAE,KAAK;QACzC,KAAK,CAAC,YAAY,GAAG,KAAK,CAAC,IAAI,EAC5B,IAAI,GACH,IAAI,CAAC,MAAM,MAAK,iBAAmB,MACnC,IAAM,KAAI,IAAI,IACd,IAAI,CAAC,IAAI,YAAY,MAAM,IAC3B,IAAI,CAAC,IAAI,CAAC,IAAI,EAAC,IAAM;;QAEzB,KAAK,CAAC,aAAa,GAAG,KAAK,CAAC,IAAI,EAC7B,IAAI,GAAK,IAAI,CAAC,MAAM,MAAK,iBAAmB;;QAE/C,EAAE,EAAE,YAAY,IAAI,aAAa,EAAE,CAAC;YAClC,EAAuD,AAAvD,qDAAuD;YACvD,EAAmD,AAAnD,iDAAmD;YACnD,EAA8C,AAA9C,4CAA8C;YAC9C,aAAa,CAAC,IAAI;QACpB,CAAC;IACH,CAAC;IAED,EAAE,EACA,MAAM,CAAC,YAAY,CAAC,SAAS,MAC7B,IAAoB,GAApB,aAAa,CAAC,MAAM,cAApB,IAAoB,UAApB,CAA2B,QAA3B,CAA2B,GAA3B,IAAoB,CAAE,KAAK,KAC3B,aAAa,CAAC,OAAO,EACrB,CAAC;QACD,EAA8C,AAA9C,4CAA8C;QAC9C,EAAkJ,AAAlJ,gJAAkJ;QAClJ,aAAa,CAAC,OAAO,CAAC,IAAI,CACxB,GAAG,CA7lD2B,QAAoC,SA6lDtD,YAAY;QAG1B,EAAkE,AAAlE,gEAAkE;QAClE,EAAiE,AAAjE,+DAAiE;QACjE,EAAkJ,AAAlJ,gJAAkJ;QAClJ,KAAK,CAAC,iBAAiB;;;QACvB,KAAK,CAAC,UAAU,GApmDgB,QAAoC;YAsmD9D,OAAO,EAAE,iBAAiB;YAC1B,MAAM,EAAE,iBAAiB;YACzB,IAAI,GAAE,cAAgB;YACtB,SAAS;gBACP,UAAU,GAAE,OAAS;gBACrB,QAAQ,GAAE,kCAAoC;;;YAIhD,MAAM,EAAE,OAAO,CAAC,OAAO,EAAC,8BAAgC;YACxD,EAAgE,AAAhE,8DAAgE;YAChE,EAAuE,AAAvE,qEAAuE;YACvE,EAAkE,AAAlE,gEAAkE;YAClE,EAAgC,AAAhC,8BAAgC;YAChC,OAAO,EAAE,iBAAiB;YAC1B,MAAM,EAAE,iBAAiB;YACzB,OAAO;gBACL,UAAU,GAAE,mBAAqB;gBACjC,UAAU,GAAE,YAAc;gBAC1B,IAAI,GAAE,qBAAuB;;;QAIrC,KAAK,CAAC,QAAQ;QACd,KAAK,CAAC,UAAU;aAEX,KAAK,CAAC,IAAI,IAAI,aAAa,CAAC,MAAM,CAAC,KAAK,CAAE,CAAC;YAC9C,EAAE,EAAE,IAAI,CAAC,OAAO,EAAE,CAAC;gBACjB,QAAQ,CAAC,IAAI,CAAC,IAAI;YACpB,CAAC,MAAM,CAAC;gBACN,EAAE,EACA,IAAI,CAAC,KAAK,MACR,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,MAAM,GAC3D,CAAC;oBACD,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,CAAC,GAAK,UAAU,CAAC,IAAI,CAAC,CAAC;;gBAC7C,CAAC,MAAM,CAAC;oBACN,UAAU,CAAC,IAAI,CAAC,IAAI;gBACtB,CAAC;YACH,CAAC;QACH,CAAC;QAED,aAAa,CAAC,MAAM,CAAC,KAAK;eACpB,QAAQ;;gBAEV,KAAK;uBAAM,UAAU;oBAAE,UAAU;;;;IAGvC,CAAC;IAED,EAA8D,AAA9D,4DAA8D;IAC9D,EAAE,SAAS,MAAM,CAAC,oBAAoB,MAAK,QAAU,GAAE,CAAC;QACtD,KAAK,CAAC,OAAO,GAAG,MAAM,CAAC,oBAAoB;YACzC,YAAY,EAAE,aAAa,CAAC,YAAY;;QAE1C,EAAE,EAAE,OAAO,CAAC,YAAY,EAAE,CAAC;YACzB,aAAa,CAAC,YAAY,GAAG,OAAO,CAAC,YAAY;QACnD,CAAC;IACH,CAAC;aAEQ,WAAW,CAAC,IAA0C,EAAW,CAAC;QACzE,EAAE,GAAG,IAAI,EAAE,CAAC;mBACH,KAAK;QACd,CAAC;QAED,KAAK,CAAC,SAAS;aACb,aAAe;aACf,cAAgB;aAChB,cAAgB;aAChB,cAAgB;aAChB,cAAgB;;QAGlB,EAAE,EAAE,IAAI,YAAY,MAAM,IAAI,SAAS,CAAC,IAAI,EAAE,KAAK,GAAK,IAAI,CAAC,IAAI,CAAC,KAAK;WAAI,CAAC;mBACnE,IAAI;QACb,CAAC;QAED,EAAE,SAAS,IAAI,MAAK,QAAU,GAAE,CAAC;YAC/B,EAAE,EACA,SAAS,CAAC,IAAI,EAAE,KAAK,GAAK,CAAC;oBACrB,CAAC;oBACH,EAAE,EAAE,IAAI,CAAC,KAAK,GAAG,CAAC;+BACT,IAAI;oBACb,CAAC;gBACH,CAAC,QAAQ,CAAC,EAAE,CAAC;gBAAA,CAAC;uBACP,KAAK;YACd,CAAC,GACD,CAAC;uBACM,IAAI;YACb,CAAC;QACH,CAAC;QAED,EAAE,EAAE,KAAK,CAAC,OAAO,CAAC,IAAI,KAAK,IAAI,CAAC,IAAI,CAAC,WAAW,GAAG,CAAC;mBAC3C,IAAI;QACb,CAAC;eAEM,KAAK;IACd,CAAC;QAGC,KAEC;IAHH,KAAK,CAAC,gBAAgB,IACpB,KAEC,IAFD,IAAoB,GAApB,aAAa,CAAC,MAAM,cAApB,IAAoB,UAApB,CAA2B,QAA3B,CAA2B,GAA3B,IAAoB,CAAE,KAAK,CAAC,IAAI,EAC7B,IAAI,GAAK,WAAW,CAAC,IAAI,CAAC,IAAI,KAAK,WAAW,CAAC,IAAI,CAAC,OAAO;mBAD9D,KAEC,cAFD,KAEC,GAAI,KAAK;IAEZ,EAAE,EAAE,gBAAgB,EAAE,CAAC;YAYjB,KAAoB,EAUpB,KAAqB,EAMrB,KAA0B;QA3B9B,EAAkC,AAAlC,gCAAkC;QAClC,EAAE,EAAE,QAAQ,EAAE,CAAC;YACb,OAAO,CAAC,IAAI,CArtDA,MAAO,SAstDX,MAAM,CAAC,IAAI,EAAC,SAAW,KAttDnB,MAAO,SAutDT,IAAI,EACR,wFAA0F,MAE5F,gFAAkF;QAExF,CAAC;QAED,EAAE,GAAE,KAAoB,GAApB,aAAa,CAAC,MAAM,cAApB,KAAoB,UAApB,CAA2B,QAA3B,CAA2B,GAA3B,KAAoB,CAAE,KAAK,CAAC,MAAM,EAAE,CAAC;YACvC,EAA4B,AAA5B,0BAA4B;YAC5B,aAAa,CAAC,MAAM,CAAC,KAAK,GAAG,aAAa,CAAC,MAAM,CAAC,KAAK,CAAC,MAAM,EAC3D,CAAC;oBAES,KAAO;kCAAP,KAAO,GAAP,CAAC,CAAC,KAAK,cAAP,KAAO,UAAP,CAAY,QAAZ,CAAY,YAAZ,KAAO,CAAG,CAAC,8BAAX,CAAY,QAAZ,CAAY,SAAE,OAAO,OAAK,MAAQ,KACzC,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,OAAO,CAAC,iBAAiB,KAAK,IAAI;;QAGrD,CAAC;QACD,EAAE,GAAE,KAAqB,GAArB,aAAa,CAAC,OAAO,cAArB,KAAqB,UAArB,CAA6B,QAA7B,CAA6B,GAA7B,KAAqB,CAAE,MAAM,EAAE,CAAC;YAClC,EAAgC,AAAhC,8BAAgC;YAChC,aAAa,CAAC,OAAO,GAAG,aAAa,CAAC,OAAO,CAAC,MAAM,EACjD,CAAC,GAAM,CAAC,CAAS,iBAAiB,KAAK,IAAI;;QAEhD,CAAC;QACD,EAAE,GAAE,KAA0B,GAA1B,aAAa,CAAC,YAAY,cAA1B,KAA0B,UAA1B,CAAqC,QAArC,CAAqC,YAArC,KAA0B,CAAE,SAAS,6BAArC,CAAqC,QAArC,CAAqC,SAAE,MAAM,EAAE,CAAC;YAClD,EAAuB,AAAvB,qBAAuB;YACvB,aAAa,CAAC,YAAY,CAAC,SAAS,GAClC,aAAa,CAAC,YAAY,CAAC,SAAS,CAAC,MAAM,EACxC,CAAC,GAAM,CAAC,CAAS,iBAAiB,KAAK,IAAI;;QAElD,CAAC;IACH,CAAC,MAAM,EAAE,GAAG,MAAM,CAAC,MAAM,CAAC,0BAA0B,EAAE,CAAC;kBArtDd,yBAAsD,6BAstD5D,GAAG,GAAG,GAAG,EAAE,aAAa;IAC3D,CAAC;IAED,EAAyE,AAAzE,uEAAyE;IACzE,EAAE,EAAE,eAAe,EAAE,CAAC;QACpB,kBAAkB,CAAC,aAAa,EAAE,cAAc,CAAC,KAAK;IACxD,CAAC;IAED,EAAwD,AAAxD,sDAAwD;IACxD,EAAE,EACA,QAAQ,IACR,aAAa,CAAC,MAAM,IACpB,KAAK,CAAC,OAAO,CAAC,aAAa,CAAC,MAAM,CAAC,KAAK,GACxC,CAAC;QACD,GAAG,CAAC,WAAW,GAAG,KAAK;QAEvB,aAAa,CAAC,MAAM,CAAC,KAAK,GAAG,aAAa,CAAC,MAAM,CAAC,KAAK,CAAC,MAAM,EAC3D,IAAI,GAAc,CAAC;YAClB,EAAE,IAAI,IAAI,CAAC,IAAI,YAAY,MAAM,UAAU,IAAI;YAC/C,EAAE,GAAE,OAAS,EAAC,KAAK,CAAC,IAAI,CAAC,IAAI,OAAM,OAAS,EAAC,KAAK,CAAC,IAAI,CAAC,IAAI,GAAG,CAAC;gBAC9D,EAA6C,AAA7C,2CAA6C;gBAC7C,WAAW,GAAG,IAAI,CAAC,GAAG,KAAK,cAAc,CAAC,KAAK;wBACvC,WAAW;YACrB,CAAC;mBACM,IAAI;QACb,CAAC;QAGH,EAAE,EAAE,WAAW,EAAE,CAAC;YAChB,OAAO,CAAC,IAAI,EACV,gKAAkK;QAEtK,CAAC;IACH,CAAC;IAED,EAAoF,AAApF,kFAAoF;IACpF,EAAE,EAAE,aAAa,CAAC,MAAM,IAAI,KAAK,CAAC,OAAO,CAAC,aAAa,CAAC,MAAM,CAAC,KAAK,GAAG,CAAC;WAClE,OAAO,CAAC,IAAI,CACd,aAAa,CAAC,MAAM,CAAC,KAAK,WAChB,IAAyB,EAAE,CAAC;YACpC,EAAE,IAAI,IAAI,CAAC,IAAI,YAAY,MAAM,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC;;YAEhE,CAAC;YAED,KAAK,CAAC,MAAM,GACV,IAAI,CAAC,IAAI,CAAC,MAAM,MAAK,QAAU,KAAI,IAAI,CAAC,IAAI,CAAC,MAAM,MAAK,QAAU;YACpE,KAAK,CAAC,MAAM,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,MAAK,QAAU;YAC9C,KAAK,CAAC,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,MAAK,OAAS;YAC5C,KAAK,CAAC,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,MAAK,QAAU;YAEhD,EAAuE,AAAvE,qEAAuE;YACvE,EAAE,IAAI,MAAM,IAAI,MAAM,IAAI,KAAK,IAAI,QAAQ,GAAG,CAAC;;YAE/C,CAAC;eAEG,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,WAAY,GAA2B,EAAE,CAAC;gBACjE,EAAE,IAEE,GAAG,WACI,GAAG,MAAK,MAAQ,KACvB,EAA0D,AAA1D,wDAA0D;iBACzD,GAAG,CAAC,MAAM,MAAK,UAAY,KAC1B,GAAG,CAAC,MAAM,MAAK,iBAAmB,MACpC,GAAG,CAAC,OAAO,WACJ,GAAG,CAAC,OAAO,MAAK,MAAQ,KAC/B,EAA8D,AAA9D,4DAA8D;gBAC9D,EAA+D,AAA/D,6DAA+D;gBAC/D,EAAmE,AAAnE,iEAAmE;gBACnE,EAA2D,AAA3D,yDAA2D;gBAC3D,EAAyC,AAAzC,uCAAyC;iBACxC,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO,GAAE,QAAU,MAC3D,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAClC,GAAG,CAAC,OAAO,GACX,gBAAkB,MAGxB,CAAC;;gBAEH,CAAC;gBAED,EAAsE,AAAtE,oEAAsE;gBACtE,EAA8B,AAA9B,4BAA8B;gBAC9B,EAAmE,AAAnE,iEAAmE;gBACnE,EAAyB,AAAzB,uBAAyB;gBACzB,EAA0B,AAA1B,wBAA0B;oBACtB,CAAC;oBACH,EAAsE,AAAtE,oEAAsE;oBACtE,EAAyB,AAAzB,uBAAyB;oBACzB,KAAK,CAAC,cAAc,GAAG,OAAO,CAAC,OAAO,EAAC,cAAgB;wBACrD,KAAK;4BACH,KAAK,GAED,GAAG,GAEH,OAAO,CAAC,OAAO,CACb,MAAM,IACF,eAAiB,IACjB,MAAM,IACN,eAAiB,IACjB,QAAQ,IACR,iBAAmB,KACnB,IAAM;;;oBAKpB,EAAmC,AAAnC,iCAAmC;oBACnC,EAAE,EAAE,cAAc,EAAE,CAAC;wBACnB,EAA4D,AAA5D,0DAA4D;wBAC5D,EAA2D,AAA3D,yDAA2D;wBAC3D,EAAuB,AAAvB,qBAAuB;wBACvB,KAAK,CAAC,gBAAgB,GAAG,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,MAAM;4BACjD,KAAK;gCAAG,cAAc;;;wBAExB,EAAE,EAAE,gBAAgB,EAAE,CAAC;4BACrB,EAAyC,AAAzC,uCAAyC;4BACzC,GAAG,CAAC,MAAM,GAAG,gBAAgB;wBAC/B,CAAC;oBACH,CAAC;gBACH,CAAC,QAAQ,CAAC,EAAE,CAAC;gBACX,EAA2C,AAA3C,yCAA2C;gBAC7C,CAAC;YACH,CAAC;QACH,CAAC;IAEL,CAAC;IAED,EAA2C,AAA3C,yCAA2C;IAC3C,EAA4C,AAA5C,0CAA4C;IAC5C,EAA4C,AAA5C,0CAA4C;IAC5C,EAA0B,AAA1B,wBAA0B;IAC1B,KAAK,CAAC,aAAa,GAAQ,aAAa,CAAC,KAAK;IAC9C,EAAE,SAAS,aAAa,MAAK,SAAW,GAAE,CAAC;QACzC,KAAK,CAAC,YAAY,aAAe,CAAC;YAChC,KAAK,CAAC,KAAK,UACF,aAAa,MAAK,QAAU,UACzB,aAAa,KACnB,aAAa;YACnB,EAA0C,AAA1C,wCAA0C;YAC1C,EAAE,EACA,aAAa,IACb,KAAK,CAAC,OAAO,CAAC,KAAK,EAAC,OAAS,OAC7B,KAAK,EAAC,OAAS,GAAE,MAAM,GAAG,CAAC,EAC3B,CAAC;gBACD,KAAK,CAAC,YAAY,GAAG,aAAa,CA52DnC,WAAyB;gBA+2DxB,KAAK,CA/2DN,WAAyB;uBAg3DnB,KAAK,EAAC,OAAS;oBAClB,YAAY;;YAEhB,CAAC;mBACM,KAAK,EAAC,OAAS;YAEtB,EAAE,EA34D4B,QAAoC,gBA24D/C,QAAQ,EAAE,CAAC;qBACvB,KAAK,CAAC,IAAI,IAAI,MAAM,CAAC,IAAI,CAAC,KAAK,EAAG,CAAC;oBACtC,EAAE,EACA,IAAI,MAAK,SAAW,KACpB,IAAI,MAAK,IAAM,KACf,IAAI,MAAK,GAAK,KACd,IAAI,MAAK,aAAe;oBAG1B,KAAK,CAAC,QAAQ,GACZ,IAAI,CAAC,UAAU,EAAC,MAAQ,MAAK,IAAI,MAAK,UAAY,KAC9C,UAAY,KACZ,IAAM;oBACZ,KAAK,CAAC,GAAG,GAAG,KAAK,CAAC,IAAI;oBACtB,EAAE,SAAS,GAAG,MAAK,MAAQ,MAAK,KAAK,CAAC,OAAO,CAAC,GAAG,GAAG,CAAC;wBACnD,KAAK,CAAC,IAAI;4BACR,QAAQ;+BACL,GAAG;;oBAEV,CAAC,MAAM,CAAC;wBACN,KAAK,CAAC,IAAI;4BACR,MAAM,EAAE,GAAG;4BACX,QAAQ;;oBAEZ,CAAC;gBACH,CAAC;YACH,CAAC;mBAEM,KAAK;QACd,CAAC;QACD,EAAsC,AAAtC,oCAAsC;QACtC,aAAa,CAAC,KAAK,GAAG,YAAY;IACpC,CAAC;IAED,EAAE,GAAG,GAAG,EAAE,CAAC;QACT,EAA6B,AAA7B,2BAA6B;QAC7B,aAAa,CAAC,KAAK,SAAU,aAAa,CAAC,KAAK;IAClD,CAAC;WAEM,aAAa;AACtB,CAAC"}