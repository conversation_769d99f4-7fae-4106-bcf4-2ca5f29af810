{"version": 3, "sources": ["../../../../../build/webpack/plugins/wellknown-errors-plugin/parseCss.ts"], "sourcesContent": ["import Chalk from 'chalk'\nimport { SimpleWebpackError } from './simpleWebpackError'\n\nconst chalk = new Chalk.constructor({ enabled: true })\nconst regexCssError =\n  /^(?:CssSyntaxError|SyntaxError)\\n\\n\\((\\d+):(\\d*)\\) (.*)$/s\n\nexport function getCssError(\n  fileName: string,\n  err: Error\n): SimpleWebpackError | false {\n  if (\n    !(\n      (err.name === 'CssSyntaxError' || err.name === 'SyntaxError') &&\n      (err as any).stack === false &&\n      !(err instanceof SyntaxError)\n    )\n  ) {\n    return false\n  }\n\n  // https://github.com/postcss/postcss-loader/blob/d6931da177ac79707bd758436e476036a55e4f59/src/Error.js\n\n  const res = regexCssError.exec(err.message)\n  if (res) {\n    const [, _lineNumer, _column, reason] = res\n    const lineNumber = Math.max(1, parseInt(_lineNumer, 10))\n    const column = Math.max(1, parseInt(_column, 10))\n\n    return new SimpleWebpackError(\n      `${chalk.cyan(fileName)}:${chalk.yellow(\n        lineNumber.toString()\n      )}:${chalk.yellow(column.toString())}`,\n      chalk.red.bold('Syntax error').concat(`: ${reason}`)\n    )\n  }\n\n  return false\n}\n"], "names": [], "mappings": ";;;;QAOgB,WAAW,GAAX,WAAW;AAPT,GAAO,CAAP,MAAO;AACU,GAAsB,CAAtB,mBAAsB;;;;;;AAEzD,KAAK,CAAC,KAAK,GAAG,GAAG,CAHC,MAAO,SAGD,WAAW;IAAG,OAAO,EAAE,IAAI;;AACnD,KAAK,CAAC,aAAa;SAGH,WAAW,CACzB,QAAgB,EAChB,GAAU,EACkB,CAAC;IAC7B,EAAE,KAEG,GAAG,CAAC,IAAI,MAAK,cAAgB,KAAI,GAAG,CAAC,IAAI,MAAK,WAAa,MAC3D,GAAG,CAAS,KAAK,KAAK,KAAK,MAC1B,GAAG,YAAY,WAAW,IAE9B,CAAC;eACM,KAAK;IACd,CAAC;IAED,EAAuG,AAAvG,qGAAuG;IAEvG,KAAK,CAAC,GAAG,GAAG,aAAa,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO;IAC1C,EAAE,EAAE,GAAG,EAAE,CAAC;QACR,KAAK,IAAI,UAAU,EAAE,OAAO,EAAE,MAAM,IAAI,GAAG;QAC3C,KAAK,CAAC,UAAU,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,QAAQ,CAAC,UAAU,EAAE,EAAE;QACtD,KAAK,CAAC,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,QAAQ,CAAC,OAAO,EAAE,EAAE;eAExC,GAAG,CA5BqB,mBAAsB,uBA6BhD,KAAK,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC,EAAE,KAAK,CAAC,MAAM,CACrC,UAAU,CAAC,QAAQ,IACnB,CAAC,EAAE,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC,QAAQ,OACjC,KAAK,CAAC,GAAG,CAAC,IAAI,EAAC,YAAc,GAAE,MAAM,EAAE,EAAE,EAAE,MAAM;IAErD,CAAC;WAEM,KAAK;AACd,CAAC"}