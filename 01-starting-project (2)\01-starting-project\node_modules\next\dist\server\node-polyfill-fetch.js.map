{"version": 3, "sources": ["../../server/node-polyfill-fetch.js"], "sourcesContent": ["import fetch, { Head<PERSON>, Request, Response } from 'node-fetch'\n\n// Polyfill fetch() in the Node.js environment\nif (!global.fetch) {\n  const agent = ({ protocol }) =>\n    protocol === 'http:' ? global.__NEXT_HTTP_AGENT : global.__NEXT_HTTPS_AGENT\n  const fetchWithAgent = (url, opts, ...rest) => {\n    if (!opts) {\n      opts = { agent }\n    } else if (!opts.agent) {\n      opts.agent = agent\n    }\n    return fetch(url, opts, ...rest)\n  }\n  global.fetch = fetchWithAgent\n  global.Headers = Headers\n  global.Request = Request\n  global.Response = Response\n}\n"], "names": [], "mappings": ";AAAkD,GAAY,CAAZ,UAAY;;;;;;;;;;;;;;;;;;;;;;;;AAE9D,EAA8C,AAA9C,4CAA8C;AAC9C,EAAE,GAAG,MAAM,CAAC,KAAK,EAAE,CAAC;IAClB,KAAK,CAAC,KAAK,MAAM,QAAQ,MACvB,QAAQ,MAAK,KAAO,IAAG,MAAM,CAAC,iBAAiB,GAAG,MAAM,CAAC,kBAAkB;;IAC7E,KAAK,CAAC,cAAc,IAAI,GAAG,EAAE,IAAI,KAAK,IAAI,GAAK,CAAC;QAC9C,EAAE,GAAG,IAAI,EAAE,CAAC;YACV,IAAI;gBAAK,KAAK;;QAChB,CAAC,MAAM,EAAE,GAAG,IAAI,CAAC,KAAK,EAAE,CAAC;YACvB,IAAI,CAAC,KAAK,GAAG,KAAK;QACpB,CAAC;mBAX6C,UAAY,UAY7C,GAAG,EAAE,IAAI,KAAK,IAAI;IACjC,CAAC;IACD,MAAM,CAAC,KAAK,GAAG,cAAc;IAC7B,MAAM,CAAC,OAAO,GAfkC,UAAY;IAgB5D,MAAM,CAAC,OAAO,GAhBkC,UAAY;IAiB5D,MAAM,CAAC,QAAQ,GAjBiC,UAAY;AAkB9D,CAAC"}