{"version": 3, "sources": ["../../../../build/webpack/plugins/profiling-plugin.ts"], "sourcesContent": ["import { webpack, isWebpack5 } from 'next/dist/compiled/webpack/webpack'\nimport { trace, Span } from '../../../telemetry/trace'\n\nconst pluginName = 'ProfilingPlugin'\nexport const spans = new WeakMap<any, Span>()\n\nfunction getNormalModuleLoaderHook(compilation: any) {\n  if (isWebpack5) {\n    // @ts-ignore TODO: Remove ignore when webpack 5 is stable\n    return webpack.NormalModule.getCompilationHooks(compilation).loader\n  }\n\n  return compilation.hooks.normalModuleLoader\n}\n\nexport class ProfilingPlugin {\n  compiler: any\n  runWebpackSpan: Span\n\n  constructor({ runWebpackSpan }: { runWebpackSpan: Span }) {\n    this.runWebpackSpan = runWebpackSpan\n  }\n  apply(compiler: any) {\n    this.traceTopLevelHooks(compiler)\n    this.traceCompilationHooks(compiler)\n    this.compiler = compiler\n  }\n\n  traceHookPair(\n    spanName: string,\n    startHook: any,\n    stopHook: any,\n    {\n      parentSpan,\n      attrs,\n      onSetSpan,\n    }: {\n      parentSpan?: () => Span\n      attrs?: any\n      onSetSpan?: (span: Span) => void\n    } = {}\n  ) {\n    let span: Span | undefined\n    startHook.tap(pluginName, () => {\n      span = parentSpan\n        ? parentSpan().traceChild(spanName, attrs ? attrs() : attrs)\n        : trace(spanName, undefined, attrs ? attrs() : attrs)\n\n      onSetSpan?.(span)\n    })\n    stopHook.tap(pluginName, () => {\n      // `stopHook` may be triggered when `startHook` has not in cases\n      // where `stopHook` is used as the terminating event for more\n      // than one pair of hooks.\n      if (!span) {\n        return\n      }\n      span.stop()\n    })\n  }\n\n  traceTopLevelHooks(compiler: any) {\n    this.traceHookPair(\n      'webpack-compilation',\n      isWebpack5 ? compiler.hooks.beforeCompile : compiler.hooks.compile,\n      isWebpack5 ? compiler.hooks.afterCompile : compiler.hooks.done,\n      {\n        parentSpan: () => this.runWebpackSpan,\n        attrs: () => ({ name: compiler.name }),\n        onSetSpan: (span) => spans.set(compiler, span),\n      }\n    )\n\n    if (compiler.options.mode === 'development') {\n      this.traceHookPair(\n        'webpack-invalidated',\n        compiler.hooks.invalid,\n        compiler.hooks.done,\n        { attrs: () => ({ name: compiler.name }) }\n      )\n    }\n  }\n\n  traceCompilationHooks(compiler: any) {\n    this.traceHookPair(\n      'webpack-emit',\n      compiler.hooks.emit,\n      compiler.hooks.afterEmit,\n      { parentSpan: () => this.runWebpackSpan }\n    )\n\n    compiler.hooks.compilation.tap(pluginName, (compilation: any) => {\n      compilation.hooks.buildModule.tap(pluginName, (module: any) => {\n        const compilerSpan = spans.get(compiler)\n        if (!compilerSpan) {\n          return\n        }\n\n        const moduleType = (() => {\n          if (!module.userRequest) {\n            return ''\n          }\n\n          return module.userRequest.split('.').pop()\n        })()\n\n        const issuerModule = compilation?.moduleGraph?.getIssuer(module)\n\n        let span: Span\n\n        const spanName = `build-module${moduleType ? `-${moduleType}` : ''}`\n        const issuerSpan: Span | undefined =\n          issuerModule && spans.get(issuerModule)\n        if (issuerSpan) {\n          span = issuerSpan.traceChild(spanName)\n        } else {\n          span = compilerSpan.traceChild(spanName)\n        }\n        span.setAttribute('name', module.userRequest)\n        spans.set(module, span)\n      })\n\n      getNormalModuleLoaderHook(compilation).tap(\n        pluginName,\n        (loaderContext: any, module: any) => {\n          const moduleSpan = spans.get(module)\n          loaderContext.currentTraceSpan = moduleSpan\n        }\n      )\n\n      compilation.hooks.succeedModule.tap(pluginName, (module: any) => {\n        spans.get(module)?.stop()\n      })\n\n      this.traceHookPair(\n        'webpack-compilation-chunk-graph',\n        compilation.hooks.beforeChunks,\n        compilation.hooks.afterChunks,\n        { parentSpan: () => this.runWebpackSpan }\n      )\n      this.traceHookPair(\n        'webpack-compilation-optimize',\n        compilation.hooks.optimize,\n        compilation.hooks.reviveModules,\n        { parentSpan: () => this.runWebpackSpan }\n      )\n      this.traceHookPair(\n        'webpack-compilation-optimize-modules',\n        compilation.hooks.optimizeModules,\n        compilation.hooks.afterOptimizeModules,\n        { parentSpan: () => this.runWebpackSpan }\n      )\n      this.traceHookPair(\n        'webpack-compilation-optimize-chunks',\n        compilation.hooks.optimizeChunks,\n        compilation.hooks.afterOptimizeChunks,\n        { parentSpan: () => this.runWebpackSpan }\n      )\n      this.traceHookPair(\n        'webpack-compilation-optimize-tree',\n        compilation.hooks.optimizeTree,\n        compilation.hooks.afterOptimizeTree,\n        { parentSpan: () => this.runWebpackSpan }\n      )\n      this.traceHookPair(\n        'webpack-compilation-hash',\n        compilation.hooks.beforeHash,\n        compilation.hooks.afterHash,\n        { parentSpan: () => this.runWebpackSpan }\n      )\n    })\n  }\n}\n"], "names": [], "mappings": ";;;;;AAAoC,GAAoC,CAApC,QAAoC;AAC5C,GAA0B,CAA1B,MAA0B;AAEtD,KAAK,CAAC,UAAU,IAAG,eAAiB;AAC7B,KAAK,CAAC,KAAK,GAAG,GAAG,CAAC,OAAO;QAAnB,KAAK,GAAL,KAAK;SAET,yBAAyB,CAAC,WAAgB,EAAE,CAAC;IACpD,EAAE,EAPgC,QAAoC,aAOtD,CAAC;QACf,EAA0D,AAA1D,wDAA0D;eAR1B,QAAoC,SASrD,YAAY,CAAC,mBAAmB,CAAC,WAAW,EAAE,MAAM;IACrE,CAAC;WAEM,WAAW,CAAC,KAAK,CAAC,kBAAkB;AAC7C,CAAC;MAEY,eAAe;kBAIZ,cAAc,IAA8B,CAAC;aACpD,cAAc,GAAG,cAAc;IACtC,CAAC;IACD,KAAK,CAAC,QAAa,EAAE,CAAC;aACf,kBAAkB,CAAC,QAAQ;aAC3B,qBAAqB,CAAC,QAAQ;aAC9B,QAAQ,GAAG,QAAQ;IAC1B,CAAC;IAED,aAAa,CACX,QAAgB,EAChB,SAAc,EACd,QAAa,IAEX,UAAU,GACV,KAAK,GACL,SAAS;OAMX,CAAC;QACD,GAAG,CAAC,IAAI;QACR,SAAS,CAAC,GAAG,CAAC,UAAU,MAAQ,CAAC;YAC/B,IAAI,GAAG,UAAU,GACb,UAAU,GAAG,UAAU,CAAC,QAAQ,EAAE,KAAK,GAAG,KAAK,KAAK,KAAK,QA5CvC,MAA0B,QA6CtC,QAAQ,EAAE,SAAS,EAAE,KAAK,GAAG,KAAK,KAAK,KAAK;YAEtD,SAAS,aAAT,SAAS,UAAT,CAAiB,QAAjB,CAAiB,GAAjB,SAAS,CAAG,IAAI;QAClB,CAAC;QACD,QAAQ,CAAC,GAAG,CAAC,UAAU,MAAQ,CAAC;YAC9B,EAAgE,AAAhE,8DAAgE;YAChE,EAA6D,AAA7D,2DAA6D;YAC7D,EAA0B,AAA1B,wBAA0B;YAC1B,EAAE,GAAG,IAAI,EAAE,CAAC;;YAEZ,CAAC;YACD,IAAI,CAAC,IAAI;QACX,CAAC;IACH,CAAC;IAED,kBAAkB,CAAC,QAAa,EAAE,CAAC;aAC5B,aAAa,EAChB,mBAAqB,GA/DS,QAAoC,cAgErD,QAAQ,CAAC,KAAK,CAAC,aAAa,GAAG,QAAQ,CAAC,KAAK,CAAC,OAAO,EAhEpC,QAAoC,cAiErD,QAAQ,CAAC,KAAK,CAAC,YAAY,GAAG,QAAQ,CAAC,KAAK,CAAC,IAAI;YAE5D,UAAU,WAAa,cAAc;;YACrC,KAAK;oBAAW,IAAI,EAAE,QAAQ,CAAC,IAAI;;;YACnC,SAAS,GAAG,IAAI,GAAK,KAAK,CAAC,GAAG,CAAC,QAAQ,EAAE,IAAI;;QAIjD,EAAE,EAAE,QAAQ,CAAC,OAAO,CAAC,IAAI,MAAK,WAAa,GAAE,CAAC;iBACvC,aAAa,EAChB,mBAAqB,GACrB,QAAQ,CAAC,KAAK,CAAC,OAAO,EACtB,QAAQ,CAAC,KAAK,CAAC,IAAI;gBACjB,KAAK;wBAAW,IAAI,EAAE,QAAQ,CAAC,IAAI;;;QAEzC,CAAC;IACH,CAAC;IAED,qBAAqB,CAAC,QAAa,EAAE,CAAC;aAC/B,aAAa,EAChB,YAAc,GACd,QAAQ,CAAC,KAAK,CAAC,IAAI,EACnB,QAAQ,CAAC,KAAK,CAAC,SAAS;YACtB,UAAU,WAAa,cAAc;;QAGzC,QAAQ,CAAC,KAAK,CAAC,WAAW,CAAC,GAAG,CAAC,UAAU,GAAG,WAAgB,GAAK,CAAC;YAChE,WAAW,CAAC,KAAK,CAAC,WAAW,CAAC,GAAG,CAAC,UAAU,GAAG,MAAW,GAAK,CAAC;oBAczC,GAAwB;gBAb7C,KAAK,CAAC,YAAY,GAAG,KAAK,CAAC,GAAG,CAAC,QAAQ;gBACvC,EAAE,GAAG,YAAY,EAAE,CAAC;;gBAEpB,CAAC;gBAED,KAAK,CAAC,UAAU,QAAU,CAAC;oBACzB,EAAE,GAAG,MAAM,CAAC,WAAW,EAAE,CAAC;;oBAE1B,CAAC;2BAEM,MAAM,CAAC,WAAW,CAAC,KAAK,EAAC,CAAG,GAAE,GAAG;gBAC1C,CAAC;gBAED,KAAK,CAAC,YAAY,GAAG,WAAW,aAAX,WAAW,UAAX,CAAwB,QAAxB,CAAwB,IAAxB,GAAwB,GAAxB,WAAW,CAAE,WAAW,cAAxB,GAAwB,UAAxB,CAAwB,QAAxB,CAAwB,GAAxB,GAAwB,CAAE,SAAS,CAAC,MAAM;gBAE/D,GAAG,CAAC,IAAI;gBAER,KAAK,CAAC,QAAQ,IAAI,YAAY,EAAE,UAAU,IAAI,CAAC,EAAE,UAAU;gBAC3D,KAAK,CAAC,UAAU,GACd,YAAY,IAAI,KAAK,CAAC,GAAG,CAAC,YAAY;gBACxC,EAAE,EAAE,UAAU,EAAE,CAAC;oBACf,IAAI,GAAG,UAAU,CAAC,UAAU,CAAC,QAAQ;gBACvC,CAAC,MAAM,CAAC;oBACN,IAAI,GAAG,YAAY,CAAC,UAAU,CAAC,QAAQ;gBACzC,CAAC;gBACD,IAAI,CAAC,YAAY,EAAC,IAAM,GAAE,MAAM,CAAC,WAAW;gBAC5C,KAAK,CAAC,GAAG,CAAC,MAAM,EAAE,IAAI;YACxB,CAAC;YAED,yBAAyB,CAAC,WAAW,EAAE,GAAG,CACxC,UAAU,GACT,aAAkB,EAAE,MAAW,GAAK,CAAC;gBACpC,KAAK,CAAC,UAAU,GAAG,KAAK,CAAC,GAAG,CAAC,MAAM;gBACnC,aAAa,CAAC,gBAAgB,GAAG,UAAU;YAC7C,CAAC;YAGH,WAAW,CAAC,KAAK,CAAC,aAAa,CAAC,GAAG,CAAC,UAAU,GAAG,MAAW,GAAK,CAAC;oBAChE,GAAiB;iBAAjB,GAAiB,GAAjB,KAAK,CAAC,GAAG,CAAC,MAAM,eAAhB,GAAiB,UAAjB,CAAuB,QAAvB,CAAuB,GAAvB,GAAiB,CAAE,IAAI;YACzB,CAAC;iBAEI,aAAa,EAChB,+BAAiC,GACjC,WAAW,CAAC,KAAK,CAAC,YAAY,EAC9B,WAAW,CAAC,KAAK,CAAC,WAAW;gBAC3B,UAAU,WAAa,cAAc;;iBAEpC,aAAa,EAChB,4BAA8B,GAC9B,WAAW,CAAC,KAAK,CAAC,QAAQ,EAC1B,WAAW,CAAC,KAAK,CAAC,aAAa;gBAC7B,UAAU,WAAa,cAAc;;iBAEpC,aAAa,EAChB,oCAAsC,GACtC,WAAW,CAAC,KAAK,CAAC,eAAe,EACjC,WAAW,CAAC,KAAK,CAAC,oBAAoB;gBACpC,UAAU,WAAa,cAAc;;iBAEpC,aAAa,EAChB,mCAAqC,GACrC,WAAW,CAAC,KAAK,CAAC,cAAc,EAChC,WAAW,CAAC,KAAK,CAAC,mBAAmB;gBACnC,UAAU,WAAa,cAAc;;iBAEpC,aAAa,EAChB,iCAAmC,GACnC,WAAW,CAAC,KAAK,CAAC,YAAY,EAC9B,WAAW,CAAC,KAAK,CAAC,iBAAiB;gBACjC,UAAU,WAAa,cAAc;;iBAEpC,aAAa,EAChB,wBAA0B,GAC1B,WAAW,CAAC,KAAK,CAAC,UAAU,EAC5B,WAAW,CAAC,KAAK,CAAC,SAAS;gBACzB,UAAU,WAAa,cAAc;;QAE3C,CAAC;IACH,CAAC;;QA5JU,eAAe,GAAf,eAAe"}