{"version": 3, "sources": ["../../../../build/polyfills/fetch/whatwg-fetch.js"], "sourcesContent": ["/* globals self */\nexports.Headers = self.Headers\nexports.Request = self.Request\nexports.Response = self.Response\nexports.fetch = self.fetch\n"], "names": [], "mappings": ";AAAA,EAAkB,AAAlB,cAAkB,AAAlB,EAAkB,CAClB,OAAO,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO;AAC9B,OAAO,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO;AAC9B,OAAO,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ;AAChC,OAAO,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK"}