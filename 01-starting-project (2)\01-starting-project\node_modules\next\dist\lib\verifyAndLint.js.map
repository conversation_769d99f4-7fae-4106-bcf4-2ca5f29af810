{"version": 3, "sources": ["../../lib/verifyAndLint.ts"], "sourcesContent": ["import chalk from 'chalk'\nimport { Worker } from 'jest-worker'\nimport { existsSync } from 'fs'\nimport { join } from 'path'\nimport { ESLINT_DEFAULT_DIRS } from './constants'\nimport { Telemetry } from '../telemetry/storage'\nimport { eventLintCheckCompleted } from '../telemetry/events'\nimport { CompileError } from './compile-error'\n\nexport async function verifyAndLint(\n  dir: string,\n  cacheLocation: string,\n  configLintDirs: string[] | undefined,\n  numWorkers: number | undefined,\n  enableWorkerThreads: boolean | undefined,\n  telemetry: Telemetry\n): Promise<void> {\n  try {\n    const lintWorkers = new Worker(require.resolve('./eslint/runLintCheck'), {\n      numWorkers,\n      enableWorkerThreads,\n    }) as Worker & {\n      runLintCheck: typeof import('./eslint/runLintCheck').runLintCheck\n    }\n\n    lintWorkers.getStdout().pipe(process.stdout)\n    lintWorkers.getStderr().pipe(process.stderr)\n\n    const lintDirs = (configLintDirs ?? ESLINT_DEFAULT_DIRS).reduce(\n      (res: string[], d: string) => {\n        const currDir = join(dir, d)\n        if (!existsSync(currDir)) return res\n        res.push(currDir)\n        return res\n      },\n      []\n    )\n\n    const lintResults = await lintWorkers.runLintCheck(dir, lintDirs, true, {\n      cacheLocation,\n    })\n    const lintOutput =\n      typeof lintResults === 'string' ? lintResults : lintResults?.output\n\n    if (typeof lintResults !== 'string' && lintResults?.eventInfo) {\n      telemetry.record(\n        eventLintCheckCompleted({\n          ...lintResults.eventInfo,\n          buildLint: true,\n        })\n      )\n    }\n\n    if (typeof lintResults !== 'string' && lintResults?.isError && lintOutput) {\n      await telemetry.flush()\n      throw new CompileError(lintOutput)\n    }\n\n    if (lintOutput) {\n      console.log(lintOutput)\n    }\n\n    lintWorkers.end()\n  } catch (err) {\n    if (err.type === 'CompileError' || err instanceof CompileError) {\n      console.error(chalk.red('\\nFailed to compile.'))\n      console.error(err.message)\n      process.exit(1)\n    } else if (err.type === 'FatalError') {\n      console.error(err.message)\n      process.exit(1)\n    }\n    throw err\n  }\n}\n"], "names": [], "mappings": ";;;;QASsB,aAAa,GAAb,aAAa;AATjB,GAAO,CAAP,MAAO;AACF,GAAa,CAAb,WAAa;AACT,GAAI,CAAJ,GAAI;AACV,GAAM,CAAN,KAAM;AACS,GAAa,CAAb,UAAa;AAET,GAAqB,CAArB,OAAqB;AAChC,GAAiB,CAAjB,aAAiB;;;;;;eAExB,aAAa,CACjC,GAAW,EACX,aAAqB,EACrB,cAAoC,EACpC,UAA8B,EAC9B,mBAAwC,EACxC,SAAoB,EACL,CAAC;QACZ,CAAC;QACH,KAAK,CAAC,WAAW,GAAG,GAAG,CAjBJ,WAAa,QAiBD,OAAO,CAAC,OAAO,EAAC,qBAAuB;YACpE,UAAU;YACV,mBAAmB;;QAKrB,WAAW,CAAC,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM;QAC3C,WAAW,CAAC,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM;QAE3C,KAAK,CAAC,QAAQ,IAAI,cAAc,aAAd,cAAc,cAAd,cAAc,GAxBA,UAAa,sBAwBY,MAAM,EAC5D,GAAa,EAAE,CAAS,GAAK,CAAC;YAC7B,KAAK,CAAC,OAAO,OA3BA,KAAM,OA2BE,GAAG,EAAE,CAAC;YAC3B,EAAE,OA7BiB,GAAI,aA6BP,OAAO,UAAU,GAAG;YACpC,GAAG,CAAC,IAAI,CAAC,OAAO;mBACT,GAAG;QACZ,CAAC;QAIH,KAAK,CAAC,WAAW,SAAS,WAAW,CAAC,YAAY,CAAC,GAAG,EAAE,QAAQ,EAAE,IAAI;YACpE,aAAa;;QAEf,KAAK,CAAC,UAAU,UACP,WAAW,MAAK,MAAQ,IAAG,WAAW,GAAG,WAAW,aAAX,WAAW,UAAX,CAAmB,QAAnB,CAAmB,GAAnB,WAAW,CAAE,MAAM;QAErE,EAAE,SAAS,WAAW,MAAK,MAAQ,MAAI,WAAW,aAAX,WAAW,UAAX,CAAsB,QAAtB,CAAsB,GAAtB,WAAW,CAAE,SAAS,GAAE,CAAC;YAC9D,SAAS,CAAC,MAAM,KAvCkB,OAAqB;mBAyChD,WAAW,CAAC,SAAS;gBACxB,SAAS,EAAE,IAAI;;QAGrB,CAAC;QAED,EAAE,SAAS,WAAW,MAAK,MAAQ,MAAI,WAAW,aAAX,WAAW,UAAX,CAAoB,QAApB,CAAoB,GAApB,WAAW,CAAE,OAAO,KAAI,UAAU,EAAE,CAAC;kBACpE,SAAS,CAAC,KAAK;YACrB,KAAK,CAAC,GAAG,CAhDc,aAAiB,cAgDjB,UAAU;QACnC,CAAC;QAED,EAAE,EAAE,UAAU,EAAE,CAAC;YACf,OAAO,CAAC,GAAG,CAAC,UAAU;QACxB,CAAC;QAED,WAAW,CAAC,GAAG;IACjB,CAAC,QAAQ,GAAG,EAAE,CAAC;QACb,EAAE,EAAE,GAAG,CAAC,IAAI,MAAK,YAAc,KAAI,GAAG,YAzDb,aAAiB,eAyDsB,CAAC;YAC/D,OAAO,CAAC,KAAK,CAjED,MAAO,SAiEC,GAAG,EAAC,oBAAsB;YAC9C,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,OAAO;YACzB,OAAO,CAAC,IAAI,CAAC,CAAC;QAChB,CAAC,MAAM,EAAE,EAAE,GAAG,CAAC,IAAI,MAAK,UAAY,GAAE,CAAC;YACrC,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,OAAO;YACzB,OAAO,CAAC,IAAI,CAAC,CAAC;QAChB,CAAC;QACD,KAAK,CAAC,GAAG;IACX,CAAC;AACH,CAAC"}