{"version": 3, "sources": ["../../../shared/lib/router-context.ts"], "sourcesContent": ["import React from 'react'\nimport { NextRouter } from './router/router'\n\nexport const RouterContext = React.createContext<NextRouter>(null as any)\n\nif (process.env.NODE_ENV !== 'production') {\n  RouterContext.displayName = 'RouterContext'\n}\n"], "names": [], "mappings": ";;;;;AAAkB,GAAO,CAAP,MAAO;;;;;;AAGlB,KAAK,CAAC,aAAa,GAHR,MAAO,SAGU,aAAa,CAAa,IAAI;QAApD,aAAa,GAAb,aAAa;AAE1B,EAAE,EAAE,OAAO,CAAC,GAAG,CAAC,QAAQ,MAAK,UAAY,GAAE,CAAC;IAC1C,aAAa,CAAC,WAAW,IAAG,aAAe;AAC7C,CAAC"}