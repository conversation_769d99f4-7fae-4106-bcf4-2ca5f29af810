{"version": 3, "sources": ["../../../shared/lib/runtime-config.ts"], "sourcesContent": ["let runtimeConfig: any\n\nexport default () => {\n  return runtimeConfig\n}\n\nexport function setConfig(configValue: any): void {\n  runtimeConfig = configValue\n}\n"], "names": [], "mappings": ";;;;QAMgB,SAAS,GAAT,SAAS;;AANzB,GAAG,CAAC,aAAa;mBAEI,CAAC;WACb,aAAa;AACtB,CAAC;;SAEe,SAAS,CAAC,WAAgB,EAAQ,CAAC;IACjD,aAAa,GAAG,WAAW;AAC7B,CAAC"}