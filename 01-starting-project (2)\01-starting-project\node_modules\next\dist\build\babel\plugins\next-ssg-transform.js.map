{"version": 3, "sources": ["../../../../build/babel/plugins/next-ssg-transform.ts"], "sourcesContent": ["import {\n  Node<PERSON><PERSON>,\n  PluginObj,\n  types as BabelTypes,\n} from 'next/dist/compiled/babel/core'\nimport { SERVER_PROPS_SSG_CONFLICT } from '../../../lib/constants'\nimport { SERVER_PROPS_ID, STATIC_PROPS_ID } from '../../../shared/lib/constants'\n\nexport const EXPORT_NAME_GET_STATIC_PROPS = 'getStaticProps'\nexport const EXPORT_NAME_GET_STATIC_PATHS = 'getStaticPaths'\nexport const EXPORT_NAME_GET_SERVER_PROPS = 'getServerSideProps'\n\nconst ssgExports = new Set([\n  EXPORT_NAME_GET_STATIC_PROPS,\n  EXPORT_NAME_GET_STATIC_PATHS,\n  EXPORT_NAME_GET_SERVER_PROPS,\n\n  // legacy methods added so build doesn't fail from importing\n  // server-side only methods\n  `unstable_getStaticProps`,\n  `unstable_getStaticPaths`,\n  `unstable_getServerProps`,\n  `unstable_getServerSideProps`,\n])\n\ntype PluginState = {\n  refs: Set<NodePath<BabelTypes.Identifier>>\n  isPrerender: boolean\n  isServerProps: boolean\n  done: boolean\n}\n\nfunction decorateSsgExport(\n  t: typeof BabelTypes,\n  path: NodePath<BabelTypes.Program>,\n  state: PluginState\n): void {\n  const gsspName = state.isPrerender ? STATIC_PROPS_ID : SERVER_PROPS_ID\n  const gsspId = t.identifier(gsspName)\n\n  const addGsspExport = (\n    exportPath:\n      | NodePath<BabelTypes.ExportDefaultDeclaration>\n      | NodePath<BabelTypes.ExportNamedDeclaration>\n  ): void => {\n    if (state.done) {\n      return\n    }\n    state.done = true\n\n    const [pageCompPath] = exportPath.replaceWithMultiple([\n      t.exportNamedDeclaration(\n        t.variableDeclaration(\n          // We use 'var' instead of 'let' or 'const' for ES5 support. Since\n          // this runs in `Program#exit`, no ES2015 transforms (preset env)\n          // will be ran against this code.\n          'var',\n          [t.variableDeclarator(gsspId, t.booleanLiteral(true))]\n        ),\n        [t.exportSpecifier(gsspId, gsspId)]\n      ),\n      exportPath.node,\n    ])\n    exportPath.scope.registerDeclaration(\n      pageCompPath as NodePath<BabelTypes.Node>\n    )\n  }\n\n  path.traverse({\n    ExportDefaultDeclaration(exportDefaultPath) {\n      addGsspExport(exportDefaultPath)\n    },\n    ExportNamedDeclaration(exportNamedPath) {\n      addGsspExport(exportNamedPath)\n    },\n  })\n}\n\nconst isDataIdentifier = (name: string, state: PluginState): boolean => {\n  if (ssgExports.has(name)) {\n    if (name === EXPORT_NAME_GET_SERVER_PROPS) {\n      if (state.isPrerender) {\n        throw new Error(SERVER_PROPS_SSG_CONFLICT)\n      }\n      state.isServerProps = true\n    } else {\n      if (state.isServerProps) {\n        throw new Error(SERVER_PROPS_SSG_CONFLICT)\n      }\n      state.isPrerender = true\n    }\n    return true\n  }\n  return false\n}\n\nexport default function nextTransformSsg({\n  types: t,\n}: {\n  types: typeof BabelTypes\n}): PluginObj<PluginState> {\n  function getIdentifier(\n    path:\n      | NodePath<BabelTypes.FunctionDeclaration>\n      | NodePath<BabelTypes.FunctionExpression>\n      | NodePath<BabelTypes.ArrowFunctionExpression>\n  ): NodePath<BabelTypes.Identifier> | null {\n    const parentPath = path.parentPath\n    if (parentPath.type === 'VariableDeclarator') {\n      const pp = parentPath as NodePath<BabelTypes.VariableDeclarator>\n      const name = pp.get('id')\n      return name.node.type === 'Identifier'\n        ? (name as NodePath<BabelTypes.Identifier>)\n        : null\n    }\n\n    if (parentPath.type === 'AssignmentExpression') {\n      const pp = parentPath as NodePath<BabelTypes.AssignmentExpression>\n      const name = pp.get('left')\n      return name.node.type === 'Identifier'\n        ? (name as NodePath<BabelTypes.Identifier>)\n        : null\n    }\n\n    if (path.node.type === 'ArrowFunctionExpression') {\n      return null\n    }\n\n    return path.node.id && path.node.id.type === 'Identifier'\n      ? (path.get('id') as NodePath<BabelTypes.Identifier>)\n      : null\n  }\n\n  function isIdentifierReferenced(\n    ident: NodePath<BabelTypes.Identifier>\n  ): boolean {\n    const b = ident.scope.getBinding(ident.node.name)\n    if (b?.referenced) {\n      // Functions can reference themselves, so we need to check if there's a\n      // binding outside the function scope or not.\n      if (b.path.type === 'FunctionDeclaration') {\n        return !b.constantViolations\n          .concat(b.referencePaths)\n          // Check that every reference is contained within the function:\n          .every((ref) => ref.findParent((p) => p === b.path))\n      }\n\n      return true\n    }\n    return false\n  }\n\n  function markFunction(\n    path:\n      | NodePath<BabelTypes.FunctionDeclaration>\n      | NodePath<BabelTypes.FunctionExpression>\n      | NodePath<BabelTypes.ArrowFunctionExpression>,\n    state: PluginState\n  ): void {\n    const ident = getIdentifier(path)\n    if (ident?.node && isIdentifierReferenced(ident)) {\n      state.refs.add(ident)\n    }\n  }\n\n  function markImport(\n    path:\n      | NodePath<BabelTypes.ImportSpecifier>\n      | NodePath<BabelTypes.ImportDefaultSpecifier>\n      | NodePath<BabelTypes.ImportNamespaceSpecifier>,\n    state: PluginState\n  ): void {\n    const local = path.get('local') as NodePath<BabelTypes.Identifier>\n    if (isIdentifierReferenced(local)) {\n      state.refs.add(local)\n    }\n  }\n\n  return {\n    visitor: {\n      Program: {\n        enter(path, state) {\n          state.refs = new Set<NodePath<BabelTypes.Identifier>>()\n          state.isPrerender = false\n          state.isServerProps = false\n          state.done = false\n\n          path.traverse(\n            {\n              VariableDeclarator(variablePath, variableState) {\n                if (variablePath.node.id.type === 'Identifier') {\n                  const local = variablePath.get(\n                    'id'\n                  ) as NodePath<BabelTypes.Identifier>\n                  if (isIdentifierReferenced(local)) {\n                    variableState.refs.add(local)\n                  }\n                } else if (variablePath.node.id.type === 'ObjectPattern') {\n                  const pattern = variablePath.get(\n                    'id'\n                  ) as NodePath<BabelTypes.ObjectPattern>\n\n                  const properties = pattern.get('properties')\n                  properties.forEach((p) => {\n                    const local = p.get(\n                      p.node.type === 'ObjectProperty'\n                        ? 'value'\n                        : p.node.type === 'RestElement'\n                        ? 'argument'\n                        : (function () {\n                            throw new Error('invariant')\n                          })()\n                    ) as NodePath<BabelTypes.Identifier>\n                    if (isIdentifierReferenced(local)) {\n                      variableState.refs.add(local)\n                    }\n                  })\n                } else if (variablePath.node.id.type === 'ArrayPattern') {\n                  const pattern = variablePath.get(\n                    'id'\n                  ) as NodePath<BabelTypes.ArrayPattern>\n\n                  const elements = pattern.get('elements')\n                  elements.forEach((e) => {\n                    let local: NodePath<BabelTypes.Identifier>\n                    if (e.node?.type === 'Identifier') {\n                      local = e as NodePath<BabelTypes.Identifier>\n                    } else if (e.node?.type === 'RestElement') {\n                      local = e.get(\n                        'argument'\n                      ) as NodePath<BabelTypes.Identifier>\n                    } else {\n                      return\n                    }\n\n                    if (isIdentifierReferenced(local)) {\n                      variableState.refs.add(local)\n                    }\n                  })\n                }\n              },\n              FunctionDeclaration: markFunction,\n              FunctionExpression: markFunction,\n              ArrowFunctionExpression: markFunction,\n              ImportSpecifier: markImport,\n              ImportDefaultSpecifier: markImport,\n              ImportNamespaceSpecifier: markImport,\n              ExportNamedDeclaration(exportNamedPath, exportNamedState) {\n                const specifiers = exportNamedPath.get('specifiers')\n                if (specifiers.length) {\n                  specifiers.forEach((s) => {\n                    if (\n                      isDataIdentifier(\n                        t.isIdentifier(s.node.exported)\n                          ? s.node.exported.name\n                          : s.node.exported.value,\n                        exportNamedState\n                      )\n                    ) {\n                      s.remove()\n                    }\n                  })\n\n                  if (exportNamedPath.node.specifiers.length < 1) {\n                    exportNamedPath.remove()\n                  }\n                  return\n                }\n\n                const decl = exportNamedPath.get('declaration') as NodePath<\n                  | BabelTypes.FunctionDeclaration\n                  | BabelTypes.VariableDeclaration\n                >\n                if (decl == null || decl.node == null) {\n                  return\n                }\n\n                switch (decl.node.type) {\n                  case 'FunctionDeclaration': {\n                    const name = decl.node.id!.name\n                    if (isDataIdentifier(name, exportNamedState)) {\n                      exportNamedPath.remove()\n                    }\n                    break\n                  }\n                  case 'VariableDeclaration': {\n                    const inner = decl.get(\n                      'declarations'\n                    ) as NodePath<BabelTypes.VariableDeclarator>[]\n                    inner.forEach((d) => {\n                      if (d.node.id.type !== 'Identifier') {\n                        return\n                      }\n                      const name = d.node.id.name\n                      if (isDataIdentifier(name, exportNamedState)) {\n                        d.remove()\n                      }\n                    })\n                    break\n                  }\n                  default: {\n                    break\n                  }\n                }\n              },\n            },\n            state\n          )\n\n          if (!state.isPrerender && !state.isServerProps) {\n            return\n          }\n\n          const refs = state.refs\n          let count: number\n\n          function sweepFunction(\n            sweepPath:\n              | NodePath<BabelTypes.FunctionDeclaration>\n              | NodePath<BabelTypes.FunctionExpression>\n              | NodePath<BabelTypes.ArrowFunctionExpression>\n          ): void {\n            const ident = getIdentifier(sweepPath)\n            if (\n              ident?.node &&\n              refs.has(ident) &&\n              !isIdentifierReferenced(ident)\n            ) {\n              ++count\n\n              if (\n                t.isAssignmentExpression(sweepPath.parentPath) ||\n                t.isVariableDeclarator(sweepPath.parentPath)\n              ) {\n                sweepPath.parentPath.remove()\n              } else {\n                sweepPath.remove()\n              }\n            }\n          }\n\n          function sweepImport(\n            sweepPath:\n              | NodePath<BabelTypes.ImportSpecifier>\n              | NodePath<BabelTypes.ImportDefaultSpecifier>\n              | NodePath<BabelTypes.ImportNamespaceSpecifier>\n          ): void {\n            const local = sweepPath.get(\n              'local'\n            ) as NodePath<BabelTypes.Identifier>\n            if (refs.has(local) && !isIdentifierReferenced(local)) {\n              ++count\n              sweepPath.remove()\n              if (\n                (sweepPath.parent as BabelTypes.ImportDeclaration).specifiers\n                  .length === 0\n              ) {\n                sweepPath.parentPath.remove()\n              }\n            }\n          }\n\n          do {\n            ;(path.scope as any).crawl()\n            count = 0\n\n            path.traverse({\n              // eslint-disable-next-line no-loop-func\n              VariableDeclarator(variablePath) {\n                if (variablePath.node.id.type === 'Identifier') {\n                  const local = variablePath.get(\n                    'id'\n                  ) as NodePath<BabelTypes.Identifier>\n                  if (refs.has(local) && !isIdentifierReferenced(local)) {\n                    ++count\n                    variablePath.remove()\n                  }\n                } else if (variablePath.node.id.type === 'ObjectPattern') {\n                  const pattern = variablePath.get(\n                    'id'\n                  ) as NodePath<BabelTypes.ObjectPattern>\n\n                  const beforeCount = count\n                  const properties = pattern.get('properties')\n                  properties.forEach((p) => {\n                    const local = p.get(\n                      p.node.type === 'ObjectProperty'\n                        ? 'value'\n                        : p.node.type === 'RestElement'\n                        ? 'argument'\n                        : (function () {\n                            throw new Error('invariant')\n                          })()\n                    ) as NodePath<BabelTypes.Identifier>\n\n                    if (refs.has(local) && !isIdentifierReferenced(local)) {\n                      ++count\n                      p.remove()\n                    }\n                  })\n\n                  if (\n                    beforeCount !== count &&\n                    pattern.get('properties').length < 1\n                  ) {\n                    variablePath.remove()\n                  }\n                } else if (variablePath.node.id.type === 'ArrayPattern') {\n                  const pattern = variablePath.get(\n                    'id'\n                  ) as NodePath<BabelTypes.ArrayPattern>\n\n                  const beforeCount = count\n                  const elements = pattern.get('elements')\n                  elements.forEach((e) => {\n                    let local: NodePath<BabelTypes.Identifier>\n                    if (e.node?.type === 'Identifier') {\n                      local = e as NodePath<BabelTypes.Identifier>\n                    } else if (e.node?.type === 'RestElement') {\n                      local = e.get(\n                        'argument'\n                      ) as NodePath<BabelTypes.Identifier>\n                    } else {\n                      return\n                    }\n\n                    if (refs.has(local) && !isIdentifierReferenced(local)) {\n                      ++count\n                      e.remove()\n                    }\n                  })\n\n                  if (\n                    beforeCount !== count &&\n                    pattern.get('elements').length < 1\n                  ) {\n                    variablePath.remove()\n                  }\n                }\n              },\n              FunctionDeclaration: sweepFunction,\n              FunctionExpression: sweepFunction,\n              ArrowFunctionExpression: sweepFunction,\n              ImportSpecifier: sweepImport,\n              ImportDefaultSpecifier: sweepImport,\n              ImportNamespaceSpecifier: sweepImport,\n            })\n          } while (count)\n\n          decorateSsgExport(t, path, state)\n        },\n      },\n    },\n  }\n}\n"], "names": [], "mappings": ";;;;kBAgGwB,gBAAgB;;AA3FE,GAAwB,CAAxB,UAAwB;AACjB,GAA+B,CAA/B,WAA+B;AAEzE,KAAK,CAAC,4BAA4B,IAAG,cAAgB;QAA/C,4BAA4B,GAA5B,4BAA4B;AAClC,KAAK,CAAC,4BAA4B,IAAG,cAAgB;QAA/C,4BAA4B,GAA5B,4BAA4B;AAClC,KAAK,CAAC,4BAA4B,IAAG,kBAAoB;QAAnD,4BAA4B,GAA5B,4BAA4B;AAEzC,KAAK,CAAC,UAAU,GAAG,GAAG,CAAC,GAAG;IACxB,4BAA4B;IAC5B,4BAA4B;IAC5B,4BAA4B;IAE5B,EAA4D,AAA5D,0DAA4D;IAC5D,EAA2B,AAA3B,yBAA2B;KAC1B,uBAAuB;KACvB,uBAAuB;KACvB,uBAAuB;KACvB,2BAA2B;;SAUrB,iBAAiB,CACxB,CAAoB,EACpB,IAAkC,EAClC,KAAkB,EACZ,CAAC;IACP,KAAK,CAAC,QAAQ,GAAG,KAAK,CAAC,WAAW,GA/Ba,WAA+B,mBAA/B,WAA+B;IAgC9E,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,UAAU,CAAC,QAAQ;IAEpC,KAAK,CAAC,aAAa,IACjB,UAE+C,GACtC,CAAC;QACV,EAAE,EAAE,KAAK,CAAC,IAAI,EAAE,CAAC;;QAEjB,CAAC;QACD,KAAK,CAAC,IAAI,GAAG,IAAI;QAEjB,KAAK,EAAE,YAAY,IAAI,UAAU,CAAC,mBAAmB;YACnD,CAAC,CAAC,sBAAsB,CACtB,CAAC,CAAC,mBAAmB,CACnB,EAAkE,AAAlE,gEAAkE;YAClE,EAAiE,AAAjE,+DAAiE;YACjE,EAAiC,AAAjC,+BAAiC;aACjC,GAAK;gBACJ,CAAC,CAAC,kBAAkB,CAAC,MAAM,EAAE,CAAC,CAAC,cAAc,CAAC,IAAI;;gBAEpD,CAAC,CAAC,eAAe,CAAC,MAAM,EAAE,MAAM;;YAEnC,UAAU,CAAC,IAAI;;QAEjB,UAAU,CAAC,KAAK,CAAC,mBAAmB,CAClC,YAAY;IAEhB,CAAC;IAED,IAAI,CAAC,QAAQ;QACX,wBAAwB,EAAC,iBAAiB,EAAE,CAAC;YAC3C,aAAa,CAAC,iBAAiB;QACjC,CAAC;QACD,sBAAsB,EAAC,eAAe,EAAE,CAAC;YACvC,aAAa,CAAC,eAAe;QAC/B,CAAC;;AAEL,CAAC;AAED,KAAK,CAAC,gBAAgB,IAAI,IAAY,EAAE,KAAkB,GAAc,CAAC;IACvE,EAAE,EAAE,UAAU,CAAC,GAAG,CAAC,IAAI,GAAG,CAAC;QACzB,EAAE,EAAE,IAAI,KAAK,4BAA4B,EAAE,CAAC;YAC1C,EAAE,EAAE,KAAK,CAAC,WAAW,EAAE,CAAC;gBACtB,KAAK,CAAC,GAAG,CAAC,KAAK,CA7EmB,UAAwB;YA8E5D,CAAC;YACD,KAAK,CAAC,aAAa,GAAG,IAAI;QAC5B,CAAC,MAAM,CAAC;YACN,EAAE,EAAE,KAAK,CAAC,aAAa,EAAE,CAAC;gBACxB,KAAK,CAAC,GAAG,CAAC,KAAK,CAlFmB,UAAwB;YAmF5D,CAAC;YACD,KAAK,CAAC,WAAW,GAAG,IAAI;QAC1B,CAAC;eACM,IAAI;IACb,CAAC;WACM,KAAK;AACd,CAAC;SAEuB,gBAAgB,GACtC,KAAK,EAAE,CAAC,KAGiB,CAAC;aACjB,aAAa,CACpB,IAGgD,EACR,CAAC;QACzC,KAAK,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU;QAClC,EAAE,EAAE,UAAU,CAAC,IAAI,MAAK,kBAAoB,GAAE,CAAC;YAC7C,KAAK,CAAC,EAAE,GAAG,UAAU;YACrB,KAAK,CAAC,IAAI,GAAG,EAAE,CAAC,GAAG,EAAC,EAAI;mBACjB,IAAI,CAAC,IAAI,CAAC,IAAI,MAAK,UAAY,IACjC,IAAI,GACL,IAAI;QACV,CAAC;QAED,EAAE,EAAE,UAAU,CAAC,IAAI,MAAK,oBAAsB,GAAE,CAAC;YAC/C,KAAK,CAAC,EAAE,GAAG,UAAU;YACrB,KAAK,CAAC,IAAI,GAAG,EAAE,CAAC,GAAG,EAAC,IAAM;mBACnB,IAAI,CAAC,IAAI,CAAC,IAAI,MAAK,UAAY,IACjC,IAAI,GACL,IAAI;QACV,CAAC;QAED,EAAE,EAAE,IAAI,CAAC,IAAI,CAAC,IAAI,MAAK,uBAAyB,GAAE,CAAC;mBAC1C,IAAI;QACb,CAAC;eAEM,IAAI,CAAC,IAAI,CAAC,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,MAAK,UAAY,IACpD,IAAI,CAAC,GAAG,EAAC,EAAI,KACd,IAAI;IACV,CAAC;aAEQ,sBAAsB,CAC7B,KAAsC,EAC7B,CAAC;QACV,KAAK,CAAC,CAAC,GAAG,KAAK,CAAC,KAAK,CAAC,UAAU,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI;QAChD,EAAE,EAAE,CAAC,aAAD,CAAC,UAAD,CAAa,QAAb,CAAa,GAAb,CAAC,CAAE,UAAU,EAAE,CAAC;YAClB,EAAuE,AAAvE,qEAAuE;YACvE,EAA6C,AAA7C,2CAA6C;YAC7C,EAAE,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,MAAK,mBAAqB,GAAE,CAAC;wBAClC,CAAC,CAAC,kBAAkB,CACzB,MAAM,CAAC,CAAC,CAAC,cAAc,CACxB,EAA+D,AAA/D,6DAA+D;iBAC9D,KAAK,EAAE,GAAG,GAAK,GAAG,CAAC,UAAU,EAAE,CAAC,GAAK,CAAC,KAAK,CAAC,CAAC,IAAI;;;YACtD,CAAC;mBAEM,IAAI;QACb,CAAC;eACM,KAAK;IACd,CAAC;aAEQ,YAAY,CACnB,IAGgD,EAChD,KAAkB,EACZ,CAAC;QACP,KAAK,CAAC,KAAK,GAAG,aAAa,CAAC,IAAI;QAChC,EAAE,GAAE,KAAK,aAAL,KAAK,UAAL,CAAW,QAAX,CAAW,GAAX,KAAK,CAAE,IAAI,KAAI,sBAAsB,CAAC,KAAK,GAAG,CAAC;YACjD,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK;QACtB,CAAC;IACH,CAAC;aAEQ,UAAU,CACjB,IAGiD,EACjD,KAAkB,EACZ,CAAC;QACP,KAAK,CAAC,KAAK,GAAG,IAAI,CAAC,GAAG,EAAC,KAAO;QAC9B,EAAE,EAAE,sBAAsB,CAAC,KAAK,GAAG,CAAC;YAClC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK;QACtB,CAAC;IACH,CAAC;;QAGC,OAAO;YACL,OAAO;gBACL,KAAK,EAAC,IAAI,EAAE,KAAK,EAAE,CAAC;oBAClB,KAAK,CAAC,IAAI,GAAG,GAAG,CAAC,GAAG;oBACpB,KAAK,CAAC,WAAW,GAAG,KAAK;oBACzB,KAAK,CAAC,aAAa,GAAG,KAAK;oBAC3B,KAAK,CAAC,IAAI,GAAG,KAAK;oBAElB,IAAI,CAAC,QAAQ;wBAET,kBAAkB,EAAC,YAAY,EAAE,aAAa,EAAE,CAAC;4BAC/C,EAAE,EAAE,YAAY,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,MAAK,UAAY,GAAE,CAAC;gCAC/C,KAAK,CAAC,KAAK,GAAG,YAAY,CAAC,GAAG,EAC5B,EAAI;gCAEN,EAAE,EAAE,sBAAsB,CAAC,KAAK,GAAG,CAAC;oCAClC,aAAa,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK;gCAC9B,CAAC;4BACH,CAAC,MAAM,EAAE,EAAE,YAAY,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,MAAK,aAAe,GAAE,CAAC;gCACzD,KAAK,CAAC,OAAO,GAAG,YAAY,CAAC,GAAG,EAC9B,EAAI;gCAGN,KAAK,CAAC,UAAU,GAAG,OAAO,CAAC,GAAG,EAAC,UAAY;gCAC3C,UAAU,CAAC,OAAO,EAAE,CAAC,GAAK,CAAC;oCACzB,KAAK,CAAC,KAAK,GAAG,CAAC,CAAC,GAAG,CACjB,CAAC,CAAC,IAAI,CAAC,IAAI,MAAK,cAAgB,KAC5B,KAAO,IACP,CAAC,CAAC,IAAI,CAAC,IAAI,MAAK,WAAa,KAC7B,QAAU,eACG,CAAC;wCACZ,KAAK,CAAC,GAAG,CAAC,KAAK,EAAC,SAAW;oCAC7B,CAAC;oCAEP,EAAE,EAAE,sBAAsB,CAAC,KAAK,GAAG,CAAC;wCAClC,aAAa,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK;oCAC9B,CAAC;gCACH,CAAC;4BACH,CAAC,MAAM,EAAE,EAAE,YAAY,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,MAAK,YAAc,GAAE,CAAC;gCACxD,KAAK,CAAC,OAAO,GAAG,YAAY,CAAC,GAAG,EAC9B,EAAI;gCAGN,KAAK,CAAC,QAAQ,GAAG,OAAO,CAAC,GAAG,EAAC,QAAU;gCACvC,QAAQ,CAAC,OAAO,EAAE,CAAC,GAAK,CAAC;wCAEnB,GAAM,EAEC,IAAM;oCAHjB,GAAG,CAAC,KAAK;oCACT,EAAE,IAAE,GAAM,GAAN,CAAC,CAAC,IAAI,cAAN,GAAM,UAAN,CAAY,QAAZ,CAAY,GAAZ,GAAM,CAAE,IAAI,OAAK,UAAY,GAAE,CAAC;wCAClC,KAAK,GAAG,CAAC;oCACX,CAAC,MAAM,EAAE,IAAE,IAAM,GAAN,CAAC,CAAC,IAAI,cAAN,IAAM,UAAN,CAAY,QAAZ,CAAY,GAAZ,IAAM,CAAE,IAAI,OAAK,WAAa,GAAE,CAAC;wCAC1C,KAAK,GAAG,CAAC,CAAC,GAAG,EACX,QAAU;oCAEd,CAAC,MAAM,CAAC;;oCAER,CAAC;oCAED,EAAE,EAAE,sBAAsB,CAAC,KAAK,GAAG,CAAC;wCAClC,aAAa,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK;oCAC9B,CAAC;gCACH,CAAC;4BACH,CAAC;wBACH,CAAC;wBACD,mBAAmB,EAAE,YAAY;wBACjC,kBAAkB,EAAE,YAAY;wBAChC,uBAAuB,EAAE,YAAY;wBACrC,eAAe,EAAE,UAAU;wBAC3B,sBAAsB,EAAE,UAAU;wBAClC,wBAAwB,EAAE,UAAU;wBACpC,sBAAsB,EAAC,eAAe,EAAE,gBAAgB,EAAE,CAAC;4BACzD,KAAK,CAAC,UAAU,GAAG,eAAe,CAAC,GAAG,EAAC,UAAY;4BACnD,EAAE,EAAE,UAAU,CAAC,MAAM,EAAE,CAAC;gCACtB,UAAU,CAAC,OAAO,EAAE,CAAC,GAAK,CAAC;oCACzB,EAAE,EACA,gBAAgB,CACd,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,IAAI,CAAC,QAAQ,IAC1B,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,GACpB,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,EACzB,gBAAgB,GAElB,CAAC;wCACD,CAAC,CAAC,MAAM;oCACV,CAAC;gCACH,CAAC;gCAED,EAAE,EAAE,eAAe,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;oCAC/C,eAAe,CAAC,MAAM;gCACxB,CAAC;;4BAEH,CAAC;4BAED,KAAK,CAAC,IAAI,GAAG,eAAe,CAAC,GAAG,EAAC,WAAa;4BAI9C,EAAE,EAAE,IAAI,IAAI,IAAI,IAAI,IAAI,CAAC,IAAI,IAAI,IAAI,EAAE,CAAC;;4BAExC,CAAC;mCAEO,IAAI,CAAC,IAAI,CAAC,IAAI;sCACf,mBAAqB;oCAAE,CAAC;wCAC3B,KAAK,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,EAAE,CAAE,IAAI;wCAC/B,EAAE,EAAE,gBAAgB,CAAC,IAAI,EAAE,gBAAgB,GAAG,CAAC;4CAC7C,eAAe,CAAC,MAAM;wCACxB,CAAC;;oCAEH,CAAC;sCACI,mBAAqB;oCAAE,CAAC;wCAC3B,KAAK,CAAC,KAAK,GAAG,IAAI,CAAC,GAAG,EACpB,YAAc;wCAEhB,KAAK,CAAC,OAAO,EAAE,CAAC,GAAK,CAAC;4CACpB,EAAE,EAAE,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,MAAK,UAAY,GAAE,CAAC;;4CAEtC,CAAC;4CACD,KAAK,CAAC,IAAI,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI;4CAC3B,EAAE,EAAE,gBAAgB,CAAC,IAAI,EAAE,gBAAgB,GAAG,CAAC;gDAC7C,CAAC,CAAC,MAAM;4CACV,CAAC;wCACH,CAAC;;oCAEH,CAAC;;oCACQ,CAAC;;oCAEV,CAAC;;wBAEL,CAAC;uBAEH,KAAK;oBAGP,EAAE,GAAG,KAAK,CAAC,WAAW,KAAK,KAAK,CAAC,aAAa,EAAE,CAAC;;oBAEjD,CAAC;oBAED,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI;oBACvB,GAAG,CAAC,KAAK;6BAEA,aAAa,CACpB,SAGgD,EAC1C,CAAC;wBACP,KAAK,CAAC,KAAK,GAAG,aAAa,CAAC,SAAS;wBACrC,EAAE,GACA,KAAK,aAAL,KAAK,UAAL,CAAW,QAAX,CAAW,GAAX,KAAK,CAAE,IAAI,KACX,IAAI,CAAC,GAAG,CAAC,KAAK,MACb,sBAAsB,CAAC,KAAK,GAC7B,CAAC;8BACC,KAAK;4BAEP,EAAE,EACA,CAAC,CAAC,sBAAsB,CAAC,SAAS,CAAC,UAAU,KAC7C,CAAC,CAAC,oBAAoB,CAAC,SAAS,CAAC,UAAU,GAC3C,CAAC;gCACD,SAAS,CAAC,UAAU,CAAC,MAAM;4BAC7B,CAAC,MAAM,CAAC;gCACN,SAAS,CAAC,MAAM;4BAClB,CAAC;wBACH,CAAC;oBACH,CAAC;6BAEQ,WAAW,CAClB,SAGiD,EAC3C,CAAC;wBACP,KAAK,CAAC,KAAK,GAAG,SAAS,CAAC,GAAG,EACzB,KAAO;wBAET,EAAE,EAAE,IAAI,CAAC,GAAG,CAAC,KAAK,MAAM,sBAAsB,CAAC,KAAK,GAAG,CAAC;8BACpD,KAAK;4BACP,SAAS,CAAC,MAAM;4BAChB,EAAE,EACC,SAAS,CAAC,MAAM,CAAkC,UAAU,CAC1D,MAAM,KAAK,CAAC,EACf,CAAC;gCACD,SAAS,CAAC,UAAU,CAAC,MAAM;4BAC7B,CAAC;wBACH,CAAC;oBACH,CAAC;uBAEE,CAAC;wBACA,IAAI,CAAC,KAAK,CAAS,KAAK;wBAC1B,KAAK,GAAG,CAAC;wBAET,IAAI,CAAC,QAAQ;4BACX,EAAwC,AAAxC,sCAAwC;4BACxC,kBAAkB,EAAC,YAAY,EAAE,CAAC;gCAChC,EAAE,EAAE,YAAY,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,MAAK,UAAY,GAAE,CAAC;oCAC/C,KAAK,CAAC,KAAK,GAAG,YAAY,CAAC,GAAG,EAC5B,EAAI;oCAEN,EAAE,EAAE,IAAI,CAAC,GAAG,CAAC,KAAK,MAAM,sBAAsB,CAAC,KAAK,GAAG,CAAC;0CACpD,KAAK;wCACP,YAAY,CAAC,MAAM;oCACrB,CAAC;gCACH,CAAC,MAAM,EAAE,EAAE,YAAY,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,MAAK,aAAe,GAAE,CAAC;oCACzD,KAAK,CAAC,OAAO,GAAG,YAAY,CAAC,GAAG,EAC9B,EAAI;oCAGN,KAAK,CAAC,WAAW,GAAG,KAAK;oCACzB,KAAK,CAAC,UAAU,GAAG,OAAO,CAAC,GAAG,EAAC,UAAY;oCAC3C,UAAU,CAAC,OAAO,EAAE,CAAC,GAAK,CAAC;wCACzB,KAAK,CAAC,KAAK,GAAG,CAAC,CAAC,GAAG,CACjB,CAAC,CAAC,IAAI,CAAC,IAAI,MAAK,cAAgB,KAC5B,KAAO,IACP,CAAC,CAAC,IAAI,CAAC,IAAI,MAAK,WAAa,KAC7B,QAAU,eACG,CAAC;4CACZ,KAAK,CAAC,GAAG,CAAC,KAAK,EAAC,SAAW;wCAC7B,CAAC;wCAGP,EAAE,EAAE,IAAI,CAAC,GAAG,CAAC,KAAK,MAAM,sBAAsB,CAAC,KAAK,GAAG,CAAC;8CACpD,KAAK;4CACP,CAAC,CAAC,MAAM;wCACV,CAAC;oCACH,CAAC;oCAED,EAAE,EACA,WAAW,KAAK,KAAK,IACrB,OAAO,CAAC,GAAG,EAAC,UAAY,GAAE,MAAM,GAAG,CAAC,EACpC,CAAC;wCACD,YAAY,CAAC,MAAM;oCACrB,CAAC;gCACH,CAAC,MAAM,EAAE,EAAE,YAAY,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,MAAK,YAAc,GAAE,CAAC;oCACxD,KAAK,CAAC,OAAO,GAAG,YAAY,CAAC,GAAG,EAC9B,EAAI;oCAGN,KAAK,CAAC,WAAW,GAAG,KAAK;oCACzB,KAAK,CAAC,QAAQ,GAAG,OAAO,CAAC,GAAG,EAAC,QAAU;oCACvC,QAAQ,CAAC,OAAO,EAAE,CAAC,GAAK,CAAC;4CAEnB,IAAM,EAEC,IAAM;wCAHjB,GAAG,CAAC,KAAK;wCACT,EAAE,IAAE,IAAM,GAAN,CAAC,CAAC,IAAI,cAAN,IAAM,UAAN,CAAY,QAAZ,CAAY,GAAZ,IAAM,CAAE,IAAI,OAAK,UAAY,GAAE,CAAC;4CAClC,KAAK,GAAG,CAAC;wCACX,CAAC,MAAM,EAAE,IAAE,IAAM,GAAN,CAAC,CAAC,IAAI,cAAN,IAAM,UAAN,CAAY,QAAZ,CAAY,GAAZ,IAAM,CAAE,IAAI,OAAK,WAAa,GAAE,CAAC;4CAC1C,KAAK,GAAG,CAAC,CAAC,GAAG,EACX,QAAU;wCAEd,CAAC,MAAM,CAAC;;wCAER,CAAC;wCAED,EAAE,EAAE,IAAI,CAAC,GAAG,CAAC,KAAK,MAAM,sBAAsB,CAAC,KAAK,GAAG,CAAC;8CACpD,KAAK;4CACP,CAAC,CAAC,MAAM;wCACV,CAAC;oCACH,CAAC;oCAED,EAAE,EACA,WAAW,KAAK,KAAK,IACrB,OAAO,CAAC,GAAG,EAAC,QAAU,GAAE,MAAM,GAAG,CAAC,EAClC,CAAC;wCACD,YAAY,CAAC,MAAM;oCACrB,CAAC;gCACH,CAAC;4BACH,CAAC;4BACD,mBAAmB,EAAE,aAAa;4BAClC,kBAAkB,EAAE,aAAa;4BACjC,uBAAuB,EAAE,aAAa;4BACtC,eAAe,EAAE,WAAW;4BAC5B,sBAAsB,EAAE,WAAW;4BACnC,wBAAwB,EAAE,WAAW;;oBAEzC,CAAC,OAAQ,KAAK;oBAEd,iBAAiB,CAAC,CAAC,EAAE,IAAI,EAAE,KAAK;gBAClC,CAAC;;;;AAIT,CAAC"}