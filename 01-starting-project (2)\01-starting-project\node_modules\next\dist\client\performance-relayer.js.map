{"version": 3, "sources": ["../../client/performance-relayer.ts"], "sourcesContent": ["import {\n  getCLS,\n  getFCP,\n  getFID,\n  getLCP,\n  getTTFB,\n  Metric,\n  ReportHandler,\n} from 'next/dist/compiled/web-vitals'\n\nconst initialHref = location.href\nlet isRegistered = false\nlet userReportHandler: ReportHandler | undefined\n\nfunction onReport(metric: Metric): void {\n  if (userReportHandler) {\n    userReportHandler(metric)\n  }\n\n  // This code is not shipped, executed, or present in the client-side\n  // JavaScript bundle unless explicitly enabled in your application.\n  //\n  // When this feature is enabled, we'll make it very clear by printing a\n  // message during the build (`next build`).\n  if (\n    process.env.NODE_ENV === 'production' &&\n    // This field is empty unless you explicitly configure it:\n    process.env.__NEXT_ANALYTICS_ID\n  ) {\n    const body: Record<string, string> = {\n      dsn: process.env.__NEXT_ANALYTICS_ID,\n      id: metric.id,\n      page: window.__NEXT_DATA__.page,\n      href: initialHref,\n      event_name: metric.name,\n      value: metric.value.toString(),\n      speed:\n        'connection' in navigator &&\n        (navigator as any)['connection'] &&\n        'effectiveType' in (navigator as any)['connection']\n          ? ((navigator as any)['connection']['effectiveType'] as string)\n          : '',\n    }\n\n    const blob = new Blob([new URLSearchParams(body).toString()], {\n      // This content type is necessary for `sendBeacon`:\n      type: 'application/x-www-form-urlencoded',\n    })\n    const vitalsUrl = 'https://vitals.vercel-insights.com/v1/vitals'\n    // Navigator has to be bound to ensure it does not error in some browsers\n    // https://xgwang.me/posts/you-may-not-know-beacon/#it-may-throw-error%2C-be-sure-to-catch\n    const send = navigator.sendBeacon && navigator.sendBeacon.bind(navigator)\n\n    function fallbackSend() {\n      fetch(vitalsUrl, {\n        body: blob,\n        method: 'POST',\n        credentials: 'omit',\n        keepalive: true,\n        // console.error is used here as when the fetch fails it does not affect functioning of the app\n      }).catch(console.error)\n    }\n\n    try {\n      // If send is undefined it'll throw as well. This reduces output code size.\n      send!(vitalsUrl, blob) || fallbackSend()\n    } catch (err) {\n      fallbackSend()\n    }\n  }\n}\n\nexport default (onPerfEntry?: ReportHandler): void => {\n  // Update function if it changes:\n  userReportHandler = onPerfEntry\n\n  // Only register listeners once:\n  if (isRegistered) {\n    return\n  }\n  isRegistered = true\n\n  getCLS(onReport)\n  getFID(onReport)\n  getFCP(onReport)\n  getLCP(onReport)\n  getTTFB(onReport)\n}\n"], "names": [], "mappings": ";;;;;AAQO,GAA+B,CAA/B,UAA+B;AAEtC,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,IAAI;AACjC,GAAG,CAAC,YAAY,GAAG,KAAK;AACxB,GAAG,CAAC,iBAAiB;SAEZ,QAAQ,CAAC,MAAc,EAAQ,CAAC;IACvC,EAAE,EAAE,iBAAiB,EAAE,CAAC;QACtB,iBAAiB,CAAC,MAAM;IAC1B,CAAC;IAED,EAAoE,AAApE,kEAAoE;IACpE,EAAmE,AAAnE,iEAAmE;IACnE,EAAE;IACF,EAAuE,AAAvE,qEAAuE;IACvE,EAA2C,AAA3C,yCAA2C;IAC3C,EAAE,EACA,OAAO,CAAC,GAAG,CAAC,QAAQ,MAAK,UAAY,KACrC,EAA0D,AAA1D,wDAA0D;IAC1D,OAAO,CAAC,GAAG,CAAC,mBAAmB,EAC/B,CAAC;QACD,KAAK,CAAC,IAAI;YACR,GAAG,EAAE,OAAO,CAAC,GAAG,CAAC,mBAAmB;YACpC,EAAE,EAAE,MAAM,CAAC,EAAE;YACb,IAAI,EAAE,MAAM,CAAC,aAAa,CAAC,IAAI;YAC/B,IAAI,EAAE,WAAW;YACjB,UAAU,EAAE,MAAM,CAAC,IAAI;YACvB,KAAK,EAAE,MAAM,CAAC,KAAK,CAAC,QAAQ;YAC5B,KAAK,GACH,UAAY,KAAI,SAAS,IACxB,SAAS,EAAS,UAAY,OAC/B,aAAe,KAAK,SAAS,EAAS,UAAY,KAC5C,SAAS,EAAS,UAAY,IAAE,aAAe;;QAIzD,KAAK,CAAC,IAAI,GAAG,GAAG,CAAC,IAAI;YAAE,GAAG,CAAC,eAAe,CAAC,IAAI,EAAE,QAAQ;;YACvD,EAAmD,AAAnD,iDAAmD;YACnD,IAAI,GAAE,iCAAmC;;QAE3C,KAAK,CAAC,SAAS,IAAG,4CAA8C;QAChE,EAAyE,AAAzE,uEAAyE;QACzE,EAA0F,AAA1F,wFAA0F;QAC1F,KAAK,CAAC,IAAI,GAAG,SAAS,CAAC,UAAU,IAAI,SAAS,CAAC,UAAU,CAAC,IAAI,CAAC,SAAS;iBAE/D,YAAY,GAAG,CAAC;YACvB,KAAK,CAAC,SAAS;gBACb,IAAI,EAAE,IAAI;gBACV,MAAM,GAAE,IAAM;gBACd,WAAW,GAAE,IAAM;gBACnB,SAAS,EAAE,IAAI;eAEd,KAAK,CAAC,OAAO,CAAC,KAAK;QACxB,CAAC;YAEG,CAAC;YACH,EAA2E,AAA3E,yEAA2E;YAC3E,IAAI,CAAE,SAAS,EAAE,IAAI,KAAK,YAAY;QACxC,CAAC,QAAQ,GAAG,EAAE,CAAC;YACb,YAAY;QACd,CAAC;IACH,CAAC;AACH,CAAC;gBAEe,WAA2B,GAAW,CAAC;IACrD,EAAiC,AAAjC,+BAAiC;IACjC,iBAAiB,GAAG,WAAW;IAE/B,EAAgC,AAAhC,8BAAgC;IAChC,EAAE,EAAE,YAAY,EAAE,CAAC;;IAEnB,CAAC;IACD,YAAY,GAAG,IAAI;QAxEd,UAA+B,SA0E7B,QAAQ;QA1EV,UAA+B,SA2E7B,QAAQ;QA3EV,UAA+B,SA4E7B,QAAQ;QA5EV,UAA+B,SA6E7B,QAAQ;QA7EV,UAA+B,UA8E5B,QAAQ;AAClB,CAAC"}