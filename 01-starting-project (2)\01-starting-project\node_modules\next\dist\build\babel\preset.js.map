{"version": 3, "sources": ["../../../build/babel/preset.ts"], "sourcesContent": ["import { PluginItem } from 'next/dist/compiled/babel/core'\nimport { dirname } from 'path'\n\nconst isLoadIntentTest = process.env.NODE_ENV === 'test'\nconst isLoadIntentDevelopment = process.env.NODE_ENV === 'development'\n\ntype StyledJsxPlugin = [string, any] | string\ntype StyledJsxBabelOptions =\n  | {\n      plugins?: StyledJsxPlugin[]\n      'babel-test'?: boolean\n    }\n  | undefined\n\n// Resolve styled-jsx plugins\nfunction styledJsxOptions(options: StyledJsxBabelOptions) {\n  if (!options) {\n    return {}\n  }\n\n  if (!Array.isArray(options.plugins)) {\n    return options\n  }\n\n  options.plugins = options.plugins.map(\n    (plugin: StyledJsxPlugin): StyledJsxPlugin => {\n      if (Array.isArray(plugin)) {\n        const [name, pluginOptions] = plugin\n        return [require.resolve(name), pluginOptions]\n      }\n\n      return require.resolve(plugin)\n    }\n  )\n\n  return options\n}\n\ntype NextBabelPresetOptions = {\n  'preset-env'?: any\n  'preset-react'?: any\n  'class-properties'?: any\n  'transform-runtime'?: any\n  'styled-jsx'?: StyledJsxBabelOptions\n  'preset-typescript'?: any\n}\n\ntype BabelPreset = {\n  presets?: PluginItem[] | null\n  plugins?: PluginItem[] | null\n  sourceType?: 'script' | 'module' | 'unambiguous'\n  overrides?: Array<{ test: RegExp } & Omit<BabelPreset, 'overrides'>>\n}\n\n// Taken from https://github.com/babel/babel/commit/d60c5e1736543a6eac4b549553e107a9ba967051#diff-b4beead8ad9195361b4537601cc22532R158\nfunction supportsStaticESM(caller: any): boolean {\n  return !!caller?.supportsStaticESM\n}\n\nexport default (\n  api: any,\n  options: NextBabelPresetOptions = {}\n): BabelPreset => {\n  const supportsESM = api.caller(supportsStaticESM)\n  const isServer = api.caller((caller: any) => !!caller && caller.isServer)\n  const isCallerDevelopment = api.caller((caller: any) => caller?.isDev)\n\n  // Look at external intent if used without a caller (e.g. via Jest):\n  const isTest = isCallerDevelopment == null && isLoadIntentTest\n\n  // Look at external intent if used without a caller (e.g. Storybook):\n  const isDevelopment =\n    isCallerDevelopment === true ||\n    (isCallerDevelopment == null && isLoadIntentDevelopment)\n\n  // Default to production mode if not `test` nor `development`:\n  const isProduction = !(isTest || isDevelopment)\n\n  const isBabelLoader = api.caller(\n    (caller: any) =>\n      !!caller &&\n      (caller.name === 'babel-loader' ||\n        caller.name === 'next-babel-turbo-loader')\n  )\n\n  const useJsxRuntime =\n    options['preset-react']?.runtime === 'automatic' ||\n    (Boolean(api.caller((caller: any) => !!caller && caller.hasJsxRuntime)) &&\n      options['preset-react']?.runtime !== 'classic')\n\n  const presetEnvConfig = {\n    // In the test environment `modules` is often needed to be set to true, babel figures that out by itself using the `'auto'` option\n    // In production/development this option is set to `false` so that webpack can handle import/export with tree-shaking\n    modules: 'auto',\n    exclude: ['transform-typeof-symbol'],\n    include: [\n      '@babel/plugin-proposal-optional-chaining',\n      '@babel/plugin-proposal-nullish-coalescing-operator',\n    ],\n    ...options['preset-env'],\n  }\n\n  // When transpiling for the server or tests, target the current Node version\n  // if not explicitly specified:\n  if (\n    (isServer || isTest) &&\n    (!presetEnvConfig.targets ||\n      !(\n        typeof presetEnvConfig.targets === 'object' &&\n        'node' in presetEnvConfig.targets\n      ))\n  ) {\n    presetEnvConfig.targets = {\n      // Targets the current process' version of Node. This requires apps be\n      // built and deployed on the same version of Node.\n      // This is the same as using \"current\" but explicit\n      node: process.versions.node,\n    }\n  }\n\n  return {\n    sourceType: 'unambiguous',\n    presets: [\n      [require('next/dist/compiled/babel/preset-env'), presetEnvConfig],\n      [\n        require('next/dist/compiled/babel/preset-react'),\n        {\n          // This adds @babel/plugin-transform-react-jsx-source and\n          // @babel/plugin-transform-react-jsx-self automatically in development\n          development: isDevelopment || isTest,\n          ...(useJsxRuntime ? { runtime: 'automatic' } : { pragma: '__jsx' }),\n          ...options['preset-react'],\n        },\n      ],\n      [\n        require('next/dist/compiled/babel/preset-typescript'),\n        { allowNamespaces: true, ...options['preset-typescript'] },\n      ],\n    ],\n    plugins: [\n      !useJsxRuntime && [\n        require('./plugins/jsx-pragma'),\n        {\n          // This produces the following injected import for modules containing JSX:\n          //   import React from 'react';\n          //   var __jsx = React.createElement;\n          module: 'react',\n          importAs: 'React',\n          pragma: '__jsx',\n          property: 'createElement',\n        },\n      ],\n      [\n        require('./plugins/optimize-hook-destructuring'),\n        {\n          // only optimize hook functions imported from React/Preact\n          lib: true,\n        },\n      ],\n      require('next/dist/compiled/babel/plugin-syntax-dynamic-import'),\n      require('./plugins/react-loadable-plugin'),\n      [\n        require('next/dist/compiled/babel/plugin-proposal-class-properties'),\n        options['class-properties'] || {},\n      ],\n      [\n        require('next/dist/compiled/babel/plugin-proposal-object-rest-spread'),\n        {\n          useBuiltIns: true,\n        },\n      ],\n      !isServer && [\n        require('next/dist/compiled/babel/plugin-transform-runtime'),\n        {\n          corejs: false,\n          helpers: true,\n          regenerator: true,\n          useESModules: supportsESM && presetEnvConfig.modules !== 'commonjs',\n          absoluteRuntime: isBabelLoader\n            ? dirname(require.resolve('@babel/runtime/package.json'))\n            : undefined,\n          ...options['transform-runtime'],\n        },\n      ],\n      [\n        isTest && options['styled-jsx'] && options['styled-jsx']['babel-test']\n          ? require('styled-jsx/babel-test')\n          : require('styled-jsx/babel'),\n        styledJsxOptions(options['styled-jsx']),\n      ],\n      require('./plugins/amp-attributes'),\n      isProduction && [\n        require('next/dist/compiled/babel/plugin-transform-react-remove-prop-types'),\n        {\n          removeImport: true,\n        },\n      ],\n      isServer && require('next/dist/compiled/babel/plugin-syntax-bigint'),\n      // Always compile numeric separator because the resulting number is\n      // smaller.\n      require('next/dist/compiled/babel/plugin-proposal-numeric-separator'),\n      require('next/dist/compiled/babel/plugin-proposal-export-namespace-from'),\n    ].filter(Boolean),\n  }\n}\n"], "names": [], "mappings": ";;;;;AACwB,GAAM,CAAN,KAAM;AAE9B,KAAK,CAAC,gBAAgB,GAAG,OAAO,CAAC,GAAG,CAAC,QAAQ,MAAK,IAAM;AACxD,KAAK,CAAC,uBAAuB,GAAG,OAAO,CAAC,GAAG,CAAC,QAAQ,MAAK,WAAa;AAUtE,EAA6B,AAA7B,2BAA6B;SACpB,gBAAgB,CAAC,OAA8B,EAAE,CAAC;IACzD,EAAE,GAAG,OAAO,EAAE,CAAC;;;IAEf,CAAC;IAED,EAAE,GAAG,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,OAAO,GAAG,CAAC;eAC7B,OAAO;IAChB,CAAC;IAED,OAAO,CAAC,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC,GAAG,EAClC,MAAuB,GAAsB,CAAC;QAC7C,EAAE,EAAE,KAAK,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC;YAC1B,KAAK,EAAE,IAAI,EAAE,aAAa,IAAI,MAAM;;gBAC5B,OAAO,CAAC,OAAO,CAAC,IAAI;gBAAG,aAAa;;QAC9C,CAAC;eAEM,OAAO,CAAC,OAAO,CAAC,MAAM;IAC/B,CAAC;WAGI,OAAO;AAChB,CAAC;AAkBD,EAAsI,AAAtI,oIAAsI;SAC7H,iBAAiB,CAAC,MAAW,EAAW,CAAC;cACvC,MAAM,aAAN,MAAM,UAAN,CAAyB,QAAzB,CAAyB,GAAzB,MAAM,CAAE,iBAAiB;AACpC,CAAC;gBAGC,GAAQ,EACR,OAA+B;IACf,CAAC;QAwBf,GAAuB,EAErB,IAAuB;IAzB3B,KAAK,CAAC,WAAW,GAAG,GAAG,CAAC,MAAM,CAAC,iBAAiB;IAChD,KAAK,CAAC,QAAQ,GAAG,GAAG,CAAC,MAAM,EAAE,MAAW,KAAO,MAAM,IAAI,MAAM,CAAC,QAAQ;;IACxE,KAAK,CAAC,mBAAmB,GAAG,GAAG,CAAC,MAAM,EAAE,MAAW;eAAK,MAAM,aAAN,MAAM,UAAN,CAAa,QAAb,CAAa,GAAb,MAAM,CAAE,KAAK;;IAErE,EAAoE,AAApE,kEAAoE;IACpE,KAAK,CAAC,MAAM,GAAG,mBAAmB,IAAI,IAAI,IAAI,gBAAgB;IAE9D,EAAqE,AAArE,mEAAqE;IACrE,KAAK,CAAC,aAAa,GACjB,mBAAmB,KAAK,IAAI,IAC3B,mBAAmB,IAAI,IAAI,IAAI,uBAAuB;IAEzD,EAA8D,AAA9D,4DAA8D;IAC9D,KAAK,CAAC,YAAY,KAAK,MAAM,IAAI,aAAa;IAE9C,KAAK,CAAC,aAAa,GAAG,GAAG,CAAC,MAAM,EAC7B,MAAW,KACR,MAAM,KACP,MAAM,CAAC,IAAI,MAAK,YAAc,KAC7B,MAAM,CAAC,IAAI,MAAK,uBAAyB;;IAG/C,KAAK,CAAC,aAAa,KACjB,GAAuB,GAAvB,OAAO,EAAC,YAAc,gBAAtB,GAAuB,UAAvB,CAAgC,QAAhC,CAAgC,GAAhC,GAAuB,CAAE,OAAO,OAAK,SAAW,KAC/C,OAAO,CAAC,GAAG,CAAC,MAAM,EAAE,MAAW,KAAO,MAAM,IAAI,MAAM,CAAC,aAAa;YACnE,IAAuB,GAAvB,OAAO,EAAC,YAAc,gBAAtB,IAAuB,UAAvB,CAAgC,QAAhC,CAAgC,GAAhC,IAAuB,CAAE,OAAO,OAAK,OAAS;IAElD,KAAK,CAAC,eAAe;QACnB,EAAkI,AAAlI,gIAAkI;QAClI,EAAqH,AAArH,mHAAqH;QACrH,OAAO,GAAE,IAAM;QACf,OAAO;aAAG,uBAAyB;;QACnC,OAAO;aACL,wCAA0C;aAC1C,kDAAoD;;WAEnD,OAAO,EAAC,UAAY;;IAGzB,EAA4E,AAA5E,0EAA4E;IAC5E,EAA+B,AAA/B,6BAA+B;IAC/B,EAAE,GACC,QAAQ,IAAI,MAAM,OACjB,eAAe,CAAC,OAAO,aAEd,eAAe,CAAC,OAAO,MAAK,MAAQ,MAC3C,IAAM,KAAI,eAAe,CAAC,OAAO,IAErC,CAAC;QACD,eAAe,CAAC,OAAO;YACrB,EAAsE,AAAtE,oEAAsE;YACtE,EAAkD,AAAlD,gDAAkD;YAClD,EAAmD,AAAnD,iDAAmD;YACnD,IAAI,EAAE,OAAO,CAAC,QAAQ,CAAC,IAAI;;IAE/B,CAAC;;QAGC,UAAU,GAAE,WAAa;QACzB,OAAO;;gBACJ,OAAO,EAAC,mCAAqC;gBAAG,eAAe;;;gBAE9D,OAAO,EAAC,qCAAuC;;oBAE7C,EAAyD,AAAzD,uDAAyD;oBACzD,EAAsE,AAAtE,oEAAsE;oBACtE,WAAW,EAAE,aAAa,IAAI,MAAM;uBAChC,aAAa;wBAAK,OAAO,GAAE,SAAW;;wBAAO,MAAM,GAAE,KAAO;;uBAC7D,OAAO,EAAC,YAAc;;;;gBAI3B,OAAO,EAAC,0CAA4C;;oBAClD,eAAe,EAAE,IAAI;uBAAK,OAAO,EAAC,iBAAmB;;;;QAG3D,OAAO;aACJ,aAAa;gBACZ,OAAO,EAAC,oBAAsB;;oBAE5B,EAA0E,AAA1E,wEAA0E;oBAC1E,EAA+B,AAA/B,6BAA+B;oBAC/B,EAAqC,AAArC,mCAAqC;oBACrC,MAAM,GAAE,KAAO;oBACf,QAAQ,GAAE,KAAO;oBACjB,MAAM,GAAE,KAAO;oBACf,QAAQ,GAAE,aAAe;;;;gBAI3B,OAAO,EAAC,qCAAuC;;oBAE7C,EAA0D,AAA1D,wDAA0D;oBAC1D,GAAG,EAAE,IAAI;;;YAGb,OAAO,EAAC,qDAAuD;YAC/D,OAAO,EAAC,+BAAiC;;gBAEvC,OAAO,EAAC,yDAA2D;gBACnE,OAAO,EAAC,gBAAkB;;;;gBAG1B,OAAO,EAAC,2DAA6D;;oBAEnE,WAAW,EAAE,IAAI;;;aAGpB,QAAQ;gBACP,OAAO,EAAC,iDAAmD;;oBAEzD,MAAM,EAAE,KAAK;oBACb,OAAO,EAAE,IAAI;oBACb,WAAW,EAAE,IAAI;oBACjB,YAAY,EAAE,WAAW,IAAI,eAAe,CAAC,OAAO,MAAK,QAAU;oBACnE,eAAe,EAAE,aAAa,OAjLhB,KAAM,UAkLR,OAAO,CAAC,OAAO,EAAC,2BAA6B,MACrD,SAAS;uBACV,OAAO,EAAC,iBAAmB;;;;gBAIhC,MAAM,IAAI,OAAO,EAAC,UAAY,MAAK,OAAO,EAAC,UAAY,IAAE,UAAY,KACjE,OAAO,EAAC,qBAAuB,KAC/B,OAAO,EAAC,gBAAkB;gBAC9B,gBAAgB,CAAC,OAAO,EAAC,UAAY;;YAEvC,OAAO,EAAC,wBAA0B;YAClC,YAAY;gBACV,OAAO,EAAC,iEAAmE;;oBAEzE,YAAY,EAAE,IAAI;;;YAGtB,QAAQ,IAAI,OAAO,EAAC,6CAA+C;YACnE,EAAmE,AAAnE,iEAAmE;YACnE,EAAW,AAAX,SAAW;YACX,OAAO,EAAC,0DAA4D;YACpE,OAAO,EAAC,8DAAgE;UACxE,MAAM,CAAC,OAAO;;AAEpB,CAAC"}