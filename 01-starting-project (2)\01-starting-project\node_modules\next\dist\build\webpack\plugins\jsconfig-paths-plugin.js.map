{"version": 3, "sources": ["../../../../build/webpack/plugins/jsconfig-paths-plugin.ts"], "sourcesContent": ["/**\n * This webpack resolver is largely based on TypeScript's \"paths\" handling\n * The TypeScript license can be found here:\n * https://github.com/microsoft/TypeScript/blob/214df64e287804577afa1fea0184c18c40f7d1ca/LICENSE.txt\n */\nimport path from 'path'\nimport { webpack } from 'next/dist/compiled/webpack/webpack'\nimport { debug } from 'next/dist/compiled/debug'\n\nconst log = debug('next:jsconfig-paths-plugin')\n\nexport interface Pattern {\n  prefix: string\n  suffix: string\n}\n\nconst asterisk = 0x2a\n\nexport function hasZeroOrOneAsteriskCharacter(str: string): boolean {\n  let seenAsterisk = false\n  for (let i = 0; i < str.length; i++) {\n    if (str.charCodeAt(i) === asterisk) {\n      if (!seenAsterisk) {\n        seenAsterisk = true\n      } else {\n        // have already seen asterisk\n        return false\n      }\n    }\n  }\n  return true\n}\n\n/**\n * Determines whether a path starts with a relative path component (i.e. `.` or `..`).\n */\nexport function pathIsRelative(testPath: string): boolean {\n  return /^\\.\\.?($|[\\\\/])/.test(testPath)\n}\n\nexport function tryParsePattern(pattern: string): Pattern | undefined {\n  // This should be verified outside of here and a proper error thrown.\n  const indexOfStar = pattern.indexOf('*')\n  return indexOfStar === -1\n    ? undefined\n    : {\n        prefix: pattern.substr(0, indexOfStar),\n        suffix: pattern.substr(indexOfStar + 1),\n      }\n}\n\nfunction isPatternMatch({ prefix, suffix }: Pattern, candidate: string) {\n  return (\n    candidate.length >= prefix.length + suffix.length &&\n    candidate.startsWith(prefix) &&\n    candidate.endsWith(suffix)\n  )\n}\n\n/** Return the object corresponding to the best pattern to match `candidate`. */\nexport function findBestPatternMatch<T>(\n  values: readonly T[],\n  getPattern: (value: T) => Pattern,\n  candidate: string\n): T | undefined {\n  let matchedValue: T | undefined\n  // use length of prefix as betterness criteria\n  let longestMatchPrefixLength = -1\n\n  for (const v of values) {\n    const pattern = getPattern(v)\n    if (\n      isPatternMatch(pattern, candidate) &&\n      pattern.prefix.length > longestMatchPrefixLength\n    ) {\n      longestMatchPrefixLength = pattern.prefix.length\n      matchedValue = v\n    }\n  }\n\n  return matchedValue\n}\n\n/**\n * patternStrings contains both pattern strings (containing \"*\") and regular strings.\n * Return an exact match if possible, or a pattern match, or undefined.\n * (These are verified by verifyCompilerOptions to have 0 or 1 \"*\" characters.)\n */\nexport function matchPatternOrExact(\n  patternStrings: readonly string[],\n  candidate: string\n): string | Pattern | undefined {\n  const patterns: Pattern[] = []\n  for (const patternString of patternStrings) {\n    if (!hasZeroOrOneAsteriskCharacter(patternString)) continue\n    const pattern = tryParsePattern(patternString)\n    if (pattern) {\n      patterns.push(pattern)\n    } else if (patternString === candidate) {\n      // pattern was matched as is - no need to search further\n      return patternString\n    }\n  }\n\n  return findBestPatternMatch(patterns, (_) => _, candidate)\n}\n\n/**\n * Tests whether a value is string\n */\nexport function isString(text: unknown): text is string {\n  return typeof text === 'string'\n}\n\n/**\n * Given that candidate matches pattern, returns the text matching the '*'.\n * E.g.: matchedText(tryParsePattern(\"foo*baz\"), \"foobarbaz\") === \"bar\"\n */\nexport function matchedText(pattern: Pattern, candidate: string): string {\n  return candidate.substring(\n    pattern.prefix.length,\n    candidate.length - pattern.suffix.length\n  )\n}\n\nexport function patternText({ prefix, suffix }: Pattern): string {\n  return `${prefix}*${suffix}`\n}\n\nconst NODE_MODULES_REGEX = /node_modules/\n\ntype Paths = { [match: string]: string[] }\n\n/**\n * Handles tsconfig.json or jsconfig.js \"paths\" option for webpack\n * Largely based on how the TypeScript compiler handles it:\n * https://github.com/microsoft/TypeScript/blob/1a9c8197fffe3dace5f8dca6633d450a88cba66d/src/compiler/moduleNameResolver.ts#L1362\n */\nexport class JsConfigPathsPlugin implements webpack.ResolvePlugin {\n  paths: Paths\n  resolvedBaseUrl: string\n  constructor(paths: Paths, resolvedBaseUrl: string) {\n    this.paths = paths\n    this.resolvedBaseUrl = resolvedBaseUrl\n    log('tsconfig.json or jsconfig.json paths: %O', paths)\n    log('resolved baseUrl: %s', resolvedBaseUrl)\n  }\n  apply(resolver: any) {\n    const paths = this.paths\n    const pathsKeys = Object.keys(paths)\n\n    // If no aliases are added bail out\n    if (pathsKeys.length === 0) {\n      log('paths are empty, bailing out')\n      return\n    }\n\n    const baseDirectory = this.resolvedBaseUrl\n    const target = resolver.ensureHook('resolve')\n    resolver\n      .getHook('described-resolve')\n      .tapPromise(\n        'JsConfigPathsPlugin',\n        async (request: any, resolveContext: any) => {\n          const moduleName = request.request\n\n          // Exclude node_modules from paths support (speeds up resolving)\n          if (request.path.match(NODE_MODULES_REGEX)) {\n            log('skipping request as it is inside node_modules %s', moduleName)\n            return\n          }\n\n          if (\n            path.posix.isAbsolute(moduleName) ||\n            (process.platform === 'win32' && path.win32.isAbsolute(moduleName))\n          ) {\n            log('skipping request as it is an absolute path %s', moduleName)\n            return\n          }\n\n          if (pathIsRelative(moduleName)) {\n            log('skipping request as it is a relative path %s', moduleName)\n            return\n          }\n\n          // log('starting to resolve request %s', moduleName)\n\n          // If the module name does not match any of the patterns in `paths` we hand off resolving to webpack\n          const matchedPattern = matchPatternOrExact(pathsKeys, moduleName)\n          if (!matchedPattern) {\n            log('moduleName did not match any paths pattern %s', moduleName)\n            return\n          }\n\n          const matchedStar = isString(matchedPattern)\n            ? undefined\n            : matchedText(matchedPattern, moduleName)\n          const matchedPatternText = isString(matchedPattern)\n            ? matchedPattern\n            : patternText(matchedPattern)\n\n          let triedPaths = []\n\n          for (const subst of paths[matchedPatternText]) {\n            const curPath = matchedStar\n              ? subst.replace('*', matchedStar)\n              : subst\n\n            // Ensure .d.ts is not matched\n            if (curPath.endsWith('.d.ts')) {\n              continue\n            }\n\n            const candidate = path.join(baseDirectory, curPath)\n            const [err, result] = await new Promise((resolve) => {\n              const obj = Object.assign({}, request, {\n                request: candidate,\n              })\n              resolver.doResolve(\n                target,\n                obj,\n                `Aliased with tsconfig.json or jsconfig.json ${matchedPatternText} to ${candidate}`,\n                resolveContext,\n                (resolverErr: any, resolverResult: any | undefined) => {\n                  resolve([resolverErr, resolverResult])\n                }\n              )\n            })\n\n            // There's multiple paths values possible, so we first have to iterate them all first before throwing an error\n            if (err || result === undefined) {\n              triedPaths.push(candidate)\n              continue\n            }\n\n            return result\n          }\n        }\n      )\n  }\n}\n"], "names": [], "mappings": ";;;;QAkBgB,6BAA6B,GAA7B,6BAA6B;QAkB7B,cAAc,GAAd,cAAc;QAId,eAAe,GAAf,eAAe;QAoBf,oBAAoB,GAApB,oBAAoB;QA4BpB,mBAAmB,GAAnB,mBAAmB;QAsBnB,QAAQ,GAAR,QAAQ;QAQR,WAAW,GAAX,WAAW;QAOX,WAAW,GAAX,WAAW;AAxHV,GAAM,CAAN,KAAM;AAED,GAA0B,CAA1B,MAA0B;;;;;;AAEhD,KAAK,CAAC,GAAG,OAFa,MAA0B,SAE9B,0BAA4B;AAO9C,KAAK,CAAC,QAAQ,GAAG,EAAI;SAEL,6BAA6B,CAAC,GAAW,EAAW,CAAC;IACnE,GAAG,CAAC,YAAY,GAAG,KAAK;QACnB,GAAG,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,MAAM,EAAE,CAAC,GAAI,CAAC;QACpC,EAAE,EAAE,GAAG,CAAC,UAAU,CAAC,CAAC,MAAM,QAAQ,EAAE,CAAC;YACnC,EAAE,GAAG,YAAY,EAAE,CAAC;gBAClB,YAAY,GAAG,IAAI;YACrB,CAAC,MAAM,CAAC;gBACN,EAA6B,AAA7B,2BAA6B;uBACtB,KAAK;YACd,CAAC;QACH,CAAC;IACH,CAAC;WACM,IAAI;AACb,CAAC;SAKe,cAAc,CAAC,QAAgB,EAAW,CAAC;6BAChC,IAAI,CAAC,QAAQ;AACxC,CAAC;SAEe,eAAe,CAAC,OAAe,EAAuB,CAAC;IACrE,EAAqE,AAArE,mEAAqE;IACrE,KAAK,CAAC,WAAW,GAAG,OAAO,CAAC,OAAO,EAAC,CAAG;WAChC,WAAW,MAAM,CAAC,GACrB,SAAS;QAEP,MAAM,EAAE,OAAO,CAAC,MAAM,CAAC,CAAC,EAAE,WAAW;QACrC,MAAM,EAAE,OAAO,CAAC,MAAM,CAAC,WAAW,GAAG,CAAC;;AAE9C,CAAC;SAEQ,cAAc,GAAG,MAAM,GAAE,MAAM,KAAa,SAAiB,EAAE,CAAC;WAErE,SAAS,CAAC,MAAM,IAAI,MAAM,CAAC,MAAM,GAAG,MAAM,CAAC,MAAM,IACjD,SAAS,CAAC,UAAU,CAAC,MAAM,KAC3B,SAAS,CAAC,QAAQ,CAAC,MAAM;AAE7B,CAAC;SAGe,oBAAoB,CAClC,MAAoB,EACpB,UAAiC,EACjC,SAAiB,EACF,CAAC;IAChB,GAAG,CAAC,YAAY;IAChB,EAA8C,AAA9C,4CAA8C;IAC9C,GAAG,CAAC,wBAAwB,IAAI,CAAC;SAE5B,KAAK,CAAC,CAAC,IAAI,MAAM,CAAE,CAAC;QACvB,KAAK,CAAC,OAAO,GAAG,UAAU,CAAC,CAAC;QAC5B,EAAE,EACA,cAAc,CAAC,OAAO,EAAE,SAAS,KACjC,OAAO,CAAC,MAAM,CAAC,MAAM,GAAG,wBAAwB,EAChD,CAAC;YACD,wBAAwB,GAAG,OAAO,CAAC,MAAM,CAAC,MAAM;YAChD,YAAY,GAAG,CAAC;QAClB,CAAC;IACH,CAAC;WAEM,YAAY;AACrB,CAAC;SAOe,mBAAmB,CACjC,cAAiC,EACjC,SAAiB,EACa,CAAC;IAC/B,KAAK,CAAC,QAAQ;SACT,KAAK,CAAC,aAAa,IAAI,cAAc,CAAE,CAAC;QAC3C,EAAE,GAAG,6BAA6B,CAAC,aAAa;QAChD,KAAK,CAAC,OAAO,GAAG,eAAe,CAAC,aAAa;QAC7C,EAAE,EAAE,OAAO,EAAE,CAAC;YACZ,QAAQ,CAAC,IAAI,CAAC,OAAO;QACvB,CAAC,MAAM,EAAE,EAAE,aAAa,KAAK,SAAS,EAAE,CAAC;YACvC,EAAwD,AAAxD,sDAAwD;mBACjD,aAAa;QACtB,CAAC;IACH,CAAC;WAEM,oBAAoB,CAAC,QAAQ,GAAG,CAAC,GAAK,CAAC;MAAE,SAAS;AAC3D,CAAC;SAKe,QAAQ,CAAC,IAAa,EAAkB,CAAC;kBACzC,IAAI,MAAK,MAAQ;AACjC,CAAC;SAMe,WAAW,CAAC,OAAgB,EAAE,SAAiB,EAAU,CAAC;WACjE,SAAS,CAAC,SAAS,CACxB,OAAO,CAAC,MAAM,CAAC,MAAM,EACrB,SAAS,CAAC,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC,MAAM;AAE5C,CAAC;SAEe,WAAW,GAAG,MAAM,GAAE,MAAM,KAAqB,CAAC;cACtD,MAAM,CAAC,CAAC,EAAE,MAAM;AAC5B,CAAC;AAED,KAAK,CAAC,kBAAkB;MASX,mBAAmB;gBAGlB,KAAY,EAAE,eAAuB,CAAE,CAAC;aAC7C,KAAK,GAAG,KAAK;aACb,eAAe,GAAG,eAAe;QACtC,GAAG,EAAC,wCAA0C,GAAE,KAAK;QACrD,GAAG,EAAC,oBAAsB,GAAE,eAAe;IAC7C,CAAC;IACD,KAAK,CAAC,QAAa,EAAE,CAAC;QACpB,KAAK,CAAC,MAAK,QAAQ,KAAK;QACxB,KAAK,CAAC,SAAS,GAAG,MAAM,CAAC,IAAI,CAAC,MAAK;QAEnC,EAAmC,AAAnC,iCAAmC;QACnC,EAAE,EAAE,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC3B,GAAG,EAAC,4BAA8B;;QAEpC,CAAC;QAED,KAAK,CAAC,aAAa,QAAQ,eAAe;QAC1C,KAAK,CAAC,MAAM,GAAG,QAAQ,CAAC,UAAU,EAAC,OAAS;QAC5C,QAAQ,CACL,OAAO,EAAC,iBAAmB,GAC3B,UAAU,EACT,mBAAqB,UACd,OAAY,EAAE,cAAmB,GAAK,CAAC;YAC5C,KAAK,CAAC,UAAU,GAAG,OAAO,CAAC,OAAO;YAElC,EAAgE,AAAhE,8DAAgE;YAChE,EAAE,EAAE,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,kBAAkB,GAAG,CAAC;gBAC3C,GAAG,EAAC,gDAAkD,GAAE,UAAU;;YAEpE,CAAC;YAED,EAAE,EAvKK,KAAM,SAwKN,KAAK,CAAC,UAAU,CAAC,UAAU,KAC/B,OAAO,CAAC,QAAQ,MAAK,KAAO,KAzKxB,KAAM,SAyK2B,KAAK,CAAC,UAAU,CAAC,UAAU,GACjE,CAAC;gBACD,GAAG,EAAC,6CAA+C,GAAE,UAAU;;YAEjE,CAAC;YAED,EAAE,EAAE,cAAc,CAAC,UAAU,GAAG,CAAC;gBAC/B,GAAG,EAAC,4CAA8C,GAAE,UAAU;;YAEhE,CAAC;YAED,EAAoD,AAApD,kDAAoD;YAEpD,EAAoG,AAApG,kGAAoG;YACpG,KAAK,CAAC,cAAc,GAAG,mBAAmB,CAAC,SAAS,EAAE,UAAU;YAChE,EAAE,GAAG,cAAc,EAAE,CAAC;gBACpB,GAAG,EAAC,6CAA+C,GAAE,UAAU;;YAEjE,CAAC;YAED,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,cAAc,IACvC,SAAS,GACT,WAAW,CAAC,cAAc,EAAE,UAAU;YAC1C,KAAK,CAAC,kBAAkB,GAAG,QAAQ,CAAC,cAAc,IAC9C,cAAc,GACd,WAAW,CAAC,cAAc;YAE9B,GAAG,CAAC,UAAU;iBAET,KAAK,CAAC,KAAK,IAAI,MAAK,CAAC,kBAAkB,EAAG,CAAC;gBAC9C,KAAK,CAAC,OAAO,GAAG,WAAW,GACvB,KAAK,CAAC,OAAO,EAAC,CAAG,GAAE,WAAW,IAC9B,KAAK;gBAET,EAA8B,AAA9B,4BAA8B;gBAC9B,EAAE,EAAE,OAAO,CAAC,QAAQ,EAAC,KAAO,IAAG,CAAC;;gBAEhC,CAAC;gBAED,KAAK,CAAC,SAAS,GAhNV,KAAM,SAgNY,IAAI,CAAC,aAAa,EAAE,OAAO;gBAClD,KAAK,EAAE,GAAG,EAAE,MAAM,UAAU,GAAG,CAAC,OAAO,EAAE,OAAO,GAAK,CAAC;oBACpD,KAAK,CAAC,GAAG,GAAG,MAAM,CAAC,MAAM;uBAAK,OAAO;wBACnC,OAAO,EAAE,SAAS;;oBAEpB,QAAQ,CAAC,SAAS,CAChB,MAAM,EACN,GAAG,GACF,4CAA4C,EAAE,kBAAkB,CAAC,IAAI,EAAE,SAAS,IACjF,cAAc,GACb,WAAgB,EAAE,cAA+B,GAAK,CAAC;wBACtD,OAAO;4BAAE,WAAW;4BAAE,cAAc;;oBACtC,CAAC;gBAEL,CAAC;gBAED,EAA8G,AAA9G,4GAA8G;gBAC9G,EAAE,EAAE,GAAG,IAAI,MAAM,KAAK,SAAS,EAAE,CAAC;oBAChC,UAAU,CAAC,IAAI,CAAC,SAAS;;gBAE3B,CAAC;uBAEM,MAAM;YACf,CAAC;QACH,CAAC;IAEP,CAAC;;QArGU,mBAAmB,GAAnB,mBAAmB"}