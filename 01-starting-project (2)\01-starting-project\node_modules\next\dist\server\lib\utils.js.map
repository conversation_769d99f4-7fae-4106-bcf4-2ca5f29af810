{"version": 3, "sources": ["../../../server/lib/utils.ts"], "sourcesContent": ["export function printAndExit(message: string, code = 1) {\n  if (code === 0) {\n    console.log(message)\n  } else {\n    console.error(message)\n  }\n\n  process.exit(code)\n}\n\nexport function getNodeOptionsWithoutInspect() {\n  const NODE_INSPECT_RE = /--inspect(-brk)?(=\\S+)?( |$)/\n  return (process.env.NODE_OPTIONS || '').replace(NODE_INSPECT_RE, '')\n}\n"], "names": [], "mappings": ";;;;QAAgB,YAAY,GAAZ,YAAY;QAUZ,4BAA4B,GAA5B,4BAA4B;SAV5B,YAAY,CAAC,OAAe,EAAE,IAAI,GAAG,CAAC,EAAE,CAAC;IACvD,EAAE,EAAE,IAAI,KAAK,CAAC,EAAE,CAAC;QACf,OAAO,CAAC,GAAG,CAAC,OAAO;IACrB,CAAC,MAAM,CAAC;QACN,OAAO,CAAC,KAAK,CAAC,OAAO;IACvB,CAAC;IAED,OAAO,CAAC,IAAI,CAAC,IAAI;AACnB,CAAC;SAEe,4BAA4B,GAAG,CAAC;IAC9C,KAAK,CAAC,eAAe;YACb,OAAO,CAAC,GAAG,CAAC,YAAY,QAAQ,OAAO,CAAC,eAAe;AACjE,CAAC"}