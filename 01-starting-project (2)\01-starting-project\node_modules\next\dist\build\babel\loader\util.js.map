{"version": 3, "sources": ["../../../../build/babel/loader/util.ts"], "sourcesContent": ["export function consumeIterator(iter: Iterator<any>) {\n  while (true) {\n    const { value, done } = iter.next()\n    if (done) {\n      return value\n    }\n  }\n}\n"], "names": [], "mappings": ";;;;QAAgB,eAAe,GAAf,eAAe;SAAf,eAAe,CAAC,IAAmB,EAAE,CAAC;UAC7C,IAAI,CAAE,CAAC;QACZ,KAAK,GAAG,KAAK,GAAE,IAAI,MAAK,IAAI,CAAC,IAAI;QACjC,EAAE,EAAE,IAAI,EAAE,CAAC;mBACF,KAAK;QACd,CAAC;IACH,CAAC;AACH,CAAC"}