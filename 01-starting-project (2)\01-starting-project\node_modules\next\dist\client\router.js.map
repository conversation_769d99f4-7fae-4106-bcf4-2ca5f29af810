{"version": 3, "sources": ["../../client/router.ts"], "sourcesContent": ["/* global window */\nimport React from 'react'\nimport Router from '../shared/lib/router/router'\nimport type { NextRouter } from '../shared/lib/router/router'\nimport { RouterContext } from '../shared/lib/router-context'\n\ntype ClassArguments<T> = T extends new (...args: infer U) => any ? U : any\n\ntype RouterArgs = ClassArguments<typeof Router>\n\ntype SingletonRouterBase = {\n  router: Router | null\n  readyCallbacks: Array<() => any>\n  ready(cb: () => any): void\n}\n\nexport { Router }\n\nexport type { NextRouter }\n\nexport type SingletonRouter = SingletonRouterBase & NextRouter\n\nconst singletonRouter: SingletonRouterBase = {\n  router: null, // holds the actual router instance\n  readyCallbacks: [],\n  ready(cb: () => void) {\n    if (this.router) return cb()\n    if (typeof window !== 'undefined') {\n      this.readyCallbacks.push(cb)\n    }\n  },\n}\n\n// Create public properties and methods of the router in the singletonRouter\nconst urlPropertyFields = [\n  'pathname',\n  'route',\n  'query',\n  'asPath',\n  'components',\n  'isFallback',\n  'basePath',\n  'locale',\n  'locales',\n  'defaultLocale',\n  'isReady',\n  'isPreview',\n  'isLocaleDomain',\n  'domainLocales',\n]\nconst routerEvents = [\n  'routeChangeStart',\n  'beforeHistoryChange',\n  'routeChangeComplete',\n  'routeChangeError',\n  'hashChangeStart',\n  'hashChangeComplete',\n] as const\nexport type RouterEvent = typeof routerEvents[number]\n\nconst coreMethodFields = [\n  'push',\n  'replace',\n  'reload',\n  'back',\n  'prefetch',\n  'beforePopState',\n]\n\n// Events is a static property on the router, the router doesn't have to be initialized to use it\nObject.defineProperty(singletonRouter, 'events', {\n  get() {\n    return Router.events\n  },\n})\n\nurlPropertyFields.forEach((field: string) => {\n  // Here we need to use Object.defineProperty because we need to return\n  // the property assigned to the actual router\n  // The value might get changed as we change routes and this is the\n  // proper way to access it\n  Object.defineProperty(singletonRouter, field, {\n    get() {\n      const router = getRouter() as any\n      return router[field] as string\n    },\n  })\n})\n\ncoreMethodFields.forEach((field: string) => {\n  // We don't really know the types here, so we add them later instead\n  ;(singletonRouter as any)[field] = (...args: any[]) => {\n    const router = getRouter() as any\n    return router[field](...args)\n  }\n})\n\nrouterEvents.forEach((event) => {\n  singletonRouter.ready(() => {\n    Router.events.on(event, (...args) => {\n      const eventField = `on${event.charAt(0).toUpperCase()}${event.substring(\n        1\n      )}`\n      const _singletonRouter = singletonRouter as any\n      if (_singletonRouter[eventField]) {\n        try {\n          _singletonRouter[eventField](...args)\n        } catch (err) {\n          console.error(`Error when running the Router event: ${eventField}`)\n          console.error(`${err.message}\\n${err.stack}`)\n        }\n      }\n    })\n  })\n})\n\nfunction getRouter(): Router {\n  if (!singletonRouter.router) {\n    const message =\n      'No router instance found.\\n' +\n      'You should only use \"next/router\" on the client side of your app.\\n'\n    throw new Error(message)\n  }\n  return singletonRouter.router\n}\n\n// Export the singletonRouter and this is the public API.\nexport default singletonRouter as SingletonRouter\n\n// Reexport the withRoute HOC\nexport { default as withRouter } from './with-router'\n\nexport function useRouter(): NextRouter {\n  return React.useContext(RouterContext)\n}\n\n// INTERNAL APIS\n// -------------\n// (do not use following exports inside the app)\n\n// Create a router and assign it as the singleton instance.\n// This is used in client side when we are initilizing the app.\n// This should **not** be used inside the server.\nexport function createRouter(...args: RouterArgs): Router {\n  singletonRouter.router = new Router(...args)\n  singletonRouter.readyCallbacks.forEach((cb) => cb())\n  singletonRouter.readyCallbacks = []\n\n  return singletonRouter.router\n}\n\n// This function is used to create the `withRouter` router instance\nexport function makePublicRouterInstance(router: Router): NextRouter {\n  const _router = router as any\n  const instance = {} as any\n\n  for (const property of urlPropertyFields) {\n    if (typeof _router[property] === 'object') {\n      instance[property] = Object.assign(\n        Array.isArray(_router[property]) ? [] : {},\n        _router[property]\n      ) // makes sure query is not stateful\n      continue\n    }\n\n    instance[property] = _router[property]\n  }\n\n  // Events is a static property on the router, the router doesn't have to be initialized to use it\n  instance.events = Router.events\n\n  coreMethodFields.forEach((field) => {\n    instance[field] = (...args: any[]) => {\n      return _router[field](...args)\n    }\n  })\n\n  return instance\n}\n"], "names": [], "mappings": ";;;;gCAgBS,MAAM;;;eAdI,OAA6B;;;gCAgI5B,UAAU;;;2BAArB,OAAO;;;QAEA,SAAS,GAAT,SAAS;QAWT,YAAY,GAAZ,YAAY;QASZ,wBAAwB,GAAxB,wBAAwB;;AAvJtB,GAAO,CAAP,MAAO;AACN,GAA6B,CAA7B,OAA6B;AAElB,GAA8B,CAA9B,cAA8B;;;;;;;AAkB5D,KAAK,CAAC,eAAe;IACnB,MAAM,EAAE,IAAI;IACZ,cAAc;IACd,KAAK,EAAC,EAAc,EAAE,CAAC;QACrB,EAAE,OAAO,MAAM,SAAS,EAAE;QAC1B,EAAE,SAAS,MAAM,MAAK,SAAW,GAAE,CAAC;iBAC7B,cAAc,CAAC,IAAI,CAAC,EAAE;QAC7B,CAAC;IACH,CAAC;;AAGH,EAA4E,AAA5E,0EAA4E;AAC5E,KAAK,CAAC,iBAAiB;KACrB,QAAU;KACV,KAAO;KACP,KAAO;KACP,MAAQ;KACR,UAAY;KACZ,UAAY;KACZ,QAAU;KACV,MAAQ;KACR,OAAS;KACT,aAAe;KACf,OAAS;KACT,SAAW;KACX,cAAgB;KAChB,aAAe;;AAEjB,KAAK,CAAC,YAAY;KAChB,gBAAkB;KAClB,mBAAqB;KACrB,mBAAqB;KACrB,gBAAkB;KAClB,eAAiB;KACjB,kBAAoB;;AAItB,KAAK,CAAC,gBAAgB;KACpB,IAAM;KACN,OAAS;KACT,MAAQ;KACR,IAAM;KACN,QAAU;KACV,cAAgB;;AAGlB,EAAiG,AAAjG,+FAAiG;AACjG,MAAM,CAAC,cAAc,CAAC,eAAe,GAAE,MAAQ;IAC7C,GAAG,IAAG,CAAC;eArEU,OAA6B,SAsE9B,MAAM;IACtB,CAAC;;AAGH,iBAAiB,CAAC,OAAO,EAAE,KAAa,GAAK,CAAC;IAC5C,EAAsE,AAAtE,oEAAsE;IACtE,EAA6C,AAA7C,2CAA6C;IAC7C,EAAkE,AAAlE,gEAAkE;IAClE,EAA0B,AAA1B,wBAA0B;IAC1B,MAAM,CAAC,cAAc,CAAC,eAAe,EAAE,KAAK;QAC1C,GAAG,IAAG,CAAC;YACL,KAAK,CAAC,MAAM,GAAG,SAAS;mBACjB,MAAM,CAAC,KAAK;QACrB,CAAC;;AAEL,CAAC;AAED,gBAAgB,CAAC,OAAO,EAAE,KAAa,GAAK,CAAC;IAEzC,eAAe,CAAS,KAAK,QAAQ,IAAI,GAAY,CAAC;QACtD,KAAK,CAAC,MAAM,GAAG,SAAS;eACjB,MAAM,CAAC,KAAK,KAAK,IAAI;IAC9B,CAAC;AACH,CAAC;AAED,YAAY,CAAC,OAAO,EAAE,KAAK,GAAK,CAAC;IAC/B,eAAe,CAAC,KAAK,KAAO,CAAC;QAhGZ,OAA6B,SAiGrC,MAAM,CAAC,EAAE,CAAC,KAAK,MAAM,IAAI,GAAK,CAAC;YACpC,KAAK,CAAC,UAAU,IAAI,EAAE,EAAE,KAAK,CAAC,MAAM,CAAC,CAAC,EAAE,WAAW,KAAK,KAAK,CAAC,SAAS,CACrE,CAAC;YAEH,KAAK,CAAC,gBAAgB,GAAG,eAAe;YACxC,EAAE,EAAE,gBAAgB,CAAC,UAAU,GAAG,CAAC;oBAC7B,CAAC;oBACH,gBAAgB,CAAC,UAAU,KAAK,IAAI;gBACtC,CAAC,QAAQ,GAAG,EAAE,CAAC;oBACb,OAAO,CAAC,KAAK,EAAE,qCAAqC,EAAE,UAAU;oBAChE,OAAO,CAAC,KAAK,IAAI,GAAG,CAAC,OAAO,CAAC,EAAE,EAAE,GAAG,CAAC,KAAK;gBAC5C,CAAC;YACH,CAAC;QACH,CAAC;IACH,CAAC;AACH,CAAC;SAEQ,SAAS,GAAW,CAAC;IAC5B,EAAE,GAAG,eAAe,CAAC,MAAM,EAAE,CAAC;QAC5B,KAAK,CAAC,OAAO,IACX,2BAA6B,KAC7B,mEAAqE;QACvE,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,OAAO;IACzB,CAAC;WACM,eAAe,CAAC,MAAM;AAC/B,CAAC;eAGc,eAAe;;SAKd,SAAS,GAAe,CAAC;WAnIvB,MAAO,SAoIV,UAAU,CAjIK,cAA8B;AAkI5D,CAAC;SASe,YAAY,IAAI,IAAI,EAAsB,CAAC;IACzD,eAAe,CAAC,MAAM,GAAG,GAAG,CA9IX,OAA6B,YA8IP,IAAI;IAC3C,eAAe,CAAC,cAAc,CAAC,OAAO,EAAE,EAAE,GAAK,EAAE;;IACjD,eAAe,CAAC,cAAc;WAEvB,eAAe,CAAC,MAAM;AAC/B,CAAC;SAGe,wBAAwB,CAAC,MAAc,EAAc,CAAC;IACpE,KAAK,CAAC,QAAO,GAAG,MAAM;IACtB,KAAK,CAAC,QAAQ;;SAET,KAAK,CAAC,QAAQ,IAAI,iBAAiB,CAAE,CAAC;QACzC,EAAE,SAAS,QAAO,CAAC,QAAQ,OAAM,MAAQ,GAAE,CAAC;YAC1C,QAAQ,CAAC,QAAQ,IAAI,MAAM,CAAC,MAAM,CAChC,KAAK,CAAC,OAAO,CAAC,QAAO,CAAC,QAAQ;eAC9B,QAAO,CAAC,QAAQ,EAChB,CAAmC,AAAnC,EAAmC,AAAnC,iCAAmC;;;QAEvC,CAAC;QAED,QAAQ,CAAC,QAAQ,IAAI,QAAO,CAAC,QAAQ;IACvC,CAAC;IAED,EAAiG,AAAjG,+FAAiG;IACjG,QAAQ,CAAC,MAAM,GAvKE,OAA6B,SAuKrB,MAAM;IAE/B,gBAAgB,CAAC,OAAO,EAAE,KAAK,GAAK,CAAC;QACnC,QAAQ,CAAC,KAAK,QAAQ,IAAI,GAAY,CAAC;mBAC9B,QAAO,CAAC,KAAK,KAAK,IAAI;QAC/B,CAAC;IACH,CAAC;WAEM,QAAQ;AACjB,CAAC"}