{"version": 3, "sources": ["../../client/request-idle-callback.ts"], "sourcesContent": ["type RequestIdleCallbackHandle = any\ntype RequestIdleCallbackOptions = {\n  timeout: number\n}\ntype RequestIdleCallbackDeadline = {\n  readonly didTimeout: boolean\n  timeRemaining: () => number\n}\n\ndeclare global {\n  interface Window {\n    requestIdleCallback: (\n      callback: (deadline: RequestIdleCallbackDeadline) => void,\n      opts?: RequestIdleCallbackOptions\n    ) => RequestIdleCallbackHandle\n    cancelIdleCallback: (id: RequestIdleCallbackHandle) => void\n  }\n}\n\nexport const requestIdleCallback =\n  (typeof self !== 'undefined' &&\n    self.requestIdleCallback &&\n    self.requestIdleCallback.bind(window)) ||\n  function (\n    cb: (deadline: RequestIdleCallbackDeadline) => void\n  ): NodeJS.Timeout {\n    let start = Date.now()\n    return setTimeout(function () {\n      cb({\n        didTimeout: false,\n        timeRemaining: function () {\n          return Math.max(0, 50 - (Date.now() - start))\n        },\n      })\n    }, 1)\n  }\n\nexport const cancelIdleCallback =\n  (typeof self !== 'undefined' &&\n    self.cancelIdleCallback &&\n    self.cancelIdleCallback.bind(window)) ||\n  function (id: RequestIdleCallbackHandle) {\n    return clearTimeout(id)\n  }\n"], "names": [], "mappings": ";;;;;AAmBO,KAAK,CAAC,mBAAmB,UACtB,IAAI,MAAK,SAAW,KAC1B,IAAI,CAAC,mBAAmB,IACxB,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,MAAM,cAEpC,EAAmD,EACnC,CAAC;IACjB,GAAG,CAAC,KAAK,GAAG,IAAI,CAAC,GAAG;WACb,UAAU,YAAa,CAAC;QAC7B,EAAE;YACA,UAAU,EAAE,KAAK;YACjB,aAAa,aAAc,CAAC;uBACnB,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,EAAE,IAAI,IAAI,CAAC,GAAG,KAAK,KAAK;YAC7C,CAAC;;IAEL,CAAC,EAAE,CAAC;AACN,CAAC;QAhBU,mBAAmB,GAAnB,mBAAmB;AAkBzB,KAAK,CAAC,kBAAkB,UACrB,IAAI,MAAK,SAAW,KAC1B,IAAI,CAAC,kBAAkB,IACvB,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,MAAM,cAC3B,EAA6B,EAAE,CAAC;WACjC,YAAY,CAAC,EAAE;AACxB,CAAC;QANU,kBAAkB,GAAlB,kBAAkB"}