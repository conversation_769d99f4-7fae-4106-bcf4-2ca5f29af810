{"version": 3, "sources": ["../../../lib/eslint/writeDefaultConfig.ts"], "sourcesContent": ["import { promises as fs } from 'fs'\nimport chalk from 'chalk'\nimport os from 'os'\nimport path from 'path'\nimport * as CommentJson from 'next/dist/compiled/comment-json'\nimport { ConfigAvailable } from './hasEslintConfiguration'\n\nimport * as Log from '../../build/output/log'\n\nexport async function writeDefaultConfig(\n  baseDir: string,\n  { exists, emptyEslintrc, emptyPkgJsonConfig }: ConfigAvailable,\n  selectedConfig: any,\n  eslintrcFile: string | null,\n  pkgJsonPath: string | null,\n  packageJsonConfig: { eslintConfig: any } | null\n) {\n  if (!exists && emptyEslintrc && eslintrcFile) {\n    const ext = path.extname(eslintrcFile)\n\n    let newFileContent\n    if (ext === '.yaml' || ext === '.yml') {\n      newFileContent = \"extends: 'next'\"\n    } else {\n      newFileContent = CommentJson.stringify(selectedConfig, null, 2)\n\n      if (ext === '.js') {\n        newFileContent = 'module.exports = ' + newFileContent\n      }\n    }\n\n    await fs.writeFile(eslintrcFile, newFileContent + os.EOL)\n\n    Log.info(\n      `We detected an empty ESLint configuration file (${chalk.bold(\n        path.basename(eslintrcFile)\n      )}) and updated it for you!`\n    )\n  } else if (!exists && emptyPkgJsonConfig && packageJsonConfig) {\n    packageJsonConfig.eslintConfig = selectedConfig\n\n    if (pkgJsonPath)\n      await fs.writeFile(\n        pkgJsonPath,\n        CommentJson.stringify(packageJsonConfig, null, 2) + os.EOL\n      )\n\n    Log.info(\n      `We detected an empty ${chalk.bold(\n        'eslintConfig'\n      )} field in package.json and updated it for you!`\n    )\n  } else if (!exists) {\n    await fs.writeFile(\n      path.join(baseDir, '.eslintrc.json'),\n      CommentJson.stringify(selectedConfig, null, 2) + os.EOL\n    )\n\n    console.log(\n      chalk.green(\n        `We created the ${chalk.bold(\n          '.eslintrc.json'\n        )} file for you and included your selected configuration.`\n      )\n    )\n  }\n}\n"], "names": [], "mappings": ";;;;QASsB,kBAAkB,GAAlB,kBAAkB;AATT,GAAI,CAAJ,GAAI;AACjB,GAAO,CAAP,MAAO;AACV,GAAI,CAAJ,GAAI;AACF,GAAM,CAAN,KAAM;AACX,GAAW,CAAX,WAAW;AAGX,GAAG,CAAH,GAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAEO,kBAAkB,CACtC,OAAe,IACb,MAAM,GAAE,aAAa,GAAE,kBAAkB,KAC3C,cAAmB,EACnB,YAA2B,EAC3B,WAA0B,EAC1B,iBAA+C,EAC/C,CAAC;IACD,EAAE,GAAG,MAAM,IAAI,aAAa,IAAI,YAAY,EAAE,CAAC;QAC7C,KAAK,CAAC,GAAG,GAfI,KAAM,SAeF,OAAO,CAAC,YAAY;QAErC,GAAG,CAAC,cAAc;QAClB,EAAE,EAAE,GAAG,MAAK,KAAO,KAAI,GAAG,MAAK,IAAM,GAAE,CAAC;YACtC,cAAc,IAAG,eAAiB;QACpC,CAAC,MAAM,CAAC;YACN,cAAc,GApBR,WAAW,CAoBY,SAAS,CAAC,cAAc,EAAE,IAAI,EAAE,CAAC;YAE9D,EAAE,EAAE,GAAG,MAAK,GAAK,GAAE,CAAC;gBAClB,cAAc,IAAG,iBAAmB,IAAG,cAAc;YACvD,CAAC;QACH,CAAC;cA7B0B,GAAI,UA+BtB,SAAS,CAAC,YAAY,EAAE,cAAc,GA7BpC,GAAI,SA6BsC,GAAG;QAxBhD,GAAG,CA0BP,IAAI,EACL,gDAAgD,EAjCrC,MAAO,SAiCsC,IAAI,CA/BlD,KAAM,SAgCV,QAAQ,CAAC,YAAY,GAC1B,yBAAyB;IAE/B,CAAC,MAAM,EAAE,GAAG,MAAM,IAAI,kBAAkB,IAAI,iBAAiB,EAAE,CAAC;QAC9D,iBAAiB,CAAC,YAAY,GAAG,cAAc;QAE/C,EAAE,EAAE,WAAW,QAzCY,GAAI,UA0CpB,SAAS,CAChB,WAAW,EAvCP,WAAW,CAwCH,SAAS,CAAC,iBAAiB,EAAE,IAAI,EAAE,CAAC,IA1CzC,GAAI,SA0C4C,GAAG;QArCtD,GAAG,CAwCP,IAAI,EACL,qBAAqB,EA/CV,MAAO,SA+CW,IAAI,EAChC,YAAc,GACd,8CAA8C;IAEpD,CAAC,MAAM,EAAE,GAAG,MAAM,EAAE,CAAC;cApDQ,GAAI,UAqDtB,SAAS,CAlDL,KAAM,SAmDZ,IAAI,CAAC,OAAO,GAAE,cAAgB,IAlD7B,WAAW,CAmDL,SAAS,CAAC,cAAc,EAAE,IAAI,EAAE,CAAC,IArDpC,GAAI,SAqDuC,GAAG;QAGzD,OAAO,CAAC,GAAG,CAzDG,MAAO,SA0Db,KAAK,EACR,eAAe,EA3DN,MAAO,SA2DO,IAAI,EAC1B,cAAgB,GAChB,uDAAuD;IAG/D,CAAC;AACH,CAAC"}