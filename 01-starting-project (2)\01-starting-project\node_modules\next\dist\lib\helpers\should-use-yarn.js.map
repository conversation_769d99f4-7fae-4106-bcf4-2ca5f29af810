{"version": 3, "sources": ["../../../lib/helpers/should-use-yarn.ts"], "sourcesContent": ["import { execSync } from 'child_process'\n\nexport function shouldUseYarn(): boolean {\n  try {\n    const userAgent = process.env.npm_config_user_agent\n    if (userAgent) {\n      return Boolean(userAgent && userAgent.startsWith('yarn'))\n    }\n    execSync('yarnpkg --version', { stdio: 'ignore' })\n    return true\n  } catch (e) {\n    return false\n  }\n}\n"], "names": [], "mappings": ";;;;QAEgB,aAAa,GAAb,aAAa;AAFJ,GAAe,CAAf,aAAe;SAExB,aAAa,GAAY,CAAC;QACpC,CAAC;QACH,KAAK,CAAC,SAAS,GAAG,OAAO,CAAC,GAAG,CAAC,qBAAqB;QACnD,EAAE,EAAE,SAAS,EAAE,CAAC;mBACP,OAAO,CAAC,SAAS,IAAI,SAAS,CAAC,UAAU,EAAC,IAAM;QACzD,CAAC;YAPoB,aAAe,YAQ3B,iBAAmB;YAAI,KAAK,GAAE,MAAQ;;eACxC,IAAI;IACb,CAAC,QAAQ,CAAC,EAAE,CAAC;eACJ,KAAK;IACd,CAAC;AACH,CAAC"}