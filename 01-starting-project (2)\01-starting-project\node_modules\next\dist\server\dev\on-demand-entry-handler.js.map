{"version": 3, "sources": ["../../../server/dev/on-demand-entry-handler.ts"], "sourcesContent": ["import { EventEmitter } from 'events'\nimport { IncomingMessage, ServerResponse } from 'http'\nimport { join, posix } from 'path'\nimport { parse } from 'url'\nimport { webpack, isWebpack5 } from 'next/dist/compiled/webpack/webpack'\nimport * as Log from '../../build/output/log'\nimport { normalizePagePath, normalizePathSep } from '../normalize-page-path'\nimport { pageNotFoundError } from '../require'\nimport { findPageFile } from '../lib/find-page-file'\nimport getRouteFromEntrypoint from '../get-route-from-entrypoint'\n\nexport const ADDED = Symbol('added')\nexport const BUILDING = Symbol('building')\nexport const BUILT = Symbol('built')\n\nexport let entries: {\n  [page: string]: {\n    serverBundlePath: string\n    clientBundlePath: string\n    absolutePagePath: string\n    status?: typeof ADDED | typeof BUILDING | typeof BUILT\n    lastActiveTime?: number\n  }\n} = {}\n\nexport default function onDemandEntryHandler(\n  watcher: any,\n  multiCompiler: webpack.MultiCompiler,\n  {\n    pagesDir,\n    pageExtensions,\n    maxInactiveAge,\n    pagesBufferLength,\n  }: {\n    pagesDir: string\n    pageExtensions: string[]\n    maxInactiveAge: number\n    pagesBufferLength: number\n  }\n) {\n  const { compilers } = multiCompiler\n  const invalidator = new Invalidator(watcher, multiCompiler)\n\n  let lastAccessPages = ['']\n  let doneCallbacks: EventEmitter | null = new EventEmitter()\n\n  for (const compiler of compilers) {\n    compiler.hooks.make.tap(\n      'NextJsOnDemandEntries',\n      (_compilation: webpack.compilation.Compilation) => {\n        invalidator.startBuilding()\n      }\n    )\n  }\n\n  function getPagePathsFromEntrypoints(entrypoints: any): string[] {\n    const pagePaths = []\n    for (const entrypoint of entrypoints.values()) {\n      const page = getRouteFromEntrypoint(entrypoint.name)\n      if (page) {\n        pagePaths.push(page)\n      }\n    }\n\n    return pagePaths\n  }\n\n  multiCompiler.hooks.done.tap('NextJsOnDemandEntries', (multiStats) => {\n    const [clientStats, serverStats] = multiStats.stats\n    const pagePaths = new Set([\n      ...getPagePathsFromEntrypoints(clientStats.compilation.entrypoints),\n      ...getPagePathsFromEntrypoints(serverStats.compilation.entrypoints),\n    ])\n\n    for (const page of pagePaths) {\n      const entry = entries[page]\n      if (!entry) {\n        continue\n      }\n\n      if (entry.status !== BUILDING) {\n        continue\n      }\n\n      entry.status = BUILT\n      entry.lastActiveTime = Date.now()\n      doneCallbacks!.emit(page)\n    }\n\n    invalidator.doneBuilding()\n  })\n\n  const disposeHandler = setInterval(function () {\n    disposeInactiveEntries(watcher, lastAccessPages, maxInactiveAge)\n  }, 5000)\n\n  disposeHandler.unref()\n\n  function handlePing(pg: string) {\n    const page = normalizePathSep(pg)\n    const entryInfo = entries[page]\n    let toSend\n\n    // If there's no entry, it may have been invalidated and needs to be re-built.\n    if (!entryInfo) {\n      // if (page !== lastEntry) client pings, but there's no entry for page\n      return { invalid: true }\n    }\n\n    // 404 is an on demand entry but when a new page is added we have to refresh the page\n    if (page === '/_error') {\n      toSend = { invalid: true }\n    } else {\n      toSend = { success: true }\n    }\n\n    // We don't need to maintain active state of anything other than BUILT entries\n    if (entryInfo.status !== BUILT) return\n\n    // If there's an entryInfo\n    if (!lastAccessPages.includes(page)) {\n      lastAccessPages.unshift(page)\n\n      // Maintain the buffer max length\n      if (lastAccessPages.length > pagesBufferLength) {\n        lastAccessPages.pop()\n      }\n    }\n    entryInfo.lastActiveTime = Date.now()\n    return toSend\n  }\n\n  return {\n    async ensurePage(page: string) {\n      let normalizedPagePath: string\n      try {\n        normalizedPagePath = normalizePagePath(page)\n      } catch (err) {\n        console.error(err)\n        throw pageNotFoundError(page)\n      }\n\n      let pagePath = await findPageFile(\n        pagesDir,\n        normalizedPagePath,\n        pageExtensions\n      )\n\n      // Default the /_error route to the Next.js provided default page\n      if (page === '/_error' && pagePath === null) {\n        pagePath = 'next/dist/pages/_error'\n      }\n\n      if (pagePath === null) {\n        throw pageNotFoundError(normalizedPagePath)\n      }\n\n      let pageUrl = pagePath.replace(/\\\\/g, '/')\n\n      pageUrl = `${pageUrl[0] !== '/' ? '/' : ''}${pageUrl\n        .replace(new RegExp(`\\\\.+(?:${pageExtensions.join('|')})$`), '')\n        .replace(/\\/index$/, '')}`\n\n      pageUrl = pageUrl === '' ? '/' : pageUrl\n\n      const bundleFile = normalizePagePath(pageUrl)\n      const serverBundlePath = posix.join('pages', bundleFile)\n      const clientBundlePath = posix.join('pages', bundleFile)\n      const absolutePagePath = pagePath.startsWith('next/dist/pages')\n        ? require.resolve(pagePath)\n        : join(pagesDir, pagePath)\n\n      page = posix.normalize(pageUrl)\n\n      return new Promise<void>((resolve, reject) => {\n        // Makes sure the page that is being kept in on-demand-entries matches the webpack output\n        const normalizedPage = normalizePathSep(page)\n        const entryInfo = entries[normalizedPage]\n\n        if (entryInfo) {\n          if (entryInfo.status === BUILT) {\n            resolve()\n            return\n          }\n\n          if (entryInfo.status === BUILDING) {\n            doneCallbacks!.once(normalizedPage, handleCallback)\n            return\n          }\n        }\n\n        Log.event(`build page: ${normalizedPage}`)\n\n        entries[normalizedPage] = {\n          serverBundlePath,\n          clientBundlePath,\n          absolutePagePath,\n          status: ADDED,\n        }\n        doneCallbacks!.once(normalizedPage, handleCallback)\n\n        invalidator.invalidate()\n\n        function handleCallback(err: Error) {\n          if (err) return reject(err)\n          resolve()\n        }\n      })\n    },\n\n    middleware(req: IncomingMessage, res: ServerResponse, next: Function) {\n      if (!req.url?.startsWith('/_next/webpack-hmr')) return next()\n\n      const { query } = parse(req.url!, true)\n      const page = query.page\n      if (!page) return next()\n\n      const runPing = () => {\n        const data = handlePing(query.page as string)\n        if (!data) return\n        res.write('data: ' + JSON.stringify(data) + '\\n\\n')\n      }\n      const pingInterval = setInterval(() => runPing(), 5000)\n\n      req.on('close', () => {\n        clearInterval(pingInterval)\n      })\n      next()\n    },\n  }\n}\n\nfunction disposeInactiveEntries(\n  watcher: any,\n  lastAccessPages: any,\n  maxInactiveAge: number\n) {\n  const disposingPages: any = []\n\n  Object.keys(entries).forEach((page) => {\n    const { lastActiveTime, status } = entries[page]\n\n    // This means this entry is currently building or just added\n    // We don't need to dispose those entries.\n    if (status !== BUILT) return\n\n    // We should not build the last accessed page even we didn't get any pings\n    // Sometimes, it's possible our XHR ping to wait before completing other requests.\n    // In that case, we should not dispose the current viewing page\n    if (lastAccessPages.includes(page)) return\n\n    if (lastActiveTime && Date.now() - lastActiveTime > maxInactiveAge) {\n      disposingPages.push(page)\n    }\n  })\n\n  if (disposingPages.length > 0) {\n    disposingPages.forEach((page: any) => {\n      delete entries[page]\n    })\n    // disposing inactive page(s)\n    watcher.invalidate()\n  }\n}\n\n// Make sure only one invalidation happens at a time\n// Otherwise, webpack hash gets changed and it'll force the client to reload.\nclass Invalidator {\n  private multiCompiler: webpack.MultiCompiler\n  private watcher: any\n  private building: boolean\n  private rebuildAgain: boolean\n\n  constructor(watcher: any, multiCompiler: webpack.MultiCompiler) {\n    this.multiCompiler = multiCompiler\n    this.watcher = watcher\n    // contains an array of types of compilers currently building\n    this.building = false\n    this.rebuildAgain = false\n  }\n\n  invalidate() {\n    // If there's a current build is processing, we won't abort it by invalidating.\n    // (If aborted, it'll cause a client side hard reload)\n    // But let it to invalidate just after the completion.\n    // So, it can re-build the queued pages at once.\n    if (this.building) {\n      this.rebuildAgain = true\n      return\n    }\n\n    this.building = true\n    if (!isWebpack5) {\n      // Work around a bug in webpack, calling `invalidate` on Watching.js\n      // doesn't trigger the invalid call used to keep track of the `.done` hook on multiCompiler\n      for (const compiler of this.multiCompiler.compilers) {\n        compiler.hooks.invalid.call()\n      }\n    }\n\n    this.watcher.invalidate()\n  }\n\n  startBuilding() {\n    this.building = true\n  }\n\n  doneBuilding() {\n    this.building = false\n\n    if (this.rebuildAgain) {\n      this.rebuildAgain = false\n      this.invalidate()\n    }\n  }\n}\n"], "names": [], "mappings": ";;;;kBAyBwB,oBAAoB;;AAzBf,GAAQ,CAAR,OAAQ;AAET,GAAM,CAAN,KAAM;AACZ,GAAK,CAAL,IAAK;AACS,GAAoC,CAApC,QAAoC;AAC5D,GAAG,CAAH,GAAG;AACqC,GAAwB,CAAxB,kBAAwB;AAC1C,GAAY,CAAZ,QAAY;AACjB,GAAuB,CAAvB,aAAuB;AACjB,GAA8B,CAA9B,uBAA8B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAE1D,KAAK,CAAC,KAAK,GAAG,MAAM,EAAC,KAAO;QAAtB,KAAK,GAAL,KAAK;AACX,KAAK,CAAC,QAAQ,GAAG,MAAM,EAAC,QAAU;QAA5B,QAAQ,GAAR,QAAQ;AACd,KAAK,CAAC,KAAK,GAAG,MAAM,EAAC,KAAO;QAAtB,KAAK,GAAL,KAAK;AAEX,GAAG,CAAC,OAAO;;QAAP,OAAO,GAAP,OAAO;SAUM,oBAAoB,CAC1C,OAAY,EACZ,aAAoC,IAElC,QAAQ,GACR,cAAc,GACd,cAAc,GACd,iBAAiB,KAOnB,CAAC;IACD,KAAK,GAAG,SAAS,MAAK,aAAa;IACnC,KAAK,CAAC,WAAW,GAAG,GAAG,CAAC,WAAW,CAAC,OAAO,EAAE,aAAa;IAE1D,GAAG,CAAC,eAAe;;;IACnB,GAAG,CAAC,aAAa,GAAwB,GAAG,CA5CjB,OAAQ;SA8C9B,KAAK,CAAC,QAAQ,IAAI,SAAS,CAAE,CAAC;QACjC,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,EACrB,qBAAuB,IACtB,YAA6C,GAAK,CAAC;YAClD,WAAW,CAAC,aAAa;QAC3B,CAAC;IAEL,CAAC;aAEQ,2BAA2B,CAAC,WAAgB,EAAY,CAAC;QAChE,KAAK,CAAC,SAAS;aACV,KAAK,CAAC,UAAU,IAAI,WAAW,CAAC,MAAM,GAAI,CAAC;YAC9C,KAAK,CAAC,IAAI,OAjDmB,uBAA8B,UAiDvB,UAAU,CAAC,IAAI;YACnD,EAAE,EAAE,IAAI,EAAE,CAAC;gBACT,SAAS,CAAC,IAAI,CAAC,IAAI;YACrB,CAAC;QACH,CAAC;eAEM,SAAS;IAClB,CAAC;IAED,aAAa,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,EAAC,qBAAuB,IAAG,UAAU,GAAK,CAAC;QACrE,KAAK,EAAE,WAAW,EAAE,WAAW,IAAI,UAAU,CAAC,KAAK;QACnD,KAAK,CAAC,SAAS,GAAG,GAAG,CAAC,GAAG;eACpB,2BAA2B,CAAC,WAAW,CAAC,WAAW,CAAC,WAAW;eAC/D,2BAA2B,CAAC,WAAW,CAAC,WAAW,CAAC,WAAW;;aAG/D,KAAK,CAAC,IAAI,IAAI,SAAS,CAAE,CAAC;YAC7B,KAAK,CAAC,KAAK,GAAG,OAAO,CAAC,IAAI;YAC1B,EAAE,GAAG,KAAK,EAAE,CAAC;;YAEb,CAAC;YAED,EAAE,EAAE,KAAK,CAAC,MAAM,KAAK,QAAQ,EAAE,CAAC;;YAEhC,CAAC;YAED,KAAK,CAAC,MAAM,GAAG,KAAK;YACpB,KAAK,CAAC,cAAc,GAAG,IAAI,CAAC,GAAG;YAC/B,aAAa,CAAE,IAAI,CAAC,IAAI;QAC1B,CAAC;QAED,WAAW,CAAC,YAAY;IAC1B,CAAC;IAED,KAAK,CAAC,cAAc,GAAG,WAAW,YAAa,CAAC;QAC9C,sBAAsB,CAAC,OAAO,EAAE,eAAe,EAAE,cAAc;IACjE,CAAC,EAAE,IAAI;IAEP,cAAc,CAAC,KAAK;aAEX,UAAU,CAAC,EAAU,EAAE,CAAC;QAC/B,KAAK,CAAC,IAAI,OA7FsC,kBAAwB,mBA6F1C,EAAE;QAChC,KAAK,CAAC,SAAS,GAAG,OAAO,CAAC,IAAI;QAC9B,GAAG,CAAC,MAAM;QAEV,EAA8E,AAA9E,4EAA8E;QAC9E,EAAE,GAAG,SAAS,EAAE,CAAC;YACf,EAAsE,AAAtE,oEAAsE;;gBAC7D,OAAO,EAAE,IAAI;;QACxB,CAAC;QAED,EAAqF,AAArF,mFAAqF;QACrF,EAAE,EAAE,IAAI,MAAK,OAAS,GAAE,CAAC;YACvB,MAAM;gBAAK,OAAO,EAAE,IAAI;;QAC1B,CAAC,MAAM,CAAC;YACN,MAAM;gBAAK,OAAO,EAAE,IAAI;;QAC1B,CAAC;QAED,EAA8E,AAA9E,4EAA8E;QAC9E,EAAE,EAAE,SAAS,CAAC,MAAM,KAAK,KAAK;QAE9B,EAA0B,AAA1B,wBAA0B;QAC1B,EAAE,GAAG,eAAe,CAAC,QAAQ,CAAC,IAAI,GAAG,CAAC;YACpC,eAAe,CAAC,OAAO,CAAC,IAAI;YAE5B,EAAiC,AAAjC,+BAAiC;YACjC,EAAE,EAAE,eAAe,CAAC,MAAM,GAAG,iBAAiB,EAAE,CAAC;gBAC/C,eAAe,CAAC,GAAG;YACrB,CAAC;QACH,CAAC;QACD,SAAS,CAAC,cAAc,GAAG,IAAI,CAAC,GAAG;eAC5B,MAAM;IACf,CAAC;;cAGO,UAAU,EAAC,IAAY,EAAE,CAAC;YAC9B,GAAG,CAAC,kBAAkB;gBAClB,CAAC;gBACH,kBAAkB,OAlI0B,kBAAwB,oBAkI7B,IAAI;YAC7C,CAAC,QAAQ,GAAG,EAAE,CAAC;gBACb,OAAO,CAAC,KAAK,CAAC,GAAG;gBACjB,KAAK,KApIqB,QAAY,oBAoId,IAAI;YAC9B,CAAC;YAED,GAAG,CAAC,QAAQ,aAtIW,aAAuB,eAuI5C,QAAQ,EACR,kBAAkB,EAClB,cAAc;YAGhB,EAAiE,AAAjE,+DAAiE;YACjE,EAAE,EAAE,IAAI,MAAK,OAAS,KAAI,QAAQ,KAAK,IAAI,EAAE,CAAC;gBAC5C,QAAQ,IAAG,sBAAwB;YACrC,CAAC;YAED,EAAE,EAAE,QAAQ,KAAK,IAAI,EAAE,CAAC;gBACtB,KAAK,KAnJqB,QAAY,oBAmJd,kBAAkB;YAC5C,CAAC;YAED,GAAG,CAAC,OAAO,GAAG,QAAQ,CAAC,OAAO,SAAQ,CAAG;YAEzC,OAAO,MAAM,OAAO,CAAC,CAAC,OAAM,CAAG,KAAG,CAAG,SAAQ,OAAO,CACjD,OAAO,CAAC,GAAG,CAAC,MAAM,EAAE,OAAO,EAAE,cAAc,CAAC,IAAI,EAAC,CAAG,GAAE,EAAE,QACxD,OAAO;YAEV,OAAO,GAAG,OAAO,WAAU,CAAG,IAAG,OAAO;YAExC,KAAK,CAAC,UAAU,OA/J8B,kBAAwB,oBA+JjC,OAAO;YAC5C,KAAK,CAAC,gBAAgB,GApKA,KAAM,OAoKG,IAAI,EAAC,KAAO,GAAE,UAAU;YACvD,KAAK,CAAC,gBAAgB,GArKA,KAAM,OAqKG,IAAI,EAAC,KAAO,GAAE,UAAU;YACvD,KAAK,CAAC,gBAAgB,GAAG,QAAQ,CAAC,UAAU,EAAC,eAAiB,KAC1D,OAAO,CAAC,OAAO,CAAC,QAAQ,QAvKN,KAAM,OAwKnB,QAAQ,EAAE,QAAQ;YAE3B,IAAI,GA1KkB,KAAM,OA0Kf,SAAS,CAAC,OAAO;mBAEvB,GAAG,CAAC,OAAO,EAAQ,OAAO,EAAE,MAAM,GAAK,CAAC;gBAC7C,EAAyF,AAAzF,uFAAyF;gBACzF,KAAK,CAAC,cAAc,OA1KwB,kBAAwB,mBA0K5B,IAAI;gBAC5C,KAAK,CAAC,SAAS,GAAG,OAAO,CAAC,cAAc;gBAExC,EAAE,EAAE,SAAS,EAAE,CAAC;oBACd,EAAE,EAAE,SAAS,CAAC,MAAM,KAAK,KAAK,EAAE,CAAC;wBAC/B,OAAO;;oBAET,CAAC;oBAED,EAAE,EAAE,SAAS,CAAC,MAAM,KAAK,QAAQ,EAAE,CAAC;wBAClC,aAAa,CAAE,IAAI,CAAC,cAAc,EAAE,cAAc;;oBAEpD,CAAC;gBACH,CAAC;gBAxLG,GAAG,CA0LH,KAAK,EAAE,YAAY,EAAE,cAAc;gBAEvC,OAAO,CAAC,cAAc;oBACpB,gBAAgB;oBAChB,gBAAgB;oBAChB,gBAAgB;oBAChB,MAAM,EAAE,KAAK;;gBAEf,aAAa,CAAE,IAAI,CAAC,cAAc,EAAE,cAAc;gBAElD,WAAW,CAAC,UAAU;yBAEb,cAAc,CAAC,GAAU,EAAE,CAAC;oBACnC,EAAE,EAAE,GAAG,SAAS,MAAM,CAAC,GAAG;oBAC1B,OAAO;gBACT,CAAC;YACH,CAAC;QACH,CAAC;QAED,UAAU,EAAC,GAAoB,EAAE,GAAmB,EAAE,IAAc,EAAE,CAAC;gBAChE,GAAO;YAAZ,EAAE,KAAG,GAAO,GAAP,GAAG,CAAC,GAAG,cAAP,GAAO,UAAP,CAAmB,QAAnB,CAAmB,GAAnB,GAAO,CAAE,UAAU,EAAC,kBAAoB,YAAU,IAAI;YAE3D,KAAK,GAAG,KAAK,UAlNG,IAAK,QAkNG,GAAG,CAAC,GAAG,EAAG,IAAI;YACtC,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI;YACvB,EAAE,GAAG,IAAI,SAAS,IAAI;YAEtB,KAAK,CAAC,OAAO,OAAS,CAAC;gBACrB,KAAK,CAAC,IAAI,GAAG,UAAU,CAAC,KAAK,CAAC,IAAI;gBAClC,EAAE,GAAG,IAAI;gBACT,GAAG,CAAC,KAAK,EAAC,MAAQ,IAAG,IAAI,CAAC,SAAS,CAAC,IAAI,KAAI,IAAM;YACpD,CAAC;YACD,KAAK,CAAC,YAAY,GAAG,WAAW,KAAO,OAAO;cAAI,IAAI;YAEtD,GAAG,CAAC,EAAE,EAAC,KAAO,OAAQ,CAAC;gBACrB,aAAa,CAAC,YAAY;YAC5B,CAAC;YACD,IAAI;QACN,CAAC;;AAEL,CAAC;SAEQ,sBAAsB,CAC7B,OAAY,EACZ,eAAoB,EACpB,cAAsB,EACtB,CAAC;IACD,KAAK,CAAC,cAAc;IAEpB,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,OAAO,EAAE,IAAI,GAAK,CAAC;QACtC,KAAK,GAAG,cAAc,GAAE,MAAM,MAAK,OAAO,CAAC,IAAI;QAE/C,EAA4D,AAA5D,0DAA4D;QAC5D,EAA0C,AAA1C,wCAA0C;QAC1C,EAAE,EAAE,MAAM,KAAK,KAAK;QAEpB,EAA0E,AAA1E,wEAA0E;QAC1E,EAAkF,AAAlF,gFAAkF;QAClF,EAA+D,AAA/D,6DAA+D;QAC/D,EAAE,EAAE,eAAe,CAAC,QAAQ,CAAC,IAAI;QAEjC,EAAE,EAAE,cAAc,IAAI,IAAI,CAAC,GAAG,KAAK,cAAc,GAAG,cAAc,EAAE,CAAC;YACnE,cAAc,CAAC,IAAI,CAAC,IAAI;QAC1B,CAAC;IACH,CAAC;IAED,EAAE,EAAE,cAAc,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;QAC9B,cAAc,CAAC,OAAO,EAAE,IAAS,GAAK,CAAC;mBAC9B,OAAO,CAAC,IAAI;QACrB,CAAC;QACD,EAA6B,AAA7B,2BAA6B;QAC7B,OAAO,CAAC,UAAU;IACpB,CAAC;AACH,CAAC;AAED,EAAoD,AAApD,kDAAoD;AACpD,EAA6E,AAA7E,2EAA6E;MACvE,WAAW;gBAMH,OAAY,EAAE,aAAoC,CAAE,CAAC;aAC1D,aAAa,GAAG,aAAa;aAC7B,OAAO,GAAG,OAAO;QACtB,EAA6D,AAA7D,2DAA6D;aACxD,QAAQ,GAAG,KAAK;aAChB,YAAY,GAAG,KAAK;IAC3B,CAAC;IAED,UAAU,GAAG,CAAC;QACZ,EAA+E,AAA/E,6EAA+E;QAC/E,EAAsD,AAAtD,oDAAsD;QACtD,EAAsD,AAAtD,oDAAsD;QACtD,EAAgD,AAAhD,8CAAgD;QAChD,EAAE,OAAO,QAAQ,EAAE,CAAC;iBACb,YAAY,GAAG,IAAI;;QAE1B,CAAC;aAEI,QAAQ,GAAG,IAAI;QACpB,EAAE,GAhS8B,QAAoC,aAgSnD,CAAC;YAChB,EAAoE,AAApE,kEAAoE;YACpE,EAA2F,AAA3F,yFAA2F;iBACtF,KAAK,CAAC,QAAQ,SAAS,aAAa,CAAC,SAAS,CAAE,CAAC;gBACpD,QAAQ,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI;YAC7B,CAAC;QACH,CAAC;aAEI,OAAO,CAAC,UAAU;IACzB,CAAC;IAED,aAAa,GAAG,CAAC;aACV,QAAQ,GAAG,IAAI;IACtB,CAAC;IAED,YAAY,GAAG,CAAC;aACT,QAAQ,GAAG,KAAK;QAErB,EAAE,OAAO,YAAY,EAAE,CAAC;iBACjB,YAAY,GAAG,KAAK;iBACpB,UAAU;QACjB,CAAC;IACH,CAAC"}