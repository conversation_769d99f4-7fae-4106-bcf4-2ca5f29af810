{"version": 3, "sources": ["../../lib/pretty-bytes.ts"], "sourcesContent": ["/*\nMIT License\n\nCopyright (c) Sindre Sorhus <<EMAIL>> (sindresorhus.com)\n\nPermission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the \"Software\"), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n*/\n\nconst UNITS = ['B', 'kB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB']\n\n/*\nFormats the given number using `Number#toLocaleString`.\n- If locale is a string, the value is expected to be a locale-key (for example: `de`).\n- If locale is true, the system default locale is used for translation.\n- If no value for locale is specified, the number is returned unmodified.\n*/\nconst toLocaleString = (number: number, locale: any) => {\n  let result: any = number\n  if (typeof locale === 'string') {\n    result = number.toLocaleString(locale)\n  } else if (locale === true) {\n    result = number.toLocaleString()\n  }\n\n  return result\n}\n\nexport default function prettyBytes(number: number, options?: any): string {\n  if (!Number.isFinite(number)) {\n    throw new TypeError(\n      `Expected a finite number, got ${typeof number}: ${number}`\n    )\n  }\n\n  options = Object.assign({}, options)\n\n  if (options.signed && number === 0) {\n    return ' 0 B'\n  }\n\n  const isNegative = number < 0\n  const prefix = isNegative ? '-' : options.signed ? '+' : ''\n\n  if (isNegative) {\n    number = -number\n  }\n\n  if (number < 1) {\n    const numberString = toLocaleString(number, options.locale)\n    return prefix + numberString + ' B'\n  }\n\n  const exponent = Math.min(\n    Math.floor(Math.log10(number) / 3),\n    UNITS.length - 1\n  )\n\n  number = Number((number / Math.pow(1000, exponent)).toPrecision(3))\n  const numberString = toLocaleString(number, options.locale)\n\n  const unit = UNITS[exponent]\n\n  return prefix + numberString + ' ' + unit\n}\n"], "names": [], "mappings": ";;;;kBA+BwB,WAAW;AA/BnC,EAUE,AAVF;;;;;;;;;;AAUE,AAVF,EAUE,CAEF,KAAK,CAAC,KAAK;KAAI,CAAG;KAAE,EAAI;KAAE,EAAI;KAAE,EAAI;KAAE,EAAI;KAAE,EAAI;KAAE,EAAI;KAAE,EAAI;KAAE,EAAI;;AAElE,EAKE,AALF;;;;;AAKE,AALF,EAKE,CACF,KAAK,CAAC,cAAc,IAAI,MAAc,EAAE,MAAW,GAAK,CAAC;IACvD,GAAG,CAAC,MAAM,GAAQ,MAAM;IACxB,EAAE,SAAS,MAAM,MAAK,MAAQ,GAAE,CAAC;QAC/B,MAAM,GAAG,MAAM,CAAC,cAAc,CAAC,MAAM;IACvC,CAAC,MAAM,EAAE,EAAE,MAAM,KAAK,IAAI,EAAE,CAAC;QAC3B,MAAM,GAAG,MAAM,CAAC,cAAc;IAChC,CAAC;WAEM,MAAM;AACf,CAAC;SAEuB,WAAW,CAAC,MAAc,EAAE,OAAa,EAAU,CAAC;IAC1E,EAAE,GAAG,MAAM,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC;QAC7B,KAAK,CAAC,GAAG,CAAC,SAAS,EAChB,8BAA8B,SAAS,MAAM,CAAC,EAAE,EAAE,MAAM;IAE7D,CAAC;IAED,OAAO,GAAG,MAAM,CAAC,MAAM;OAAK,OAAO;IAEnC,EAAE,EAAE,OAAO,CAAC,MAAM,IAAI,MAAM,KAAK,CAAC,EAAE,CAAC;gBAC5B,IAAM;IACf,CAAC;IAED,KAAK,CAAC,UAAU,GAAG,MAAM,GAAG,CAAC;IAC7B,KAAK,CAAC,MAAM,GAAG,UAAU,IAAG,CAAG,IAAG,OAAO,CAAC,MAAM,IAAG,CAAG;IAEtD,EAAE,EAAE,UAAU,EAAE,CAAC;QACf,MAAM,IAAI,MAAM;IAClB,CAAC;IAED,EAAE,EAAE,MAAM,GAAG,CAAC,EAAE,CAAC;QACf,KAAK,CAAC,YAAY,GAAG,cAAc,CAAC,MAAM,EAAE,OAAO,CAAC,MAAM;eACnD,MAAM,GAAG,YAAY,IAAG,EAAI;IACrC,CAAC;IAED,KAAK,CAAC,QAAQ,GAAG,IAAI,CAAC,GAAG,CACvB,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,IAAI,CAAC,GACjC,KAAK,CAAC,MAAM,GAAG,CAAC;IAGlB,MAAM,GAAG,MAAM,EAAE,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,QAAQ,GAAG,WAAW,CAAC,CAAC;IACjE,KAAK,CAAC,YAAY,GAAG,cAAc,CAAC,MAAM,EAAE,OAAO,CAAC,MAAM;IAE1D,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,QAAQ;WAEpB,MAAM,GAAG,YAAY,IAAG,CAAG,IAAG,IAAI;AAC3C,CAAC"}