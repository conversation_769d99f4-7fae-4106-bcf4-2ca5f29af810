{"version": 3, "sources": ["../../../build/profiler/profiler.js"], "sourcesContent": ["import fs from 'fs'\nimport path from 'path'\n\nlet maybe<PERSON><PERSON><PERSON><PERSON>\ntry {\n  maybeInspector = require('inspector')\n} catch (e) {\n  console.log('Unable to CPU profile in < node 8.0')\n}\n\nclass Profiler {\n  constructor(inspector) {\n    this.session = undefined\n    this.inspector = inspector\n  }\n\n  hasSession() {\n    return this.session !== undefined\n  }\n\n  startProfiling() {\n    if (this.inspector === undefined) {\n      return Promise.resolve()\n    }\n\n    try {\n      this.session = new maybeInspector.Session()\n      this.session.connect()\n    } catch (_) {\n      this.session = undefined\n      return Promise.resolve()\n    }\n\n    return Promise.all([\n      this.sendCommand('Profiler.setSamplingInterval', {\n        interval: 100,\n      }),\n      this.sendCommand('Profiler.enable'),\n      this.sendCommand('Profiler.start'),\n    ])\n  }\n\n  sendCommand(method, params) {\n    if (this.hasSession()) {\n      return new Promise((resolve, reject) => {\n        return this.session.post(method, params, (err, sessionParams) => {\n          if (err !== null) {\n            reject(err)\n          } else {\n            resolve(sessionParams)\n          }\n        })\n      })\n    } else {\n      return Promise.resolve()\n    }\n  }\n\n  destroy() {\n    if (this.hasSession()) {\n      this.session.disconnect()\n    }\n\n    return Promise.resolve()\n  }\n\n  stopProfiling() {\n    return this.sendCommand('Profiler.stop')\n  }\n}\n\n// eslint-disable-next-line import/no-extraneous-dependencies\nconst { Tracer } = require('chrome-trace-event')\n\n/**\n * an object that wraps Tracer and Profiler with a counter\n * @typedef {Object} Trace\n * @property {Tracer} trace instance of Tracer\n * @property {number} counter Counter\n * @property {Profiler} profiler instance of Profiler\n * @property {Function} end the end function\n */\n\n/**\n * @param {string} outputPath The location where to write the log.\n * @returns {Trace} The trace object\n */\nexport const createTrace = (outputPath) => {\n  const trace = new Tracer({\n    noStream: true,\n  })\n  const profiler = new Profiler(maybeInspector)\n  if (/\\/|\\\\/.test(outputPath)) {\n    const dirPath = path.dirname(outputPath)\n    fs.mkdirSync(dirPath, { recursive: true })\n  }\n  const fsStream = fs.createWriteStream(outputPath)\n\n  let counter = 0\n\n  trace.pipe(fsStream)\n  // These are critical events that need to be inserted so that tools like\n  // chrome dev tools can load the profile.\n  trace.instantEvent({\n    name: 'TracingStartedInPage',\n    id: ++counter,\n    cat: ['disabled-by-default-devtools.timeline'],\n    args: {\n      data: {\n        sessionId: '-1',\n        page: '0xfff',\n        frames: [\n          {\n            frame: '0xfff',\n            url: 'webpack',\n            name: '',\n          },\n        ],\n      },\n    },\n  })\n\n  trace.instantEvent({\n    name: 'TracingStartedInBrowser',\n    id: ++counter,\n    cat: ['disabled-by-default-devtools.timeline'],\n    args: {\n      data: {\n        sessionId: '-1',\n      },\n    },\n  })\n\n  return {\n    trace,\n    counter,\n    profiler,\n    end: (callback) => {\n      // Wait until the write stream finishes.\n      fsStream.on('finish', () => {\n        callback()\n      })\n      // Tear down the readable trace stream.\n      trace.push(null)\n    },\n  }\n}\n"], "names": [], "mappings": ";;;;;AAAe,GAAI,CAAJ,GAAI;AACF,GAAM,CAAN,KAAM;;;;;;AAEvB,GAAG,CAAC,cAAc;IACd,CAAC;IACH,cAAc,GAAG,OAAO,EAAC,SAAW;AACtC,CAAC,QAAQ,CAAC,EAAE,CAAC;IACX,OAAO,CAAC,GAAG,EAAC,mCAAqC;AACnD,CAAC;MAEK,QAAQ;gBACA,SAAS,CAAE,CAAC;aACjB,OAAO,GAAG,SAAS;aACnB,SAAS,GAAG,SAAS;IAC5B,CAAC;IAED,UAAU,GAAG,CAAC;oBACA,OAAO,KAAK,SAAS;IACnC,CAAC;IAED,cAAc,GAAG,CAAC;QAChB,EAAE,OAAO,SAAS,KAAK,SAAS,EAAE,CAAC;mBAC1B,OAAO,CAAC,OAAO;QACxB,CAAC;YAEG,CAAC;iBACE,OAAO,GAAG,GAAG,CAAC,cAAc,CAAC,OAAO;iBACpC,OAAO,CAAC,OAAO;QACtB,CAAC,QAAQ,CAAC,EAAE,CAAC;iBACN,OAAO,GAAG,SAAS;mBACjB,OAAO,CAAC,OAAO;QACxB,CAAC;eAEM,OAAO,CAAC,GAAG;iBACX,WAAW,EAAC,4BAA8B;gBAC7C,QAAQ,EAAE,GAAG;;iBAEV,WAAW,EAAC,eAAiB;iBAC7B,WAAW,EAAC,cAAgB;;IAErC,CAAC;IAED,WAAW,CAAC,MAAM,EAAE,MAAM,EAAE,CAAC;QAC3B,EAAE,OAAO,UAAU,IAAI,CAAC;mBACf,GAAG,CAAC,OAAO,EAAE,OAAO,EAAE,MAAM,GAAK,CAAC;4BAC3B,OAAO,CAAC,IAAI,CAAC,MAAM,EAAE,MAAM,GAAG,GAAG,EAAE,aAAa,GAAK,CAAC;oBAChE,EAAE,EAAE,GAAG,KAAK,IAAI,EAAE,CAAC;wBACjB,MAAM,CAAC,GAAG;oBACZ,CAAC,MAAM,CAAC;wBACN,OAAO,CAAC,aAAa;oBACvB,CAAC;gBACH,CAAC;YACH,CAAC;QACH,CAAC,MAAM,CAAC;mBACC,OAAO,CAAC,OAAO;QACxB,CAAC;IACH,CAAC;IAED,OAAO,GAAG,CAAC;QACT,EAAE,OAAO,UAAU,IAAI,CAAC;iBACjB,OAAO,CAAC,UAAU;QACzB,CAAC;eAEM,OAAO,CAAC,OAAO;IACxB,CAAC;IAED,aAAa,GAAG,CAAC;oBACH,WAAW,EAAC,aAAe;IACzC,CAAC;;AAGH,EAA6D,AAA7D,2DAA6D;AAC7D,KAAK,GAAG,MAAM,MAAK,OAAO,EAAC,kBAAoB;AAexC,KAAK,CAAC,WAAW,IAAI,UAAU,GAAK,CAAC;IAC1C,KAAK,CAAC,KAAK,GAAG,GAAG,CAAC,MAAM;QACtB,QAAQ,EAAE,IAAI;;IAEhB,KAAK,CAAC,QAAQ,GAAG,GAAG,CAAC,QAAQ,CAAC,cAAc;IAC5C,EAAE,UAAU,IAAI,CAAC,UAAU,GAAG,CAAC;QAC7B,KAAK,CAAC,OAAO,GA5FA,KAAM,SA4FE,OAAO,CAAC,UAAU;QA7F5B,GAAI,SA8FZ,SAAS,CAAC,OAAO;YAAI,SAAS,EAAE,IAAI;;IACzC,CAAC;IACD,KAAK,CAAC,QAAQ,GAhGD,GAAI,SAgGG,iBAAiB,CAAC,UAAU;IAEhD,GAAG,CAAC,OAAO,GAAG,CAAC;IAEf,KAAK,CAAC,IAAI,CAAC,QAAQ;IACnB,EAAwE,AAAxE,sEAAwE;IACxE,EAAyC,AAAzC,uCAAyC;IACzC,KAAK,CAAC,YAAY;QAChB,IAAI,GAAE,oBAAsB;QAC5B,EAAE,IAAI,OAAO;QACb,GAAG;aAAG,qCAAuC;;QAC7C,IAAI;YACF,IAAI;gBACF,SAAS,GAAE,EAAI;gBACf,IAAI,GAAE,KAAO;gBACb,MAAM;;wBAEF,KAAK,GAAE,KAAO;wBACd,GAAG,GAAE,OAAS;wBACd,IAAI;;;;;;IAOd,KAAK,CAAC,YAAY;QAChB,IAAI,GAAE,uBAAyB;QAC/B,EAAE,IAAI,OAAO;QACb,GAAG;aAAG,qCAAuC;;QAC7C,IAAI;YACF,IAAI;gBACF,SAAS,GAAE,EAAI;;;;;QAMnB,KAAK;QACL,OAAO;QACP,QAAQ;QACR,GAAG,GAAG,QAAQ,GAAK,CAAC;YAClB,EAAwC,AAAxC,sCAAwC;YACxC,QAAQ,CAAC,EAAE,EAAC,MAAQ,OAAQ,CAAC;gBAC3B,QAAQ;YACV,CAAC;YACD,EAAuC,AAAvC,qCAAuC;YACvC,KAAK,CAAC,IAAI,CAAC,IAAI;QACjB,CAAC;;AAEL,CAAC;QA3DY,WAAW,GAAX,WAAW"}