{"version": 3, "sources": ["../../../../build/webpack/plugins/mini-css-extract-plugin.ts"], "sourcesContent": ["// @ts-ignore: TODO: remove when webpack 5 is stable\nimport MiniCssExtractPlugin from 'next/dist/compiled/mini-css-extract-plugin'\n\nexport default class NextMiniCssExtractPlugin extends MiniCssExtractPlugin {\n  __next_css_remove = true\n}\n"], "names": [], "mappings": ";;;;;AACiC,GAA4C,CAA5C,qBAA4C;;;;;;MAExD,wBAAwB,SAFZ,qBAA4C;;;aAG3E,iBAAiB,GAAG,IAAI;;;kBADL,wBAAwB"}