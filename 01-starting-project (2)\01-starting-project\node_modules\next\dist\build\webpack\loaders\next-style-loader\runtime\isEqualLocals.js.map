{"version": 3, "sources": ["../../../../../../build/webpack/loaders/next-style-loader/runtime/isEqualLocals.js"], "sourcesContent": ["function isEqualLocals(a, b, isNamedExport) {\n  if ((!a && b) || (a && !b)) {\n    return false\n  }\n\n  let p\n\n  for (p in a) {\n    if (isNamedExport && p === 'default') {\n      // eslint-disable-next-line no-continue\n      continue\n    }\n\n    if (a[p] !== b[p]) {\n      return false\n    }\n  }\n\n  for (p in b) {\n    if (isNamedExport && p === 'default') {\n      // eslint-disable-next-line no-continue\n      continue\n    }\n\n    if (!a[p]) {\n      return false\n    }\n  }\n\n  return true\n}\n\nmodule.exports = isEqualLocals\n"], "names": [], "mappings": ";SAAS,aAAa,CAAC,CAAC,EAAE,CAAC,EAAE,aAAa,EAAE,CAAC;IAC3C,EAAE,GAAI,CAAC,IAAI,CAAC,IAAM,CAAC,KAAK,CAAC,EAAG,CAAC;eACpB,KAAK;IACd,CAAC;IAED,GAAG,CAAC,CAAC;QAEA,CAAC,IAAI,CAAC,CAAE,CAAC;QACZ,EAAE,EAAE,aAAa,IAAI,CAAC,MAAK,OAAS,GAAE,CAAC;;QAGvC,CAAC;QAED,EAAE,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC;mBACX,KAAK;QACd,CAAC;IACH,CAAC;QAEI,CAAC,IAAI,CAAC,CAAE,CAAC;QACZ,EAAE,EAAE,aAAa,IAAI,CAAC,MAAK,OAAS,GAAE,CAAC;;QAGvC,CAAC;QAED,EAAE,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC;mBA<PERSON>,<PERSON>AAK;QACd,CAAC;IACH,CAAC;WAEM,IAAI;AACb,CAAC;AAED,MAAM,CAAC,OAAO,GAAG,aAAa"}