{"version": 3, "sources": ["../../lib/load-custom-routes.ts"], "sourcesContent": ["import chalk from 'chalk'\nimport { parse as parseUrl } from 'url'\nimport { NextConfig } from '../server/config'\nimport * as pathToRegexp from 'next/dist/compiled/path-to-regexp'\nimport escapeStringRegexp from 'next/dist/compiled/escape-string-regexp'\nimport {\n  PERMANENT_REDIRECT_STATUS,\n  TEMPORARY_REDIRECT_STATUS,\n} from '../shared/lib/constants'\n\nexport type RouteHas =\n  | {\n      type: 'header' | 'query' | 'cookie'\n      key: string\n      value?: string\n    }\n  | {\n      type: 'host'\n      key?: undefined\n      value: string\n    }\n\nexport type Rewrite = {\n  source: string\n  destination: string\n  basePath?: false\n  locale?: false\n  has?: RouteHas[]\n}\n\nexport type Header = {\n  source: string\n  basePath?: false\n  locale?: false\n  headers: Array<{ key: string; value: string }>\n  has?: RouteHas[]\n}\n\n// internal type used for validation (not user facing)\nexport type Redirect = {\n  source: string\n  destination: string\n  basePath?: false\n  locale?: false\n  has?: RouteHas[]\n  statusCode?: number\n  permanent?: boolean\n}\n\nexport const allowedStatusCodes = new Set([301, 302, 303, 307, 308])\nconst allowedHasTypes = new Set(['header', 'cookie', 'query', 'host'])\nconst namedGroupsRegex = /\\(\\?<([a-zA-Z][a-zA-Z0-9]*)>/g\n\nexport function getRedirectStatus(route: {\n  statusCode?: number\n  permanent?: boolean\n}): number {\n  return (\n    route.statusCode ||\n    (route.permanent ? PERMANENT_REDIRECT_STATUS : TEMPORARY_REDIRECT_STATUS)\n  )\n}\n\nexport function normalizeRouteRegex(regex: string) {\n  // clean up un-necessary escaping from regex.source which turns / into \\\\/\n  return regex.replace(/\\\\\\//g, '/')\n}\n\n// for redirects we restrict matching /_next and for all routes\n// we add an optional trailing slash at the end for easier\n// configuring between trailingSlash: true/false\nexport function modifyRouteRegex(regex: string, restrictedPaths?: string[]) {\n  if (restrictedPaths) {\n    regex = regex.replace(\n      /\\^/,\n      `^(?!${restrictedPaths\n        .map((path) => path.replace(/\\//g, '\\\\/'))\n        .join('|')})`\n    )\n  }\n  regex = regex.replace(/\\$$/, '(?:\\\\/)?$')\n  return regex\n}\n\nfunction checkRedirect(route: Redirect): {\n  invalidParts: string[]\n  hadInvalidStatus: boolean\n} {\n  const invalidParts: string[] = []\n  let hadInvalidStatus: boolean = false\n\n  if (route.statusCode && !allowedStatusCodes.has(route.statusCode)) {\n    hadInvalidStatus = true\n    invalidParts.push(`\\`statusCode\\` is not undefined or valid statusCode`)\n  }\n  if (typeof route.permanent !== 'boolean' && !route.statusCode) {\n    invalidParts.push(`\\`permanent\\` is not set to \\`true\\` or \\`false\\``)\n  }\n\n  return {\n    invalidParts,\n    hadInvalidStatus,\n  }\n}\n\nfunction checkHeader(route: Header): string[] {\n  const invalidParts: string[] = []\n\n  if (!Array.isArray(route.headers)) {\n    invalidParts.push('`headers` field must be an array')\n  } else if (route.headers.length === 0) {\n    invalidParts.push('`headers` field cannot be empty')\n  } else {\n    for (const header of route.headers) {\n      if (!header || typeof header !== 'object') {\n        invalidParts.push(\n          \"`headers` items must be object with { key: '', value: '' }\"\n        )\n        break\n      }\n      if (typeof header.key !== 'string') {\n        invalidParts.push('`key` in header item must be string')\n        break\n      }\n      if (typeof header.value !== 'string') {\n        invalidParts.push('`value` in header item must be string')\n        break\n      }\n    }\n  }\n  return invalidParts\n}\n\ntype ParseAttemptResult = {\n  error?: boolean\n  tokens?: pathToRegexp.Token[]\n  regexStr?: string\n}\n\nfunction tryParsePath(route: string, handleUrl?: boolean): ParseAttemptResult {\n  const result: ParseAttemptResult = {}\n  let routePath = route\n\n  try {\n    if (handleUrl) {\n      const parsedDestination = parseUrl(route, true)\n      routePath = `${parsedDestination.pathname!}${\n        parsedDestination.hash || ''\n      }`\n    }\n\n    // Make sure we can parse the source properly\n    result.tokens = pathToRegexp.parse(routePath)\n\n    const regex = pathToRegexp.tokensToRegexp(result.tokens)\n    result.regexStr = regex.source\n  } catch (err) {\n    // If there is an error show our error link but still show original error or a formatted one if we can\n    const errMatches = err.message.match(/at (\\d{0,})/)\n\n    if (errMatches) {\n      const position = parseInt(errMatches[1], 10)\n      console.error(\n        `\\nError parsing \\`${route}\\` ` +\n          `https://nextjs.org/docs/messages/invalid-route-source\\n` +\n          `Reason: ${err.message}\\n\\n` +\n          `  ${routePath}\\n` +\n          `  ${new Array(position).fill(' ').join('')}^\\n`\n      )\n    } else {\n      console.error(\n        `\\nError parsing ${route} https://nextjs.org/docs/messages/invalid-route-source`,\n        err\n      )\n    }\n    result.error = true\n  }\n\n  return result\n}\n\nexport type RouteType = 'rewrite' | 'redirect' | 'header'\n\nfunction checkCustomRoutes(\n  routes: Redirect[] | Header[] | Rewrite[],\n  type: RouteType\n): void {\n  if (!Array.isArray(routes)) {\n    console.error(\n      `Error: ${type}s must return an array, received ${typeof routes}.\\n` +\n        `See here for more info: https://nextjs.org/docs/messages/routes-must-be-array`\n    )\n    process.exit(1)\n  }\n\n  let numInvalidRoutes = 0\n  let hadInvalidStatus = false\n  let hadInvalidHas = false\n\n  const allowedKeys = new Set<string>(['source', 'basePath', 'locale', 'has'])\n\n  if (type === 'rewrite') {\n    allowedKeys.add('destination')\n  }\n  if (type === 'redirect') {\n    allowedKeys.add('statusCode')\n    allowedKeys.add('permanent')\n    allowedKeys.add('destination')\n  }\n  if (type === 'header') {\n    allowedKeys.add('headers')\n  }\n\n  for (const route of routes) {\n    if (!route || typeof route !== 'object') {\n      console.error(\n        `The route ${JSON.stringify(\n          route\n        )} is not a valid object with \\`source\\` and \\`${\n          type === 'header' ? 'headers' : 'destination'\n        }\\``\n      )\n      numInvalidRoutes++\n      continue\n    }\n\n    if (\n      type === 'rewrite' &&\n      (route as Rewrite).basePath === false &&\n      !(\n        (route as Rewrite).destination.startsWith('http://') ||\n        (route as Rewrite).destination.startsWith('https://')\n      )\n    ) {\n      console.error(\n        `The route ${\n          (route as Rewrite).source\n        } rewrites urls outside of the basePath. Please use a destination that starts with \\`http://\\` or \\`https://\\` https://nextjs.org/docs/messages/invalid-external-rewrite`\n      )\n      numInvalidRoutes++\n      continue\n    }\n\n    const keys = Object.keys(route)\n    const invalidKeys = keys.filter((key) => !allowedKeys.has(key))\n    const invalidParts: string[] = []\n\n    if (typeof route.basePath !== 'undefined' && route.basePath !== false) {\n      invalidParts.push('`basePath` must be undefined or false')\n    }\n\n    if (typeof route.locale !== 'undefined' && route.locale !== false) {\n      invalidParts.push('`locale` must be undefined or false')\n    }\n\n    if (typeof route.has !== 'undefined' && !Array.isArray(route.has)) {\n      invalidParts.push('`has` must be undefined or valid has object')\n      hadInvalidHas = true\n    } else if (route.has) {\n      const invalidHasItems = []\n\n      for (const hasItem of route.has) {\n        let invalidHasParts = []\n\n        if (!allowedHasTypes.has(hasItem.type)) {\n          invalidHasParts.push(`invalid type \"${hasItem.type}\"`)\n        }\n        if (typeof hasItem.key !== 'string' && hasItem.type !== 'host') {\n          invalidHasParts.push(`invalid key \"${hasItem.key}\"`)\n        }\n        if (\n          typeof hasItem.value !== 'undefined' &&\n          typeof hasItem.value !== 'string'\n        ) {\n          invalidHasParts.push(`invalid value \"${hasItem.value}\"`)\n        }\n        if (typeof hasItem.value === 'undefined' && hasItem.type === 'host') {\n          invalidHasParts.push(`value is required for \"host\" type`)\n        }\n\n        if (invalidHasParts.length > 0) {\n          invalidHasItems.push(\n            `${invalidHasParts.join(', ')} for ${JSON.stringify(hasItem)}`\n          )\n        }\n      }\n\n      if (invalidHasItems.length > 0) {\n        hadInvalidHas = true\n        const itemStr = `item${invalidHasItems.length === 1 ? '' : 's'}`\n\n        console.error(\n          `Invalid \\`has\\` ${itemStr}:\\n` + invalidHasItems.join('\\n')\n        )\n        console.error()\n        invalidParts.push(`invalid \\`has\\` ${itemStr} found`)\n      }\n    }\n\n    if (!route.source) {\n      invalidParts.push('`source` is missing')\n    } else if (typeof route.source !== 'string') {\n      invalidParts.push('`source` is not a string')\n    } else if (!route.source.startsWith('/')) {\n      invalidParts.push('`source` does not start with /')\n    }\n\n    if (type === 'header') {\n      invalidParts.push(...checkHeader(route as Header))\n    } else {\n      let _route = route as Rewrite | Redirect\n      if (!_route.destination) {\n        invalidParts.push('`destination` is missing')\n      } else if (typeof _route.destination !== 'string') {\n        invalidParts.push('`destination` is not a string')\n      } else if (\n        type === 'rewrite' &&\n        !_route.destination.match(/^(\\/|https:\\/\\/|http:\\/\\/)/)\n      ) {\n        invalidParts.push(\n          '`destination` does not start with `/`, `http://`, or `https://`'\n        )\n      }\n    }\n\n    if (type === 'redirect') {\n      const result = checkRedirect(route as Redirect)\n      hadInvalidStatus = hadInvalidStatus || result.hadInvalidStatus\n      invalidParts.push(...result.invalidParts)\n    }\n\n    let sourceTokens: pathToRegexp.Token[] | undefined\n\n    if (typeof route.source === 'string' && route.source.startsWith('/')) {\n      // only show parse error if we didn't already show error\n      // for not being a string\n      const { tokens, error, regexStr } = tryParsePath(route.source)\n\n      if (error) {\n        invalidParts.push('`source` parse failed')\n      }\n\n      if (regexStr && regexStr.length > 4096) {\n        invalidParts.push('`source` exceeds max built length of 4096')\n      }\n\n      sourceTokens = tokens\n    }\n    const hasSegments = new Set<string>()\n\n    if (route.has) {\n      for (const hasItem of route.has) {\n        if (!hasItem.value && hasItem.key) {\n          hasSegments.add(hasItem.key)\n        }\n\n        if (hasItem.value) {\n          for (const match of hasItem.value.matchAll(namedGroupsRegex)) {\n            if (match[1]) {\n              hasSegments.add(match[1])\n            }\n          }\n\n          if (hasItem.type === 'host') {\n            hasSegments.add('host')\n          }\n        }\n      }\n    }\n\n    // make sure no unnamed patterns are attempted to be used in the\n    // destination as this can cause confusion and is not allowed\n    if (typeof (route as Rewrite).destination === 'string') {\n      if (\n        (route as Rewrite).destination.startsWith('/') &&\n        Array.isArray(sourceTokens)\n      ) {\n        const unnamedInDest = new Set()\n\n        for (const token of sourceTokens) {\n          if (typeof token === 'object' && typeof token.name === 'number') {\n            const unnamedIndex = new RegExp(`:${token.name}(?!\\\\d)`)\n            if ((route as Rewrite).destination.match(unnamedIndex)) {\n              unnamedInDest.add(`:${token.name}`)\n            }\n          }\n        }\n\n        if (unnamedInDest.size > 0) {\n          invalidParts.push(\n            `\\`destination\\` has unnamed params ${[...unnamedInDest].join(\n              ', '\n            )}`\n          )\n        } else {\n          const {\n            tokens: destTokens,\n            regexStr: destRegexStr,\n            error: destinationParseFailed,\n          } = tryParsePath((route as Rewrite).destination, true)\n\n          if (destRegexStr && destRegexStr.length > 4096) {\n            invalidParts.push('`destination` exceeds max built length of 4096')\n          }\n\n          if (destinationParseFailed) {\n            invalidParts.push('`destination` parse failed')\n          } else {\n            const sourceSegments = new Set(\n              sourceTokens\n                .map((item) => typeof item === 'object' && item.name)\n                .filter(Boolean)\n            )\n            const invalidDestSegments = new Set()\n\n            for (const token of destTokens!) {\n              if (\n                typeof token === 'object' &&\n                !sourceSegments.has(token.name) &&\n                !hasSegments.has(token.name as string)\n              ) {\n                invalidDestSegments.add(token.name)\n              }\n            }\n\n            if (invalidDestSegments.size) {\n              invalidParts.push(\n                `\\`destination\\` has segments not in \\`source\\` or \\`has\\` (${[\n                  ...invalidDestSegments,\n                ].join(', ')})`\n              )\n            }\n          }\n        }\n      }\n    }\n\n    const hasInvalidKeys = invalidKeys.length > 0\n    const hasInvalidParts = invalidParts.length > 0\n\n    if (hasInvalidKeys || hasInvalidParts) {\n      console.error(\n        `${invalidParts.join(', ')}${\n          invalidKeys.length\n            ? (hasInvalidParts ? ',' : '') +\n              ` invalid field${invalidKeys.length === 1 ? '' : 's'}: ` +\n              invalidKeys.join(',')\n            : ''\n        } for route ${JSON.stringify(route)}`\n      )\n      console.error()\n      numInvalidRoutes++\n    }\n  }\n\n  if (numInvalidRoutes > 0) {\n    if (hadInvalidStatus) {\n      console.error(\n        `\\nValid redirect statusCode values are ${[...allowedStatusCodes].join(\n          ', '\n        )}`\n      )\n    }\n    if (hadInvalidHas) {\n      console.error(\n        `\\nValid \\`has\\` object shape is ${JSON.stringify(\n          {\n            type: [...allowedHasTypes].join(', '),\n            key: 'the key to check for',\n            value: 'undefined or a value string to match against',\n          },\n          null,\n          2\n        )}`\n      )\n    }\n    console.error()\n    console.error(\n      `Error: Invalid ${type}${numInvalidRoutes === 1 ? '' : 's'} found`\n    )\n    process.exit(1)\n  }\n}\n\nexport interface CustomRoutes {\n  headers: Header[]\n  rewrites: {\n    fallback: Rewrite[]\n    afterFiles: Rewrite[]\n    beforeFiles: Rewrite[]\n  }\n  redirects: Redirect[]\n}\n\nfunction processRoutes<T>(\n  routes: T,\n  config: NextConfig,\n  type: 'redirect' | 'rewrite' | 'header'\n): T {\n  const _routes = routes as any as Array<{\n    source: string\n    locale?: false\n    basePath?: false\n    destination?: string\n  }>\n  const newRoutes: typeof _routes = []\n  const defaultLocales: Array<{\n    locale: string\n    base: string\n  }> = []\n\n  if (config.i18n && type === 'redirect') {\n    for (const item of config.i18n?.domains || []) {\n      defaultLocales.push({\n        locale: item.defaultLocale,\n        base: `http${item.http ? '' : 's'}://${item.domain}`,\n      })\n    }\n\n    defaultLocales.push({\n      locale: config.i18n.defaultLocale,\n      base: '',\n    })\n  }\n\n  for (const r of _routes) {\n    const srcBasePath =\n      config.basePath && r.basePath !== false ? config.basePath : ''\n    const isExternal = !r.destination?.startsWith('/')\n    const destBasePath = srcBasePath && !isExternal ? srcBasePath : ''\n\n    if (config.i18n && r.locale !== false) {\n      if (!isExternal) {\n        defaultLocales.forEach((item) => {\n          let destination\n\n          if (r.destination) {\n            destination = item.base\n              ? `${item.base}${destBasePath}${r.destination}`\n              : `${destBasePath}${r.destination}`\n          }\n\n          newRoutes.push({\n            ...r,\n            destination,\n            source: `${srcBasePath}/${item.locale}${r.source}`,\n          })\n        })\n      }\n\n      r.source = `/:nextInternalLocale(${config.i18n.locales\n        .map((locale: string) => escapeStringRegexp(locale))\n        .join('|')})${\n        r.source === '/' && !config.trailingSlash ? '' : r.source\n      }`\n\n      if (r.destination && r.destination?.startsWith('/')) {\n        r.destination = `/:nextInternalLocale${\n          r.destination === '/' && !config.trailingSlash ? '' : r.destination\n        }`\n      }\n    }\n    r.source = `${srcBasePath}${\n      r.source === '/' && srcBasePath ? '' : r.source\n    }`\n\n    if (r.destination) {\n      r.destination = `${destBasePath}${\n        r.destination === '/' && destBasePath ? '' : r.destination\n      }`\n    }\n    newRoutes.push(r)\n  }\n  return newRoutes as any as T\n}\n\nasync function loadRedirects(config: NextConfig) {\n  if (typeof config.redirects !== 'function') {\n    return []\n  }\n  let redirects = await config.redirects()\n  // check before we process the routes and after to ensure\n  // they are still valid\n  checkCustomRoutes(redirects, 'redirect')\n\n  redirects = processRoutes(redirects, config, 'redirect')\n  checkCustomRoutes(redirects, 'redirect')\n  return redirects\n}\n\nasync function loadRewrites(config: NextConfig) {\n  if (typeof config.rewrites !== 'function') {\n    return {\n      beforeFiles: [],\n      afterFiles: [],\n      fallback: [],\n    }\n  }\n  const _rewrites = await config.rewrites()\n  let beforeFiles: Rewrite[] = []\n  let afterFiles: Rewrite[] = []\n  let fallback: Rewrite[] = []\n\n  if (\n    !Array.isArray(_rewrites) &&\n    typeof _rewrites === 'object' &&\n    Object.keys(_rewrites).every(\n      (key) =>\n        key === 'beforeFiles' || key === 'afterFiles' || key === 'fallback'\n    )\n  ) {\n    beforeFiles = _rewrites.beforeFiles || []\n    afterFiles = _rewrites.afterFiles || []\n    fallback = _rewrites.fallback || []\n  } else {\n    afterFiles = _rewrites as any\n  }\n  // check before we process the routes and after to ensure\n  // they are still valid\n  checkCustomRoutes(beforeFiles, 'rewrite')\n  checkCustomRoutes(afterFiles, 'rewrite')\n  checkCustomRoutes(fallback, 'rewrite')\n\n  beforeFiles = processRoutes(beforeFiles, config, 'rewrite')\n  afterFiles = processRoutes(afterFiles, config, 'rewrite')\n  fallback = processRoutes(fallback, config, 'rewrite')\n\n  checkCustomRoutes(beforeFiles, 'rewrite')\n  checkCustomRoutes(afterFiles, 'rewrite')\n  checkCustomRoutes(fallback, 'rewrite')\n\n  return {\n    beforeFiles,\n    afterFiles,\n    fallback,\n  }\n}\n\nasync function loadHeaders(config: NextConfig) {\n  if (typeof config.headers !== 'function') {\n    return []\n  }\n  let headers = await config.headers()\n  // check before we process the routes and after to ensure\n  // they are still valid\n  checkCustomRoutes(headers, 'header')\n\n  headers = processRoutes(headers, config, 'header')\n  checkCustomRoutes(headers, 'header')\n  return headers\n}\n\nexport default async function loadCustomRoutes(\n  config: NextConfig\n): Promise<CustomRoutes> {\n  const [headers, rewrites, redirects] = await Promise.all([\n    loadHeaders(config),\n    loadRewrites(config),\n    loadRedirects(config),\n  ])\n\n  const totalRewrites =\n    rewrites.beforeFiles.length +\n    rewrites.afterFiles.length +\n    rewrites.fallback.length\n\n  const totalRoutes = headers.length + redirects.length + totalRewrites\n\n  if (totalRoutes > 1000) {\n    console.warn(\n      chalk.bold.yellow(`Warning: `) +\n        `total number of custom routes exceeds 1000, this can reduce performance. Route counts:\\n` +\n        `headers: ${headers.length}\\n` +\n        `rewrites: ${totalRewrites}\\n` +\n        `redirects: ${redirects.length}\\n` +\n        `See more info: https://nextjs.org/docs/messages/max-custom-routes-reached`\n    )\n  }\n\n  if (config.trailingSlash) {\n    redirects.unshift(\n      {\n        source: '/:file((?!\\\\.well-known(?:/.*)?)(?:[^/]+/)*[^/]+\\\\.\\\\w+)/',\n        destination: '/:file',\n        permanent: true,\n        locale: config.i18n ? false : undefined,\n        internal: true,\n      } as Redirect,\n      {\n        source: '/:notfile((?!\\\\.well-known(?:/.*)?)(?:[^/]+/)*[^/\\\\.]+)',\n        destination: '/:notfile/',\n        permanent: true,\n        locale: config.i18n ? false : undefined,\n        internal: true,\n      } as Redirect\n    )\n    if (config.basePath) {\n      redirects.unshift({\n        source: config.basePath,\n        destination: config.basePath + '/',\n        permanent: true,\n        basePath: false,\n        locale: config.i18n ? false : undefined,\n        internal: true,\n      } as Redirect)\n    }\n  } else {\n    redirects.unshift({\n      source: '/:path+/',\n      destination: '/:path+',\n      permanent: true,\n      locale: config.i18n ? false : undefined,\n      internal: true,\n    } as Redirect)\n    if (config.basePath) {\n      redirects.unshift({\n        source: config.basePath + '/',\n        destination: config.basePath,\n        permanent: true,\n        basePath: false,\n        locale: config.i18n ? false : undefined,\n        internal: true,\n      } as Redirect)\n    }\n  }\n\n  return {\n    headers,\n    rewrites,\n    redirects,\n  }\n}\n"], "names": [], "mappings": ";;;;QAqDgB,iBAAiB,GAAjB,iBAAiB;QAUjB,mBAAmB,GAAnB,mBAAmB;QAQnB,gBAAgB,GAAhB,gBAAgB;kBAqkBF,gBAAgB;;AA5oB5B,GAAO,CAAP,MAAO;AACS,GAAK,CAAL,IAAK;AAE3B,GAAY,CAAZ,YAAY;AACO,GAAyC,CAAzC,mBAAyC;AAIjE,GAAyB,CAAzB,UAAyB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyCzB,KAAK,CAAC,kBAAkB,GAAG,GAAG,CAAC,GAAG;IAAE,GAAG;IAAE,GAAG;IAAE,GAAG;IAAE,GAAG;IAAE,GAAG;;QAArD,kBAAkB,GAAlB,kBAAkB;AAC/B,KAAK,CAAC,eAAe,GAAG,GAAG,CAAC,GAAG;KAAE,MAAQ;KAAE,MAAQ;KAAE,KAAO;KAAE,IAAM;;AACpE,KAAK,CAAC,gBAAgB;SAEN,iBAAiB,CAAC,KAGjC,EAAU,CAAC;WAER,KAAK,CAAC,UAAU,KACf,KAAK,CAAC,SAAS,GAnDb,UAAyB,6BAAzB,UAAyB;AAqDhC,CAAC;SAEe,mBAAmB,CAAC,KAAa,EAAE,CAAC;IAClD,EAA0E,AAA1E,wEAA0E;WACnE,KAAK,CAAC,OAAO,WAAU,CAAG;AACnC,CAAC;SAKe,gBAAgB,CAAC,KAAa,EAAE,eAA0B,EAAE,CAAC;IAC3E,EAAE,EAAE,eAAe,EAAE,CAAC;QACpB,KAAK,GAAG,KAAK,CAAC,OAAO,QAElB,IAAI,EAAE,eAAe,CACnB,GAAG,EAAE,IAAI,GAAK,IAAI,CAAC,OAAO,SAAQ,GAAK;UACvC,IAAI,EAAC,CAAG,GAAE,CAAC;IAElB,CAAC;IACD,KAAK,GAAG,KAAK,CAAC,OAAO,SAAQ,SAAW;WACjC,KAAK;AACd,CAAC;SAEQ,aAAa,CAAC,KAAe,EAGpC,CAAC;IACD,KAAK,CAAC,YAAY;IAClB,GAAG,CAAC,gBAAgB,GAAY,KAAK;IAErC,EAAE,EAAE,KAAK,CAAC,UAAU,KAAK,kBAAkB,CAAC,GAAG,CAAC,KAAK,CAAC,UAAU,GAAG,CAAC;QAClE,gBAAgB,GAAG,IAAI;QACvB,YAAY,CAAC,IAAI,EAAE,mDAAmD;IACxE,CAAC;IACD,EAAE,SAAS,KAAK,CAAC,SAAS,MAAK,OAAS,MAAK,KAAK,CAAC,UAAU,EAAE,CAAC;QAC9D,YAAY,CAAC,IAAI,EAAE,iDAAiD;IACtE,CAAC;;QAGC,YAAY;QACZ,gBAAgB;;AAEpB,CAAC;SAEQ,WAAW,CAAC,KAAa,EAAY,CAAC;IAC7C,KAAK,CAAC,YAAY;IAElB,EAAE,GAAG,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,OAAO,GAAG,CAAC;QAClC,YAAY,CAAC,IAAI,EAAC,gCAAkC;IACtD,CAAC,MAAM,EAAE,EAAE,KAAK,CAAC,OAAO,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;QACtC,YAAY,CAAC,IAAI,EAAC,+BAAiC;IACrD,CAAC,MAAM,CAAC;aACD,KAAK,CAAC,MAAM,IAAI,KAAK,CAAC,OAAO,CAAE,CAAC;YACnC,EAAE,GAAG,MAAM,WAAW,MAAM,MAAK,MAAQ,GAAE,CAAC;gBAC1C,YAAY,CAAC,IAAI,EACf,0DAA4D;;YAGhE,CAAC;YACD,EAAE,SAAS,MAAM,CAAC,GAAG,MAAK,MAAQ,GAAE,CAAC;gBACnC,YAAY,CAAC,IAAI,EAAC,mCAAqC;;YAEzD,CAAC;YACD,EAAE,SAAS,MAAM,CAAC,KAAK,MAAK,MAAQ,GAAE,CAAC;gBACrC,YAAY,CAAC,IAAI,EAAC,qCAAuC;;YAE3D,CAAC;QACH,CAAC;IACH,CAAC;WACM,YAAY;AACrB,CAAC;SAQQ,YAAY,CAAC,KAAa,EAAE,SAAmB,EAAsB,CAAC;IAC7E,KAAK,CAAC,MAAM;;IACZ,GAAG,CAAC,SAAS,GAAG,KAAK;QAEjB,CAAC;QACH,EAAE,EAAE,SAAS,EAAE,CAAC;YACd,KAAK,CAAC,iBAAiB,OAhJK,IAAK,QAgJE,KAAK,EAAE,IAAI;YAC9C,SAAS,MAAM,iBAAiB,CAAC,QAAQ,GACvC,iBAAiB,CAAC,IAAI;QAE1B,CAAC;QAED,EAA6C,AAA7C,2CAA6C;QAC7C,MAAM,CAAC,MAAM,GArJL,YAAY,CAqJS,KAAK,CAAC,SAAS;QAE5C,KAAK,CAAC,KAAK,GAvJH,YAAY,CAuJO,cAAc,CAAC,MAAM,CAAC,MAAM;QACvD,MAAM,CAAC,QAAQ,GAAG,KAAK,CAAC,MAAM;IAChC,CAAC,QAAQ,GAAG,EAAE,CAAC;QACb,EAAsG,AAAtG,oGAAsG;QACtG,KAAK,CAAC,UAAU,GAAG,GAAG,CAAC,OAAO,CAAC,KAAK;QAEpC,EAAE,EAAE,UAAU,EAAE,CAAC;YACf,KAAK,CAAC,QAAQ,GAAG,QAAQ,CAAC,UAAU,CAAC,CAAC,GAAG,EAAE;YAC3C,OAAO,CAAC,KAAK,EACV,kBAAkB,EAAE,KAAK,CAAC,GAAG,KAC3B,uDAAuD,KACvD,QAAQ,EAAE,GAAG,CAAC,OAAO,CAAC,IAAI,KAC1B,EAAE,EAAE,SAAS,CAAC,EAAE,KAChB,EAAE,EAAE,GAAG,CAAC,KAAK,CAAC,QAAQ,EAAE,IAAI,EAAC,CAAG,GAAE,IAAI,KAAK,GAAG;QAErD,CAAC,MAAM,CAAC;YACN,OAAO,CAAC,KAAK,EACV,gBAAgB,EAAE,KAAK,CAAC,sDAAsD,GAC/E,GAAG;QAEP,CAAC;QACD,MAAM,CAAC,KAAK,GAAG,IAAI;IACrB,CAAC;WAEM,MAAM;AACf,CAAC;SAIQ,iBAAiB,CACxB,MAAyC,EACzC,IAAe,EACT,CAAC;IACP,EAAE,GAAG,KAAK,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC;QAC3B,OAAO,CAAC,KAAK,EACV,OAAO,EAAE,IAAI,CAAC,iCAAiC,SAAS,MAAM,CAAC,GAAG,KAChE,6EAA6E;QAElF,OAAO,CAAC,IAAI,CAAC,CAAC;IAChB,CAAC;IAED,GAAG,CAAC,gBAAgB,GAAG,CAAC;IACxB,GAAG,CAAC,gBAAgB,GAAG,KAAK;IAC5B,GAAG,CAAC,aAAa,GAAG,KAAK;IAEzB,KAAK,CAAC,WAAW,GAAG,GAAG,CAAC,GAAG;SAAU,MAAQ;SAAE,QAAU;SAAE,MAAQ;SAAE,GAAK;;IAE1E,EAAE,EAAE,IAAI,MAAK,OAAS,GAAE,CAAC;QACvB,WAAW,CAAC,GAAG,EAAC,WAAa;IAC/B,CAAC;IACD,EAAE,EAAE,IAAI,MAAK,QAAU,GAAE,CAAC;QACxB,WAAW,CAAC,GAAG,EAAC,UAAY;QAC5B,WAAW,CAAC,GAAG,EAAC,SAAW;QAC3B,WAAW,CAAC,GAAG,EAAC,WAAa;IAC/B,CAAC;IACD,EAAE,EAAE,IAAI,MAAK,MAAQ,GAAE,CAAC;QACtB,WAAW,CAAC,GAAG,EAAC,OAAS;IAC3B,CAAC;SAEI,KAAK,CAAC,KAAK,IAAI,MAAM,CAAE,CAAC;QAC3B,EAAE,GAAG,KAAK,WAAW,KAAK,MAAK,MAAQ,GAAE,CAAC;YACxC,OAAO,CAAC,KAAK,EACV,UAAU,EAAE,IAAI,CAAC,SAAS,CACzB,KAAK,EACL,6CAA6C,EAC7C,IAAI,MAAK,MAAQ,KAAG,OAAS,KAAG,WAAa,EAC9C,EAAE;YAEL,gBAAgB;;QAElB,CAAC;QAED,EAAE,EACA,IAAI,MAAK,OAAS,KACjB,KAAK,CAAa,QAAQ,KAAK,KAAK,MAElC,KAAK,CAAa,WAAW,CAAC,UAAU,EAAC,OAAS,MAClD,KAAK,CAAa,WAAW,CAAC,UAAU,EAAC,QAAU,KAEtD,CAAC;YACD,OAAO,CAAC,KAAK,EACV,UAAU,EACR,KAAK,CAAa,MAAM,CAC1B,uKAAuK;YAE1K,gBAAgB;;QAElB,CAAC;QAED,KAAK,CAAC,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,KAAK;QAC9B,KAAK,CAAC,WAAW,GAAG,IAAI,CAAC,MAAM,EAAE,GAAG,IAAM,WAAW,CAAC,GAAG,CAAC,GAAG;;QAC7D,KAAK,CAAC,YAAY;QAElB,EAAE,SAAS,KAAK,CAAC,QAAQ,MAAK,SAAW,KAAI,KAAK,CAAC,QAAQ,KAAK,KAAK,EAAE,CAAC;YACtE,YAAY,CAAC,IAAI,EAAC,qCAAuC;QAC3D,CAAC;QAED,EAAE,SAAS,KAAK,CAAC,MAAM,MAAK,SAAW,KAAI,KAAK,CAAC,MAAM,KAAK,KAAK,EAAE,CAAC;YAClE,YAAY,CAAC,IAAI,EAAC,mCAAqC;QACzD,CAAC;QAED,EAAE,SAAS,KAAK,CAAC,GAAG,MAAK,SAAW,MAAK,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,GAAG,GAAG,CAAC;YAClE,YAAY,CAAC,IAAI,EAAC,2CAA6C;YAC/D,aAAa,GAAG,IAAI;QACtB,CAAC,MAAM,EAAE,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC;YACrB,KAAK,CAAC,eAAe;iBAEhB,KAAK,CAAC,OAAO,IAAI,KAAK,CAAC,GAAG,CAAE,CAAC;gBAChC,GAAG,CAAC,eAAe;gBAEnB,EAAE,GAAG,eAAe,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI,GAAG,CAAC;oBACvC,eAAe,CAAC,IAAI,EAAE,cAAc,EAAE,OAAO,CAAC,IAAI,CAAC,CAAC;gBACtD,CAAC;gBACD,EAAE,SAAS,OAAO,CAAC,GAAG,MAAK,MAAQ,KAAI,OAAO,CAAC,IAAI,MAAK,IAAM,GAAE,CAAC;oBAC/D,eAAe,CAAC,IAAI,EAAE,aAAa,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC;gBACpD,CAAC;gBACD,EAAE,SACO,OAAO,CAAC,KAAK,MAAK,SAAW,YAC7B,OAAO,CAAC,KAAK,MAAK,MAAQ,GACjC,CAAC;oBACD,eAAe,CAAC,IAAI,EAAE,eAAe,EAAE,OAAO,CAAC,KAAK,CAAC,CAAC;gBACxD,CAAC;gBACD,EAAE,SAAS,OAAO,CAAC,KAAK,MAAK,SAAW,KAAI,OAAO,CAAC,IAAI,MAAK,IAAM,GAAE,CAAC;oBACpE,eAAe,CAAC,IAAI,EAAE,iCAAiC;gBACzD,CAAC;gBAED,EAAE,EAAE,eAAe,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;oBAC/B,eAAe,CAAC,IAAI,IACf,eAAe,CAAC,IAAI,EAAC,EAAI,GAAE,KAAK,EAAE,IAAI,CAAC,SAAS,CAAC,OAAO;gBAE/D,CAAC;YACH,CAAC;YAED,EAAE,EAAE,eAAe,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAC/B,aAAa,GAAG,IAAI;gBACpB,KAAK,CAAC,OAAO,IAAI,IAAI,EAAE,eAAe,CAAC,MAAM,KAAK,CAAC,SAAQ,CAAG;gBAE9D,OAAO,CAAC,KAAK,EACV,gBAAgB,EAAE,OAAO,CAAC,GAAG,IAAI,eAAe,CAAC,IAAI,EAAC,EAAI;gBAE7D,OAAO,CAAC,KAAK;gBACb,YAAY,CAAC,IAAI,EAAE,gBAAgB,EAAE,OAAO,CAAC,MAAM;YACrD,CAAC;QACH,CAAC;QAED,EAAE,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC;YAClB,YAAY,CAAC,IAAI,EAAC,mBAAqB;QACzC,CAAC,MAAM,EAAE,SAAS,KAAK,CAAC,MAAM,MAAK,MAAQ,GAAE,CAAC;YAC5C,YAAY,CAAC,IAAI,EAAC,wBAA0B;QAC9C,CAAC,MAAM,EAAE,GAAG,KAAK,CAAC,MAAM,CAAC,UAAU,EAAC,CAAG,IAAG,CAAC;YACzC,YAAY,CAAC,IAAI,EAAC,8BAAgC;QACpD,CAAC;QAED,EAAE,EAAE,IAAI,MAAK,MAAQ,GAAE,CAAC;YACtB,YAAY,CAAC,IAAI,IAAI,WAAW,CAAC,KAAK;QACxC,CAAC,MAAM,CAAC;YACN,GAAG,CAAC,MAAM,GAAG,KAAK;YAClB,EAAE,GAAG,MAAM,CAAC,WAAW,EAAE,CAAC;gBACxB,YAAY,CAAC,IAAI,EAAC,wBAA0B;YAC9C,CAAC,MAAM,EAAE,SAAS,MAAM,CAAC,WAAW,MAAK,MAAQ,GAAE,CAAC;gBAClD,YAAY,CAAC,IAAI,EAAC,6BAA+B;YACnD,CAAC,MAAM,EAAE,EACP,IAAI,MAAK,OAAS,MACjB,MAAM,CAAC,WAAW,CAAC,KAAK,gCACzB,CAAC;gBACD,YAAY,CAAC,IAAI,EACf,+DAAiE;YAErE,CAAC;QACH,CAAC;QAED,EAAE,EAAE,IAAI,MAAK,QAAU,GAAE,CAAC;YACxB,KAAK,CAAC,MAAM,GAAG,aAAa,CAAC,KAAK;YAClC,gBAAgB,GAAG,gBAAgB,IAAI,MAAM,CAAC,gBAAgB;YAC9D,YAAY,CAAC,IAAI,IAAI,MAAM,CAAC,YAAY;QAC1C,CAAC;QAED,GAAG,CAAC,YAAY;QAEhB,EAAE,SAAS,KAAK,CAAC,MAAM,MAAK,MAAQ,KAAI,KAAK,CAAC,MAAM,CAAC,UAAU,EAAC,CAAG,IAAG,CAAC;YACrE,EAAwD,AAAxD,sDAAwD;YACxD,EAAyB,AAAzB,uBAAyB;YACzB,KAAK,GAAG,MAAM,GAAE,KAAK,GAAE,QAAQ,MAAK,YAAY,CAAC,KAAK,CAAC,MAAM;YAE7D,EAAE,EAAE,KAAK,EAAE,CAAC;gBACV,YAAY,CAAC,IAAI,EAAC,qBAAuB;YAC3C,CAAC;YAED,EAAE,EAAE,QAAQ,IAAI,QAAQ,CAAC,MAAM,GAAG,IAAI,EAAE,CAAC;gBACvC,YAAY,CAAC,IAAI,EAAC,yCAA2C;YAC/D,CAAC;YAED,YAAY,GAAG,MAAM;QACvB,CAAC;QACD,KAAK,CAAC,WAAW,GAAG,GAAG,CAAC,GAAG;QAE3B,EAAE,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC;iBACT,KAAK,CAAC,OAAO,IAAI,KAAK,CAAC,GAAG,CAAE,CAAC;gBAChC,EAAE,GAAG,OAAO,CAAC,KAAK,IAAI,OAAO,CAAC,GAAG,EAAE,CAAC;oBAClC,WAAW,CAAC,GAAG,CAAC,OAAO,CAAC,GAAG;gBAC7B,CAAC;gBAED,EAAE,EAAE,OAAO,CAAC,KAAK,EAAE,CAAC;yBACb,KAAK,CAAC,KAAK,IAAI,OAAO,CAAC,KAAK,CAAC,QAAQ,CAAC,gBAAgB,EAAG,CAAC;wBAC7D,EAAE,EAAE,KAAK,CAAC,CAAC,GAAG,CAAC;4BACb,WAAW,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;wBACzB,CAAC;oBACH,CAAC;oBAED,EAAE,EAAE,OAAO,CAAC,IAAI,MAAK,IAAM,GAAE,CAAC;wBAC5B,WAAW,CAAC,GAAG,EAAC,IAAM;oBACxB,CAAC;gBACH,CAAC;YACH,CAAC;QACH,CAAC;QAED,EAAgE,AAAhE,8DAAgE;QAChE,EAA6D,AAA7D,2DAA6D;QAC7D,EAAE,SAAU,KAAK,CAAa,WAAW,MAAK,MAAQ,GAAE,CAAC;YACvD,EAAE,EACC,KAAK,CAAa,WAAW,CAAC,UAAU,EAAC,CAAG,MAC7C,KAAK,CAAC,OAAO,CAAC,YAAY,GAC1B,CAAC;gBACD,KAAK,CAAC,aAAa,GAAG,GAAG,CAAC,GAAG;qBAExB,KAAK,CAAC,KAAK,IAAI,YAAY,CAAE,CAAC;oBACjC,EAAE,SAAS,KAAK,MAAK,MAAQ,YAAW,KAAK,CAAC,IAAI,MAAK,MAAQ,GAAE,CAAC;wBAChE,KAAK,CAAC,YAAY,GAAG,GAAG,CAAC,MAAM,EAAE,CAAC,EAAE,KAAK,CAAC,IAAI,CAAC,OAAO;wBACtD,EAAE,EAAG,KAAK,CAAa,WAAW,CAAC,KAAK,CAAC,YAAY,GAAG,CAAC;4BACvD,aAAa,CAAC,GAAG,EAAE,CAAC,EAAE,KAAK,CAAC,IAAI;wBAClC,CAAC;oBACH,CAAC;gBACH,CAAC;gBAED,EAAE,EAAE,aAAa,CAAC,IAAI,GAAG,CAAC,EAAE,CAAC;oBAC3B,YAAY,CAAC,IAAI,EACd,mCAAmC;2BAAM,aAAa;sBAAE,IAAI,EAC3D,EAAI;gBAGV,CAAC,MAAM,CAAC;oBACN,KAAK,GACH,MAAM,EAAE,UAAU,GAClB,QAAQ,EAAE,YAAY,GACtB,KAAK,EAAE,sBAAsB,QAC3B,YAAY,CAAE,KAAK,CAAa,WAAW,EAAE,IAAI;oBAErD,EAAE,EAAE,YAAY,IAAI,YAAY,CAAC,MAAM,GAAG,IAAI,EAAE,CAAC;wBAC/C,YAAY,CAAC,IAAI,EAAC,8CAAgD;oBACpE,CAAC;oBAED,EAAE,EAAE,sBAAsB,EAAE,CAAC;wBAC3B,YAAY,CAAC,IAAI,EAAC,0BAA4B;oBAChD,CAAC,MAAM,CAAC;wBACN,KAAK,CAAC,cAAc,GAAG,GAAG,CAAC,GAAG,CAC5B,YAAY,CACT,GAAG,EAAE,IAAI,UAAY,IAAI,MAAK,MAAQ,KAAI,IAAI,CAAC,IAAI;0BACnD,MAAM,CAAC,OAAO;wBAEnB,KAAK,CAAC,mBAAmB,GAAG,GAAG,CAAC,GAAG;6BAE9B,KAAK,CAAC,MAAK,IAAI,UAAU,CAAG,CAAC;4BAChC,EAAE,SACO,MAAK,MAAK,MAAQ,MACxB,cAAc,CAAC,GAAG,CAAC,MAAK,CAAC,IAAI,MAC7B,WAAW,CAAC,GAAG,CAAC,MAAK,CAAC,IAAI,GAC3B,CAAC;gCACD,mBAAmB,CAAC,GAAG,CAAC,MAAK,CAAC,IAAI;4BACpC,CAAC;wBACH,CAAC;wBAED,EAAE,EAAE,mBAAmB,CAAC,IAAI,EAAE,CAAC;4BAC7B,YAAY,CAAC,IAAI,EACd,2DAA2D;mCACvD,mBAAmB;8BACtB,IAAI,EAAC,EAAI,GAAE,CAAC;wBAElB,CAAC;oBACH,CAAC;gBACH,CAAC;YACH,CAAC;QACH,CAAC;QAED,KAAK,CAAC,cAAc,GAAG,WAAW,CAAC,MAAM,GAAG,CAAC;QAC7C,KAAK,CAAC,eAAe,GAAG,YAAY,CAAC,MAAM,GAAG,CAAC;QAE/C,EAAE,EAAE,cAAc,IAAI,eAAe,EAAE,CAAC;YACtC,OAAO,CAAC,KAAK,IACR,YAAY,CAAC,IAAI,EAAC,EAAI,KACvB,WAAW,CAAC,MAAM,IACb,eAAe,IAAG,CAAG,WACrB,cAAc,EAAE,WAAW,CAAC,MAAM,KAAK,CAAC,SAAQ,CAAG,EAAC,EAAE,IACvD,WAAW,CAAC,IAAI,EAAC,CAAG,QAEzB,WAAW,EAAE,IAAI,CAAC,SAAS,CAAC,KAAK;YAEpC,OAAO,CAAC,KAAK;YACb,gBAAgB;QAClB,CAAC;IACH,CAAC;IAED,EAAE,EAAE,gBAAgB,GAAG,CAAC,EAAE,CAAC;QACzB,EAAE,EAAE,gBAAgB,EAAE,CAAC;YACrB,OAAO,CAAC,KAAK,EACV,uCAAuC;mBAAM,kBAAkB;cAAE,IAAI,EACpE,EAAI;QAGV,CAAC;QACD,EAAE,EAAE,aAAa,EAAE,CAAC;YAClB,OAAO,CAAC,KAAK,EACV,gCAAgC,EAAE,IAAI,CAAC,SAAS;gBAE7C,IAAI;uBAAM,eAAe;kBAAE,IAAI,EAAC,EAAI;gBACpC,GAAG,GAAE,oBAAsB;gBAC3B,KAAK,GAAE,4CAA8C;eAEvD,IAAI,EACJ,CAAC;QAGP,CAAC;QACD,OAAO,CAAC,KAAK;QACb,OAAO,CAAC,KAAK,EACV,eAAe,EAAE,IAAI,GAAG,gBAAgB,KAAK,CAAC,SAAQ,CAAG,EAAC,MAAM;QAEnE,OAAO,CAAC,IAAI,CAAC,CAAC;IAChB,CAAC;AACH,CAAC;SAYQ,aAAa,CACpB,MAAS,EACT,MAAkB,EAClB,IAAuC,EACpC,CAAC;IACJ,KAAK,CAAC,OAAO,GAAG,MAAM;IAMtB,KAAK,CAAC,SAAS;IACf,KAAK,CAAC,cAAc;IAKpB,EAAE,EAAE,MAAM,CAAC,IAAI,IAAI,IAAI,MAAK,QAAU,GAAE,CAAC;YACpB,GAAW;aAAzB,KAAK,CAAC,IAAI,MAAI,GAAW,GAAX,MAAM,CAAC,IAAI,cAAX,GAAW,UAAX,CAAoB,QAApB,CAAoB,GAApB,GAAW,CAAE,OAAO,QAAQ,CAAC;YAC9C,cAAc,CAAC,IAAI;gBACjB,MAAM,EAAE,IAAI,CAAC,aAAa;gBAC1B,IAAI,GAAG,IAAI,EAAE,IAAI,CAAC,IAAI,SAAQ,CAAG,EAAC,GAAG,EAAE,IAAI,CAAC,MAAM;;QAEtD,CAAC;QAED,cAAc,CAAC,IAAI;YACjB,MAAM,EAAE,MAAM,CAAC,IAAI,CAAC,aAAa;YACjC,IAAI;;IAER,CAAC;SAEI,KAAK,CAAC,CAAC,IAAI,OAAO,CAAE,CAAC;YAGJ,GAAa;QAFjC,KAAK,CAAC,WAAW,GACf,MAAM,CAAC,QAAQ,IAAI,CAAC,CAAC,QAAQ,KAAK,KAAK,GAAG,MAAM,CAAC,QAAQ;QAC3D,KAAK,CAAC,UAAU,MAAI,GAAa,GAAb,CAAC,CAAC,WAAW,cAAb,GAAa,UAAb,CAAyB,QAAzB,CAAyB,GAAzB,GAAa,CAAE,UAAU,EAAC,CAAG;QACjD,KAAK,CAAC,YAAY,GAAG,WAAW,KAAK,UAAU,GAAG,WAAW;QAE7D,EAAE,EAAE,MAAM,CAAC,IAAI,IAAI,CAAC,CAAC,MAAM,KAAK,KAAK,EAAE,CAAC;gBAyBjB,IAAa;YAxBlC,EAAE,GAAG,UAAU,EAAE,CAAC;gBAChB,cAAc,CAAC,OAAO,EAAE,IAAI,GAAK,CAAC;oBAChC,GAAG,CAAC,WAAW;oBAEf,EAAE,EAAE,CAAC,CAAC,WAAW,EAAE,CAAC;wBAClB,WAAW,GAAG,IAAI,CAAC,IAAI,MAChB,IAAI,CAAC,IAAI,GAAG,YAAY,GAAG,CAAC,CAAC,WAAW,QACxC,YAAY,GAAG,CAAC,CAAC,WAAW;oBACrC,CAAC;oBAED,SAAS,CAAC,IAAI;2BACT,CAAC;wBACJ,WAAW;wBACX,MAAM,KAAK,WAAW,CAAC,CAAC,EAAE,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,MAAM;;gBAEpD,CAAC;YACH,CAAC;YAED,CAAC,CAAC,MAAM,IAAI,qBAAqB,EAAE,MAAM,CAAC,IAAI,CAAC,OAAO,CACnD,GAAG,EAAE,MAAc,OAniBG,mBAAyC,UAmiBpB,MAAM;cACjD,IAAI,EAAC,CAAG,GAAE,CAAC,EACZ,CAAC,CAAC,MAAM,MAAK,CAAG,MAAK,MAAM,CAAC,aAAa,QAAQ,CAAC,CAAC,MAAM;YAG3D,EAAE,EAAE,CAAC,CAAC,WAAW,MAAI,IAAa,GAAb,CAAC,CAAC,WAAW,cAAb,IAAa,UAAb,CAAyB,QAAzB,CAAyB,GAAzB,IAAa,CAAE,UAAU,EAAC,CAAG,KAAG,CAAC;gBACpD,CAAC,CAAC,WAAW,IAAI,oBAAoB,EACnC,CAAC,CAAC,WAAW,MAAK,CAAG,MAAK,MAAM,CAAC,aAAa,QAAQ,CAAC,CAAC,WAAW;YAEvE,CAAC;QACH,CAAC;QACD,CAAC,CAAC,MAAM,MAAM,WAAW,GACvB,CAAC,CAAC,MAAM,MAAK,CAAG,KAAI,WAAW,QAAQ,CAAC,CAAC,MAAM;QAGjD,EAAE,EAAE,CAAC,CAAC,WAAW,EAAE,CAAC;YAClB,CAAC,CAAC,WAAW,MAAM,YAAY,GAC7B,CAAC,CAAC,WAAW,MAAK,CAAG,KAAI,YAAY,QAAQ,CAAC,CAAC,WAAW;QAE9D,CAAC;QACD,SAAS,CAAC,IAAI,CAAC,CAAC;IAClB,CAAC;WACM,SAAS;AAClB,CAAC;eAEc,aAAa,CAAC,MAAkB,EAAE,CAAC;IAChD,EAAE,SAAS,MAAM,CAAC,SAAS,MAAK,QAAU,GAAE,CAAC;;IAE7C,CAAC;IACD,GAAG,CAAC,SAAS,SAAS,MAAM,CAAC,SAAS;IACtC,EAAyD,AAAzD,uDAAyD;IACzD,EAAuB,AAAvB,qBAAuB;IACvB,iBAAiB,CAAC,SAAS,GAAE,QAAU;IAEvC,SAAS,GAAG,aAAa,CAAC,SAAS,EAAE,MAAM,GAAE,QAAU;IACvD,iBAAiB,CAAC,SAAS,GAAE,QAAU;WAChC,SAAS;AAClB,CAAC;eAEc,YAAY,CAAC,MAAkB,EAAE,CAAC;IAC/C,EAAE,SAAS,MAAM,CAAC,QAAQ,MAAK,QAAU,GAAE,CAAC;;YAExC,WAAW;YACX,UAAU;YACV,QAAQ;;IAEZ,CAAC;IACD,KAAK,CAAC,SAAS,SAAS,MAAM,CAAC,QAAQ;IACvC,GAAG,CAAC,WAAW;IACf,GAAG,CAAC,UAAU;IACd,GAAG,CAAC,QAAQ;IAEZ,EAAE,GACC,KAAK,CAAC,OAAO,CAAC,SAAS,YACjB,SAAS,MAAK,MAAQ,KAC7B,MAAM,CAAC,IAAI,CAAC,SAAS,EAAE,KAAK,EACzB,GAAG,GACF,GAAG,MAAK,WAAa,KAAI,GAAG,MAAK,UAAY,KAAI,GAAG,MAAK,QAAU;OAEvE,CAAC;QACD,WAAW,GAAG,SAAS,CAAC,WAAW;QACnC,UAAU,GAAG,SAAS,CAAC,UAAU;QACjC,QAAQ,GAAG,SAAS,CAAC,QAAQ;IAC/B,CAAC,MAAM,CAAC;QACN,UAAU,GAAG,SAAS;IACxB,CAAC;IACD,EAAyD,AAAzD,uDAAyD;IACzD,EAAuB,AAAvB,qBAAuB;IACvB,iBAAiB,CAAC,WAAW,GAAE,OAAS;IACxC,iBAAiB,CAAC,UAAU,GAAE,OAAS;IACvC,iBAAiB,CAAC,QAAQ,GAAE,OAAS;IAErC,WAAW,GAAG,aAAa,CAAC,WAAW,EAAE,MAAM,GAAE,OAAS;IAC1D,UAAU,GAAG,aAAa,CAAC,UAAU,EAAE,MAAM,GAAE,OAAS;IACxD,QAAQ,GAAG,aAAa,CAAC,QAAQ,EAAE,MAAM,GAAE,OAAS;IAEpD,iBAAiB,CAAC,WAAW,GAAE,OAAS;IACxC,iBAAiB,CAAC,UAAU,GAAE,OAAS;IACvC,iBAAiB,CAAC,QAAQ,GAAE,OAAS;;QAGnC,WAAW;QACX,UAAU;QACV,QAAQ;;AAEZ,CAAC;eAEc,WAAW,CAAC,MAAkB,EAAE,CAAC;IAC9C,EAAE,SAAS,MAAM,CAAC,OAAO,MAAK,QAAU,GAAE,CAAC;;IAE3C,CAAC;IACD,GAAG,CAAC,OAAO,SAAS,MAAM,CAAC,OAAO;IAClC,EAAyD,AAAzD,uDAAyD;IACzD,EAAuB,AAAvB,qBAAuB;IACvB,iBAAiB,CAAC,OAAO,GAAE,MAAQ;IAEnC,OAAO,GAAG,aAAa,CAAC,OAAO,EAAE,MAAM,GAAE,MAAQ;IACjD,iBAAiB,CAAC,OAAO,GAAE,MAAQ;WAC5B,OAAO;AAChB,CAAC;eAE6B,gBAAgB,CAC5C,MAAkB,EACK,CAAC;IACxB,KAAK,EAAE,OAAO,EAAE,QAAQ,EAAE,SAAS,UAAU,OAAO,CAAC,GAAG;QACtD,WAAW,CAAC,MAAM;QAClB,YAAY,CAAC,MAAM;QACnB,aAAa,CAAC,MAAM;;IAGtB,KAAK,CAAC,aAAa,GACjB,QAAQ,CAAC,WAAW,CAAC,MAAM,GAC3B,QAAQ,CAAC,UAAU,CAAC,MAAM,GAC1B,QAAQ,CAAC,QAAQ,CAAC,MAAM;IAE1B,KAAK,CAAC,WAAW,GAAG,OAAO,CAAC,MAAM,GAAG,SAAS,CAAC,MAAM,GAAG,aAAa;IAErE,EAAE,EAAE,WAAW,GAAG,IAAI,EAAE,CAAC;QACvB,OAAO,CAAC,IAAI,CA7pBE,MAAO,SA8pBb,IAAI,CAAC,MAAM,EAAE,SAAS,MACzB,wFAAwF,KACxF,SAAS,EAAE,OAAO,CAAC,MAAM,CAAC,EAAE,KAC5B,UAAU,EAAE,aAAa,CAAC,EAAE,KAC5B,WAAW,EAAE,SAAS,CAAC,MAAM,CAAC,EAAE,KAChC,yEAAyE;IAEhF,CAAC;IAED,EAAE,EAAE,MAAM,CAAC,aAAa,EAAE,CAAC;QACzB,SAAS,CAAC,OAAO;YAEb,MAAM,GAAE,yDAA2D;YACnE,WAAW,GAAE,MAAQ;YACrB,SAAS,EAAE,IAAI;YACf,MAAM,EAAE,MAAM,CAAC,IAAI,GAAG,KAAK,GAAG,SAAS;YACvC,QAAQ,EAAE,IAAI;;YAGd,MAAM,GAAE,uDAAyD;YACjE,WAAW,GAAE,UAAY;YACzB,SAAS,EAAE,IAAI;YACf,MAAM,EAAE,MAAM,CAAC,IAAI,GAAG,KAAK,GAAG,SAAS;YACvC,QAAQ,EAAE,IAAI;;QAGlB,EAAE,EAAE,MAAM,CAAC,QAAQ,EAAE,CAAC;YACpB,SAAS,CAAC,OAAO;gBACf,MAAM,EAAE,MAAM,CAAC,QAAQ;gBACvB,WAAW,EAAE,MAAM,CAAC,QAAQ,IAAG,CAAG;gBAClC,SAAS,EAAE,IAAI;gBACf,QAAQ,EAAE,KAAK;gBACf,MAAM,EAAE,MAAM,CAAC,IAAI,GAAG,KAAK,GAAG,SAAS;gBACvC,QAAQ,EAAE,IAAI;;QAElB,CAAC;IACH,CAAC,MAAM,CAAC;QACN,SAAS,CAAC,OAAO;YACf,MAAM,GAAE,QAAU;YAClB,WAAW,GAAE,OAAS;YACtB,SAAS,EAAE,IAAI;YACf,MAAM,EAAE,MAAM,CAAC,IAAI,GAAG,KAAK,GAAG,SAAS;YACvC,QAAQ,EAAE,IAAI;;QAEhB,EAAE,EAAE,MAAM,CAAC,QAAQ,EAAE,CAAC;YACpB,SAAS,CAAC,OAAO;gBACf,MAAM,EAAE,MAAM,CAAC,QAAQ,IAAG,CAAG;gBAC7B,WAAW,EAAE,MAAM,CAAC,QAAQ;gBAC5B,SAAS,EAAE,IAAI;gBACf,QAAQ,EAAE,KAAK;gBACf,MAAM,EAAE,MAAM,CAAC,IAAI,GAAG,KAAK,GAAG,SAAS;gBACvC,QAAQ,EAAE,IAAI;;QAElB,CAAC;IACH,CAAC;;QAGC,OAAO;QACP,QAAQ;QACR,SAAS;;AAEb,CAAC"}