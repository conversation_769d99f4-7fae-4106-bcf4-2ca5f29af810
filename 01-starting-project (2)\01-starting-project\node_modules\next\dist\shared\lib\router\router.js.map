{"version": 3, "sources": ["../../../../shared/lib/router/router.ts"], "sourcesContent": ["// tslint:disable:no-console\nimport { ParsedUrlQuery } from 'querystring'\nimport { ComponentType } from 'react'\nimport { UrlObject } from 'url'\nimport {\n  normalizePathTrailingSlash,\n  removePathTrailingSlash,\n} from '../../../client/normalize-trailing-slash'\nimport { GoodPageCache, StyleSheetTuple } from '../../../client/page-loader'\nimport {\n  getClientBuildManifest,\n  isAssetError,\n  markAssetError,\n} from '../../../client/route-loader'\nimport { RouterEvent } from '../../../client/router'\nimport type { DomainLocale } from '../../../server/config'\nimport { denormalizePagePath } from '../../../server/denormalize-page-path'\nimport { normalizeLocalePath } from '../i18n/normalize-locale-path'\nimport mitt, { MittEmitter } from '../mitt'\nimport {\n  AppContextType,\n  formatWithValidation,\n  getLocationOrigin,\n  getURL,\n  loadGetInitialProps,\n  normalizeRepeatedSlashes,\n  NextPageContext,\n  ST,\n  NEXT_DATA,\n} from '../utils'\nimport { isDynamicRoute } from './utils/is-dynamic'\nimport { parseRelativeUrl } from './utils/parse-relative-url'\nimport { searchParamsToUrlQuery } from './utils/querystring'\nimport resolveRewrites from './utils/resolve-rewrites'\nimport { getRouteMatcher } from './utils/route-matcher'\nimport { getRouteRegex } from './utils/route-regex'\n\ndeclare global {\n  interface Window {\n    /* prod */\n    __NEXT_DATA__: NEXT_DATA\n  }\n}\n\ninterface RouteProperties {\n  shallow: boolean\n}\n\ninterface TransitionOptions {\n  shallow?: boolean\n  locale?: string | false\n  scroll?: boolean\n}\n\ninterface NextHistoryState {\n  url: string\n  as: string\n  options: TransitionOptions\n}\n\ntype HistoryState =\n  | null\n  | { __N: false }\n  | ({ __N: true; idx: number } & NextHistoryState)\n\nlet detectDomainLocale: typeof import('../i18n/detect-domain-locale').detectDomainLocale\n\nif (process.env.__NEXT_I18N_SUPPORT) {\n  detectDomainLocale =\n    require('../i18n/detect-domain-locale').detectDomainLocale\n}\n\nconst basePath = (process.env.__NEXT_ROUTER_BASEPATH as string) || ''\n\nfunction buildCancellationError() {\n  return Object.assign(new Error('Route Cancelled'), {\n    cancelled: true,\n  })\n}\n\nfunction addPathPrefix(path: string, prefix?: string) {\n  return prefix && path.startsWith('/')\n    ? path === '/'\n      ? normalizePathTrailingSlash(prefix)\n      : `${prefix}${pathNoQueryHash(path) === '/' ? path.substring(1) : path}`\n    : path\n}\n\nexport function getDomainLocale(\n  path: string,\n  locale?: string | false,\n  locales?: string[],\n  domainLocales?: DomainLocale[]\n) {\n  if (process.env.__NEXT_I18N_SUPPORT) {\n    locale = locale || normalizeLocalePath(path, locales).detectedLocale\n\n    const detectedDomain = detectDomainLocale(domainLocales, undefined, locale)\n\n    if (detectedDomain) {\n      return `http${detectedDomain.http ? '' : 's'}://${detectedDomain.domain}${\n        basePath || ''\n      }${locale === detectedDomain.defaultLocale ? '' : `/${locale}`}${path}`\n    }\n    return false\n  } else {\n    return false\n  }\n}\n\nexport function addLocale(\n  path: string,\n  locale?: string | false,\n  defaultLocale?: string\n) {\n  if (process.env.__NEXT_I18N_SUPPORT) {\n    const pathname = pathNoQueryHash(path)\n    const pathLower = pathname.toLowerCase()\n    const localeLower = locale && locale.toLowerCase()\n\n    return locale &&\n      locale !== defaultLocale &&\n      !pathLower.startsWith('/' + localeLower + '/') &&\n      pathLower !== '/' + localeLower\n      ? addPathPrefix(path, '/' + locale)\n      : path\n  }\n  return path\n}\n\nexport function delLocale(path: string, locale?: string) {\n  if (process.env.__NEXT_I18N_SUPPORT) {\n    const pathname = pathNoQueryHash(path)\n    const pathLower = pathname.toLowerCase()\n    const localeLower = locale && locale.toLowerCase()\n\n    return locale &&\n      (pathLower.startsWith('/' + localeLower + '/') ||\n        pathLower === '/' + localeLower)\n      ? (pathname.length === locale.length + 1 ? '/' : '') +\n          path.substr(locale.length + 1)\n      : path\n  }\n  return path\n}\n\nfunction pathNoQueryHash(path: string) {\n  const queryIndex = path.indexOf('?')\n  const hashIndex = path.indexOf('#')\n\n  if (queryIndex > -1 || hashIndex > -1) {\n    path = path.substring(0, queryIndex > -1 ? queryIndex : hashIndex)\n  }\n  return path\n}\n\nexport function hasBasePath(path: string): boolean {\n  path = pathNoQueryHash(path)\n  return path === basePath || path.startsWith(basePath + '/')\n}\n\nexport function addBasePath(path: string): string {\n  // we only add the basepath on relative urls\n  return addPathPrefix(path, basePath)\n}\n\nexport function delBasePath(path: string): string {\n  path = path.slice(basePath.length)\n  if (!path.startsWith('/')) path = `/${path}`\n  return path\n}\n\n/**\n * Detects whether a given url is routable by the Next.js router (browser only).\n */\nexport function isLocalURL(url: string): boolean {\n  // prevent a hydration mismatch on href for url with anchor refs\n  if (url.startsWith('/') || url.startsWith('#') || url.startsWith('?'))\n    return true\n  try {\n    // absolute urls can be local if they are on the same origin\n    const locationOrigin = getLocationOrigin()\n    const resolved = new URL(url, locationOrigin)\n    return resolved.origin === locationOrigin && hasBasePath(resolved.pathname)\n  } catch (_) {\n    return false\n  }\n}\n\ntype Url = UrlObject | string\n\nexport function interpolateAs(\n  route: string,\n  asPathname: string,\n  query: ParsedUrlQuery\n) {\n  let interpolatedRoute = ''\n\n  const dynamicRegex = getRouteRegex(route)\n  const dynamicGroups = dynamicRegex.groups\n  const dynamicMatches =\n    // Try to match the dynamic route against the asPath\n    (asPathname !== route ? getRouteMatcher(dynamicRegex)(asPathname) : '') ||\n    // Fall back to reading the values from the href\n    // TODO: should this take priority; also need to change in the router.\n    query\n\n  interpolatedRoute = route\n  const params = Object.keys(dynamicGroups)\n\n  if (\n    !params.every((param) => {\n      let value = dynamicMatches[param] || ''\n      const { repeat, optional } = dynamicGroups[param]\n\n      // support single-level catch-all\n      // TODO: more robust handling for user-error (passing `/`)\n      let replaced = `[${repeat ? '...' : ''}${param}]`\n      if (optional) {\n        replaced = `${!value ? '/' : ''}[${replaced}]`\n      }\n      if (repeat && !Array.isArray(value)) value = [value]\n\n      return (\n        (optional || param in dynamicMatches) &&\n        // Interpolate group into data URL if present\n        (interpolatedRoute =\n          interpolatedRoute!.replace(\n            replaced,\n            repeat\n              ? (value as string[])\n                  .map(\n                    // these values should be fully encoded instead of just\n                    // path delimiter escaped since they are being inserted\n                    // into the URL and we expect URL encoded segments\n                    // when parsing dynamic route params\n                    (segment) => encodeURIComponent(segment)\n                  )\n                  .join('/')\n              : encodeURIComponent(value as string)\n          ) || '/')\n      )\n    })\n  ) {\n    interpolatedRoute = '' // did not satisfy all requirements\n\n    // n.b. We ignore this error because we handle warning for this case in\n    // development in the `<Link>` component directly.\n  }\n  return {\n    params,\n    result: interpolatedRoute,\n  }\n}\n\nfunction omitParmsFromQuery(query: ParsedUrlQuery, params: string[]) {\n  const filteredQuery: ParsedUrlQuery = {}\n\n  Object.keys(query).forEach((key) => {\n    if (!params.includes(key)) {\n      filteredQuery[key] = query[key]\n    }\n  })\n  return filteredQuery\n}\n\n/**\n * Resolves a given hyperlink with a certain router state (basePath not included).\n * Preserves absolute urls.\n */\nexport function resolveHref(\n  router: NextRouter,\n  href: Url,\n  resolveAs?: boolean\n): string {\n  // we use a dummy base url for relative urls\n  let base: URL\n  let urlAsString = typeof href === 'string' ? href : formatWithValidation(href)\n\n  // repeated slashes and backslashes in the URL are considered\n  // invalid and will never match a Next.js page/file\n  const urlProtoMatch = urlAsString.match(/^[a-zA-Z]{1,}:\\/\\//)\n  const urlAsStringNoProto = urlProtoMatch\n    ? urlAsString.substr(urlProtoMatch[0].length)\n    : urlAsString\n\n  const urlParts = urlAsStringNoProto.split('?')\n\n  if ((urlParts[0] || '').match(/(\\/\\/|\\\\)/)) {\n    console.error(\n      `Invalid href passed to next/router: ${urlAsString}, repeated forward-slashes (//) or backslashes \\\\ are not valid in the href`\n    )\n    const normalizedUrl = normalizeRepeatedSlashes(urlAsStringNoProto)\n    urlAsString = (urlProtoMatch ? urlProtoMatch[0] : '') + normalizedUrl\n  }\n\n  // Return because it cannot be routed by the Next.js router\n  if (!isLocalURL(urlAsString)) {\n    return (resolveAs ? [urlAsString] : urlAsString) as string\n  }\n\n  try {\n    base = new URL(\n      urlAsString.startsWith('#') ? router.asPath : router.pathname,\n      'http://n'\n    )\n  } catch (_) {\n    // fallback to / for invalid asPath values e.g. //\n    base = new URL('/', 'http://n')\n  }\n  try {\n    const finalUrl = new URL(urlAsString, base)\n    finalUrl.pathname = normalizePathTrailingSlash(finalUrl.pathname)\n    let interpolatedAs = ''\n\n    if (\n      isDynamicRoute(finalUrl.pathname) &&\n      finalUrl.searchParams &&\n      resolveAs\n    ) {\n      const query = searchParamsToUrlQuery(finalUrl.searchParams)\n\n      const { result, params } = interpolateAs(\n        finalUrl.pathname,\n        finalUrl.pathname,\n        query\n      )\n\n      if (result) {\n        interpolatedAs = formatWithValidation({\n          pathname: result,\n          hash: finalUrl.hash,\n          query: omitParmsFromQuery(query, params),\n        })\n      }\n    }\n\n    // if the origin didn't change, it means we received a relative href\n    const resolvedHref =\n      finalUrl.origin === base.origin\n        ? finalUrl.href.slice(finalUrl.origin.length)\n        : finalUrl.href\n\n    return (\n      resolveAs ? [resolvedHref, interpolatedAs || resolvedHref] : resolvedHref\n    ) as string\n  } catch (_) {\n    return (resolveAs ? [urlAsString] : urlAsString) as string\n  }\n}\n\nfunction stripOrigin(url: string) {\n  const origin = getLocationOrigin()\n\n  return url.startsWith(origin) ? url.substring(origin.length) : url\n}\n\nfunction prepareUrlAs(router: NextRouter, url: Url, as?: Url) {\n  // If url and as provided as an object representation,\n  // we'll format them into the string version here.\n  let [resolvedHref, resolvedAs] = resolveHref(router, url, true)\n  const origin = getLocationOrigin()\n  const hrefHadOrigin = resolvedHref.startsWith(origin)\n  const asHadOrigin = resolvedAs && resolvedAs.startsWith(origin)\n\n  resolvedHref = stripOrigin(resolvedHref)\n  resolvedAs = resolvedAs ? stripOrigin(resolvedAs) : resolvedAs\n\n  const preparedUrl = hrefHadOrigin ? resolvedHref : addBasePath(resolvedHref)\n  const preparedAs = as\n    ? stripOrigin(resolveHref(router, as))\n    : resolvedAs || resolvedHref\n\n  return {\n    url: preparedUrl,\n    as: asHadOrigin ? preparedAs : addBasePath(preparedAs),\n  }\n}\n\nfunction resolveDynamicRoute(pathname: string, pages: string[]) {\n  const cleanPathname = removePathTrailingSlash(denormalizePagePath(pathname!))\n\n  if (cleanPathname === '/404' || cleanPathname === '/_error') {\n    return pathname\n  }\n\n  // handle resolving href for dynamic routes\n  if (!pages.includes(cleanPathname!)) {\n    // eslint-disable-next-line array-callback-return\n    pages.some((page) => {\n      if (isDynamicRoute(page) && getRouteRegex(page).re.test(cleanPathname!)) {\n        pathname = page\n        return true\n      }\n    })\n  }\n  return removePathTrailingSlash(pathname)\n}\n\nexport type BaseRouter = {\n  route: string\n  pathname: string\n  query: ParsedUrlQuery\n  asPath: string\n  basePath: string\n  locale?: string\n  locales?: string[]\n  defaultLocale?: string\n  domainLocales?: DomainLocale[]\n  isLocaleDomain: boolean\n}\n\nexport type NextRouter = BaseRouter &\n  Pick<\n    Router,\n    | 'push'\n    | 'replace'\n    | 'reload'\n    | 'back'\n    | 'prefetch'\n    | 'beforePopState'\n    | 'events'\n    | 'isFallback'\n    | 'isReady'\n    | 'isPreview'\n  >\n\nexport type PrefetchOptions = {\n  priority?: boolean\n  locale?: string | false\n}\n\nexport type PrivateRouteInfo =\n  | (Omit<CompletePrivateRouteInfo, 'styleSheets'> & { initial: true })\n  | CompletePrivateRouteInfo\n\nexport type CompletePrivateRouteInfo = {\n  Component: ComponentType\n  styleSheets: StyleSheetTuple[]\n  __N_SSG?: boolean\n  __N_SSP?: boolean\n  props?: Record<string, any>\n  err?: Error\n  error?: any\n}\n\nexport type AppProps = Pick<CompletePrivateRouteInfo, 'Component' | 'err'> & {\n  router: Router\n} & Record<string, any>\nexport type AppComponent = ComponentType<AppProps>\n\ntype Subscription = (\n  data: PrivateRouteInfo,\n  App: AppComponent,\n  resetScroll: { x: number; y: number } | null\n) => Promise<void>\n\ntype BeforePopStateCallback = (state: NextHistoryState) => boolean\n\ntype ComponentLoadCancel = (() => void) | null\n\ntype HistoryMethod = 'replaceState' | 'pushState'\n\nconst manualScrollRestoration =\n  process.env.__NEXT_SCROLL_RESTORATION &&\n  typeof window !== 'undefined' &&\n  'scrollRestoration' in window.history &&\n  !!(function () {\n    try {\n      let v = '__next'\n      // eslint-disable-next-line no-sequences\n      return sessionStorage.setItem(v, v), sessionStorage.removeItem(v), true\n    } catch (n) {}\n  })()\n\nconst SSG_DATA_NOT_FOUND = Symbol('SSG_DATA_NOT_FOUND')\n\nfunction fetchRetry(url: string, attempts: number): Promise<any> {\n  return fetch(url, {\n    // Cookies are required to be present for Next.js' SSG \"Preview Mode\".\n    // Cookies may also be required for `getServerSideProps`.\n    //\n    // > `fetch` won’t send cookies, unless you set the credentials init\n    // > option.\n    // https://developer.mozilla.org/en-US/docs/Web/API/Fetch_API/Using_Fetch\n    //\n    // > For maximum browser compatibility when it comes to sending &\n    // > receiving cookies, always supply the `credentials: 'same-origin'`\n    // > option instead of relying on the default.\n    // https://github.com/github/fetch#caveats\n    credentials: 'same-origin',\n  }).then((res) => {\n    if (!res.ok) {\n      if (attempts > 1 && res.status >= 500) {\n        return fetchRetry(url, attempts - 1)\n      }\n      if (res.status === 404) {\n        return res.json().then((data) => {\n          if (data.notFound) {\n            return { notFound: SSG_DATA_NOT_FOUND }\n          }\n          throw new Error(`Failed to load static props`)\n        })\n      }\n      throw new Error(`Failed to load static props`)\n    }\n    return res.json()\n  })\n}\n\nfunction fetchNextData(dataHref: string, isServerRender: boolean) {\n  return fetchRetry(dataHref, isServerRender ? 3 : 1).catch((err: Error) => {\n    // We should only trigger a server-side transition if this was caused\n    // on a client-side transition. Otherwise, we'd get into an infinite\n    // loop.\n\n    if (!isServerRender) {\n      markAssetError(err)\n    }\n    throw err\n  })\n}\n\nexport default class Router implements BaseRouter {\n  route: string\n  pathname: string\n  query: ParsedUrlQuery\n  asPath: string\n  basePath: string\n\n  /**\n   * Map of all components loaded in `Router`\n   */\n  components: { [pathname: string]: PrivateRouteInfo }\n  // Static Data Cache\n  sdc: { [asPath: string]: object } = {}\n  // In-flight Server Data Requests, for deduping\n  sdr: { [asPath: string]: Promise<object> } = {}\n\n  sub: Subscription\n  clc: ComponentLoadCancel\n  pageLoader: any\n  _bps: BeforePopStateCallback | undefined\n  events: MittEmitter<RouterEvent>\n  _wrapApp: (App: AppComponent) => any\n  isSsr: boolean\n  isFallback: boolean\n  _inFlightRoute?: string\n  _shallow?: boolean\n  locale?: string\n  locales?: string[]\n  defaultLocale?: string\n  domainLocales?: DomainLocale[]\n  isReady: boolean\n  isPreview: boolean\n  isLocaleDomain: boolean\n\n  private _idx: number = 0\n\n  static events: MittEmitter<RouterEvent> = mitt()\n\n  constructor(\n    pathname: string,\n    query: ParsedUrlQuery,\n    as: string,\n    {\n      initialProps,\n      pageLoader,\n      App,\n      wrapApp,\n      Component,\n      err,\n      subscription,\n      isFallback,\n      locale,\n      locales,\n      defaultLocale,\n      domainLocales,\n      isPreview,\n    }: {\n      subscription: Subscription\n      initialProps: any\n      pageLoader: any\n      Component: ComponentType\n      App: AppComponent\n      wrapApp: (WrapAppComponent: AppComponent) => any\n      err?: Error\n      isFallback: boolean\n      locale?: string\n      locales?: string[]\n      defaultLocale?: string\n      domainLocales?: DomainLocale[]\n      isPreview?: boolean\n    }\n  ) {\n    // represents the current component key\n    this.route = removePathTrailingSlash(pathname)\n\n    // set up the component cache (by route keys)\n    this.components = {}\n    // We should not keep the cache, if there's an error\n    // Otherwise, this cause issues when when going back and\n    // come again to the errored page.\n    if (pathname !== '/_error') {\n      this.components[this.route] = {\n        Component,\n        initial: true,\n        props: initialProps,\n        err,\n        __N_SSG: initialProps && initialProps.__N_SSG,\n        __N_SSP: initialProps && initialProps.__N_SSP,\n      }\n    }\n\n    this.components['/_app'] = {\n      Component: App as ComponentType,\n      styleSheets: [\n        /* /_app does not need its stylesheets managed */\n      ],\n    }\n\n    // Backwards compat for Router.router.events\n    // TODO: Should be remove the following major version as it was never documented\n    this.events = Router.events\n\n    this.pageLoader = pageLoader\n    this.pathname = pathname\n    this.query = query\n    // if auto prerendered and dynamic route wait to update asPath\n    // until after mount to prevent hydration mismatch\n    const autoExportDynamic =\n      isDynamicRoute(pathname) && self.__NEXT_DATA__.autoExport\n\n    this.asPath = autoExportDynamic ? pathname : as\n    this.basePath = basePath\n    this.sub = subscription\n    this.clc = null\n    this._wrapApp = wrapApp\n    // make sure to ignore extra popState in safari on navigating\n    // back from external site\n    this.isSsr = true\n\n    this.isFallback = isFallback\n\n    this.isReady = !!(\n      self.__NEXT_DATA__.gssp ||\n      self.__NEXT_DATA__.gip ||\n      (self.__NEXT_DATA__.appGip && !self.__NEXT_DATA__.gsp) ||\n      (!autoExportDynamic &&\n        !self.location.search &&\n        !process.env.__NEXT_HAS_REWRITES)\n    )\n    this.isPreview = !!isPreview\n    this.isLocaleDomain = false\n\n    if (process.env.__NEXT_I18N_SUPPORT) {\n      this.locale = locale\n      this.locales = locales\n      this.defaultLocale = defaultLocale\n      this.domainLocales = domainLocales\n      this.isLocaleDomain = !!detectDomainLocale(\n        domainLocales,\n        self.location.hostname\n      )\n    }\n\n    if (typeof window !== 'undefined') {\n      // make sure \"as\" doesn't start with double slashes or else it can\n      // throw an error as it's considered invalid\n      if (as.substr(0, 2) !== '//') {\n        // in order for `e.state` to work on the `onpopstate` event\n        // we have to register the initial route upon initialization\n        const options: TransitionOptions = { locale }\n        ;(options as any)._shouldResolveHref = as !== pathname\n\n        this.changeState(\n          'replaceState',\n          formatWithValidation({ pathname: addBasePath(pathname), query }),\n          getURL(),\n          options\n        )\n      }\n\n      window.addEventListener('popstate', this.onPopState)\n\n      // enable custom scroll restoration handling when available\n      // otherwise fallback to browser's default handling\n      if (process.env.__NEXT_SCROLL_RESTORATION) {\n        if (manualScrollRestoration) {\n          window.history.scrollRestoration = 'manual'\n        }\n      }\n    }\n  }\n\n  onPopState = (e: PopStateEvent): void => {\n    const state = e.state as HistoryState\n\n    if (!state) {\n      // We get state as undefined for two reasons.\n      //  1. With older safari (< 8) and older chrome (< 34)\n      //  2. When the URL changed with #\n      //\n      // In the both cases, we don't need to proceed and change the route.\n      // (as it's already changed)\n      // But we can simply replace the state with the new changes.\n      // Actually, for (1) we don't need to nothing. But it's hard to detect that event.\n      // So, doing the following for (1) does no harm.\n      const { pathname, query } = this\n      this.changeState(\n        'replaceState',\n        formatWithValidation({ pathname: addBasePath(pathname), query }),\n        getURL()\n      )\n      return\n    }\n\n    if (!state.__N) {\n      return\n    }\n\n    let forcedScroll: { x: number; y: number } | undefined\n    const { url, as, options, idx } = state\n    if (process.env.__NEXT_SCROLL_RESTORATION) {\n      if (manualScrollRestoration) {\n        if (this._idx !== idx) {\n          // Snapshot current scroll position:\n          try {\n            sessionStorage.setItem(\n              '__next_scroll_' + this._idx,\n              JSON.stringify({ x: self.pageXOffset, y: self.pageYOffset })\n            )\n          } catch {}\n\n          // Restore old scroll position:\n          try {\n            const v = sessionStorage.getItem('__next_scroll_' + idx)\n            forcedScroll = JSON.parse(v!)\n          } catch {\n            forcedScroll = { x: 0, y: 0 }\n          }\n        }\n      }\n    }\n    this._idx = idx\n\n    const { pathname } = parseRelativeUrl(url)\n\n    // Make sure we don't re-render on initial load,\n    // can be caused by navigating back from an external site\n    if (this.isSsr && as === this.asPath && pathname === this.pathname) {\n      return\n    }\n\n    // If the downstream application returns falsy, return.\n    // They will then be responsible for handling the event.\n    if (this._bps && !this._bps(state)) {\n      return\n    }\n\n    this.change(\n      'replaceState',\n      url,\n      as,\n      Object.assign<{}, TransitionOptions, TransitionOptions>({}, options, {\n        shallow: options.shallow && this._shallow,\n        locale: options.locale || this.defaultLocale,\n      }),\n      forcedScroll\n    )\n  }\n\n  reload(): void {\n    window.location.reload()\n  }\n\n  /**\n   * Go back in history\n   */\n  back() {\n    window.history.back()\n  }\n\n  /**\n   * Performs a `pushState` with arguments\n   * @param url of the route\n   * @param as masks `url` for the browser\n   * @param options object you can define `shallow` and other options\n   */\n  push(url: Url, as?: Url, options: TransitionOptions = {}) {\n    if (process.env.__NEXT_SCROLL_RESTORATION) {\n      // TODO: remove in the future when we update history before route change\n      // is complete, as the popstate event should handle this capture.\n      if (manualScrollRestoration) {\n        try {\n          // Snapshot scroll position right before navigating to a new page:\n          sessionStorage.setItem(\n            '__next_scroll_' + this._idx,\n            JSON.stringify({ x: self.pageXOffset, y: self.pageYOffset })\n          )\n        } catch {}\n      }\n    }\n    ;({ url, as } = prepareUrlAs(this, url, as))\n    return this.change('pushState', url, as, options)\n  }\n\n  /**\n   * Performs a `replaceState` with arguments\n   * @param url of the route\n   * @param as masks `url` for the browser\n   * @param options object you can define `shallow` and other options\n   */\n  replace(url: Url, as?: Url, options: TransitionOptions = {}) {\n    ;({ url, as } = prepareUrlAs(this, url, as))\n    return this.change('replaceState', url, as, options)\n  }\n\n  private async change(\n    method: HistoryMethod,\n    url: string,\n    as: string,\n    options: TransitionOptions,\n    forcedScroll?: { x: number; y: number }\n  ): Promise<boolean> {\n    if (!isLocalURL(url)) {\n      window.location.href = url\n      return false\n    }\n    const shouldResolveHref =\n      url === as || (options as any)._h || (options as any)._shouldResolveHref\n\n    // for static pages with query params in the URL we delay\n    // marking the router ready until after the query is updated\n    if ((options as any)._h) {\n      this.isReady = true\n    }\n\n    const prevLocale = this.locale\n\n    if (process.env.__NEXT_I18N_SUPPORT) {\n      this.locale =\n        options.locale === false\n          ? this.defaultLocale\n          : options.locale || this.locale\n\n      if (typeof options.locale === 'undefined') {\n        options.locale = this.locale\n      }\n\n      const parsedAs = parseRelativeUrl(hasBasePath(as) ? delBasePath(as) : as)\n      const localePathResult = normalizeLocalePath(\n        parsedAs.pathname,\n        this.locales\n      )\n\n      if (localePathResult.detectedLocale) {\n        this.locale = localePathResult.detectedLocale\n        parsedAs.pathname = addBasePath(parsedAs.pathname)\n        as = formatWithValidation(parsedAs)\n        url = addBasePath(\n          normalizeLocalePath(\n            hasBasePath(url) ? delBasePath(url) : url,\n            this.locales\n          ).pathname\n        )\n      }\n      let didNavigate = false\n\n      // we need to wrap this in the env check again since regenerator runtime\n      // moves this on its own due to the return\n      if (process.env.__NEXT_I18N_SUPPORT) {\n        // if the locale isn't configured hard navigate to show 404 page\n        if (!this.locales?.includes(this.locale!)) {\n          parsedAs.pathname = addLocale(parsedAs.pathname, this.locale)\n          window.location.href = formatWithValidation(parsedAs)\n          // this was previously a return but was removed in favor\n          // of better dead code elimination with regenerator runtime\n          didNavigate = true\n        }\n      }\n\n      const detectedDomain = detectDomainLocale(\n        this.domainLocales,\n        undefined,\n        this.locale\n      )\n\n      // we need to wrap this in the env check again since regenerator runtime\n      // moves this on its own due to the return\n      if (process.env.__NEXT_I18N_SUPPORT) {\n        // if we are navigating to a domain locale ensure we redirect to the\n        // correct domain\n        if (\n          !didNavigate &&\n          detectedDomain &&\n          this.isLocaleDomain &&\n          self.location.hostname !== detectedDomain.domain\n        ) {\n          const asNoBasePath = delBasePath(as)\n          window.location.href = `http${detectedDomain.http ? '' : 's'}://${\n            detectedDomain.domain\n          }${addBasePath(\n            `${\n              this.locale === detectedDomain.defaultLocale\n                ? ''\n                : `/${this.locale}`\n            }${asNoBasePath === '/' ? '' : asNoBasePath}` || '/'\n          )}`\n          // this was previously a return but was removed in favor\n          // of better dead code elimination with regenerator runtime\n          didNavigate = true\n        }\n      }\n\n      if (didNavigate) {\n        return new Promise(() => {})\n      }\n    }\n\n    if (!(options as any)._h) {\n      this.isSsr = false\n    }\n    // marking route changes as a navigation start entry\n    if (ST) {\n      performance.mark('routeChange')\n    }\n\n    const { shallow = false } = options\n    const routeProps = { shallow }\n\n    if (this._inFlightRoute) {\n      this.abortComponentLoad(this._inFlightRoute, routeProps)\n    }\n\n    as = addBasePath(\n      addLocale(\n        hasBasePath(as) ? delBasePath(as) : as,\n        options.locale,\n        this.defaultLocale\n      )\n    )\n    const cleanedAs = delLocale(\n      hasBasePath(as) ? delBasePath(as) : as,\n      this.locale\n    )\n    this._inFlightRoute = as\n\n    let localeChange = prevLocale !== this.locale\n\n    // If the url change is only related to a hash change\n    // We should not proceed. We should only change the state.\n\n    // WARNING: `_h` is an internal option for handing Next.js client-side\n    // hydration. Your app should _never_ use this property. It may change at\n    // any time without notice.\n    if (\n      !(options as any)._h &&\n      this.onlyAHashChange(cleanedAs) &&\n      !localeChange\n    ) {\n      this.asPath = cleanedAs\n      Router.events.emit('hashChangeStart', as, routeProps)\n      // TODO: do we need the resolved href when only a hash change?\n      this.changeState(method, url, as, options)\n      this.scrollToHash(cleanedAs)\n      this.notify(this.components[this.route], null)\n      Router.events.emit('hashChangeComplete', as, routeProps)\n      return true\n    }\n\n    let parsed = parseRelativeUrl(url)\n    let { pathname, query } = parsed\n\n    // The build manifest needs to be loaded before auto-static dynamic pages\n    // get their query parameters to allow ensuring they can be parsed properly\n    // when rewritten to\n    let pages: any, rewrites: any\n    try {\n      pages = await this.pageLoader.getPageList()\n      ;({ __rewrites: rewrites } = await getClientBuildManifest())\n    } catch (err) {\n      // If we fail to resolve the page list or client-build manifest, we must\n      // do a server-side transition:\n      window.location.href = as\n      return false\n    }\n\n    // If asked to change the current URL we should reload the current page\n    // (not location.reload() but reload getInitialProps and other Next.js stuffs)\n    // We also need to set the method = replaceState always\n    // as this should not go into the history (That's how browsers work)\n    // We should compare the new asPath to the current asPath, not the url\n    if (!this.urlIsNew(cleanedAs) && !localeChange) {\n      method = 'replaceState'\n    }\n\n    // we need to resolve the as value using rewrites for dynamic SSG\n    // pages to allow building the data URL correctly\n    let resolvedAs = as\n\n    // url and as should always be prefixed with basePath by this\n    // point by either next/link or router.push/replace so strip the\n    // basePath from the pathname to match the pages dir 1-to-1\n    pathname = pathname\n      ? removePathTrailingSlash(delBasePath(pathname))\n      : pathname\n\n    if (shouldResolveHref && pathname !== '/_error') {\n      ;(options as any)._shouldResolveHref = true\n\n      if (process.env.__NEXT_HAS_REWRITES && as.startsWith('/')) {\n        const rewritesResult = resolveRewrites(\n          addBasePath(addLocale(cleanedAs, this.locale)),\n          pages,\n          rewrites,\n          query,\n          (p: string) => resolveDynamicRoute(p, pages),\n          this.locales\n        )\n        resolvedAs = rewritesResult.asPath\n\n        if (rewritesResult.matchedPage && rewritesResult.resolvedHref) {\n          // if this directly matches a page we need to update the href to\n          // allow the correct page chunk to be loaded\n          pathname = rewritesResult.resolvedHref\n          parsed.pathname = addBasePath(pathname)\n          url = formatWithValidation(parsed)\n        }\n      } else {\n        parsed.pathname = resolveDynamicRoute(pathname, pages)\n\n        if (parsed.pathname !== pathname) {\n          pathname = parsed.pathname\n          parsed.pathname = addBasePath(pathname)\n          url = formatWithValidation(parsed)\n        }\n      }\n    }\n\n    const route = removePathTrailingSlash(pathname)\n\n    if (!isLocalURL(as)) {\n      if (process.env.NODE_ENV !== 'production') {\n        throw new Error(\n          `Invalid href: \"${url}\" and as: \"${as}\", received relative href and external as` +\n            `\\nSee more info: https://nextjs.org/docs/messages/invalid-relative-url-external-as`\n        )\n      }\n\n      window.location.href = as\n      return false\n    }\n\n    resolvedAs = delLocale(delBasePath(resolvedAs), this.locale)\n\n    if (isDynamicRoute(route)) {\n      const parsedAs = parseRelativeUrl(resolvedAs)\n      const asPathname = parsedAs.pathname\n\n      const routeRegex = getRouteRegex(route)\n      const routeMatch = getRouteMatcher(routeRegex)(asPathname)\n      const shouldInterpolate = route === asPathname\n      const interpolatedAs = shouldInterpolate\n        ? interpolateAs(route, asPathname, query)\n        : ({} as { result: undefined; params: undefined })\n\n      if (!routeMatch || (shouldInterpolate && !interpolatedAs.result)) {\n        const missingParams = Object.keys(routeRegex.groups).filter(\n          (param) => !query[param]\n        )\n\n        if (missingParams.length > 0) {\n          if (process.env.NODE_ENV !== 'production') {\n            console.warn(\n              `${\n                shouldInterpolate\n                  ? `Interpolating href`\n                  : `Mismatching \\`as\\` and \\`href\\``\n              } failed to manually provide ` +\n                `the params: ${missingParams.join(\n                  ', '\n                )} in the \\`href\\`'s \\`query\\``\n            )\n          }\n\n          throw new Error(\n            (shouldInterpolate\n              ? `The provided \\`href\\` (${url}) value is missing query values (${missingParams.join(\n                  ', '\n                )}) to be interpolated properly. `\n              : `The provided \\`as\\` value (${asPathname}) is incompatible with the \\`href\\` value (${route}). `) +\n              `Read more: https://nextjs.org/docs/messages/${\n                shouldInterpolate\n                  ? 'href-interpolation-failed'\n                  : 'incompatible-href-as'\n              }`\n          )\n        }\n      } else if (shouldInterpolate) {\n        as = formatWithValidation(\n          Object.assign({}, parsedAs, {\n            pathname: interpolatedAs.result,\n            query: omitParmsFromQuery(query, interpolatedAs.params!),\n          })\n        )\n      } else {\n        // Merge params into `query`, overwriting any specified in search\n        Object.assign(query, routeMatch)\n      }\n    }\n\n    Router.events.emit('routeChangeStart', as, routeProps)\n\n    try {\n      let routeInfo = await this.getRouteInfo(\n        route,\n        pathname,\n        query,\n        as,\n        resolvedAs,\n        routeProps\n      )\n      let { error, props, __N_SSG, __N_SSP } = routeInfo\n\n      // handle redirect on client-transition\n      if ((__N_SSG || __N_SSP) && props) {\n        if ((props as any).pageProps && (props as any).pageProps.__N_REDIRECT) {\n          const destination = (props as any).pageProps.__N_REDIRECT\n\n          // check if destination is internal (resolves to a page) and attempt\n          // client-navigation if it is falling back to hard navigation if\n          // it's not\n          if (destination.startsWith('/')) {\n            const parsedHref = parseRelativeUrl(destination)\n            parsedHref.pathname = resolveDynamicRoute(\n              parsedHref.pathname,\n              pages\n            )\n\n            const { url: newUrl, as: newAs } = prepareUrlAs(\n              this,\n              destination,\n              destination\n            )\n            return this.change(method, newUrl, newAs, options)\n          }\n\n          window.location.href = destination\n          return new Promise(() => {})\n        }\n\n        this.isPreview = !!props.__N_PREVIEW\n\n        // handle SSG data 404\n        if (props.notFound === SSG_DATA_NOT_FOUND) {\n          let notFoundRoute\n\n          try {\n            await this.fetchComponent('/404')\n            notFoundRoute = '/404'\n          } catch (_) {\n            notFoundRoute = '/_error'\n          }\n\n          routeInfo = await this.getRouteInfo(\n            notFoundRoute,\n            notFoundRoute,\n            query,\n            as,\n            resolvedAs,\n            { shallow: false }\n          )\n        }\n      }\n\n      Router.events.emit('beforeHistoryChange', as, routeProps)\n      this.changeState(method, url, as, options)\n\n      if (process.env.NODE_ENV !== 'production') {\n        const appComp: any = this.components['/_app'].Component\n        ;(window as any).next.isPrerendered =\n          appComp.getInitialProps === appComp.origGetInitialProps &&\n          !(routeInfo.Component as any).getInitialProps\n      }\n\n      if (\n        (options as any)._h &&\n        pathname === '/_error' &&\n        self.__NEXT_DATA__.props?.pageProps?.statusCode === 500 &&\n        props?.pageProps\n      ) {\n        // ensure statusCode is still correct for static 500 page\n        // when updating query information\n        props.pageProps.statusCode = 500\n      }\n\n      // shallow routing is only allowed for same page URL changes.\n      const isValidShallowRoute = options.shallow && this.route === route\n\n      const shouldScroll = options.scroll ?? !isValidShallowRoute\n      const resetScroll = shouldScroll ? { x: 0, y: 0 } : null\n      await this.set(\n        route,\n        pathname!,\n        query,\n        cleanedAs,\n        routeInfo,\n        forcedScroll ?? resetScroll\n      ).catch((e) => {\n        if (e.cancelled) error = error || e\n        else throw e\n      })\n\n      if (error) {\n        Router.events.emit('routeChangeError', error, cleanedAs, routeProps)\n        throw error\n      }\n\n      if (process.env.__NEXT_I18N_SUPPORT) {\n        if (this.locale) {\n          document.documentElement.lang = this.locale\n        }\n      }\n      Router.events.emit('routeChangeComplete', as, routeProps)\n\n      return true\n    } catch (err) {\n      if (err.cancelled) {\n        return false\n      }\n      throw err\n    }\n  }\n\n  changeState(\n    method: HistoryMethod,\n    url: string,\n    as: string,\n    options: TransitionOptions = {}\n  ): void {\n    if (process.env.NODE_ENV !== 'production') {\n      if (typeof window.history === 'undefined') {\n        console.error(`Warning: window.history is not available.`)\n        return\n      }\n\n      if (typeof window.history[method] === 'undefined') {\n        console.error(`Warning: window.history.${method} is not available`)\n        return\n      }\n    }\n\n    if (method !== 'pushState' || getURL() !== as) {\n      this._shallow = options.shallow\n      window.history[method](\n        {\n          url,\n          as,\n          options,\n          __N: true,\n          idx: (this._idx = method !== 'pushState' ? this._idx : this._idx + 1),\n        } as HistoryState,\n        // Most browsers currently ignores this parameter, although they may use it in the future.\n        // Passing the empty string here should be safe against future changes to the method.\n        // https://developer.mozilla.org/en-US/docs/Web/API/History/replaceState\n        '',\n        as\n      )\n    }\n  }\n\n  async handleRouteInfoError(\n    err: Error & { code: any; cancelled: boolean },\n    pathname: string,\n    query: ParsedUrlQuery,\n    as: string,\n    routeProps: RouteProperties,\n    loadErrorFail?: boolean\n  ): Promise<CompletePrivateRouteInfo> {\n    if (err.cancelled) {\n      // bubble up cancellation errors\n      throw err\n    }\n\n    if (isAssetError(err) || loadErrorFail) {\n      Router.events.emit('routeChangeError', err, as, routeProps)\n\n      // If we can't load the page it could be one of following reasons\n      //  1. Page doesn't exists\n      //  2. Page does exist in a different zone\n      //  3. Internal error while loading the page\n\n      // So, doing a hard reload is the proper way to deal with this.\n      window.location.href = as\n\n      // Changing the URL doesn't block executing the current code path.\n      // So let's throw a cancellation error stop the routing logic.\n      throw buildCancellationError()\n    }\n\n    try {\n      let Component: ComponentType\n      let styleSheets: StyleSheetTuple[]\n      let props: Record<string, any> | undefined\n\n      if (\n        typeof Component! === 'undefined' ||\n        typeof styleSheets! === 'undefined'\n      ) {\n        ;({ page: Component, styleSheets } = await this.fetchComponent(\n          '/_error'\n        ))\n      }\n\n      const routeInfo: CompletePrivateRouteInfo = {\n        props,\n        Component,\n        styleSheets,\n        err,\n        error: err,\n      }\n\n      if (!routeInfo.props) {\n        try {\n          routeInfo.props = await this.getInitialProps(Component, {\n            err,\n            pathname,\n            query,\n          } as any)\n        } catch (gipErr) {\n          console.error('Error in error page `getInitialProps`: ', gipErr)\n          routeInfo.props = {}\n        }\n      }\n\n      return routeInfo\n    } catch (routeInfoErr) {\n      return this.handleRouteInfoError(\n        routeInfoErr,\n        pathname,\n        query,\n        as,\n        routeProps,\n        true\n      )\n    }\n  }\n\n  async getRouteInfo(\n    route: string,\n    pathname: string,\n    query: any,\n    as: string,\n    resolvedAs: string,\n    routeProps: RouteProperties\n  ): Promise<PrivateRouteInfo> {\n    try {\n      const existingRouteInfo: PrivateRouteInfo | undefined =\n        this.components[route]\n      if (routeProps.shallow && existingRouteInfo && this.route === route) {\n        return existingRouteInfo\n      }\n\n      const cachedRouteInfo: CompletePrivateRouteInfo | undefined =\n        existingRouteInfo && 'initial' in existingRouteInfo\n          ? undefined\n          : existingRouteInfo\n      const routeInfo: CompletePrivateRouteInfo = cachedRouteInfo\n        ? cachedRouteInfo\n        : await this.fetchComponent(route).then((res) => ({\n            Component: res.page,\n            styleSheets: res.styleSheets,\n            __N_SSG: res.mod.__N_SSG,\n            __N_SSP: res.mod.__N_SSP,\n          }))\n\n      const { Component, __N_SSG, __N_SSP } = routeInfo\n\n      if (process.env.NODE_ENV !== 'production') {\n        const { isValidElementType } = require('react-is')\n        if (!isValidElementType(Component)) {\n          throw new Error(\n            `The default export is not a React Component in page: \"${pathname}\"`\n          )\n        }\n      }\n\n      let dataHref: string | undefined\n\n      if (__N_SSG || __N_SSP) {\n        dataHref = this.pageLoader.getDataHref(\n          formatWithValidation({ pathname, query }),\n          resolvedAs,\n          __N_SSG,\n          this.locale\n        )\n      }\n\n      const props = await this._getData<CompletePrivateRouteInfo>(() =>\n        __N_SSG\n          ? this._getStaticData(dataHref!)\n          : __N_SSP\n          ? this._getServerData(dataHref!)\n          : this.getInitialProps(\n              Component,\n              // we provide AppTree later so this needs to be `any`\n              {\n                pathname,\n                query,\n                asPath: as,\n                locale: this.locale,\n                locales: this.locales,\n                defaultLocale: this.defaultLocale,\n              } as any\n            )\n      )\n\n      routeInfo.props = props\n      this.components[route] = routeInfo\n      return routeInfo\n    } catch (err) {\n      return this.handleRouteInfoError(err, pathname, query, as, routeProps)\n    }\n  }\n\n  set(\n    route: string,\n    pathname: string,\n    query: ParsedUrlQuery,\n    as: string,\n    data: PrivateRouteInfo,\n    resetScroll: { x: number; y: number } | null\n  ): Promise<void> {\n    this.isFallback = false\n\n    this.route = route\n    this.pathname = pathname\n    this.query = query\n    this.asPath = as\n    return this.notify(data, resetScroll)\n  }\n\n  /**\n   * Callback to execute before replacing router state\n   * @param cb callback to be executed\n   */\n  beforePopState(cb: BeforePopStateCallback) {\n    this._bps = cb\n  }\n\n  onlyAHashChange(as: string): boolean {\n    if (!this.asPath) return false\n    const [oldUrlNoHash, oldHash] = this.asPath.split('#')\n    const [newUrlNoHash, newHash] = as.split('#')\n\n    // Makes sure we scroll to the provided hash if the url/hash are the same\n    if (newHash && oldUrlNoHash === newUrlNoHash && oldHash === newHash) {\n      return true\n    }\n\n    // If the urls are change, there's more than a hash change\n    if (oldUrlNoHash !== newUrlNoHash) {\n      return false\n    }\n\n    // If the hash has changed, then it's a hash only change.\n    // This check is necessary to handle both the enter and\n    // leave hash === '' cases. The identity case falls through\n    // and is treated as a next reload.\n    return oldHash !== newHash\n  }\n\n  scrollToHash(as: string): void {\n    const [, hash] = as.split('#')\n    // Scroll to top if the hash is just `#` with no value or `#top`\n    // To mirror browsers\n    if (hash === '' || hash === 'top') {\n      window.scrollTo(0, 0)\n      return\n    }\n\n    // First we check if the element by id is found\n    const idEl = document.getElementById(hash)\n    if (idEl) {\n      idEl.scrollIntoView()\n      return\n    }\n    // If there's no element with the id, we check the `name` property\n    // To mirror browsers\n    const nameEl = document.getElementsByName(hash)[0]\n    if (nameEl) {\n      nameEl.scrollIntoView()\n    }\n  }\n\n  urlIsNew(asPath: string): boolean {\n    return this.asPath !== asPath\n  }\n\n  /**\n   * Prefetch page code, you may wait for the data during page rendering.\n   * This feature only works in production!\n   * @param url the href of prefetched page\n   * @param asPath the as path of the prefetched page\n   */\n  async prefetch(\n    url: string,\n    asPath: string = url,\n    options: PrefetchOptions = {}\n  ): Promise<void> {\n    let parsed = parseRelativeUrl(url)\n\n    let { pathname } = parsed\n\n    if (process.env.__NEXT_I18N_SUPPORT) {\n      if (options.locale === false) {\n        pathname = normalizeLocalePath!(pathname, this.locales).pathname\n        parsed.pathname = pathname\n        url = formatWithValidation(parsed)\n\n        let parsedAs = parseRelativeUrl(asPath)\n        const localePathResult = normalizeLocalePath!(\n          parsedAs.pathname,\n          this.locales\n        )\n        parsedAs.pathname = localePathResult.pathname\n        options.locale = localePathResult.detectedLocale || this.defaultLocale\n        asPath = formatWithValidation(parsedAs)\n      }\n    }\n\n    const pages = await this.pageLoader.getPageList()\n    let resolvedAs = asPath\n\n    if (process.env.__NEXT_HAS_REWRITES && asPath.startsWith('/')) {\n      let rewrites: any\n      ;({ __rewrites: rewrites } = await getClientBuildManifest())\n\n      const rewritesResult = resolveRewrites(\n        addBasePath(addLocale(asPath, this.locale)),\n        pages,\n        rewrites,\n        parsed.query,\n        (p: string) => resolveDynamicRoute(p, pages),\n        this.locales\n      )\n      resolvedAs = delLocale(delBasePath(rewritesResult.asPath), this.locale)\n\n      if (rewritesResult.matchedPage && rewritesResult.resolvedHref) {\n        // if this directly matches a page we need to update the href to\n        // allow the correct page chunk to be loaded\n        pathname = rewritesResult.resolvedHref\n        parsed.pathname = pathname\n        url = formatWithValidation(parsed)\n      }\n    } else {\n      parsed.pathname = resolveDynamicRoute(parsed.pathname, pages)\n\n      if (parsed.pathname !== pathname) {\n        pathname = parsed.pathname\n        parsed.pathname = pathname\n        url = formatWithValidation(parsed)\n      }\n    }\n    const route = removePathTrailingSlash(pathname)\n\n    // Prefetch is not supported in development mode because it would trigger on-demand-entries\n    if (process.env.NODE_ENV !== 'production') {\n      return\n    }\n\n    await Promise.all([\n      this.pageLoader._isSsg(route).then((isSsg: boolean) => {\n        return isSsg\n          ? this._getStaticData(\n              this.pageLoader.getDataHref(\n                url,\n                resolvedAs,\n                true,\n                typeof options.locale !== 'undefined'\n                  ? options.locale\n                  : this.locale\n              )\n            )\n          : false\n      }),\n      this.pageLoader[options.priority ? 'loadPage' : 'prefetch'](route),\n    ])\n  }\n\n  async fetchComponent(route: string): Promise<GoodPageCache> {\n    let cancelled = false\n    const cancel = (this.clc = () => {\n      cancelled = true\n    })\n\n    const componentResult = await this.pageLoader.loadPage(route)\n\n    if (cancelled) {\n      const error: any = new Error(\n        `Abort fetching component for route: \"${route}\"`\n      )\n      error.cancelled = true\n      throw error\n    }\n\n    if (cancel === this.clc) {\n      this.clc = null\n    }\n\n    return componentResult\n  }\n\n  _getData<T>(fn: () => Promise<T>): Promise<T> {\n    let cancelled = false\n    const cancel = () => {\n      cancelled = true\n    }\n    this.clc = cancel\n    return fn().then((data) => {\n      if (cancel === this.clc) {\n        this.clc = null\n      }\n\n      if (cancelled) {\n        const err: any = new Error('Loading initial props cancelled')\n        err.cancelled = true\n        throw err\n      }\n\n      return data\n    })\n  }\n\n  _getStaticData(dataHref: string): Promise<object> {\n    const { href: cacheKey } = new URL(dataHref, window.location.href)\n    if (\n      process.env.NODE_ENV === 'production' &&\n      !this.isPreview &&\n      this.sdc[cacheKey]\n    ) {\n      return Promise.resolve(this.sdc[cacheKey])\n    }\n    return fetchNextData(dataHref, this.isSsr).then((data) => {\n      this.sdc[cacheKey] = data\n      return data\n    })\n  }\n\n  _getServerData(dataHref: string): Promise<object> {\n    const { href: resourceKey } = new URL(dataHref, window.location.href)\n    if (this.sdr[resourceKey]) {\n      return this.sdr[resourceKey]\n    }\n    return (this.sdr[resourceKey] = fetchNextData(dataHref, this.isSsr)\n      .then((data) => {\n        delete this.sdr[resourceKey]\n        return data\n      })\n      .catch((err) => {\n        delete this.sdr[resourceKey]\n        throw err\n      }))\n  }\n\n  getInitialProps(\n    Component: ComponentType,\n    ctx: NextPageContext\n  ): Promise<any> {\n    const { Component: App } = this.components['/_app']\n    const AppTree = this._wrapApp(App as AppComponent)\n    ctx.AppTree = AppTree\n    return loadGetInitialProps<AppContextType<Router>>(App, {\n      AppTree,\n      Component,\n      router: this,\n      ctx,\n    })\n  }\n\n  abortComponentLoad(as: string, routeProps: RouteProperties): void {\n    if (this.clc) {\n      Router.events.emit(\n        'routeChangeError',\n        buildCancellationError(),\n        as,\n        routeProps\n      )\n      this.clc()\n      this.clc = null\n    }\n  }\n\n  notify(\n    data: PrivateRouteInfo,\n    resetScroll: { x: number; y: number } | null\n  ): Promise<void> {\n    return this.sub(\n      data,\n      this.components['/_app'].Component as AppComponent,\n      resetScroll\n    )\n  }\n}\n"], "names": [], "mappings": ";;;;QAwFgB,eAAe,GAAf,eAAe;QAsBf,SAAS,GAAT,SAAS;QAoBT,SAAS,GAAT,SAAS;QA0BT,WAAW,GAAX,WAAW;QAKX,WAAW,GAAX,WAAW;QAKX,WAAW,GAAX,WAAW;QASX,UAAU,GAAV,UAAU;QAgBV,aAAa,GAAb,aAAa;QA+Eb,WAAW,GAAX,WAAW;;AAvQpB,GAA0C,CAA1C,uBAA0C;AAM1C,GAA8B,CAA9B,YAA8B;AAGD,GAAuC,CAAvC,oBAAuC;AACvC,GAA+B,CAA/B,oBAA+B;AACjC,GAAS,CAAT,KAAS;AAWpC,GAAU,CAAV,MAAU;AACc,GAAoB,CAApB,UAAoB;AAClB,GAA4B,CAA5B,iBAA4B;AACtB,GAAqB,CAArB,YAAqB;AAChC,GAA0B,CAA1B,gBAA0B;AACtB,GAAuB,CAAvB,aAAuB;AACzB,GAAqB,CAArB,WAAqB;;;;;;AA8BnD,GAAG,CAAC,kBAAkB;AAEtB,EAAE,EAAE,OAAO,CAAC,GAAG,CAAC,mBAAmB,EAAE,CAAC;IACpC,kBAAkB,GAChB,OAAO,EAAC,4BAA8B,GAAE,kBAAkB;AAC9D,CAAC;AAED,KAAK,CAAC,QAAQ,GAAI,OAAO,CAAC,GAAG,CAAC,sBAAsB;SAE3C,sBAAsB,GAAG,CAAC;WAC1B,MAAM,CAAC,MAAM,CAAC,GAAG,CAAC,KAAK,EAAC,eAAiB;QAC9C,SAAS,EAAE,IAAI;;AAEnB,CAAC;SAEQ,aAAa,CAAC,IAAY,EAAE,MAAe,EAAE,CAAC;WAC9C,MAAM,IAAI,IAAI,CAAC,UAAU,EAAC,CAAG,KAChC,IAAI,MAAK,CAAG,QA3EX,uBAA0C,6BA4Ed,MAAM,OAC9B,MAAM,GAAG,eAAe,CAAC,IAAI,OAAM,CAAG,IAAG,IAAI,CAAC,SAAS,CAAC,CAAC,IAAI,IAAI,KACtE,IAAI;AACV,CAAC;SAEe,eAAe,CAC7B,IAAY,EACZ,MAAuB,EACvB,OAAkB,EAClB,aAA8B,EAC9B,CAAC;IACD,EAAE,EAAE,OAAO,CAAC,GAAG,CAAC,mBAAmB,EAAE,CAAC;QACpC,MAAM,GAAG,MAAM,QA9EiB,oBAA+B,sBA8ExB,IAAI,EAAE,OAAO,EAAE,cAAc;QAEpE,KAAK,CAAC,cAAc,GAAG,kBAAkB,CAAC,aAAa,EAAE,SAAS,EAAE,MAAM;QAE1E,EAAE,EAAE,cAAc,EAAE,CAAC;oBACX,IAAI,EAAE,cAAc,CAAC,IAAI,SAAQ,CAAG,EAAC,GAAG,EAAE,cAAc,CAAC,MAAM,GACrE,QAAQ,SACP,MAAM,KAAK,cAAc,CAAC,aAAa,SAAS,CAAC,EAAE,MAAM,KAAK,IAAI;QACvE,CAAC;eACM,KAAK;IACd,CAAC,MAAM,CAAC;eACC,KAAK;IACd,CAAC;AACH,CAAC;SAEe,SAAS,CACvB,IAAY,EACZ,MAAuB,EACvB,aAAsB,EACtB,CAAC;IACD,EAAE,EAAE,OAAO,CAAC,GAAG,CAAC,mBAAmB,EAAE,CAAC;QACpC,KAAK,CAAC,QAAQ,GAAG,eAAe,CAAC,IAAI;QACrC,KAAK,CAAC,SAAS,GAAG,QAAQ,CAAC,WAAW;QACtC,KAAK,CAAC,WAAW,GAAG,MAAM,IAAI,MAAM,CAAC,WAAW;eAEzC,MAAM,IACX,MAAM,KAAK,aAAa,KACvB,SAAS,CAAC,UAAU,EAAC,CAAG,IAAG,WAAW,IAAG,CAAG,MAC7C,SAAS,MAAK,CAAG,IAAG,WAAW,GAC7B,aAAa,CAAC,IAAI,GAAE,CAAG,IAAG,MAAM,IAChC,IAAI;IACV,CAAC;WACM,IAAI;AACb,CAAC;SAEe,SAAS,CAAC,IAAY,EAAE,MAAe,EAAE,CAAC;IACxD,EAAE,EAAE,OAAO,CAAC,GAAG,CAAC,mBAAmB,EAAE,CAAC;QACpC,KAAK,CAAC,QAAQ,GAAG,eAAe,CAAC,IAAI;QACrC,KAAK,CAAC,SAAS,GAAG,QAAQ,CAAC,WAAW;QACtC,KAAK,CAAC,WAAW,GAAG,MAAM,IAAI,MAAM,CAAC,WAAW;eAEzC,MAAM,KACV,SAAS,CAAC,UAAU,EAAC,CAAG,IAAG,WAAW,IAAG,CAAG,MAC3C,SAAS,MAAK,CAAG,IAAG,WAAW,KAC9B,QAAQ,CAAC,MAAM,KAAK,MAAM,CAAC,MAAM,GAAG,CAAC,IAAG,CAAG,UAC1C,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,IAC/B,IAAI;IACV,CAAC;WACM,IAAI;AACb,CAAC;SAEQ,eAAe,CAAC,IAAY,EAAE,CAAC;IACtC,KAAK,CAAC,UAAU,GAAG,IAAI,CAAC,OAAO,EAAC,CAAG;IACnC,KAAK,CAAC,SAAS,GAAG,IAAI,CAAC,OAAO,EAAC,CAAG;IAElC,EAAE,EAAE,UAAU,IAAI,CAAC,IAAI,SAAS,IAAI,CAAC,EAAE,CAAC;QACtC,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,UAAU,IAAI,CAAC,GAAG,UAAU,GAAG,SAAS;IACnE,CAAC;WACM,IAAI;AACb,CAAC;SAEe,WAAW,CAAC,IAAY,EAAW,CAAC;IAClD,IAAI,GAAG,eAAe,CAAC,IAAI;WACpB,IAAI,KAAK,QAAQ,IAAI,IAAI,CAAC,UAAU,CAAC,QAAQ,IAAG,CAAG;AAC5D,CAAC;SAEe,WAAW,CAAC,IAAY,EAAU,CAAC;IACjD,EAA4C,AAA5C,0CAA4C;WACrC,aAAa,CAAC,IAAI,EAAE,QAAQ;AACrC,CAAC;SAEe,WAAW,CAAC,IAAY,EAAU,CAAC;IACjD,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,MAAM;IACjC,EAAE,GAAG,IAAI,CAAC,UAAU,EAAC,CAAG,IAAG,IAAI,IAAI,CAAC,EAAE,IAAI;WACnC,IAAI;AACb,CAAC;SAKe,UAAU,CAAC,GAAW,EAAW,CAAC;IAChD,EAAgE,AAAhE,8DAAgE;IAChE,EAAE,EAAE,GAAG,CAAC,UAAU,EAAC,CAAG,MAAK,GAAG,CAAC,UAAU,EAAC,CAAG,MAAK,GAAG,CAAC,UAAU,EAAC,CAAG,WAC3D,IAAI;QACT,CAAC;QACH,EAA4D,AAA5D,0DAA4D;QAC5D,KAAK,CAAC,cAAc,OAxJjB,MAAU;QAyJb,KAAK,CAAC,QAAQ,GAAG,GAAG,CAAC,GAAG,CAAC,GAAG,EAAE,cAAc;eACrC,QAAQ,CAAC,MAAM,KAAK,cAAc,IAAI,WAAW,CAAC,QAAQ,CAAC,QAAQ;IAC5E,CAAC,QAAQ,CAAC,EAAE,CAAC;eACJ,KAAK;IACd,CAAC;AACH,CAAC;SAIe,aAAa,CAC3B,KAAa,EACb,UAAkB,EAClB,KAAqB,EACrB,CAAC;IACD,GAAG,CAAC,iBAAiB;IAErB,KAAK,CAAC,YAAY,OAnKU,WAAqB,gBAmKd,KAAK;IACxC,KAAK,CAAC,aAAa,GAAG,YAAY,CAAC,MAAM;IACzC,KAAK,CAAC,cAAc,GAClB,EAAoD,AAApD,kDAAoD;KACnD,UAAU,KAAK,KAAK,OAxKO,aAAuB,kBAwKX,YAAY,EAAE,UAAU,WAChE,EAAgD,AAAhD,8CAAgD;IAChD,EAAsE,AAAtE,oEAAsE;IACtE,KAAK;IAEP,iBAAiB,GAAG,KAAK;IACzB,KAAK,CAAC,MAAM,GAAG,MAAM,CAAC,IAAI,CAAC,aAAa;IAExC,EAAE,GACC,MAAM,CAAC,KAAK,EAAE,KAAK,GAAK,CAAC;QACxB,GAAG,CAAC,KAAK,GAAG,cAAc,CAAC,KAAK;QAChC,KAAK,GAAG,MAAM,GAAE,QAAQ,MAAK,aAAa,CAAC,KAAK;QAEhD,EAAiC,AAAjC,+BAAiC;QACjC,EAA0D,AAA1D,wDAA0D;QAC1D,GAAG,CAAC,QAAQ,IAAI,CAAC,EAAE,MAAM,IAAG,GAAK,SAAQ,KAAK,CAAC,CAAC;QAChD,EAAE,EAAE,QAAQ,EAAE,CAAC;YACb,QAAQ,OAAO,KAAK,IAAG,CAAG,OAAM,CAAC,EAAE,QAAQ,CAAC,CAAC;QAC/C,CAAC;QACD,EAAE,EAAE,MAAM,KAAK,KAAK,CAAC,OAAO,CAAC,KAAK,GAAG,KAAK;YAAI,KAAK;;gBAGhD,QAAQ,IAAI,KAAK,IAAI,cAAc,KACpC,EAA6C,AAA7C,2CAA6C;SAC5C,iBAAiB,GAChB,iBAAiB,CAAE,OAAO,CACxB,QAAQ,EACR,MAAM,GACD,KAAK,CACH,GAAG,CACF,EAAuD,AAAvD,qDAAuD;QACvD,EAAuD,AAAvD,qDAAuD;QACvD,EAAkD,AAAlD,gDAAkD;QAClD,EAAoC,AAApC,kCAAoC;SACnC,OAAO,GAAK,kBAAkB,CAAC,OAAO;UAExC,IAAI,EAAC,CAAG,KACX,kBAAkB,CAAC,KAAK,OACzB,CAAG;IAEd,CAAC,GACD,CAAC;QACD,iBAAiB,KAAM,CAAmC,AAAnC,EAAmC,AAAnC,iCAAmC;;IAE1D,EAAuE,AAAvE,qEAAuE;IACvE,EAAkD,AAAlD,gDAAkD;IACpD,CAAC;;QAEC,MAAM;QACN,MAAM,EAAE,iBAAiB;;AAE7B,CAAC;SAEQ,kBAAkB,CAAC,KAAqB,EAAE,MAAgB,EAAE,CAAC;IACpE,KAAK,CAAC,aAAa;;IAEnB,MAAM,CAAC,IAAI,CAAC,KAAK,EAAE,OAAO,EAAE,GAAG,GAAK,CAAC;QACnC,EAAE,GAAG,MAAM,CAAC,QAAQ,CAAC,GAAG,GAAG,CAAC;YAC1B,aAAa,CAAC,GAAG,IAAI,KAAK,CAAC,GAAG;QAChC,CAAC;IACH,CAAC;WACM,aAAa;AACtB,CAAC;SAMe,WAAW,CACzB,MAAkB,EAClB,IAAS,EACT,SAAmB,EACX,CAAC;IACT,EAA4C,AAA5C,0CAA4C;IAC5C,GAAG,CAAC,IAAI;IACR,GAAG,CAAC,WAAW,UAAU,IAAI,MAAK,MAAQ,IAAG,IAAI,OAxP5C,MAAU,uBAwP0D,IAAI;IAE7E,EAA6D,AAA7D,2DAA6D;IAC7D,EAAmD,AAAnD,iDAAmD;IACnD,KAAK,CAAC,aAAa,GAAG,WAAW,CAAC,KAAK;IACvC,KAAK,CAAC,kBAAkB,GAAG,aAAa,GACpC,WAAW,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC,EAAE,MAAM,IAC1C,WAAW;IAEf,KAAK,CAAC,QAAQ,GAAG,kBAAkB,CAAC,KAAK,EAAC,CAAG;IAE7C,EAAE,GAAG,QAAQ,CAAC,CAAC,SAAS,KAAK,eAAe,CAAC;QAC3C,OAAO,CAAC,KAAK,EACV,oCAAoC,EAAE,WAAW,CAAC,2EAA2E;QAEhI,KAAK,CAAC,aAAa,OAvQhB,MAAU,2BAuQkC,kBAAkB;QACjE,WAAW,IAAI,aAAa,GAAG,aAAa,CAAC,CAAC,UAAU,aAAa;IACvE,CAAC;IAED,EAA2D,AAA3D,yDAA2D;IAC3D,EAAE,GAAG,UAAU,CAAC,WAAW,GAAG,CAAC;eACrB,SAAS;YAAI,WAAW;YAAI,WAAW;IACjD,CAAC;QAEG,CAAC;QACH,IAAI,GAAG,GAAG,CAAC,GAAG,CACZ,WAAW,CAAC,UAAU,EAAC,CAAG,KAAI,MAAM,CAAC,MAAM,GAAG,MAAM,CAAC,QAAQ,GAC7D,QAAU;IAEd,CAAC,QAAQ,CAAC,EAAE,CAAC;QACX,EAAkD,AAAlD,gDAAkD;QAClD,IAAI,GAAG,GAAG,CAAC,GAAG,EAAC,CAAG,IAAE,QAAU;IAChC,CAAC;QACG,CAAC;QACH,KAAK,CAAC,QAAQ,GAAG,GAAG,CAAC,GAAG,CAAC,WAAW,EAAE,IAAI;QAC1C,QAAQ,CAAC,QAAQ,OAjTd,uBAA0C,6BAiTE,QAAQ,CAAC,QAAQ;QAChE,GAAG,CAAC,cAAc;QAElB,EAAE,MA7RyB,UAAoB,iBA8R9B,QAAQ,CAAC,QAAQ,KAChC,QAAQ,CAAC,YAAY,IACrB,SAAS,EACT,CAAC;YACD,KAAK,CAAC,KAAK,OAhSsB,YAAqB,yBAgSjB,QAAQ,CAAC,YAAY;YAE1D,KAAK,GAAG,MAAM,GAAE,MAAM,MAAK,aAAa,CACtC,QAAQ,CAAC,QAAQ,EACjB,QAAQ,CAAC,QAAQ,EACjB,KAAK;YAGP,EAAE,EAAE,MAAM,EAAE,CAAC;gBACX,cAAc,OA5Sf,MAAU;oBA6SP,QAAQ,EAAE,MAAM;oBAChB,IAAI,EAAE,QAAQ,CAAC,IAAI;oBACnB,KAAK,EAAE,kBAAkB,CAAC,KAAK,EAAE,MAAM;;YAE3C,CAAC;QACH,CAAC;QAED,EAAoE,AAApE,kEAAoE;QACpE,KAAK,CAAC,YAAY,GAChB,QAAQ,CAAC,MAAM,KAAK,IAAI,CAAC,MAAM,GAC3B,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,MAAM,CAAC,MAAM,IAC1C,QAAQ,CAAC,IAAI;eAGjB,SAAS;YAAI,YAAY;YAAE,cAAc,IAAI,YAAY;YAAI,YAAY;IAE7E,CAAC,QAAQ,CAAC,EAAE,CAAC;eACH,SAAS;YAAI,WAAW;YAAI,WAAW;IACjD,CAAC;AACH,CAAC;SAEQ,WAAW,CAAC,GAAW,EAAE,CAAC;IACjC,KAAK,CAAC,MAAM,OAnUP,MAAU;WAqUR,GAAG,CAAC,UAAU,CAAC,MAAM,IAAI,GAAG,CAAC,SAAS,CAAC,MAAM,CAAC,MAAM,IAAI,GAAG;AACpE,CAAC;SAEQ,YAAY,CAAC,MAAkB,EAAE,GAAQ,EAAE,EAAQ,EAAE,CAAC;IAC7D,EAAsD,AAAtD,oDAAsD;IACtD,EAAkD,AAAlD,gDAAkD;IAClD,GAAG,EAAE,YAAY,EAAE,UAAU,IAAI,WAAW,CAAC,MAAM,EAAE,GAAG,EAAE,IAAI;IAC9D,KAAK,CAAC,MAAM,OA5UP,MAAU;IA6Uf,KAAK,CAAC,aAAa,GAAG,YAAY,CAAC,UAAU,CAAC,MAAM;IACpD,KAAK,CAAC,WAAW,GAAG,UAAU,IAAI,UAAU,CAAC,UAAU,CAAC,MAAM;IAE9D,YAAY,GAAG,WAAW,CAAC,YAAY;IACvC,UAAU,GAAG,UAAU,GAAG,WAAW,CAAC,UAAU,IAAI,UAAU;IAE9D,KAAK,CAAC,WAAW,GAAG,aAAa,GAAG,YAAY,GAAG,WAAW,CAAC,YAAY;IAC3E,KAAK,CAAC,UAAU,GAAG,EAAE,GACjB,WAAW,CAAC,WAAW,CAAC,MAAM,EAAE,EAAE,KAClC,UAAU,IAAI,YAAY;;QAG5B,GAAG,EAAE,WAAW;QAChB,EAAE,EAAE,WAAW,GAAG,UAAU,GAAG,WAAW,CAAC,UAAU;;AAEzD,CAAC;SAEQ,mBAAmB,CAAC,QAAgB,EAAE,KAAe,EAAE,CAAC;IAC/D,KAAK,CAAC,aAAa,OArXd,uBAA0C,8BASb,oBAAuC,sBA4WP,QAAQ;IAE1E,EAAE,EAAE,aAAa,MAAK,IAAM,KAAI,aAAa,MAAK,OAAS,GAAE,CAAC;eACrD,QAAQ;IACjB,CAAC;IAED,EAA2C,AAA3C,yCAA2C;IAC3C,EAAE,GAAG,KAAK,CAAC,QAAQ,CAAC,aAAa,GAAI,CAAC;QACpC,EAAiD,AAAjD,+CAAiD;QACjD,KAAK,CAAC,IAAI,EAAE,IAAI,GAAK,CAAC;YACpB,EAAE,MAxWuB,UAAoB,iBAwW1B,IAAI,SAnWC,WAAqB,gBAmWH,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,aAAa,GAAI,CAAC;gBACxE,QAAQ,GAAG,IAAI;uBACR,IAAI;YACb,CAAC;QACH,CAAC;IACH,CAAC;eApYI,uBAA0C,0BAqYhB,QAAQ;AACzC,CAAC;AAkED,KAAK,CAAC,uBAAuB,GAC3B,OAAO,CAAC,GAAG,CAAC,yBAAyB,WAC9B,MAAM,MAAK,SAAW,MAC7B,iBAAmB,KAAI,MAAM,CAAC,OAAO,iBACtB,CAAC;QACV,CAAC;QACH,GAAG,CAAC,CAAC,IAAG,MAAQ;QAChB,EAAwC,AAAxC,sCAAwC;eACjC,cAAc,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,GAAG,cAAc,CAAC,UAAU,CAAC,CAAC,GAAG,IAAI;IACzE,CAAC,QAAQ,CAAC,EAAE,CAAC;IAAA,CAAC;AAChB,CAAC;AAEH,KAAK,CAAC,kBAAkB,GAAG,MAAM,EAAC,kBAAoB;SAE7C,UAAU,CAAC,GAAW,EAAE,QAAgB,EAAgB,CAAC;WACzD,KAAK,CAAC,GAAG;QACd,EAAsE,AAAtE,oEAAsE;QACtE,EAAyD,AAAzD,uDAAyD;QACzD,EAAE;QACF,EAAoE,AAApE,oEAAoE;QACpE,EAAY,AAAZ,UAAY;QACZ,EAAyE,AAAzE,uEAAyE;QACzE,EAAE;QACF,EAAiE,AAAjE,+DAAiE;QACjE,EAAsE,AAAtE,oEAAsE;QACtE,EAA8C,AAA9C,4CAA8C;QAC9C,EAA0C,AAA1C,wCAA0C;QAC1C,WAAW,GAAE,WAAa;OACzB,IAAI,EAAE,GAAG,GAAK,CAAC;QAChB,EAAE,GAAG,GAAG,CAAC,EAAE,EAAE,CAAC;YACZ,EAAE,EAAE,QAAQ,GAAG,CAAC,IAAI,GAAG,CAAC,MAAM,IAAI,GAAG,EAAE,CAAC;uBAC/B,UAAU,CAAC,GAAG,EAAE,QAAQ,GAAG,CAAC;YACrC,CAAC;YACD,EAAE,EAAE,GAAG,CAAC,MAAM,KAAK,GAAG,EAAE,CAAC;uBAChB,GAAG,CAAC,IAAI,GAAG,IAAI,EAAE,IAAI,GAAK,CAAC;oBAChC,EAAE,EAAE,IAAI,CAAC,QAAQ,EAAE,CAAC;;4BACT,QAAQ,EAAE,kBAAkB;;oBACvC,CAAC;oBACD,KAAK,CAAC,GAAG,CAAC,KAAK,EAAE,2BAA2B;gBAC9C,CAAC;YACH,CAAC;YACD,KAAK,CAAC,GAAG,CAAC,KAAK,EAAE,2BAA2B;QAC9C,CAAC;eACM,GAAG,CAAC,IAAI;IACjB,CAAC;AACH,CAAC;SAEQ,aAAa,CAAC,QAAgB,EAAE,cAAuB,EAAE,CAAC;WAC1D,UAAU,CAAC,QAAQ,EAAE,cAAc,GAAG,CAAC,GAAG,CAAC,EAAE,KAAK,EAAE,GAAU,GAAK,CAAC;QACzE,EAAqE,AAArE,mEAAqE;QACrE,EAAoE,AAApE,kEAAoE;QACpE,EAAQ,AAAR,MAAQ;QAER,EAAE,GAAG,cAAc,EAAE,CAAC;gBAvfnB,YAA8B,iBAwfhB,GAAG;QACpB,CAAC;QACD,KAAK,CAAC,GAAG;IACX,CAAC;AACH,CAAC;MAEoB,MAAM;gBAuCvB,SAAgB,EAChB,MAAqB,EACrB,GAAU,IAER,YAAY,GACZ,UAAU,GACV,GAAG,GACH,OAAO,GACP,SAAS,EAAT,UAAS,GACT,GAAG,EAAH,IAAG,GACH,YAAY,GACZ,UAAU,GACV,MAAM,GACN,OAAO,GACP,aAAa,GACb,aAAa,GACb,SAAS,IAgBX,CAAC;QA5DH,EAAoB,AAApB,kBAAoB;aACpB,GAAG;;QACH,EAA+C,AAA/C,6CAA+C;aAC/C,GAAG;;aAoBK,IAAI,GAAW,CAAC;aA0IxB,UAAU,IAAI,CAAgB,GAAW,CAAC;YACxC,KAAK,CAAC,KAAK,GAAG,CAAC,CAAC,KAAK;YAErB,EAAE,GAAG,KAAK,EAAE,CAAC;gBACX,EAA6C,AAA7C,2CAA6C;gBAC7C,EAAsD,AAAtD,oDAAsD;gBACtD,EAAkC,AAAlC,gCAAkC;gBAClC,EAAE;gBACF,EAAoE,AAApE,kEAAoE;gBACpE,EAA4B,AAA5B,0BAA4B;gBAC5B,EAA4D,AAA5D,0DAA4D;gBAC5D,EAAkF,AAAlF,gFAAkF;gBAClF,EAAgD,AAAhD,8CAAgD;gBAChD,KAAK,GAAG,QAAQ,EAAR,SAAQ,GAAE,KAAK,EAAL,MAAK;qBAClB,WAAW,EACd,YAAc,OAzqBf,MAAU;oBA0qBc,QAAQ,EAAE,WAAW,CAAC,SAAQ;oBAAG,KAAK,EAAL,MAAK;wBA1qB9D,MAAU;;YA8qBb,CAAC;YAED,EAAE,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC;;YAEjB,CAAC;YAED,GAAG,CAAC,YAAY;YAChB,KAAK,GAAG,GAAG,GAAE,EAAE,EAAF,GAAE,GAAE,OAAO,GAAE,GAAG,MAAK,KAAK;YACvC,EAAE,EAAE,OAAO,CAAC,GAAG,CAAC,yBAAyB,EAAE,CAAC;gBAC1C,EAAE,EAAE,uBAAuB,EAAE,CAAC;oBAC5B,EAAE,OAAO,IAAI,KAAK,GAAG,EAAE,CAAC;wBACtB,EAAoC,AAApC,kCAAoC;4BAChC,CAAC;4BACH,cAAc,CAAC,OAAO,EACpB,cAAgB,SAAQ,IAAI,EAC5B,IAAI,CAAC,SAAS;gCAAG,CAAC,EAAE,IAAI,CAAC,WAAW;gCAAE,CAAC,EAAE,IAAI,CAAC,WAAW;;wBAE7D,CAAC,QAAO,CAAC;wBAAA,CAAC;wBAEV,EAA+B,AAA/B,6BAA+B;4BAC3B,CAAC;4BACH,KAAK,CAAC,CAAC,GAAG,cAAc,CAAC,OAAO,EAAC,cAAgB,IAAG,GAAG;4BACvD,YAAY,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC;wBAC7B,CAAC,QAAO,CAAC;4BACP,YAAY;gCAAK,CAAC,EAAE,CAAC;gCAAE,CAAC,EAAE,CAAC;;wBAC7B,CAAC;oBACH,CAAC;gBACH,CAAC;YACH,CAAC;iBACI,IAAI,GAAG,GAAG;YAEf,KAAK,GAAG,QAAQ,EAAR,SAAQ,UA3sBa,iBAA4B,mBA2sBnB,GAAG;YAEzC,EAAgD,AAAhD,8CAAgD;YAChD,EAAyD,AAAzD,uDAAyD;YACzD,EAAE,OAAO,KAAK,IAAI,GAAE,UAAU,MAAM,IAAI,SAAQ,UAAU,QAAQ,EAAE,CAAC;;YAErE,CAAC;YAED,EAAuD,AAAvD,qDAAuD;YACvD,EAAwD,AAAxD,sDAAwD;YACxD,EAAE,OAAO,IAAI,UAAU,IAAI,CAAC,KAAK,GAAG,CAAC;;YAErC,CAAC;iBAEI,MAAM,EACT,YAAc,GACd,GAAG,EACH,GAAE,EACF,MAAM,CAAC,MAAM;eAA+C,OAAO;gBACjE,OAAO,EAAE,OAAO,CAAC,OAAO,SAAS,QAAQ;gBACzC,MAAM,EAAE,OAAO,CAAC,MAAM,SAAS,aAAa;gBAE9C,YAAY;QAEhB,CAAC;QA/KC,EAAuC,AAAvC,qCAAuC;aAClC,KAAK,OA7kBP,uBAA0C,0BA6kBR,SAAQ;QAE7C,EAA6C,AAA7C,2CAA6C;aACxC,UAAU;;QACf,EAAoD,AAApD,kDAAoD;QACpD,EAAwD,AAAxD,sDAAwD;QACxD,EAAkC,AAAlC,gCAAkC;QAClC,EAAE,EAAE,SAAQ,MAAK,OAAS,GAAE,CAAC;iBACtB,UAAU,MAAM,KAAK;gBACxB,SAAS,EAAT,UAAS;gBACT,OAAO,EAAE,IAAI;gBACb,KAAK,EAAE,YAAY;gBACnB,GAAG,EAAH,IAAG;gBACH,OAAO,EAAE,YAAY,IAAI,YAAY,CAAC,OAAO;gBAC7C,OAAO,EAAE,YAAY,IAAI,YAAY,CAAC,OAAO;;QAEjD,CAAC;aAEI,UAAU,EAAC,KAAO;YACrB,SAAS,EAAE,GAAG;YACd,WAAW;;QAKb,EAA4C,AAA5C,0CAA4C;QAC5C,EAAgF,AAAhF,8EAAgF;aAC3E,MAAM,GAAG,MAAM,CAAC,MAAM;aAEtB,UAAU,GAAG,UAAU;aACvB,QAAQ,GAAG,SAAQ;aACnB,KAAK,GAAG,MAAK;QAClB,EAA8D,AAA9D,4DAA8D;QAC9D,EAAkD,AAAlD,gDAAkD;QAClD,KAAK,CAAC,iBAAiB,OAxlBI,UAAoB,iBAylB9B,SAAQ,KAAK,IAAI,CAAC,aAAa,CAAC,UAAU;aAEtD,MAAM,GAAG,iBAAiB,GAAG,SAAQ,GAAG,GAAE;aAC1C,QAAQ,GAAG,QAAQ;aACnB,GAAG,GAAG,YAAY;aAClB,GAAG,GAAG,IAAI;aACV,QAAQ,GAAG,OAAO;QACvB,EAA6D,AAA7D,2DAA6D;QAC7D,EAA0B,AAA1B,wBAA0B;aACrB,KAAK,GAAG,IAAI;aAEZ,UAAU,GAAG,UAAU;aAEvB,OAAO,MACV,IAAI,CAAC,aAAa,CAAC,IAAI,IACvB,IAAI,CAAC,aAAa,CAAC,GAAG,IACrB,IAAI,CAAC,aAAa,CAAC,MAAM,KAAK,IAAI,CAAC,aAAa,CAAC,GAAG,KACnD,iBAAiB,KAChB,IAAI,CAAC,QAAQ,CAAC,MAAM,KACpB,OAAO,CAAC,GAAG,CAAC,mBAAmB;aAE/B,SAAS,KAAK,SAAS;aACvB,cAAc,GAAG,KAAK;QAE3B,EAAE,EAAE,OAAO,CAAC,GAAG,CAAC,mBAAmB,EAAE,CAAC;iBAC/B,MAAM,GAAG,MAAM;iBACf,OAAO,GAAG,OAAO;iBACjB,aAAa,GAAG,aAAa;iBAC7B,aAAa,GAAG,aAAa;iBAC7B,cAAc,KAAK,kBAAkB,CACxC,aAAa,EACb,IAAI,CAAC,QAAQ,CAAC,QAAQ;QAE1B,CAAC;QAED,EAAE,SAAS,MAAM,MAAK,SAAW,GAAE,CAAC;YAClC,EAAkE,AAAlE,gEAAkE;YAClE,EAA4C,AAA5C,0CAA4C;YAC5C,EAAE,EAAE,GAAE,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,OAAM,EAAI,GAAE,CAAC;gBAC7B,EAA2D,AAA3D,yDAA2D;gBAC3D,EAA4D,AAA5D,0DAA4D;gBAC5D,KAAK,CAAC,OAAO;oBAAwB,MAAM;;gBACzC,OAAO,CAAS,kBAAkB,GAAG,GAAE,KAAK,SAAQ;qBAEjD,WAAW,EACd,YAAc,OAvoBjB,MAAU;oBAwoBgB,QAAQ,EAAE,WAAW,CAAC,SAAQ;oBAAG,KAAK,EAAL,MAAK;wBAxoBhE,MAAU,YA0oBP,OAAO;YAEX,CAAC;YAED,MAAM,CAAC,gBAAgB,EAAC,QAAU,QAAO,UAAU;YAEnD,EAA2D,AAA3D,yDAA2D;YAC3D,EAAmD,AAAnD,iDAAmD;YACnD,EAAE,EAAE,OAAO,CAAC,GAAG,CAAC,yBAAyB,EAAE,CAAC;gBAC1C,EAAE,EAAE,uBAAuB,EAAE,CAAC;oBAC5B,MAAM,CAAC,OAAO,CAAC,iBAAiB,IAAG,MAAQ;gBAC7C,CAAC;YACH,CAAC;QACH,CAAC;IACH,CAAC;IA+ED,MAAM,GAAS,CAAC;QACd,MAAM,CAAC,QAAQ,CAAC,MAAM;IACxB,CAAC;IAED,EAEG,AAFH;;GAEG,AAFH,EAEG,CACH,IAAI,GAAG,CAAC;QACN,MAAM,CAAC,OAAO,CAAC,IAAI;IACrB,CAAC;IAED,EAKG,AALH;;;;;GAKG,AALH,EAKG,CACH,IAAI,CAAC,GAAQ,EAAE,EAAQ,EAAE,OAA0B;OAAO,CAAC;QACzD,EAAE,EAAE,OAAO,CAAC,GAAG,CAAC,yBAAyB,EAAE,CAAC;YAC1C,EAAwE,AAAxE,sEAAwE;YACxE,EAAiE,AAAjE,+DAAiE;YACjE,EAAE,EAAE,uBAAuB,EAAE,CAAC;oBACxB,CAAC;oBACH,EAAkE,AAAlE,gEAAkE;oBAClE,cAAc,CAAC,OAAO,EACpB,cAAgB,SAAQ,IAAI,EAC5B,IAAI,CAAC,SAAS;wBAAG,CAAC,EAAE,IAAI,CAAC,WAAW;wBAAE,CAAC,EAAE,IAAI,CAAC,WAAW;;gBAE7D,CAAC,QAAO,CAAC;gBAAA,CAAC;YACZ,CAAC;QACH,CAAC;WACG,GAAG,GAAE,EAAE,MAAK,YAAY,OAAO,GAAG,EAAE,EAAE;oBAC9B,MAAM,EAAC,SAAW,GAAE,GAAG,EAAE,EAAE,EAAE,OAAO;IAClD,CAAC;IAED,EAKG,AALH;;;;;GAKG,AALH,EAKG,CACH,OAAO,CAAC,GAAQ,EAAE,EAAQ,EAAE,OAA0B;OAAO,CAAC;WACxD,GAAG,GAAE,EAAE,MAAK,YAAY,OAAO,GAAG,EAAE,EAAE;oBAC9B,MAAM,EAAC,YAAc,GAAE,GAAG,EAAE,EAAE,EAAE,OAAO;IACrD,CAAC;UAEa,MAAM,CAClB,MAAqB,EACrB,GAAW,EACX,EAAU,EACV,OAA0B,EAC1B,YAAuC,EACrB,CAAC;QACnB,EAAE,GAAG,UAAU,CAAC,GAAG,GAAG,CAAC;YACrB,MAAM,CAAC,QAAQ,CAAC,IAAI,GAAG,GAAG;mBACnB,KAAK;QACd,CAAC;QACD,KAAK,CAAC,iBAAiB,GACrB,GAAG,KAAK,EAAE,IAAK,OAAO,CAAS,EAAE,IAAK,OAAO,CAAS,kBAAkB;QAE1E,EAAyD,AAAzD,uDAAyD;QACzD,EAA4D,AAA5D,0DAA4D;QAC5D,EAAE,EAAG,OAAO,CAAS,EAAE,EAAE,CAAC;iBACnB,OAAO,GAAG,IAAI;QACrB,CAAC;QAED,KAAK,CAAC,UAAU,QAAQ,MAAM;QAE9B,EAAE,EAAE,OAAO,CAAC,GAAG,CAAC,mBAAmB,EAAE,CAAC;iBAC/B,MAAM,GACT,OAAO,CAAC,MAAM,KAAK,KAAK,QACf,aAAa,GAClB,OAAO,CAAC,MAAM,SAAS,MAAM;YAEnC,EAAE,SAAS,OAAO,CAAC,MAAM,MAAK,SAAW,GAAE,CAAC;gBAC1C,OAAO,CAAC,MAAM,QAAQ,MAAM;YAC9B,CAAC;YAED,KAAK,CAAC,QAAQ,OAnzBa,iBAA4B,mBAmzBrB,WAAW,CAAC,EAAE,IAAI,WAAW,CAAC,EAAE,IAAI,EAAE;YACxE,KAAK,CAAC,gBAAgB,OAl0BQ,oBAA+B,sBAm0B3D,QAAQ,CAAC,QAAQ,OACZ,OAAO;YAGd,EAAE,EAAE,gBAAgB,CAAC,cAAc,EAAE,CAAC;qBAC/B,MAAM,GAAG,gBAAgB,CAAC,cAAc;gBAC7C,QAAQ,CAAC,QAAQ,GAAG,WAAW,CAAC,QAAQ,CAAC,QAAQ;gBACjD,EAAE,OA9zBH,MAAU,uBA8zBiB,QAAQ;gBAClC,GAAG,GAAG,WAAW,KA30BW,oBAA+B,sBA60BvD,WAAW,CAAC,GAAG,IAAI,WAAW,CAAC,GAAG,IAAI,GAAG,OACpC,OAAO,EACZ,QAAQ;YAEd,CAAC;YACD,GAAG,CAAC,WAAW,GAAG,KAAK;YAEvB,EAAwE,AAAxE,sEAAwE;YACxE,EAA0C,AAA1C,wCAA0C;YAC1C,EAAE,EAAE,OAAO,CAAC,GAAG,CAAC,mBAAmB,EAAE,CAAC;oBAE/B,GAAY;gBADjB,EAAgE,AAAhE,8DAAgE;gBAChE,EAAE,KAAG,GAAY,QAAP,OAAO,cAAZ,GAAY,UAAZ,CAAsB,QAAtB,CAAsB,GAAtB,GAAY,CAAE,QAAQ,MAAM,MAAM,IAAI,CAAC;oBAC1C,QAAQ,CAAC,QAAQ,GAAG,SAAS,CAAC,QAAQ,CAAC,QAAQ,OAAO,MAAM;oBAC5D,MAAM,CAAC,QAAQ,CAAC,IAAI,OA90BvB,MAAU,uBA80BqC,QAAQ;oBACpD,EAAwD,AAAxD,sDAAwD;oBACxD,EAA2D,AAA3D,yDAA2D;oBAC3D,WAAW,GAAG,IAAI;gBACpB,CAAC;YACH,CAAC;YAED,KAAK,CAAC,cAAc,GAAG,kBAAkB,MAClC,aAAa,EAClB,SAAS,OACJ,MAAM;YAGb,EAAwE,AAAxE,sEAAwE;YACxE,EAA0C,AAA1C,wCAA0C;YAC1C,EAAE,EAAE,OAAO,CAAC,GAAG,CAAC,mBAAmB,EAAE,CAAC;gBACpC,EAAoE,AAApE,kEAAoE;gBACpE,EAAiB,AAAjB,eAAiB;gBACjB,EAAE,GACC,WAAW,IACZ,cAAc,SACT,cAAc,IACnB,IAAI,CAAC,QAAQ,CAAC,QAAQ,KAAK,cAAc,CAAC,MAAM,EAChD,CAAC;oBACD,KAAK,CAAC,YAAY,GAAG,WAAW,CAAC,EAAE;oBACnC,MAAM,CAAC,QAAQ,CAAC,IAAI,IAAI,IAAI,EAAE,cAAc,CAAC,IAAI,SAAQ,CAAG,EAAC,GAAG,EAC9D,cAAc,CAAC,MAAM,GACpB,WAAW,SAEL,MAAM,KAAK,cAAc,CAAC,aAAa,SAEvC,CAAC,OAAO,MAAM,KAClB,YAAY,MAAK,CAAG,SAAQ,YAAY,OAAM,CAAG;oBAEtD,EAAwD,AAAxD,sDAAwD;oBACxD,EAA2D,AAA3D,yDAA2D;oBAC3D,WAAW,GAAG,IAAI;gBACpB,CAAC;YACH,CAAC;YAED,EAAE,EAAE,WAAW,EAAE,CAAC;uBACT,GAAG,CAAC,OAAO,KAAO,CAAC;gBAAA,CAAC;YAC7B,CAAC;QACH,CAAC;QAED,EAAE,GAAI,OAAO,CAAS,EAAE,EAAE,CAAC;iBACpB,KAAK,GAAG,KAAK;QACpB,CAAC;QACD,EAAoD,AAApD,kDAAoD;QACpD,EAAE,EA/3BC,MAAU,KA+3BL,CAAC;YACP,WAAW,CAAC,IAAI,EAAC,WAAa;QAChC,CAAC;QAED,KAAK,GAAG,OAAO,EAAG,KAAK,MAAK,OAAO;QACnC,KAAK,CAAC,UAAU;YAAK,OAAO;;QAE5B,EAAE,OAAO,cAAc,EAAE,CAAC;iBACnB,kBAAkB,MAAM,cAAc,EAAE,UAAU;QACzD,CAAC;QAED,EAAE,GAAG,WAAW,CACd,SAAS,CACP,WAAW,CAAC,EAAE,IAAI,WAAW,CAAC,EAAE,IAAI,EAAE,EACtC,OAAO,CAAC,MAAM,OACT,aAAa;QAGtB,KAAK,CAAC,SAAS,GAAG,SAAS,CACzB,WAAW,CAAC,EAAE,IAAI,WAAW,CAAC,EAAE,IAAI,EAAE,OACjC,MAAM;aAER,cAAc,GAAG,EAAE;QAExB,GAAG,CAAC,YAAY,GAAG,UAAU,UAAU,MAAM;QAE7C,EAAqD,AAArD,mDAAqD;QACrD,EAA0D,AAA1D,wDAA0D;QAE1D,EAAsE,AAAtE,oEAAsE;QACtE,EAAyE,AAAzE,uEAAyE;QACzE,EAA2B,AAA3B,yBAA2B;QAC3B,EAAE,GACE,OAAO,CAAS,EAAE,SACf,eAAe,CAAC,SAAS,MAC7B,YAAY,EACb,CAAC;iBACI,MAAM,GAAG,SAAS;YACvB,MAAM,CAAC,MAAM,CAAC,IAAI,EAAC,eAAiB,GAAE,EAAE,EAAE,UAAU;YACpD,EAA8D,AAA9D,4DAA8D;iBACzD,WAAW,CAAC,MAAM,EAAE,GAAG,EAAE,EAAE,EAAE,OAAO;iBACpC,YAAY,CAAC,SAAS;iBACtB,MAAM,MAAM,UAAU,MAAM,KAAK,GAAG,IAAI;YAC7C,MAAM,CAAC,MAAM,CAAC,IAAI,EAAC,kBAAoB,GAAE,EAAE,EAAE,UAAU;mBAChD,IAAI;QACb,CAAC;QAED,GAAG,CAAC,MAAM,OA56BmB,iBAA4B,mBA46B3B,GAAG;QACjC,GAAG,GAAG,QAAQ,EAAR,SAAQ,GAAE,KAAK,EAAL,MAAK,MAAK,MAAM;QAEhC,EAAyE,AAAzE,uEAAyE;QACzE,EAA2E,AAA3E,yEAA2E;QAC3E,EAAoB,AAApB,kBAAoB;QACpB,GAAG,CAAC,KAAK,EAAO,QAAQ;YACpB,CAAC;YACH,KAAK,cAAc,UAAU,CAAC,WAAW;eACrC,UAAU,EAAE,QAAQ,gBAv8BvB,YAA8B;QAw8BjC,CAAC,QAAQ,IAAG,EAAE,CAAC;YACb,EAAwE,AAAxE,sEAAwE;YACxE,EAA+B,AAA/B,6BAA+B;YAC/B,MAAM,CAAC,QAAQ,CAAC,IAAI,GAAG,EAAE;mBAClB,KAAK;QACd,CAAC;QAED,EAAuE,AAAvE,qEAAuE;QACvE,EAA8E,AAA9E,4EAA8E;QAC9E,EAAuD,AAAvD,qDAAuD;QACvD,EAAoE,AAApE,kEAAoE;QACpE,EAAsE,AAAtE,oEAAsE;QACtE,EAAE,QAAQ,QAAQ,CAAC,SAAS,MAAM,YAAY,EAAE,CAAC;YAC/C,MAAM,IAAG,YAAc;QACzB,CAAC;QAED,EAAiE,AAAjE,+DAAiE;QACjE,EAAiD,AAAjD,+CAAiD;QACjD,GAAG,CAAC,UAAU,GAAG,EAAE;QAEnB,EAA6D,AAA7D,2DAA6D;QAC7D,EAAgE,AAAhE,8DAAgE;QAChE,EAA2D,AAA3D,yDAA2D;QAC3D,SAAQ,GAAG,SAAQ,OAr+BhB,uBAA0C,0BAs+BjB,WAAW,CAAC,SAAQ,KAC5C,SAAQ;QAEZ,EAAE,EAAE,iBAAiB,IAAI,SAAQ,MAAK,OAAS,GAAE,CAAC;YAC9C,OAAO,CAAS,kBAAkB,GAAG,IAAI;YAE3C,EAAE,EAAE,OAAO,CAAC,GAAG,CAAC,mBAAmB,IAAI,EAAE,CAAC,UAAU,EAAC,CAAG,IAAG,CAAC;gBAC1D,KAAK,CAAC,cAAc,OAn9BA,gBAA0B,UAo9B5C,WAAW,CAAC,SAAS,CAAC,SAAS,OAAO,MAAM,IAC5C,KAAK,EACL,QAAQ,EACR,MAAK,GACJ,CAAS,GAAK,mBAAmB,CAAC,CAAC,EAAE,KAAK;uBACtC,OAAO;gBAEd,UAAU,GAAG,cAAc,CAAC,MAAM;gBAElC,EAAE,EAAE,cAAc,CAAC,WAAW,IAAI,cAAc,CAAC,YAAY,EAAE,CAAC;oBAC9D,EAAgE,AAAhE,8DAAgE;oBAChE,EAA4C,AAA5C,0CAA4C;oBAC5C,SAAQ,GAAG,cAAc,CAAC,YAAY;oBACtC,MAAM,CAAC,QAAQ,GAAG,WAAW,CAAC,SAAQ;oBACtC,GAAG,OAt+BN,MAAU,uBAs+BoB,MAAM;gBACnC,CAAC;YACH,CAAC,MAAM,CAAC;gBACN,MAAM,CAAC,QAAQ,GAAG,mBAAmB,CAAC,SAAQ,EAAE,KAAK;gBAErD,EAAE,EAAE,MAAM,CAAC,QAAQ,KAAK,SAAQ,EAAE,CAAC;oBACjC,SAAQ,GAAG,MAAM,CAAC,QAAQ;oBAC1B,MAAM,CAAC,QAAQ,GAAG,WAAW,CAAC,SAAQ;oBACtC,GAAG,OA9+BN,MAAU,uBA8+BoB,MAAM;gBACnC,CAAC;YACH,CAAC;QACH,CAAC;QAED,KAAK,CAAC,KAAK,OAzgCR,uBAA0C,0BAygCP,SAAQ;QAE9C,EAAE,GAAG,UAAU,CAAC,EAAE,GAAG,CAAC;YACpB,EAAE,EAAE,OAAO,CAAC,GAAG,CAAC,QAAQ,MAAK,UAAY,GAAE,CAAC;gBAC1C,KAAK,CAAC,GAAG,CAAC,KAAK,EACZ,eAAe,EAAE,GAAG,CAAC,WAAW,EAAE,EAAE,CAAC,yCAAyC,KAC5E,kFAAkF;YAEzF,CAAC;YAED,MAAM,CAAC,QAAQ,CAAC,IAAI,GAAG,EAAE;mBAClB,KAAK;QACd,CAAC;QAED,UAAU,GAAG,SAAS,CAAC,WAAW,CAAC,UAAU,QAAQ,MAAM;QAE3D,EAAE,MAlgCyB,UAAoB,iBAkgC5B,KAAK,GAAG,CAAC;YAC1B,KAAK,CAAC,QAAQ,OAlgCa,iBAA4B,mBAkgCrB,UAAU;YAC5C,KAAK,CAAC,UAAU,GAAG,QAAQ,CAAC,QAAQ;YAEpC,KAAK,CAAC,UAAU,OAjgCQ,WAAqB,gBAigCZ,KAAK;YACtC,KAAK,CAAC,UAAU,OAngCU,aAAuB,kBAmgCd,UAAU,EAAE,UAAU;YACzD,KAAK,CAAC,iBAAiB,GAAG,KAAK,KAAK,UAAU;YAC9C,KAAK,CAAC,cAAc,GAAG,iBAAiB,GACpC,aAAa,CAAC,KAAK,EAAE,UAAU,EAAE,MAAK;;YAG1C,EAAE,GAAG,UAAU,IAAK,iBAAiB,KAAK,cAAc,CAAC,MAAM,EAAG,CAAC;gBACjE,KAAK,CAAC,aAAa,GAAG,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,MAAM,EACxD,KAAK,IAAM,MAAK,CAAC,KAAK;;gBAGzB,EAAE,EAAE,aAAa,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;oBAC7B,EAAE,EAAE,OAAO,CAAC,GAAG,CAAC,QAAQ,MAAK,UAAY,GAAE,CAAC;wBAC1C,OAAO,CAAC,IAAI,IAER,iBAAiB,IACZ,kBAAkB,KAClB,+BAA+B,EACrC,4BAA4B,KAC1B,YAAY,EAAE,aAAa,CAAC,IAAI,EAC/B,EAAI,GACJ,4BAA4B;oBAEpC,CAAC;oBAED,KAAK,CAAC,GAAG,CAAC,KAAK,EACZ,iBAAiB,IACb,uBAAuB,EAAE,GAAG,CAAC,iCAAiC,EAAE,aAAa,CAAC,IAAI,EACjF,EAAI,GACJ,+BAA+B,KAChC,2BAA2B,EAAE,UAAU,CAAC,2CAA2C,EAAE,KAAK,CAAC,GAAG,MAChG,4CAA4C,EAC3C,iBAAiB,IACb,yBAA2B,KAC3B,oBAAsB;gBAGlC,CAAC;YACH,CAAC,MAAM,EAAE,EAAE,iBAAiB,EAAE,CAAC;gBAC7B,EAAE,OA/iCH,MAAU,uBAgjCP,MAAM,CAAC,MAAM;mBAAK,QAAQ;oBACxB,QAAQ,EAAE,cAAc,CAAC,MAAM;oBAC/B,KAAK,EAAE,kBAAkB,CAAC,MAAK,EAAE,cAAc,CAAC,MAAM;;YAG5D,CAAC,MAAM,CAAC;gBACN,EAAiE,AAAjE,+DAAiE;gBACjE,MAAM,CAAC,MAAM,CAAC,MAAK,EAAE,UAAU;YACjC,CAAC;QACH,CAAC;QAED,MAAM,CAAC,MAAM,CAAC,IAAI,EAAC,gBAAkB,GAAE,EAAE,EAAE,UAAU;YAEjD,CAAC;gBA2ED,GAAwB;YA1E1B,GAAG,CAAC,SAAS,cAAc,YAAY,CACrC,KAAK,EACL,SAAQ,EACR,MAAK,EACL,EAAE,EACF,UAAU,EACV,UAAU;YAEZ,GAAG,GAAG,KAAK,GAAE,KAAK,GAAE,OAAO,GAAE,OAAO,MAAK,SAAS;YAElD,EAAuC,AAAvC,qCAAuC;YACvC,EAAE,GAAG,OAAO,IAAI,OAAO,KAAK,KAAK,EAAE,CAAC;gBAClC,EAAE,EAAG,KAAK,CAAS,SAAS,IAAK,KAAK,CAAS,SAAS,CAAC,YAAY,EAAE,CAAC;oBACtE,KAAK,CAAC,WAAW,GAAI,KAAK,CAAS,SAAS,CAAC,YAAY;oBAEzD,EAAoE,AAApE,kEAAoE;oBACpE,EAAgE,AAAhE,8DAAgE;oBAChE,EAAW,AAAX,SAAW;oBACX,EAAE,EAAE,WAAW,CAAC,UAAU,EAAC,CAAG,IAAG,CAAC;wBAChC,KAAK,CAAC,UAAU,OA/kCK,iBAA4B,mBA+kCb,WAAW;wBAC/C,UAAU,CAAC,QAAQ,GAAG,mBAAmB,CACvC,UAAU,CAAC,QAAQ,EACnB,KAAK;wBAGP,KAAK,GAAG,GAAG,EAAE,MAAM,GAAE,EAAE,EAAE,KAAK,MAAK,YAAY,OAE7C,WAAW,EACX,WAAW;oCAED,MAAM,CAAC,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,OAAO;oBACnD,CAAC;oBAED,MAAM,CAAC,QAAQ,CAAC,IAAI,GAAG,WAAW;2BAC3B,GAAG,CAAC,OAAO,KAAO,CAAC;oBAAA,CAAC;gBAC7B,CAAC;qBAEI,SAAS,KAAK,KAAK,CAAC,WAAW;gBAEpC,EAAsB,AAAtB,oBAAsB;gBACtB,EAAE,EAAE,KAAK,CAAC,QAAQ,KAAK,kBAAkB,EAAE,CAAC;oBAC1C,GAAG,CAAC,aAAa;wBAEb,CAAC;mCACQ,cAAc,EAAC,IAAM;wBAChC,aAAa,IAAG,IAAM;oBACxB,CAAC,QAAQ,CAAC,EAAE,CAAC;wBACX,aAAa,IAAG,OAAS;oBAC3B,CAAC;oBAED,SAAS,cAAc,YAAY,CACjC,aAAa,EACb,aAAa,EACb,MAAK,EACL,EAAE,EACF,UAAU;wBACR,OAAO,EAAE,KAAK;;gBAEpB,CAAC;YACH,CAAC;YAED,MAAM,CAAC,MAAM,CAAC,IAAI,EAAC,mBAAqB,GAAE,EAAE,EAAE,UAAU;iBACnD,WAAW,CAAC,MAAM,EAAE,GAAG,EAAE,EAAE,EAAE,OAAO;YAEzC,EAAE,EAAE,OAAO,CAAC,GAAG,CAAC,QAAQ,MAAK,UAAY,GAAE,CAAC;gBAC1C,KAAK,CAAC,OAAO,QAAa,UAAU,EAAC,KAAO,GAAE,SAAS;gBACrD,MAAM,CAAS,IAAI,CAAC,aAAa,GACjC,OAAO,CAAC,eAAe,KAAK,OAAO,CAAC,mBAAmB,KACrD,SAAS,CAAC,SAAS,CAAS,eAAe;YACjD,CAAC;YAED,EAAE,EACC,OAAO,CAAS,EAAE,IACnB,SAAQ,MAAK,OAAS,OACtB,GAAwB,GAAxB,IAAI,CAAC,aAAa,CAAC,KAAK,cAAxB,GAAwB,UAAxB,CAAmC,QAAnC,CAAmC,WAAnC,GAAwB,CAAE,SAAS,4BAAnC,CAAmC,QAAnC,CAAmC,QAAE,UAAU,MAAK,GAAG,KACvD,KAAK,aAAL,KAAK,UAAL,CAAgB,QAAhB,CAAgB,GAAhB,KAAK,CAAE,SAAS,GAChB,CAAC;gBACD,EAAyD,AAAzD,uDAAyD;gBACzD,EAAkC,AAAlC,gCAAkC;gBAClC,KAAK,CAAC,SAAS,CAAC,UAAU,GAAG,GAAG;YAClC,CAAC;YAED,EAA6D,AAA7D,2DAA6D;YAC7D,KAAK,CAAC,mBAAmB,GAAG,OAAO,CAAC,OAAO,SAAS,KAAK,KAAK,KAAK;gBAE9C,OAAc;YAAnC,KAAK,CAAC,YAAY,IAAG,OAAc,GAAd,OAAO,CAAC,MAAM,cAAd,OAAc,cAAd,OAAc,IAAK,mBAAmB;YAC3D,KAAK,CAAC,WAAW,GAAG,YAAY;gBAAK,CAAC,EAAE,CAAC;gBAAE,CAAC,EAAE,CAAC;gBAAK,IAAI;uBAC7C,GAAG,CACZ,KAAK,EACL,SAAQ,EACR,MAAK,EACL,SAAS,EACT,SAAS,EACT,YAAY,aAAZ,YAAY,cAAZ,YAAY,GAAI,WAAW,EAC3B,KAAK,EAAE,CAAC,GAAK,CAAC;gBACd,EAAE,EAAE,CAAC,CAAC,SAAS,EAAE,KAAK,GAAG,KAAK,IAAI,CAAC;qBAC9B,KAAK,CAAC,CAAC;YACd,CAAC;YAED,EAAE,EAAE,KAAK,EAAE,CAAC;gBACV,MAAM,CAAC,MAAM,CAAC,IAAI,EAAC,gBAAkB,GAAE,KAAK,EAAE,SAAS,EAAE,UAAU;gBACnE,KAAK,CAAC,KAAK;YACb,CAAC;YAED,EAAE,EAAE,OAAO,CAAC,GAAG,CAAC,mBAAmB,EAAE,CAAC;gBACpC,EAAE,OAAO,MAAM,EAAE,CAAC;oBAChB,QAAQ,CAAC,eAAe,CAAC,IAAI,QAAQ,MAAM;gBAC7C,CAAC;YACH,CAAC;YACD,MAAM,CAAC,MAAM,CAAC,IAAI,EAAC,mBAAqB,GAAE,EAAE,EAAE,UAAU;mBAEjD,IAAI;QACb,CAAC,QAAQ,IAAG,EAAE,CAAC;YACb,EAAE,EAAE,IAAG,CAAC,SAAS,EAAE,CAAC;uBACX,KAAK;YACd,CAAC;YACD,KAAK,CAAC,IAAG;QACX,CAAC;IACH,CAAC;IAED,WAAW,CACT,MAAqB,EACrB,GAAW,EACX,EAAU,EACV,OAA0B;OACpB,CAAC;QACP,EAAE,EAAE,OAAO,CAAC,GAAG,CAAC,QAAQ,MAAK,UAAY,GAAE,CAAC;YAC1C,EAAE,SAAS,MAAM,CAAC,OAAO,MAAK,SAAW,GAAE,CAAC;gBAC1C,OAAO,CAAC,KAAK,EAAE,yCAAyC;;YAE1D,CAAC;YAED,EAAE,SAAS,MAAM,CAAC,OAAO,CAAC,MAAM,OAAM,SAAW,GAAE,CAAC;gBAClD,OAAO,CAAC,KAAK,EAAE,wBAAwB,EAAE,MAAM,CAAC,iBAAiB;;YAEnE,CAAC;QACH,CAAC;QAED,EAAE,EAAE,MAAM,MAAK,SAAW,SAxsCvB,MAAU,eAwsC8B,EAAE,EAAE,CAAC;iBACzC,QAAQ,GAAG,OAAO,CAAC,OAAO;YAC/B,MAAM,CAAC,OAAO,CAAC,MAAM;gBAEjB,GAAG;gBACH,EAAE;gBACF,OAAO;gBACP,GAAG,EAAE,IAAI;gBACT,GAAG,OAAQ,IAAI,GAAG,MAAM,MAAK,SAAW,SAAQ,IAAI,QAAQ,IAAI,GAAG,CAAC;eAEtE,EAA0F,AAA1F,wFAA0F;YAC1F,EAAqF,AAArF,mFAAqF;YACrF,EAAwE,AAAxE,sEAAwE;gBAExE,EAAE;QAEN,CAAC;IACH,CAAC;UAEK,oBAAoB,CACxB,GAA8C,EAC9C,QAAgB,EAChB,KAAqB,EACrB,EAAU,EACV,UAA2B,EAC3B,aAAuB,EACY,CAAC;QACpC,EAAE,EAAE,GAAG,CAAC,SAAS,EAAE,CAAC;YAClB,EAAgC,AAAhC,8BAAgC;YAChC,KAAK,CAAC,GAAG;QACX,CAAC;QAED,EAAE,MAxvCC,YAA8B,eAwvChB,GAAG,KAAK,aAAa,EAAE,CAAC;YACvC,MAAM,CAAC,MAAM,CAAC,IAAI,EAAC,gBAAkB,GAAE,GAAG,EAAE,EAAE,EAAE,UAAU;YAE1D,EAAiE,AAAjE,+DAAiE;YACjE,EAA0B,AAA1B,wBAA0B;YAC1B,EAA0C,AAA1C,wCAA0C;YAC1C,EAA4C,AAA5C,0CAA4C;YAE5C,EAA+D,AAA/D,6DAA+D;YAC/D,MAAM,CAAC,QAAQ,CAAC,IAAI,GAAG,EAAE;YAEzB,EAAkE,AAAlE,gEAAkE;YAClE,EAA8D,AAA9D,4DAA8D;YAC9D,KAAK,CAAC,sBAAsB;QAC9B,CAAC;YAEG,CAAC;YACH,GAAG,CAAC,UAAS;YACb,GAAG,CAAC,WAAW;YACf,GAAG,CAAC,KAAK;YAET,EAAE,SACO,UAAS,MAAM,SAAW,YAC1B,WAAW,MAAM,SAAW,GACnC,CAAC;mBACG,IAAI,EAAE,UAAS,GAAE,WAAW,iBAAgB,cAAc,EAC5D,OAAS;YAEb,CAAC;YAED,KAAK,CAAC,SAAS;gBACb,KAAK;gBACL,SAAS,EAAT,UAAS;gBACT,WAAW;gBACX,GAAG;gBACH,KAAK,EAAE,GAAG;;YAGZ,EAAE,GAAG,SAAS,CAAC,KAAK,EAAE,CAAC;oBACjB,CAAC;oBACH,SAAS,CAAC,KAAK,cAAc,eAAe,CAAC,UAAS;wBACpD,GAAG;wBACH,QAAQ;wBACR,KAAK;;gBAET,CAAC,QAAQ,MAAM,EAAE,CAAC;oBAChB,OAAO,CAAC,KAAK,EAAC,uCAAyC,GAAE,MAAM;oBAC/D,SAAS,CAAC,KAAK;;gBACjB,CAAC;YACH,CAAC;mBAEM,SAAS;QAClB,CAAC,QAAQ,YAAY,EAAE,CAAC;wBACV,oBAAoB,CAC9B,YAAY,EACZ,QAAQ,EACR,KAAK,EACL,EAAE,EACF,UAAU,EACV,IAAI;QAER,CAAC;IACH,CAAC;UAEK,YAAY,CAChB,KAAa,EACb,QAAgB,EAChB,KAAU,EACV,EAAU,EACV,UAAkB,EAClB,UAA2B,EACA,CAAC;YACxB,CAAC;YACH,KAAK,CAAC,iBAAiB,QAChB,UAAU,CAAC,KAAK;YACvB,EAAE,EAAE,UAAU,CAAC,OAAO,IAAI,iBAAiB,SAAS,KAAK,KAAK,KAAK,EAAE,CAAC;uBAC7D,iBAAiB;YAC1B,CAAC;YAED,KAAK,CAAC,eAAe,GACnB,iBAAiB,KAAI,OAAS,KAAI,iBAAiB,GAC/C,SAAS,GACT,iBAAiB;YACvB,KAAK,CAAC,SAAS,GAA6B,eAAe,GACvD,eAAe,cACJ,cAAc,CAAC,KAAK,EAAE,IAAI,EAAE,GAAG;oBACxC,SAAS,EAAE,GAAG,CAAC,IAAI;oBACnB,WAAW,EAAE,GAAG,CAAC,WAAW;oBAC5B,OAAO,EAAE,GAAG,CAAC,GAAG,CAAC,OAAO;oBACxB,OAAO,EAAE,GAAG,CAAC,GAAG,CAAC,OAAO;;;YAG9B,KAAK,GAAG,SAAS,EAAT,UAAS,GAAE,OAAO,GAAE,OAAO,MAAK,SAAS;YAEjD,EAAE,EAAE,OAAO,CAAC,GAAG,CAAC,QAAQ,MAAK,UAAY,GAAE,CAAC;gBAC1C,KAAK,GAAG,kBAAkB,MAAK,OAAO,EAAC,QAAU;gBACjD,EAAE,GAAG,kBAAkB,CAAC,UAAS,GAAG,CAAC;oBACnC,KAAK,CAAC,GAAG,CAAC,KAAK,EACZ,sDAAsD,EAAE,QAAQ,CAAC,CAAC;gBAEvE,CAAC;YACH,CAAC;YAED,GAAG,CAAC,QAAQ;YAEZ,EAAE,EAAE,OAAO,IAAI,OAAO,EAAE,CAAC;gBACvB,QAAQ,QAAQ,UAAU,CAAC,WAAW,KAl1CvC,MAAU;oBAm1CgB,QAAQ;oBAAE,KAAK;oBACtC,UAAU,EACV,OAAO,OACF,MAAM;YAEf,CAAC;YAED,KAAK,CAAC,KAAK,cAAc,QAAQ,KAC/B,OAAO,QACE,cAAc,CAAC,QAAQ,IAC5B,OAAO,QACF,cAAc,CAAC,QAAQ,SACvB,eAAe,CAClB,UAAS,EACT,EAAqD,AAArD,mDAAqD;;oBAEnD,QAAQ;oBACR,KAAK;oBACL,MAAM,EAAE,EAAE;oBACV,MAAM,OAAO,MAAM;oBACnB,OAAO,OAAO,OAAO;oBACrB,aAAa,OAAO,aAAa;;;YAK3C,SAAS,CAAC,KAAK,GAAG,KAAK;iBAClB,UAAU,CAAC,KAAK,IAAI,SAAS;mBAC3B,SAAS;QAClB,CAAC,QAAQ,IAAG,EAAE,CAAC;wBACD,oBAAoB,CAAC,IAAG,EAAE,QAAQ,EAAE,KAAK,EAAE,EAAE,EAAE,UAAU;QACvE,CAAC;IACH,CAAC;IAED,GAAG,CACD,KAAa,EACb,QAAgB,EAChB,KAAqB,EACrB,EAAU,EACV,IAAsB,EACtB,WAA4C,EAC7B,CAAC;aACX,UAAU,GAAG,KAAK;aAElB,KAAK,GAAG,KAAK;aACb,QAAQ,GAAG,QAAQ;aACnB,KAAK,GAAG,KAAK;aACb,MAAM,GAAG,EAAE;oBACJ,MAAM,CAAC,IAAI,EAAE,WAAW;IACtC,CAAC;IAED,EAGG,AAHH;;;GAGG,AAHH,EAGG,CACH,cAAc,CAAC,EAA0B,EAAE,CAAC;aACrC,IAAI,GAAG,EAAE;IAChB,CAAC;IAED,eAAe,CAAC,EAAU,EAAW,CAAC;QACpC,EAAE,QAAQ,MAAM,SAAS,KAAK;QAC9B,KAAK,EAAE,YAAY,EAAE,OAAO,SAAS,MAAM,CAAC,KAAK,EAAC,CAAG;QACrD,KAAK,EAAE,YAAY,EAAE,OAAO,IAAI,EAAE,CAAC,KAAK,EAAC,CAAG;QAE5C,EAAyE,AAAzE,uEAAyE;QACzE,EAAE,EAAE,OAAO,IAAI,YAAY,KAAK,YAAY,IAAI,OAAO,KAAK,OAAO,EAAE,CAAC;mBAC7D,IAAI;QACb,CAAC;QAED,EAA0D,AAA1D,wDAA0D;QAC1D,EAAE,EAAE,YAAY,KAAK,YAAY,EAAE,CAAC;mBAC3B,KAAK;QACd,CAAC;QAED,EAAyD,AAAzD,uDAAyD;QACzD,EAAuD,AAAvD,qDAAuD;QACvD,EAA2D,AAA3D,yDAA2D;QAC3D,EAAmC,AAAnC,iCAAmC;eAC5B,OAAO,KAAK,OAAO;IAC5B,CAAC;IAED,YAAY,CAAC,EAAU,EAAQ,CAAC;QAC9B,KAAK,IAAI,IAAI,IAAI,EAAE,CAAC,KAAK,EAAC,CAAG;QAC7B,EAAgE,AAAhE,8DAAgE;QAChE,EAAqB,AAArB,mBAAqB;QACrB,EAAE,EAAE,IAAI,WAAW,IAAI,MAAK,GAAK,GAAE,CAAC;YAClC,MAAM,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC;;QAEtB,CAAC;QAED,EAA+C,AAA/C,6CAA+C;QAC/C,KAAK,CAAC,IAAI,GAAG,QAAQ,CAAC,cAAc,CAAC,IAAI;QACzC,EAAE,EAAE,IAAI,EAAE,CAAC;YACT,IAAI,CAAC,cAAc;;QAErB,CAAC;QACD,EAAkE,AAAlE,gEAAkE;QAClE,EAAqB,AAArB,mBAAqB;QACrB,KAAK,CAAC,MAAM,GAAG,QAAQ,CAAC,iBAAiB,CAAC,IAAI,EAAE,CAAC;QACjD,EAAE,EAAE,MAAM,EAAE,CAAC;YACX,MAAM,CAAC,cAAc;QACvB,CAAC;IACH,CAAC;IAED,QAAQ,CAAC,MAAc,EAAW,CAAC;oBACrB,MAAM,KAAK,MAAM;IAC/B,CAAC;IAED,EAKG,AALH;;;;;GAKG,AALH,EAKG,OACG,QAAQ,CACZ,GAAW,EACX,MAAc,GAAG,GAAG,EACpB,OAAwB;OACT,CAAC;QAChB,GAAG,CAAC,MAAM,OAx8CmB,iBAA4B,mBAw8C3B,GAAG;QAEjC,GAAG,GAAG,QAAQ,EAAR,SAAQ,MAAK,MAAM;QAEzB,EAAE,EAAE,OAAO,CAAC,GAAG,CAAC,mBAAmB,EAAE,CAAC;YACpC,EAAE,EAAE,OAAO,CAAC,MAAM,KAAK,KAAK,EAAE,CAAC;gBAC7B,SAAQ,OA59CoB,oBAA+B,sBA49C3B,SAAQ,OAAO,OAAO,EAAE,QAAQ;gBAChE,MAAM,CAAC,QAAQ,GAAG,SAAQ;gBAC1B,GAAG,OAl9CJ,MAAU,uBAk9CkB,MAAM;gBAEjC,GAAG,CAAC,QAAQ,OAl9Ca,iBAA4B,mBAk9CrB,MAAM;gBACtC,KAAK,CAAC,gBAAgB,OAj+CM,oBAA+B,sBAk+CzD,QAAQ,CAAC,QAAQ,OACZ,OAAO;gBAEd,QAAQ,CAAC,QAAQ,GAAG,gBAAgB,CAAC,QAAQ;gBAC7C,OAAO,CAAC,MAAM,GAAG,gBAAgB,CAAC,cAAc,SAAS,aAAa;gBACtE,MAAM,OA39CP,MAAU,uBA29CqB,QAAQ;YACxC,CAAC;QACH,CAAC;QAED,KAAK,CAAC,KAAK,cAAc,UAAU,CAAC,WAAW;QAC/C,GAAG,CAAC,UAAU,GAAG,MAAM;QAEvB,EAAE,EAAE,OAAO,CAAC,GAAG,CAAC,mBAAmB,IAAI,MAAM,CAAC,UAAU,EAAC,CAAG,IAAG,CAAC;YAC9D,GAAG,CAAC,QAAQ;eACR,UAAU,EAAE,QAAQ,gBAp/CvB,YAA8B;YAs/C/B,KAAK,CAAC,cAAc,OAl+CE,gBAA0B,UAm+C9C,WAAW,CAAC,SAAS,CAAC,MAAM,OAAO,MAAM,IACzC,KAAK,EACL,QAAQ,EACR,MAAM,CAAC,KAAK,GACX,CAAS,GAAK,mBAAmB,CAAC,CAAC,EAAE,KAAK;mBACtC,OAAO;YAEd,UAAU,GAAG,SAAS,CAAC,WAAW,CAAC,cAAc,CAAC,MAAM,QAAQ,MAAM;YAEtE,EAAE,EAAE,cAAc,CAAC,WAAW,IAAI,cAAc,CAAC,YAAY,EAAE,CAAC;gBAC9D,EAAgE,AAAhE,8DAAgE;gBAChE,EAA4C,AAA5C,0CAA4C;gBAC5C,SAAQ,GAAG,cAAc,CAAC,YAAY;gBACtC,MAAM,CAAC,QAAQ,GAAG,SAAQ;gBAC1B,GAAG,OAr/CJ,MAAU,uBAq/CkB,MAAM;YACnC,CAAC;QACH,CAAC,MAAM,CAAC;YACN,MAAM,CAAC,QAAQ,GAAG,mBAAmB,CAAC,MAAM,CAAC,QAAQ,EAAE,KAAK;YAE5D,EAAE,EAAE,MAAM,CAAC,QAAQ,KAAK,SAAQ,EAAE,CAAC;gBACjC,SAAQ,GAAG,MAAM,CAAC,QAAQ;gBAC1B,MAAM,CAAC,QAAQ,GAAG,SAAQ;gBAC1B,GAAG,OA7/CJ,MAAU,uBA6/CkB,MAAM;YACnC,CAAC;QACH,CAAC;QACD,KAAK,CAAC,KAAK,OAthDR,uBAA0C,0BAshDP,SAAQ;QAE9C,EAA2F,AAA3F,yFAA2F;QAC3F,EAAE,EAAE,OAAO,CAAC,GAAG,CAAC,QAAQ,MAAK,UAAY,GAAE,CAAC;;QAE5C,CAAC;cAEK,OAAO,CAAC,GAAG;iBACV,UAAU,CAAC,MAAM,CAAC,KAAK,EAAE,IAAI,EAAE,KAAc,GAAK,CAAC;uBAC/C,KAAK,QACH,cAAc,MACZ,UAAU,CAAC,WAAW,CACzB,GAAG,EACH,UAAU,EACV,IAAI,SACG,OAAO,CAAC,MAAM,MAAK,SAAW,IACjC,OAAO,CAAC,MAAM,QACT,MAAM,KAGnB,KAAK;YACX,CAAC;iBACI,UAAU,CAAC,OAAO,CAAC,QAAQ,IAAG,QAAU,KAAG,QAAU,GAAE,KAAK;;IAErE,CAAC;UAEK,cAAc,CAAC,KAAa,EAA0B,CAAC;QAC3D,GAAG,CAAC,SAAS,GAAG,KAAK;QACrB,KAAK,CAAC,MAAM,QAAS,GAAG,OAAS,CAAC;YAChC,SAAS,GAAG,IAAI;QAClB,CAAC;QAED,KAAK,CAAC,eAAe,cAAc,UAAU,CAAC,QAAQ,CAAC,KAAK;QAE5D,EAAE,EAAE,SAAS,EAAE,CAAC;YACd,KAAK,CAAC,KAAK,GAAQ,GAAG,CAAC,KAAK,EACzB,qCAAqC,EAAE,KAAK,CAAC,CAAC;YAEjD,KAAK,CAAC,SAAS,GAAG,IAAI;YACtB,KAAK,CAAC,KAAK;QACb,CAAC;QAED,EAAE,EAAE,MAAM,UAAU,GAAG,EAAE,CAAC;iBACnB,GAAG,GAAG,IAAI;QACjB,CAAC;eAEM,eAAe;IACxB,CAAC;IAED,QAAQ,CAAI,EAAoB,EAAc,CAAC;QAC7C,GAAG,CAAC,SAAS,GAAG,KAAK;QACrB,KAAK,CAAC,MAAM,OAAS,CAAC;YACpB,SAAS,GAAG,IAAI;QAClB,CAAC;aACI,GAAG,GAAG,MAAM;eACV,EAAE,GAAG,IAAI,EAAE,IAAI,GAAK,CAAC;YAC1B,EAAE,EAAE,MAAM,UAAU,GAAG,EAAE,CAAC;qBACnB,GAAG,GAAG,IAAI;YACjB,CAAC;YAED,EAAE,EAAE,SAAS,EAAE,CAAC;gBACd,KAAK,CAAC,IAAG,GAAQ,GAAG,CAAC,KAAK,EAAC,+BAAiC;gBAC5D,IAAG,CAAC,SAAS,GAAG,IAAI;gBACpB,KAAK,CAAC,IAAG;YACX,CAAC;mBAEM,IAAI;QACb,CAAC;IACH,CAAC;IAED,cAAc,CAAC,QAAgB,EAAmB,CAAC;QACjD,KAAK,GAAG,IAAI,EAAE,QAAQ,MAAK,GAAG,CAAC,GAAG,CAAC,QAAQ,EAAE,MAAM,CAAC,QAAQ,CAAC,IAAI;QACjE,EAAE,EACA,OAAO,CAAC,GAAG,CAAC,QAAQ,MAAK,UAAY,WAC/B,SAAS,SACV,GAAG,CAAC,QAAQ,GACjB,CAAC;mBACM,OAAO,CAAC,OAAO,MAAM,GAAG,CAAC,QAAQ;QAC1C,CAAC;eACM,aAAa,CAAC,QAAQ,OAAO,KAAK,EAAE,IAAI,EAAE,IAAI,GAAK,CAAC;iBACpD,GAAG,CAAC,QAAQ,IAAI,IAAI;mBAClB,IAAI;QACb,CAAC;IACH,CAAC;IAED,cAAc,CAAC,QAAgB,EAAmB,CAAC;QACjD,KAAK,GAAG,IAAI,EAAE,WAAW,MAAK,GAAG,CAAC,GAAG,CAAC,QAAQ,EAAE,MAAM,CAAC,QAAQ,CAAC,IAAI;QACpE,EAAE,OAAO,GAAG,CAAC,WAAW,GAAG,CAAC;wBACd,GAAG,CAAC,WAAW;QAC7B,CAAC;oBACY,GAAG,CAAC,WAAW,IAAI,aAAa,CAAC,QAAQ,OAAO,KAAK,EAC/D,IAAI,EAAE,IAAI,GAAK,CAAC;wBACH,GAAG,CAAC,WAAW;mBACpB,IAAI;QACb,CAAC,EACA,KAAK,EAAE,IAAG,GAAK,CAAC;wBACH,GAAG,CAAC,WAAW;YAC3B,KAAK,CAAC,IAAG;QACX,CAAC;IACL,CAAC;IAED,eAAe,CACb,SAAwB,EACxB,GAAoB,EACN,CAAC;QACf,KAAK,GAAG,SAAS,EAAE,IAAG,WAAU,UAAU,EAAC,KAAO;QAClD,KAAK,CAAC,OAAO,QAAQ,QAAQ,CAAC,IAAG;QACjC,GAAG,CAAC,OAAO,GAAG,OAAO;mBA3mDlB,MAAU,sBA4mDsC,IAAG;YACpD,OAAO;YACP,SAAS;YACT,MAAM;YACN,GAAG;;IAEP,CAAC;IAED,kBAAkB,CAAC,EAAU,EAAE,UAA2B,EAAQ,CAAC;QACjE,EAAE,OAAO,GAAG,EAAE,CAAC;YACb,MAAM,CAAC,MAAM,CAAC,IAAI,EAChB,gBAAkB,GAClB,sBAAsB,IACtB,EAAE,EACF,UAAU;iBAEP,GAAG;iBACH,GAAG,GAAG,IAAI;QACjB,CAAC;IACH,CAAC;IAED,MAAM,CACJ,IAAsB,EACtB,WAA4C,EAC7B,CAAC;oBACJ,GAAG,CACb,IAAI,OACC,UAAU,EAAC,KAAO,GAAE,SAAS,EAClC,WAAW;IAEf,CAAC;;AA5pCkB,MAAM,CAoClB,MAAM,OA7hBmB,KAAS;kBAyftB,MAAM"}