{"version": 3, "sources": ["../../../../../build/webpack/plugins/wellknown-errors-plugin/parseScss.ts"], "sourcesContent": ["import { codeFrameColumns } from 'next/dist/compiled/babel/code-frame'\nimport Chalk from 'chalk'\nimport { SimpleWebpackError } from './simpleWebpackError'\n\nconst chalk = new Chalk.constructor({ enabled: true })\nconst regexScssError =\n  /SassError: (.+)\\n\\s+on line (\\d+) [\\s\\S]*?>> (.+)\\n\\s*(-+)\\^$/m\n\nexport function getScssError(\n  fileName: string,\n  fileContent: string | null,\n  err: Error\n): SimpleWebpackError | false {\n  if (err.name !== 'SassError') {\n    return false\n  }\n\n  const res = regexScssError.exec(err.message)\n  if (res) {\n    const [, reason, _lineNumer, backupFrame, columnString] = res\n    const lineNumber = Math.max(1, parseInt(_lineNumer, 10))\n    const column = columnString?.length ?? 1\n\n    let frame: string | undefined\n    if (fileContent) {\n      try {\n        frame = codeFrameColumns(\n          fileContent,\n          { start: { line: lineNumber, column } },\n          { forceColor: true }\n        ) as string\n      } catch {}\n    }\n\n    return new SimpleWebpackError(\n      `${chalk.cyan(fileName)}:${chalk.yellow(\n        lineNumber.toString()\n      )}:${chalk.yellow(column.toString())}`,\n      chalk.red\n        .bold('Syntax error')\n        .concat(`: ${reason}\\n\\n${frame ?? backupFrame}`)\n    )\n  }\n\n  return false\n}\n"], "names": [], "mappings": ";;;;QAQgB,YAAY,GAAZ,YAAY;AARK,GAAqC,CAArC,UAAqC;AACpD,GAAO,CAAP,MAAO;AACU,GAAsB,CAAtB,mBAAsB;;;;;;AAEzD,KAAK,CAAC,KAAK,GAAG,GAAG,CAHC,MAAO,SAGD,WAAW;IAAG,OAAO,EAAE,IAAI;;AACnD,KAAK,CAAC,cAAc;SAGJ,YAAY,CAC1B,QAAgB,EAChB,WAA0B,EAC1B,GAAU,EACkB,CAAC;IAC7B,EAAE,EAAE,GAAG,CAAC,IAAI,MAAK,SAAW,GAAE,CAAC;eACtB,KAAK;IACd,CAAC;IAED,KAAK,CAAC,GAAG,GAAG,cAAc,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO;IAC3C,EAAE,EAAE,GAAG,EAAE,CAAC;QACR,KAAK,IAAI,MAAM,EAAE,UAAU,EAAE,WAAW,EAAE,YAAY,IAAI,GAAG;QAC7D,KAAK,CAAC,UAAU,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,QAAQ,CAAC,UAAU,EAAE,EAAE;YACvC,GAAoB;QAAnC,KAAK,CAAC,MAAM,IAAG,GAAoB,GAApB,YAAY,aAAZ,YAAY,UAAZ,CAAoB,QAApB,CAAoB,GAApB,YAAY,CAAE,MAAM,cAApB,GAAoB,cAApB,GAAoB,GAAI,CAAC;QAExC,GAAG,CAAC,KAAK;QACT,EAAE,EAAE,WAAW,EAAE,CAAC;gBACZ,CAAC;gBACH,KAAK,OA1BoB,UAAqC,mBA2B5D,WAAW;oBACT,KAAK;wBAAI,IAAI,EAAE,UAAU;wBAAE,MAAM;;;oBACjC,UAAU,EAAE,IAAI;;YAEtB,CAAC,QAAO,CAAC;YAAA,CAAC;QACZ,CAAC;eAEM,GAAG,CAhCqB,mBAAsB,uBAiChD,KAAK,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC,EAAE,KAAK,CAAC,MAAM,CACrC,UAAU,CAAC,QAAQ,IACnB,CAAC,EAAE,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC,QAAQ,OACjC,KAAK,CAAC,GAAG,CACN,IAAI,EAAC,YAAc,GACnB,MAAM,EAAE,EAAE,EAAE,MAAM,CAAC,IAAI,EAAE,KAAK,aAAL,KAAK,cAAL,KAAK,GAAI,WAAW;IAEpD,CAAC;WAEM,KAAK;AACd,CAAC"}