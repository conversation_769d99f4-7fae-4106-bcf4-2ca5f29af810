{"version": 3, "sources": ["../../lib/is-yarn.ts"], "sourcesContent": ["import { join } from 'path'\nimport { fileExists } from './file-exists'\n\nexport const isYarn = async (dir: string) =>\n  await fileExists(join(dir, 'yarn.lock')).catch(() => false)\n"], "names": [], "mappings": ";;;;;AAAqB,GAAM,CAAN,KAAM;AACA,GAAe,CAAf,WAAe;AAEnC,KAAK,CAAC,MAAM,UAAU,GAAW,aAFb,WAAe,iBADrB,KAAM,OAIH,GAAG,GAAE,SAAW,IAAG,KAAK,KAAO,KAAK;;;QAD/C,MAAM,GAAN,MAAM"}