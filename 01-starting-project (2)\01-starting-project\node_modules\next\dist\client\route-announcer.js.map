{"version": 3, "sources": ["../../client/route-announcer.tsx"], "sourcesContent": ["import React from 'react'\nimport { useRouter } from './router'\n\nexport function RouteAnnouncer() {\n  const { asPath } = useRouter()\n  const [routeAnnouncement, setRouteAnnouncement] = React.useState('')\n\n  // Only announce the path change, but not for the first load because screen reader will do that automatically.\n  const initialPathLoaded = React.useRef(false)\n\n  // Every time the path changes, announce the route change. The announcement will be prioritized by h1, then title\n  // (from metadata), and finally if those don't exist, then the pathName that is in the URL. This methodology is\n  // inspired by <PERSON><PERSON>'s accessible client routing user testing. More information can be found here:\n  // https://www.gatsbyjs.com/blog/2019-07-11-user-testing-accessible-client-routing/\n  React.useEffect(\n    () => {\n      if (!initialPathLoaded.current) {\n        initialPathLoaded.current = true\n        return\n      }\n\n      let newRouteAnnouncement\n      const pageHeader = document.querySelector('h1')\n\n      if (pageHeader) {\n        newRouteAnnouncement = pageHeader.innerText || pageHeader.textContent\n      }\n      if (!newRouteAnnouncement) {\n        if (document.title) {\n          newRouteAnnouncement = document.title\n        } else {\n          newRouteAnnouncement = asPath\n        }\n      }\n\n      setRouteAnnouncement(newRouteAnnouncement)\n    },\n    // TODO: switch to pathname + query object of dynamic route requirements\n    [asPath]\n  )\n\n  return (\n    <p\n      aria-live=\"assertive\" // Make the announcement immediately.\n      id=\"__next-route-announcer__\"\n      role=\"alert\"\n      style={{\n        border: 0,\n        clip: 'rect(0 0 0 0)',\n        height: '1px',\n        margin: '-1px',\n        overflow: 'hidden',\n        padding: 0,\n        position: 'absolute',\n        width: '1px',\n\n        // https://medium.com/@jessebeach/beware-smushed-off-screen-accessible-text-5952a4c2cbfe\n        whiteSpace: 'nowrap',\n        wordWrap: 'normal',\n      }}\n    >\n      {routeAnnouncement}\n    </p>\n  )\n}\n\nexport default RouteAnnouncer\n"], "names": [], "mappings": ";;;;QAGgB,cAAc,GAAd,cAAc;;AAHZ,GAAO,CAAP,MAAO;AACC,GAAU,CAAV,OAAU;;;;;;SAEpB,cAAc,GAAG,CAAC;IAChC,KAAK,GAAG,MAAM,UAHU,OAAU;IAIlC,KAAK,EAAE,iBAAiB,EAAE,oBAAoB,IAL9B,MAAO,SAKiC,QAAQ;IAEhE,EAA8G,AAA9G,4GAA8G;IAC9G,KAAK,CAAC,iBAAiB,GARP,MAAO,SAQS,MAAM,CAAC,KAAK;IAE5C,EAAiH,AAAjH,+GAAiH;IACjH,EAA+G,AAA/G,6GAA+G;IAC/G,EAAyG,AAAzG,uGAAyG;IACzG,EAAmF,AAAnF,iFAAmF;IAbnE,MAAO,SAcjB,SAAS,KACP,CAAC;QACL,EAAE,GAAG,iBAAiB,CAAC,OAAO,EAAE,CAAC;YAC/B,iBAAiB,CAAC,OAAO,GAAG,IAAI;;QAElC,CAAC;QAED,GAAG,CAAC,oBAAoB;QACxB,KAAK,CAAC,UAAU,GAAG,QAAQ,CAAC,aAAa,EAAC,EAAI;QAE9C,EAAE,EAAE,UAAU,EAAE,CAAC;YACf,oBAAoB,GAAG,UAAU,CAAC,SAAS,IAAI,UAAU,CAAC,WAAW;QACvE,CAAC;QACD,EAAE,GAAG,oBAAoB,EAAE,CAAC;YAC1B,EAAE,EAAE,QAAQ,CAAC,KAAK,EAAE,CAAC;gBACnB,oBAAoB,GAAG,QAAQ,CAAC,KAAK;YACvC,CAAC,MAAM,CAAC;gBACN,oBAAoB,GAAG,MAAM;YAC/B,CAAC;QACH,CAAC;QAED,oBAAoB,CAAC,oBAAoB;IAC3C,CAAC,EACD,EAAwE,AAAxE,sEAAwE;;QACvE,MAAM;;yBAtCO,MAAO,wBA0CpB,CAAC;SACA,SAAS,IAAC,SAAW,CAAC,CAAqC,AAArC,EAAqC,AAArC,mCAAqC;;QAC3D,EAAE,GAAC,wBAA0B;QAC7B,IAAI,GAAC,KAAO;QACZ,KAAK;YACH,MAAM,EAAE,CAAC;YACT,IAAI,GAAE,aAAe;YACrB,MAAM,GAAE,GAAK;YACb,MAAM,GAAE,IAAM;YACd,QAAQ,GAAE,MAAQ;YAClB,OAAO,EAAE,CAAC;YACV,QAAQ,GAAE,QAAU;YACpB,KAAK,GAAE,GAAK;YAEZ,EAAwF,AAAxF,sFAAwF;YACxF,UAAU,GAAE,MAAQ;YACpB,QAAQ,GAAE,MAAQ;;OAGnB,iBAAiB;AAGxB,CAAC;eAEc,cAAc"}