{"version": 3, "sources": ["../../../../../shared/lib/router/utils/sorted-routes.ts"], "sourcesContent": ["class UrlNode {\n  placeholder: boolean = true\n  children: Map<string, UrlNode> = new Map()\n  slugName: string | null = null\n  restSlugName: string | null = null\n  optionalRestSlugName: string | null = null\n\n  insert(urlPath: string): void {\n    this._insert(urlPath.split('/').filter(Boolean), [], false)\n  }\n\n  smoosh(): string[] {\n    return this._smoosh()\n  }\n\n  private _smoosh(prefix: string = '/'): string[] {\n    const childrenPaths = [...this.children.keys()].sort()\n    if (this.slugName !== null) {\n      childrenPaths.splice(childrenPaths.indexOf('[]'), 1)\n    }\n    if (this.restSlugName !== null) {\n      childrenPaths.splice(childrenPaths.indexOf('[...]'), 1)\n    }\n    if (this.optionalRestSlugName !== null) {\n      childrenPaths.splice(childrenPaths.indexOf('[[...]]'), 1)\n    }\n\n    const routes = childrenPaths\n      .map((c) => this.children.get(c)!._smoosh(`${prefix}${c}/`))\n      .reduce((prev, curr) => [...prev, ...curr], [])\n\n    if (this.slugName !== null) {\n      routes.push(\n        ...this.children.get('[]')!._smoosh(`${prefix}[${this.slugName}]/`)\n      )\n    }\n\n    if (!this.placeholder) {\n      const r = prefix === '/' ? '/' : prefix.slice(0, -1)\n      if (this.optionalRestSlugName != null) {\n        throw new Error(\n          `You cannot define a route with the same specificity as a optional catch-all route (\"${r}\" and \"${r}[[...${this.optionalRestSlugName}]]\").`\n        )\n      }\n\n      routes.unshift(r)\n    }\n\n    if (this.restSlugName !== null) {\n      routes.push(\n        ...this.children\n          .get('[...]')!\n          ._smoosh(`${prefix}[...${this.restSlugName}]/`)\n      )\n    }\n\n    if (this.optionalRestSlugName !== null) {\n      routes.push(\n        ...this.children\n          .get('[[...]]')!\n          ._smoosh(`${prefix}[[...${this.optionalRestSlugName}]]/`)\n      )\n    }\n\n    return routes\n  }\n\n  private _insert(\n    urlPaths: string[],\n    slugNames: string[],\n    isCatchAll: boolean\n  ): void {\n    if (urlPaths.length === 0) {\n      this.placeholder = false\n      return\n    }\n\n    if (isCatchAll) {\n      throw new Error(`Catch-all must be the last part of the URL.`)\n    }\n\n    // The next segment in the urlPaths list\n    let nextSegment = urlPaths[0]\n\n    // Check if the segment matches `[something]`\n    if (nextSegment.startsWith('[') && nextSegment.endsWith(']')) {\n      // Strip `[` and `]`, leaving only `something`\n      let segmentName = nextSegment.slice(1, -1)\n\n      let isOptional = false\n      if (segmentName.startsWith('[') && segmentName.endsWith(']')) {\n        // Strip optional `[` and `]`, leaving only `something`\n        segmentName = segmentName.slice(1, -1)\n        isOptional = true\n      }\n\n      if (segmentName.startsWith('...')) {\n        // Strip `...`, leaving only `something`\n        segmentName = segmentName.substring(3)\n        isCatchAll = true\n      }\n\n      if (segmentName.startsWith('[') || segmentName.endsWith(']')) {\n        throw new Error(\n          `Segment names may not start or end with extra brackets ('${segmentName}').`\n        )\n      }\n\n      if (segmentName.startsWith('.')) {\n        throw new Error(\n          `Segment names may not start with erroneous periods ('${segmentName}').`\n        )\n      }\n\n      function handleSlug(previousSlug: string | null, nextSlug: string) {\n        if (previousSlug !== null) {\n          // If the specific segment already has a slug but the slug is not `something`\n          // This prevents collisions like:\n          // pages/[post]/index.js\n          // pages/[id]/index.js\n          // Because currently multiple dynamic params on the same segment level are not supported\n          if (previousSlug !== nextSlug) {\n            // TODO: This error seems to be confusing for users, needs an error link, the description can be based on above comment.\n            throw new Error(\n              `You cannot use different slug names for the same dynamic path ('${previousSlug}' !== '${nextSlug}').`\n            )\n          }\n        }\n\n        slugNames.forEach((slug) => {\n          if (slug === nextSlug) {\n            throw new Error(\n              `You cannot have the same slug name \"${nextSlug}\" repeat within a single dynamic path`\n            )\n          }\n\n          if (slug.replace(/\\W/g, '') === nextSegment.replace(/\\W/g, '')) {\n            throw new Error(\n              `You cannot have the slug names \"${slug}\" and \"${nextSlug}\" differ only by non-word symbols within a single dynamic path`\n            )\n          }\n        })\n\n        slugNames.push(nextSlug)\n      }\n\n      if (isCatchAll) {\n        if (isOptional) {\n          if (this.restSlugName != null) {\n            throw new Error(\n              `You cannot use both an required and optional catch-all route at the same level (\"[...${this.restSlugName}]\" and \"${urlPaths[0]}\" ).`\n            )\n          }\n\n          handleSlug(this.optionalRestSlugName, segmentName)\n          // slugName is kept as it can only be one particular slugName\n          this.optionalRestSlugName = segmentName\n          // nextSegment is overwritten to [[...]] so that it can later be sorted specifically\n          nextSegment = '[[...]]'\n        } else {\n          if (this.optionalRestSlugName != null) {\n            throw new Error(\n              `You cannot use both an optional and required catch-all route at the same level (\"[[...${this.optionalRestSlugName}]]\" and \"${urlPaths[0]}\").`\n            )\n          }\n\n          handleSlug(this.restSlugName, segmentName)\n          // slugName is kept as it can only be one particular slugName\n          this.restSlugName = segmentName\n          // nextSegment is overwritten to [...] so that it can later be sorted specifically\n          nextSegment = '[...]'\n        }\n      } else {\n        if (isOptional) {\n          throw new Error(\n            `Optional route parameters are not yet supported (\"${urlPaths[0]}\").`\n          )\n        }\n        handleSlug(this.slugName, segmentName)\n        // slugName is kept as it can only be one particular slugName\n        this.slugName = segmentName\n        // nextSegment is overwritten to [] so that it can later be sorted specifically\n        nextSegment = '[]'\n      }\n    }\n\n    // If this UrlNode doesn't have the nextSegment yet we create a new child UrlNode\n    if (!this.children.has(nextSegment)) {\n      this.children.set(nextSegment, new UrlNode())\n    }\n\n    this.children\n      .get(nextSegment)!\n      ._insert(urlPaths.slice(1), slugNames, isCatchAll)\n  }\n}\n\nexport function getSortedRoutes(normalizedPages: string[]): string[] {\n  // First the UrlNode is created, and every UrlNode can have only 1 dynamic segment\n  // Eg you can't have pages/[post]/abc.js and pages/[hello]/something-else.js\n  // Only 1 dynamic segment per nesting level\n\n  // So in the case that is test/integration/dynamic-routing it'll be this:\n  // pages/[post]/comments.js\n  // pages/blog/[post]/comment/[id].js\n  // Both are fine because `pages/[post]` and `pages/blog` are on the same level\n  // So in this case `UrlNode` created here has `this.slugName === 'post'`\n  // And since your PR passed through `slugName` as an array basically it'd including it in too many possibilities\n  // Instead what has to be passed through is the upwards path's dynamic names\n  const root = new UrlNode()\n\n  // Here the `root` gets injected multiple paths, and insert will break them up into sublevels\n  normalizedPages.forEach((pagePath) => root.insert(pagePath))\n  // Smoosh will then sort those sublevels up to the point where you get the correct route definition priority\n  return root.smoosh()\n}\n"], "names": [], "mappings": ";;;;QAqMgB,eAAe,GAAf,eAAe;MArMzB,OAAO;IAOX,MAAM,CAAC,OAAe,EAAQ,CAAC;aACxB,OAAO,CAAC,OAAO,CAAC,KAAK,EAAC,CAAG,GAAE,MAAM,CAAC,OAAO,OAAO,KAAK;IAC5D,CAAC;IAED,MAAM,GAAa,CAAC;oBACN,OAAO;IACrB,CAAC;IAEO,OAAO,CAAC,MAAc,IAAG,CAAG,GAAY,CAAC;QAC/C,KAAK,CAAC,aAAa;oBAAY,QAAQ,CAAC,IAAI;UAAI,IAAI;QACpD,EAAE,OAAO,QAAQ,KAAK,IAAI,EAAE,CAAC;YAC3B,aAAa,CAAC,MAAM,CAAC,aAAa,CAAC,OAAO,EAAC,EAAI,IAAG,CAAC;QACrD,CAAC;QACD,EAAE,OAAO,YAAY,KAAK,IAAI,EAAE,CAAC;YAC/B,aAAa,CAAC,MAAM,CAAC,aAAa,CAAC,OAAO,EAAC,KAAO,IAAG,CAAC;QACxD,CAAC;QACD,EAAE,OAAO,oBAAoB,KAAK,IAAI,EAAE,CAAC;YACvC,aAAa,CAAC,MAAM,CAAC,aAAa,CAAC,OAAO,EAAC,OAAS,IAAG,CAAC;QAC1D,CAAC;QAED,KAAK,CAAC,MAAM,GAAG,aAAa,CACzB,GAAG,EAAE,CAAC,QAAU,QAAQ,CAAC,GAAG,CAAC,CAAC,EAAG,OAAO,IAAI,MAAM,GAAG,CAAC,CAAC,CAAC;UACxD,MAAM,EAAE,IAAI,EAAE,IAAI;mBAAS,IAAI;mBAAK,IAAI;;;QAE3C,EAAE,OAAO,QAAQ,KAAK,IAAI,EAAE,CAAC;YAC3B,MAAM,CAAC,IAAI,SACD,QAAQ,CAAC,GAAG,EAAC,EAAI,GAAG,OAAO,IAAI,MAAM,CAAC,CAAC,OAAO,QAAQ,CAAC,EAAE;QAErE,CAAC;QAED,EAAE,QAAQ,WAAW,EAAE,CAAC;YACtB,KAAK,CAAC,CAAC,GAAG,MAAM,MAAK,CAAG,KAAG,CAAG,IAAG,MAAM,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC;YACnD,EAAE,OAAO,oBAAoB,IAAI,IAAI,EAAE,CAAC;gBACtC,KAAK,CAAC,GAAG,CAAC,KAAK,EACZ,oFAAoF,EAAE,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC,KAAK,OAAO,oBAAoB,CAAC,KAAK;YAE9I,CAAC;YAED,MAAM,CAAC,OAAO,CAAC,CAAC;QAClB,CAAC;QAED,EAAE,OAAO,YAAY,KAAK,IAAI,EAAE,CAAC;YAC/B,MAAM,CAAC,IAAI,SACD,QAAQ,CACb,GAAG,EAAC,KAAO,GACX,OAAO,IAAI,MAAM,CAAC,IAAI,OAAO,YAAY,CAAC,EAAE;QAEnD,CAAC;QAED,EAAE,OAAO,oBAAoB,KAAK,IAAI,EAAE,CAAC;YACvC,MAAM,CAAC,IAAI,SACD,QAAQ,CACb,GAAG,EAAC,OAAS,GACb,OAAO,IAAI,MAAM,CAAC,KAAK,OAAO,oBAAoB,CAAC,GAAG;QAE7D,CAAC;eAEM,MAAM;IACf,CAAC;IAEO,OAAO,CACb,QAAkB,EAClB,SAAmB,EACnB,UAAmB,EACb,CAAC;QACP,EAAE,EAAE,QAAQ,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;iBACrB,WAAW,GAAG,KAAK;;QAE1B,CAAC;QAED,EAAE,EAAE,UAAU,EAAE,CAAC;YACf,KAAK,CAAC,GAAG,CAAC,KAAK,EAAE,2CAA2C;QAC9D,CAAC;QAED,EAAwC,AAAxC,sCAAwC;QACxC,GAAG,CAAC,WAAW,GAAG,QAAQ,CAAC,CAAC;QAE5B,EAA6C,AAA7C,2CAA6C;QAC7C,EAAE,EAAE,WAAW,CAAC,UAAU,EAAC,CAAG,MAAK,WAAW,CAAC,QAAQ,EAAC,CAAG,IAAG,CAAC;YAC7D,EAA8C,AAA9C,4CAA8C;YAC9C,GAAG,CAAC,WAAW,GAAG,WAAW,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC;YAEzC,GAAG,CAAC,UAAU,GAAG,KAAK;YACtB,EAAE,EAAE,WAAW,CAAC,UAAU,EAAC,CAAG,MAAK,WAAW,CAAC,QAAQ,EAAC,CAAG,IAAG,CAAC;gBAC7D,EAAuD,AAAvD,qDAAuD;gBACvD,WAAW,GAAG,WAAW,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC;gBACrC,UAAU,GAAG,IAAI;YACnB,CAAC;YAED,EAAE,EAAE,WAAW,CAAC,UAAU,EAAC,GAAK,IAAG,CAAC;gBAClC,EAAwC,AAAxC,sCAAwC;gBACxC,WAAW,GAAG,WAAW,CAAC,SAAS,CAAC,CAAC;gBACrC,UAAU,GAAG,IAAI;YACnB,CAAC;YAED,EAAE,EAAE,WAAW,CAAC,UAAU,EAAC,CAAG,MAAK,WAAW,CAAC,QAAQ,EAAC,CAAG,IAAG,CAAC;gBAC7D,KAAK,CAAC,GAAG,CAAC,KAAK,EACZ,yDAAyD,EAAE,WAAW,CAAC,GAAG;YAE/E,CAAC;YAED,EAAE,EAAE,WAAW,CAAC,UAAU,EAAC,CAAG,IAAG,CAAC;gBAChC,KAAK,CAAC,GAAG,CAAC,KAAK,EACZ,qDAAqD,EAAE,WAAW,CAAC,GAAG;YAE3E,CAAC;qBAEQ,UAAU,CAAC,YAA2B,EAAE,QAAgB,EAAE,CAAC;gBAClE,EAAE,EAAE,YAAY,KAAK,IAAI,EAAE,CAAC;oBAC1B,EAA6E,AAA7E,2EAA6E;oBAC7E,EAAiC,AAAjC,+BAAiC;oBACjC,EAAwB,AAAxB,sBAAwB;oBACxB,EAAsB,AAAtB,oBAAsB;oBACtB,EAAwF,AAAxF,sFAAwF;oBACxF,EAAE,EAAE,YAAY,KAAK,QAAQ,EAAE,CAAC;wBAC9B,EAAwH,AAAxH,sHAAwH;wBACxH,KAAK,CAAC,GAAG,CAAC,KAAK,EACZ,gEAAgE,EAAE,YAAY,CAAC,OAAO,EAAE,QAAQ,CAAC,GAAG;oBAEzG,CAAC;gBACH,CAAC;gBAED,SAAS,CAAC,OAAO,EAAE,IAAI,GAAK,CAAC;oBAC3B,EAAE,EAAE,IAAI,KAAK,QAAQ,EAAE,CAAC;wBACtB,KAAK,CAAC,GAAG,CAAC,KAAK,EACZ,oCAAoC,EAAE,QAAQ,CAAC,qCAAqC;oBAEzF,CAAC;oBAED,EAAE,EAAE,IAAI,CAAC,OAAO,gBAAgB,WAAW,CAAC,OAAO,aAAa,CAAC;wBAC/D,KAAK,CAAC,GAAG,CAAC,KAAK,EACZ,gCAAgC,EAAE,IAAI,CAAC,OAAO,EAAE,QAAQ,CAAC,8DAA8D;oBAE5H,CAAC;gBACH,CAAC;gBAED,SAAS,CAAC,IAAI,CAAC,QAAQ;YACzB,CAAC;YAED,EAAE,EAAE,UAAU,EAAE,CAAC;gBACf,EAAE,EAAE,UAAU,EAAE,CAAC;oBACf,EAAE,OAAO,YAAY,IAAI,IAAI,EAAE,CAAC;wBAC9B,KAAK,CAAC,GAAG,CAAC,KAAK,EACZ,qFAAqF,OAAO,YAAY,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC,EAAE,IAAI;oBAExI,CAAC;oBAED,UAAU,MAAM,oBAAoB,EAAE,WAAW;oBACjD,EAA6D,AAA7D,2DAA6D;yBACxD,oBAAoB,GAAG,WAAW;oBACvC,EAAoF,AAApF,kFAAoF;oBACpF,WAAW,IAAG,OAAS;gBACzB,CAAC,MAAM,CAAC;oBACN,EAAE,OAAO,oBAAoB,IAAI,IAAI,EAAE,CAAC;wBACtC,KAAK,CAAC,GAAG,CAAC,KAAK,EACZ,sFAAsF,OAAO,oBAAoB,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC,EAAE,GAAG;oBAEjJ,CAAC;oBAED,UAAU,MAAM,YAAY,EAAE,WAAW;oBACzC,EAA6D,AAA7D,2DAA6D;yBACxD,YAAY,GAAG,WAAW;oBAC/B,EAAkF,AAAlF,gFAAkF;oBAClF,WAAW,IAAG,KAAO;gBACvB,CAAC;YACH,CAAC,MAAM,CAAC;gBACN,EAAE,EAAE,UAAU,EAAE,CAAC;oBACf,KAAK,CAAC,GAAG,CAAC,KAAK,EACZ,kDAAkD,EAAE,QAAQ,CAAC,CAAC,EAAE,GAAG;gBAExE,CAAC;gBACD,UAAU,MAAM,QAAQ,EAAE,WAAW;gBACrC,EAA6D,AAA7D,2DAA6D;qBACxD,QAAQ,GAAG,WAAW;gBAC3B,EAA+E,AAA/E,6EAA+E;gBAC/E,WAAW,IAAG,EAAI;YACpB,CAAC;QACH,CAAC;QAED,EAAiF,AAAjF,+EAAiF;QACjF,EAAE,QAAQ,QAAQ,CAAC,GAAG,CAAC,WAAW,GAAG,CAAC;iBAC/B,QAAQ,CAAC,GAAG,CAAC,WAAW,EAAE,GAAG,CAAC,OAAO;QAC5C,CAAC;aAEI,QAAQ,CACV,GAAG,CAAC,WAAW,EACf,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,GAAG,SAAS,EAAE,UAAU;IACrD,CAAC;;aAjMD,WAAW,GAAY,IAAI;aAC3B,QAAQ,GAAyB,GAAG,CAAC,GAAG;aACxC,QAAQ,GAAkB,IAAI;aAC9B,YAAY,GAAkB,IAAI;aAClC,oBAAoB,GAAkB,IAAI;;;SAgM5B,eAAe,CAAC,eAAyB,EAAY,CAAC;IACpE,EAAkF,AAAlF,gFAAkF;IAClF,EAA4E,AAA5E,0EAA4E;IAC5E,EAA2C,AAA3C,yCAA2C;IAE3C,EAAyE,AAAzE,uEAAyE;IACzE,EAA2B,AAA3B,yBAA2B;IAC3B,EAAoC,AAApC,kCAAoC;IACpC,EAA8E,AAA9E,4EAA8E;IAC9E,EAAwE,AAAxE,sEAAwE;IACxE,EAAgH,AAAhH,8GAAgH;IAChH,EAA4E,AAA5E,0EAA4E;IAC5E,KAAK,CAAC,IAAI,GAAG,GAAG,CAAC,OAAO;IAExB,EAA6F,AAA7F,2FAA6F;IAC7F,eAAe,CAAC,OAAO,EAAE,QAAQ,GAAK,IAAI,CAAC,MAAM,CAAC,QAAQ;;IAC1D,EAA4G,AAA5G,0GAA4G;WACrG,IAAI,CAAC,MAAM;AACpB,CAAC"}