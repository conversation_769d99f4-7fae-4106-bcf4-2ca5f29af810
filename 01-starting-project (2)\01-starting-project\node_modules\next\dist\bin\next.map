{"version": 3, "sources": ["../../bin/next.ts"], "sourcesContent": ["#!/usr/bin/env node\nimport * as log from '../build/output/log'\nimport arg from 'next/dist/compiled/arg/index.js'\nimport { NON_STANDARD_NODE_ENV } from '../lib/constants'\n;['react', 'react-dom'].forEach((dependency) => {\n  try {\n    // When 'npm link' is used it checks the clone location. Not the project.\n    require.resolve(dependency)\n  } catch (err) {\n    console.warn(\n      `The module '${dependency}' was not found. Next.js requires that you include it in 'dependencies' of your 'package.json'. To add it, run 'npm install ${dependency}'`\n    )\n  }\n})\n\nconst defaultCommand = 'dev'\nexport type cliCommand = (argv?: string[]) => void\nconst commands: { [command: string]: () => Promise<cliCommand> } = {\n  build: () => import('../cli/next-build').then((i) => i.nextBuild),\n  start: () => import('../cli/next-start').then((i) => i.nextStart),\n  export: () => import('../cli/next-export').then((i) => i.nextExport),\n  dev: () => import('../cli/next-dev').then((i) => i.nextDev),\n  lint: () => import('../cli/next-lint').then((i) => i.nextLint),\n  telemetry: () => import('../cli/next-telemetry').then((i) => i.nextTelemetry),\n}\n\nconst args = arg(\n  {\n    // Types\n    '--version': Boolean,\n    '--help': Boolean,\n    '--inspect': Boolean,\n\n    // Aliases\n    '-v': '--version',\n    '-h': '--help',\n  },\n  {\n    permissive: true,\n  }\n)\n\n// Version is inlined into the file using taskr build pipeline\nif (args['--version']) {\n  console.log(`Next.js v${process.env.__NEXT_VERSION}`)\n  process.exit(0)\n}\n\n// Check if we are running `next <subcommand>` or `next`\nconst foundCommand = Boolean(commands[args._[0]])\n\n// Makes sure the `next --help` case is covered\n// This help message is only showed for `next --help`\n// `next <subcommand> --help` falls through to be handled later\nif (!foundCommand && args['--help']) {\n  console.log(`\n    Usage\n      $ next <command>\n\n    Available commands\n      ${Object.keys(commands).join(', ')}\n\n    Options\n      --version, -v   Version number\n      --help, -h      Displays this message\n\n    For more information run a command with the --help flag\n      $ next build --help\n  `)\n  process.exit(0)\n}\n\nconst command = foundCommand ? args._[0] : defaultCommand\nconst forwardedArgs = foundCommand ? args._.slice(1) : args._\n\nif (args['--inspect'])\n  throw new Error(\n    `--inspect flag is deprecated. Use env variable NODE_OPTIONS instead: NODE_OPTIONS='--inspect' next ${command}`\n  )\n\n// Make sure the `next <subcommand> --help` case is covered\nif (args['--help']) {\n  forwardedArgs.push('--help')\n}\n\nconst defaultEnv = command === 'dev' ? 'development' : 'production'\n\nconst standardEnv = ['production', 'development', 'test']\n\nif (process.env.NODE_ENV && !standardEnv.includes(process.env.NODE_ENV)) {\n  log.warn(NON_STANDARD_NODE_ENV)\n}\n\n;(process.env as any).NODE_ENV = process.env.NODE_ENV || defaultEnv\n\n// Make sure commands gracefully respect termination signals (e.g. from Docker)\nprocess.on('SIGTERM', () => process.exit(0))\nprocess.on('SIGINT', () => process.exit(0))\n\ncommands[command]()\n  .then((exec) => exec(forwardedArgs))\n  .then(() => {\n    if (command === 'build') {\n      // ensure process exits after build completes so open handles/connections\n      // don't cause process to hang\n      process.exit(0)\n    }\n  })\n\nif (command === 'dev') {\n  const { CONFIG_FILE } = require('../shared/lib/constants')\n  const { watchFile } = require('fs')\n  watchFile(`${process.cwd()}/${CONFIG_FILE}`, (cur: any, prev: any) => {\n    if (cur.size > 0 || prev.size > 0) {\n      console.log(\n        `\\n> Found a change in ${CONFIG_FILE}. Restart the server to see the changes in effect.`\n      )\n    }\n  })\n}\n"], "names": [], "mappings": ";;AACY,GAAG,CAAH,GAAG;AACC,GAAiC,CAAjC,QAAiC;AACX,GAAkB,CAAlB,UAAkB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;KACtD,KAAO;KAAE,SAAW;EAAE,OAAO,EAAE,UAAU,GAAK,CAAC;QAC3C,CAAC;QACH,EAAyE,AAAzE,uEAAyE;QACzE,OAAO,CAAC,OAAO,CAAC,UAAU;IAC5B,CAAC,QAAQ,GAAG,EAAE,CAAC;QACb,OAAO,CAAC,IAAI,EACT,YAAY,EAAE,UAAU,CAAC,4HAA4H,EAAE,UAAU,CAAC,CAAC;IAExK,CAAC;AACH,CAAC;AAED,KAAK,CAAC,cAAc,IAAG,GAAK;AAE5B,KAAK,CAAC,QAAQ;IACZ,KAAK;oDAAe,iBAAmB;WAAE,IAAI,EAAE,CAAC,GAAK,CAAC,CAAC,SAAS;;;IAChE,KAAK;oDAAe,iBAAmB;WAAE,IAAI,EAAE,CAAC,GAAK,CAAC,CAAC,SAAS;;;IAChE,MAAM;oDAAe,kBAAoB;WAAE,IAAI,EAAE,CAAC,GAAK,CAAC,CAAC,UAAU;;;IACnE,GAAG;oDAAe,eAAiB;WAAE,IAAI,EAAE,CAAC,GAAK,CAAC,CAAC,OAAO;;;IAC1D,IAAI;oDAAe,gBAAkB;WAAE,IAAI,EAAE,CAAC,GAAK,CAAC,CAAC,QAAQ;;;IAC7D,SAAS;oDAAe,qBAAuB;WAAE,IAAI,EAAE,CAAC,GAAK,CAAC,CAAC,aAAa;;;AAG9E,KAAK,CAAC,IAAI,OAxBM,QAAiC;IA0B7C,EAAQ,AAAR,MAAQ;KACR,SAAW,GAAE,OAAO;KACpB,MAAQ,GAAE,OAAO;KACjB,SAAW,GAAE,OAAO;IAEpB,EAAU,AAAV,QAAU;KACV,EAAI,IAAE,SAAW;KACjB,EAAI,IAAE,MAAQ;;IAGd,UAAU,EAAE,IAAI;;AAIpB,EAA8D,AAA9D,4DAA8D;AAC9D,EAAE,EAAE,IAAI,EAAC,SAAW,IAAG,CAAC;IACtB,OAAO,CAAC,GAAG,EAAE,SAAS,EAAE,OAAO,CAAC,GAAG,CAAC,cAAc;IAClD,OAAO,CAAC,IAAI,CAAC,CAAC;AAChB,CAAC;AAED,EAAwD,AAAxD,sDAAwD;AACxD,KAAK,CAAC,YAAY,GAAG,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AAE9C,EAA+C,AAA/C,6CAA+C;AAC/C,EAAqD,AAArD,mDAAqD;AACrD,EAA+D,AAA/D,6DAA+D;AAC/D,EAAE,GAAG,YAAY,IAAI,IAAI,EAAC,MAAQ,IAAG,CAAC;IACpC,OAAO,CAAC,GAAG,EAAE,gEAKT,EAAE,MAAM,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,EAAC,EAAI,GAAE,wLAQvC;IACA,OAAO,CAAC,IAAI,CAAC,CAAC;AAChB,CAAC;AAED,KAAK,CAAC,OAAO,GAAG,YAAY,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,cAAc;AACzD,KAAK,CAAC,aAAa,GAAG,YAAY,GAAG,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,IAAI,IAAI,CAAC,CAAC;AAE7D,EAAE,EAAE,IAAI,EAAC,SAAW,IAClB,KAAK,CAAC,GAAG,CAAC,KAAK,EACZ,mGAAmG,EAAE,OAAO;AAGjH,EAA2D,AAA3D,yDAA2D;AAC3D,EAAE,EAAE,IAAI,EAAC,MAAQ,IAAG,CAAC;IACnB,aAAa,CAAC,IAAI,EAAC,MAAQ;AAC7B,CAAC;AAED,KAAK,CAAC,UAAU,GAAG,OAAO,MAAK,GAAK,KAAG,WAAa,KAAG,UAAY;AAEnE,KAAK,CAAC,WAAW;KAAI,UAAY;KAAE,WAAa;KAAE,IAAM;;AAExD,EAAE,EAAE,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,WAAW,CAAC,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,QAAQ,GAAG,CAAC;IAxF9D,GAAG,CAyFT,IAAI,CAvF4B,UAAkB;AAwFxD,CAAC;AAEC,OAAO,CAAC,GAAG,CAAS,QAAQ,GAAG,OAAO,CAAC,GAAG,CAAC,QAAQ,IAAI,UAAU;AAEnE,EAA+E,AAA/E,6EAA+E;AAC/E,OAAO,CAAC,EAAE,EAAC,OAAS,OAAQ,OAAO,CAAC,IAAI,CAAC,CAAC;;AAC1C,OAAO,CAAC,EAAE,EAAC,MAAQ,OAAQ,OAAO,CAAC,IAAI,CAAC,CAAC;;AAEzC,QAAQ,CAAC,OAAO,IACb,IAAI,EAAE,IAAI,GAAK,IAAI,CAAC,aAAa;EACjC,IAAI,KAAO,CAAC;IACX,EAAE,EAAE,OAAO,MAAK,KAAO,GAAE,CAAC;QACxB,EAAyE,AAAzE,uEAAyE;QACzE,EAA8B,AAA9B,4BAA8B;QAC9B,OAAO,CAAC,IAAI,CAAC,CAAC;IAChB,CAAC;AACH,CAAC;AAEH,EAAE,EAAE,OAAO,MAAK,GAAK,GAAE,CAAC;IACtB,KAAK,GAAG,WAAW,MAAK,OAAO,EAAC,uBAAyB;IACzD,KAAK,GAAG,SAAS,MAAK,OAAO,EAAC,EAAI;IAClC,SAAS,IAAI,OAAO,CAAC,GAAG,GAAG,CAAC,EAAE,WAAW,KAAK,GAAQ,EAAE,IAAS,GAAK,CAAC;QACrE,EAAE,EAAE,GAAG,CAAC,IAAI,GAAG,CAAC,IAAI,IAAI,CAAC,IAAI,GAAG,CAAC,EAAE,CAAC;YAClC,OAAO,CAAC,GAAG,EACR,sBAAsB,EAAE,WAAW,CAAC,kDAAkD;QAE3F,CAAC;IACH,CAAC;AACH,CAAC"}