{"version": 3, "sources": ["../../../../build/babel/plugins/optimize-hook-destructuring.ts"], "sourcesContent": ["import {\n  Node<PERSON><PERSON>,\n  PluginObj,\n  types as BabelTypes,\n} from 'next/dist/compiled/babel/core'\n\n// matches any hook-like (the default)\nconst isHook = /^use[A-Z]/\n\n// matches only built-in hooks provided by <PERSON><PERSON> et al\nconst isBuiltInHook =\n  /^use(Callback|Context|DebugValue|Effect|ImperativeHandle|LayoutEffect|Memo|Reducer|Ref|State)$/\n\nexport default function ({\n  types: t,\n}: {\n  types: typeof BabelTypes\n}): PluginObj<any> {\n  const visitor = {\n    CallExpression(path: NodePath<BabelTypes.CallExpression>, state: any) {\n      const onlyBuiltIns = state.opts.onlyBuiltIns\n\n      // if specified, options.lib is a list of libraries that provide hook functions\n      const libs =\n        state.opts.lib &&\n        (state.opts.lib === true\n          ? ['react', 'preact/hooks']\n          : [].concat(state.opts.lib))\n\n      // skip function calls that are not the init of a variable declaration:\n      if (!t.isVariableDeclarator(path.parent)) return\n\n      // skip function calls where the return value is not Array-destructured:\n      if (!t.isArrayPattern(path.parent.id)) return\n\n      // name of the (hook) function being called:\n      const hookName = (path.node.callee as BabelTypes.Identifier).name\n\n      if (libs) {\n        const binding = path.scope.getBinding(hookName)\n        // not an import\n        if (!binding || binding.kind !== 'module') return\n\n        const specifier = (binding.path.parent as BabelTypes.ImportDeclaration)\n          .source.value\n        // not a match\n        if (!libs.some((lib: any) => lib === specifier)) return\n      }\n\n      // only match function calls with names that look like a hook\n      if (!(onlyBuiltIns ? isBuiltInHook : isHook).test(hookName)) return\n\n      path.parent.id = t.objectPattern(\n        path.parent.id.elements.reduce<Array<BabelTypes.ObjectProperty>>(\n          (patterns, element, i) => {\n            if (element === null) {\n              return patterns\n            }\n\n            return patterns.concat(\n              t.objectProperty(t.numericLiteral(i), element)\n            )\n          },\n          []\n        )\n      )\n    },\n  }\n\n  return {\n    name: 'optimize-hook-destructuring',\n    visitor: {\n      // this is a workaround to run before preset-env destroys destructured assignments\n      Program(path, state) {\n        path.traverse(visitor, state)\n      },\n    },\n  }\n}\n"], "names": [], "mappings": ";;;;;AAMA,EAAsC,AAAtC,oCAAsC;AACtC,KAAK,CAAC,MAAM;AAEZ,EAAsD,AAAtD,oDAAsD;AACtD,KAAK,CAAC,aAAa;oBAIjB,KAAK,EAAE,CAAC,KAGS,CAAC;IAClB,KAAK,CAAC,OAAO;QACX,cAAc,EAAC,IAAyC,EAAE,KAAU,EAAE,CAAC;YACrE,KAAK,CAAC,YAAY,GAAG,KAAK,CAAC,IAAI,CAAC,YAAY;YAE5C,EAA+E,AAA/E,6EAA+E;YAC/E,KAAK,CAAC,IAAI,GACR,KAAK,CAAC,IAAI,CAAC,GAAG,KACb,KAAK,CAAC,IAAI,CAAC,GAAG,KAAK,IAAI;iBACnB,KAAO;iBAAE,YAAc;mBACrB,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG;YAE9B,EAAuE,AAAvE,qEAAuE;YACvE,EAAE,GAAG,CAAC,CAAC,oBAAoB,CAAC,IAAI,CAAC,MAAM;YAEvC,EAAwE,AAAxE,sEAAwE;YACxE,EAAE,GAAG,CAAC,CAAC,cAAc,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE;YAEpC,EAA4C,AAA5C,0CAA4C;YAC5C,KAAK,CAAC,QAAQ,GAAI,IAAI,CAAC,IAAI,CAAC,MAAM,CAA2B,IAAI;YAEjE,EAAE,EAAE,IAAI,EAAE,CAAC;gBACT,KAAK,CAAC,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,QAAQ;gBAC9C,EAAgB,AAAhB,cAAgB;gBAChB,EAAE,GAAG,OAAO,IAAI,OAAO,CAAC,IAAI,MAAK,MAAQ;gBAEzC,KAAK,CAAC,SAAS,GAAI,OAAO,CAAC,IAAI,CAAC,MAAM,CACnC,MAAM,CAAC,KAAK;gBACf,EAAc,AAAd,YAAc;gBACd,EAAE,GAAG,IAAI,CAAC,IAAI,EAAE,GAAQ,GAAK,GAAG,KAAK,SAAS;;YAChD,CAAC;YAED,EAA6D,AAA7D,2DAA6D;YAC7D,EAAE,IAAI,YAAY,GAAG,aAAa,GAAG,MAAM,EAAE,IAAI,CAAC,QAAQ;YAE1D,IAAI,CAAC,MAAM,CAAC,EAAE,GAAG,CAAC,CAAC,aAAa,CAC9B,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,QAAQ,CAAC,MAAM,EAC3B,QAAQ,EAAE,OAAO,EAAE,CAAC,GAAK,CAAC;gBACzB,EAAE,EAAE,OAAO,KAAK,IAAI,EAAE,CAAC;2BACd,QAAQ;gBACjB,CAAC;uBAEM,QAAQ,CAAC,MAAM,CACpB,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,GAAG,OAAO;YAEjD,CAAC;QAIP,CAAC;;;QAID,IAAI,GAAE,2BAA6B;QACnC,OAAO;YACL,EAAkF,AAAlF,gFAAkF;YAClF,OAAO,EAAC,IAAI,EAAE,KAAK,EAAE,CAAC;gBACpB,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE,KAAK;YAC9B,CAAC;;;AAGP,CAAC"}