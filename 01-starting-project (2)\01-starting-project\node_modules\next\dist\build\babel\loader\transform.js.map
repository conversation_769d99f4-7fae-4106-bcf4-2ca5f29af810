{"version": 3, "sources": ["../../../../build/babel/loader/transform.ts"], "sourcesContent": ["/*\n * Partially adapted from @babel/core (MIT license).\n */\n\nimport traverse from 'next/dist/compiled/babel/traverse'\nimport generate from 'next/dist/compiled/babel/generator'\nimport normalizeFile from 'next/dist/compiled/babel/core-lib-normalize-file'\nimport normalizeOpts from 'next/dist/compiled/babel/core-lib-normalize-opts'\nimport loadBlockHoistPlugin from 'next/dist/compiled/babel/core-lib-block-hoist-plugin'\nimport PluginPass from 'next/dist/compiled/babel/core-lib-plugin-pass'\n\nimport getConfig from './get-config'\nimport { consumeIterator } from './util'\nimport { Span } from '../../../telemetry/trace'\nimport { NextJsLoaderContext } from './types'\n\nfunction getTraversalParams(file: any, pluginPairs: any[]) {\n  const passPairs = []\n  const passes = []\n  const visitors = []\n\n  for (const plugin of pluginPairs.concat(loadBlockHoistPlugin())) {\n    const pass = new PluginPass(file, plugin.key, plugin.options)\n    passPairs.push([plugin, pass])\n    passes.push(pass)\n    visitors.push(plugin.visitor)\n  }\n\n  return { passPairs, passes, visitors }\n}\n\nfunction invokePluginPre(file: any, passPairs: any[]) {\n  for (const [{ pre }, pass] of passPairs) {\n    if (pre) {\n      pre.call(pass, file)\n    }\n  }\n}\n\nfunction invokePluginPost(file: any, passPairs: any[]) {\n  for (const [{ post }, pass] of passPairs) {\n    if (post) {\n      post.call(pass, file)\n    }\n  }\n}\n\nfunction transformAstPass(file: any, pluginPairs: any[], parentSpan: Span) {\n  const { passPairs, passes, visitors } = getTraversalParams(file, pluginPairs)\n\n  invokePluginPre(file, passPairs)\n  const visitor = traverse.visitors.merge(\n    visitors,\n    passes,\n    // @ts-ignore - the exported types are incorrect here\n    file.opts.wrapPluginVisitorMethod\n  )\n\n  parentSpan\n    .traceChild('babel-turbo-traverse')\n    .traceFn(() => traverse(file.ast, visitor, file.scope))\n\n  invokePluginPost(file, passPairs)\n}\n\nfunction transformAst(file: any, babelConfig: any, parentSpan: Span) {\n  for (const pluginPairs of babelConfig.passes) {\n    transformAstPass(file, pluginPairs, parentSpan)\n  }\n}\n\nexport default function transform(\n  this: NextJsLoaderContext,\n  source: string,\n  inputSourceMap: object | null | undefined,\n  loaderOptions: any,\n  filename: string,\n  target: string,\n  parentSpan: Span\n) {\n  const getConfigSpan = parentSpan.traceChild('babel-turbo-get-config')\n  const babelConfig = getConfig.call(this, {\n    source,\n    loaderOptions,\n    inputSourceMap,\n    target,\n    filename,\n  })\n  getConfigSpan.stop()\n\n  const normalizeSpan = parentSpan.traceChild('babel-turbo-normalize-file')\n  const file = consumeIterator(\n    normalizeFile(babelConfig.passes, normalizeOpts(babelConfig), source)\n  )\n  normalizeSpan.stop()\n\n  const transformSpan = parentSpan.traceChild('babel-turbo-transform')\n  transformAst(file, babelConfig, transformSpan)\n  transformSpan.stop()\n\n  const generateSpan = parentSpan.traceChild('babel-turbo-generate')\n  const { code, map } = generate(file.ast, file.opts.generatorOpts, file.code)\n  generateSpan.stop()\n\n  return { code, map }\n}\n"], "names": [], "mappings": ";;;;kBAuEwB,SAAS;AAnEZ,GAAmC,CAAnC,SAAmC;AACnC,GAAoC,CAApC,UAAoC;AAC/B,GAAkD,CAAlD,qBAAkD;AAClD,GAAkD,CAAlD,qBAAkD;AAC3C,GAAsD,CAAtD,wBAAsD;AAChE,GAA+C,CAA/C,kBAA+C;AAEhD,GAAc,CAAd,UAAc;AACJ,GAAQ,CAAR,KAAQ;;;;;;SAI/B,kBAAkB,CAAC,IAAS,EAAE,WAAkB,EAAE,CAAC;IAC1D,KAAK,CAAC,SAAS;IACf,KAAK,CAAC,MAAM;IACZ,KAAK,CAAC,QAAQ;SAET,KAAK,CAAC,MAAM,IAAI,WAAW,CAAC,MAAM,KAbR,wBAAsD,aAapB,CAAC;QAChE,KAAK,CAAC,IAAI,GAAG,GAAG,CAbG,kBAA+C,SAatC,IAAI,EAAE,MAAM,CAAC,GAAG,EAAE,MAAM,CAAC,OAAO;QAC5D,SAAS,CAAC,IAAI;YAAE,MAAM;YAAE,IAAI;;QAC5B,MAAM,CAAC,IAAI,CAAC,IAAI;QAChB,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO;IAC9B,CAAC;;QAEQ,SAAS;QAAE,MAAM;QAAE,QAAQ;;AACtC,CAAC;SAEQ,eAAe,CAAC,IAAS,EAAE,SAAgB,EAAE,CAAC;SAChD,KAAK,IAAI,GAAG,KAAI,IAAI,KAAK,SAAS,CAAE,CAAC;QACxC,EAAE,EAAE,GAAG,EAAE,CAAC;YACR,GAAG,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI;QACrB,CAAC;IACH,CAAC;AACH,CAAC;SAEQ,gBAAgB,CAAC,IAAS,EAAE,SAAgB,EAAE,CAAC;SACjD,KAAK,IAAI,IAAI,KAAI,IAAI,KAAK,SAAS,CAAE,CAAC;QACzC,EAAE,EAAE,IAAI,EAAE,CAAC;YACT,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI;QACtB,CAAC;IACH,CAAC;AACH,CAAC;SAEQ,gBAAgB,CAAC,IAAS,EAAE,WAAkB,EAAE,UAAgB,EAAE,CAAC;IAC1E,KAAK,GAAG,SAAS,GAAE,MAAM,GAAE,QAAQ,MAAK,kBAAkB,CAAC,IAAI,EAAE,WAAW;IAE5E,eAAe,CAAC,IAAI,EAAE,SAAS;IAC/B,KAAK,CAAC,OAAO,GA/CM,SAAmC,SA+C7B,QAAQ,CAAC,KAAK,CACrC,QAAQ,EACR,MAAM,EACN,EAAqD,AAArD,mDAAqD;IACrD,IAAI,CAAC,IAAI,CAAC,uBAAuB;IAGnC,UAAU,CACP,UAAU,EAAC,oBAAsB,GACjC,OAAO,SAxDS,SAAmC,UAwD5B,IAAI,CAAC,GAAG,EAAE,OAAO,EAAE,IAAI,CAAC,KAAK;;IAEvD,gBAAgB,CAAC,IAAI,EAAE,SAAS;AAClC,CAAC;SAEQ,YAAY,CAAC,IAAS,EAAE,WAAgB,EAAE,UAAgB,EAAE,CAAC;SAC/D,KAAK,CAAC,WAAW,IAAI,WAAW,CAAC,MAAM,CAAE,CAAC;QAC7C,gBAAgB,CAAC,IAAI,EAAE,WAAW,EAAE,UAAU;IAChD,CAAC;AACH,CAAC;SAEuB,SAAS,CAE/B,MAAc,EACd,cAAyC,EACzC,aAAkB,EAClB,QAAgB,EAChB,MAAc,EACd,UAAgB,EAChB,CAAC;IACD,KAAK,CAAC,aAAa,GAAG,UAAU,CAAC,UAAU,EAAC,sBAAwB;IACpE,KAAK,CAAC,WAAW,GAtEG,UAAc,SAsEJ,IAAI;QAChC,MAAM;QACN,aAAa;QACb,cAAc;QACd,MAAM;QACN,QAAQ;;IAEV,aAAa,CAAC,IAAI;IAElB,KAAK,CAAC,aAAa,GAAG,UAAU,CAAC,UAAU,EAAC,0BAA4B;IACxE,KAAK,CAAC,IAAI,OA/EoB,KAAQ,sBANd,qBAAkD,UAsF1D,WAAW,CAAC,MAAM,MArFV,qBAAkD,UAqFxB,WAAW,GAAG,MAAM;IAEtE,aAAa,CAAC,IAAI;IAElB,KAAK,CAAC,aAAa,GAAG,UAAU,CAAC,UAAU,EAAC,qBAAuB;IACnE,YAAY,CAAC,IAAI,EAAE,WAAW,EAAE,aAAa;IAC7C,aAAa,CAAC,IAAI;IAElB,KAAK,CAAC,YAAY,GAAG,UAAU,CAAC,UAAU,EAAC,oBAAsB;IACjE,KAAK,GAAG,IAAI,GAAE,GAAG,UAhGE,UAAoC,UAgGxB,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,IAAI,CAAC,IAAI;IAC3E,YAAY,CAAC,IAAI;;QAER,IAAI;QAAE,GAAG;;AACpB,CAAC"}