{"version": 3, "sources": ["../../server/router.ts"], "sourcesContent": ["import { IncomingMessage, ServerResponse } from 'http'\nimport { UrlWithParsedQuery } from 'url'\n\nimport pathMatch from '../shared/lib/router/utils/path-match'\nimport { removePathTrailingSlash } from '../client/normalize-trailing-slash'\nimport { normalizeLocalePath } from '../shared/lib/i18n/normalize-locale-path'\nimport { RouteHas } from '../lib/load-custom-routes'\nimport { matchHas } from '../shared/lib/router/utils/prepare-destination'\n\nexport const route = pathMatch()\n\nexport type Params = { [param: string]: any }\n\nexport type RouteMatch = (pathname: string | null | undefined) => false | Params\n\ntype RouteResult = {\n  finished: boolean\n  pathname?: string\n  query?: { [k: string]: string }\n}\n\nexport type Route = {\n  match: RouteMatch\n  has?: RouteHas[]\n  type: string\n  check?: boolean\n  statusCode?: number\n  name: string\n  requireBasePath?: false\n  internal?: true\n  fn: (\n    req: IncomingMessage,\n    res: ServerResponse,\n    params: Params,\n    parsedUrl: UrlWithParsedQuery\n  ) => Promise<RouteResult> | RouteResult\n}\n\nexport type DynamicRoutes = Array<{ page: string; match: RouteMatch }>\n\nexport type PageChecker = (pathname: string) => Promise<boolean>\n\nconst customRouteTypes = new Set(['rewrite', 'redirect', 'header'])\n\nfunction replaceBasePath(basePath: string, pathname: string) {\n  // If replace ends up replacing the full url it'll be `undefined`, meaning we have to default it to `/`\n  return pathname!.replace(basePath, '') || '/'\n}\n\nexport default class Router {\n  basePath: string\n  headers: Route[]\n  fsRoutes: Route[]\n  redirects: Route[]\n  rewrites: {\n    beforeFiles: Route[]\n    afterFiles: Route[]\n    fallback: Route[]\n  }\n  catchAllRoute: Route\n  pageChecker: PageChecker\n  dynamicRoutes: DynamicRoutes\n  useFileSystemPublicRoutes: boolean\n  locales: string[]\n\n  constructor({\n    basePath = '',\n    headers = [],\n    fsRoutes = [],\n    rewrites = {\n      beforeFiles: [],\n      afterFiles: [],\n      fallback: [],\n    },\n    redirects = [],\n    catchAllRoute,\n    dynamicRoutes = [],\n    pageChecker,\n    useFileSystemPublicRoutes,\n    locales = [],\n  }: {\n    basePath: string\n    headers: Route[]\n    fsRoutes: Route[]\n    rewrites: {\n      beforeFiles: Route[]\n      afterFiles: Route[]\n      fallback: Route[]\n    }\n    redirects: Route[]\n    catchAllRoute: Route\n    dynamicRoutes: DynamicRoutes | undefined\n    pageChecker: PageChecker\n    useFileSystemPublicRoutes: boolean\n    locales: string[]\n  }) {\n    this.basePath = basePath\n    this.headers = headers\n    this.fsRoutes = fsRoutes\n    this.rewrites = rewrites\n    this.redirects = redirects\n    this.pageChecker = pageChecker\n    this.catchAllRoute = catchAllRoute\n    this.dynamicRoutes = dynamicRoutes\n    this.useFileSystemPublicRoutes = useFileSystemPublicRoutes\n    this.locales = locales\n  }\n\n  setDynamicRoutes(routes: DynamicRoutes = []) {\n    this.dynamicRoutes = routes\n  }\n\n  addFsRoute(fsRoute: Route) {\n    this.fsRoutes.unshift(fsRoute)\n  }\n\n  async execute(\n    req: IncomingMessage,\n    res: ServerResponse,\n    parsedUrl: UrlWithParsedQuery\n  ): Promise<boolean> {\n    // memoize page check calls so we don't duplicate checks for pages\n    const pageChecks: { [name: string]: Promise<boolean> } = {}\n    const memoizedPageChecker = async (p: string): Promise<boolean> => {\n      p = normalizeLocalePath(p, this.locales).pathname\n\n      if (pageChecks[p]) {\n        return pageChecks[p]\n      }\n      const result = this.pageChecker(p)\n      pageChecks[p] = result\n      return result\n    }\n\n    let parsedUrlUpdated = parsedUrl\n\n    const applyCheckTrue = async (checkParsedUrl: UrlWithParsedQuery) => {\n      const originalFsPathname = checkParsedUrl.pathname\n      const fsPathname = replaceBasePath(this.basePath, originalFsPathname!)\n\n      for (const fsRoute of this.fsRoutes) {\n        const fsParams = fsRoute.match(fsPathname)\n\n        if (fsParams) {\n          checkParsedUrl.pathname = fsPathname\n\n          const fsResult = await fsRoute.fn(req, res, fsParams, checkParsedUrl)\n\n          if (fsResult.finished) {\n            return true\n          }\n\n          checkParsedUrl.pathname = originalFsPathname\n        }\n      }\n      let matchedPage = await memoizedPageChecker(fsPathname)\n\n      // If we didn't match a page check dynamic routes\n      if (!matchedPage) {\n        const normalizedFsPathname = normalizeLocalePath(\n          fsPathname,\n          this.locales\n        ).pathname\n\n        for (const dynamicRoute of this.dynamicRoutes) {\n          if (dynamicRoute.match(normalizedFsPathname)) {\n            matchedPage = true\n          }\n        }\n      }\n\n      // Matched a page or dynamic route so render it using catchAllRoute\n      if (matchedPage) {\n        const pageParams = this.catchAllRoute.match(checkParsedUrl.pathname)\n        checkParsedUrl.pathname = fsPathname\n        checkParsedUrl.query._nextBubbleNoFallback = '1'\n\n        const result = await this.catchAllRoute.fn(\n          req,\n          res,\n          pageParams as Params,\n          checkParsedUrl\n        )\n        return result.finished\n      }\n    }\n\n    /*\n      Desired routes order\n      - headers\n      - redirects\n      - Check filesystem (including pages), if nothing found continue\n      - User rewrites (checking filesystem and pages each match)\n    */\n\n    const allRoutes = [\n      ...this.headers,\n      ...this.redirects,\n      ...this.rewrites.beforeFiles,\n      ...this.fsRoutes,\n      // We only check the catch-all route if public page routes hasn't been\n      // disabled\n      ...(this.useFileSystemPublicRoutes\n        ? [\n            {\n              type: 'route',\n              name: 'page checker',\n              requireBasePath: false,\n              match: route('/:path*'),\n              fn: async (checkerReq, checkerRes, params, parsedCheckerUrl) => {\n                let { pathname } = parsedCheckerUrl\n                pathname = removePathTrailingSlash(pathname || '/')\n\n                if (!pathname) {\n                  return { finished: false }\n                }\n\n                if (await memoizedPageChecker(pathname)) {\n                  return this.catchAllRoute.fn(\n                    checkerReq,\n                    checkerRes,\n                    params,\n                    parsedCheckerUrl\n                  )\n                }\n                return { finished: false }\n              },\n            } as Route,\n          ]\n        : []),\n      ...this.rewrites.afterFiles,\n      ...(this.rewrites.fallback.length\n        ? [\n            {\n              type: 'route',\n              name: 'dynamic route/page check',\n              requireBasePath: false,\n              match: route('/:path*'),\n              fn: async (\n                _checkerReq,\n                _checkerRes,\n                _params,\n                parsedCheckerUrl\n              ) => {\n                return {\n                  finished: await applyCheckTrue(parsedCheckerUrl),\n                }\n              },\n            } as Route,\n            ...this.rewrites.fallback,\n          ]\n        : []),\n\n      // We only check the catch-all route if public page routes hasn't been\n      // disabled\n      ...(this.useFileSystemPublicRoutes ? [this.catchAllRoute] : []),\n    ]\n    const originallyHadBasePath =\n      !this.basePath || (req as any)._nextHadBasePath\n\n    for (const testRoute of allRoutes) {\n      // if basePath is being used, the basePath will still be included\n      // in the pathname here to allow custom-routes to require containing\n      // it or not, filesystem routes and pages must always include the basePath\n      // if it is set\n      let currentPathname = parsedUrlUpdated.pathname as string\n      const originalPathname = currentPathname\n      const requireBasePath = testRoute.requireBasePath !== false\n      const isCustomRoute = customRouteTypes.has(testRoute.type)\n      const isPublicFolderCatchall = testRoute.name === 'public folder catchall'\n      const keepBasePath = isCustomRoute || isPublicFolderCatchall\n      const keepLocale = isCustomRoute\n\n      const currentPathnameNoBasePath = replaceBasePath(\n        this.basePath,\n        currentPathname\n      )\n\n      if (!keepBasePath) {\n        currentPathname = currentPathnameNoBasePath\n      }\n\n      const localePathResult = normalizeLocalePath(\n        currentPathnameNoBasePath,\n        this.locales\n      )\n      const activeBasePath = keepBasePath ? this.basePath : ''\n\n      if (keepLocale) {\n        if (\n          !testRoute.internal &&\n          parsedUrl.query.__nextLocale &&\n          !localePathResult.detectedLocale\n        ) {\n          currentPathname = `${activeBasePath}/${parsedUrl.query.__nextLocale}${\n            currentPathnameNoBasePath === '/' ? '' : currentPathnameNoBasePath\n          }`\n        }\n\n        if (\n          (req as any).__nextHadTrailingSlash &&\n          !currentPathname.endsWith('/')\n        ) {\n          currentPathname += '/'\n        }\n      } else {\n        currentPathname = `${\n          (req as any)._nextHadBasePath ? activeBasePath : ''\n        }${\n          activeBasePath && localePathResult.pathname === '/'\n            ? ''\n            : localePathResult.pathname\n        }`\n      }\n\n      let newParams = testRoute.match(currentPathname)\n\n      if (testRoute.has && newParams) {\n        const hasParams = matchHas(req, testRoute.has, parsedUrlUpdated.query)\n\n        if (hasParams) {\n          Object.assign(newParams, hasParams)\n        } else {\n          newParams = false\n        }\n      }\n\n      // Check if the match function matched\n      if (newParams) {\n        // since we require basePath be present for non-custom-routes we\n        // 404 here when we matched an fs route\n        if (!keepBasePath) {\n          if (!originallyHadBasePath && !(req as any)._nextDidRewrite) {\n            if (requireBasePath) {\n              // consider this a non-match so the 404 renders\n              return false\n            }\n            // page checker occurs before rewrites so we need to continue\n            // to check those since they don't always require basePath\n            continue\n          }\n\n          parsedUrlUpdated.pathname = currentPathname\n        }\n\n        const result = await testRoute.fn(req, res, newParams, parsedUrlUpdated)\n\n        // The response was handled\n        if (result.finished) {\n          return true\n        }\n\n        // since the fs route didn't match we need to re-add the basePath\n        // to continue checking rewrites with the basePath present\n        if (!keepBasePath) {\n          parsedUrlUpdated.pathname = originalPathname\n        }\n\n        if (result.pathname) {\n          parsedUrlUpdated.pathname = result.pathname\n        }\n\n        if (result.query) {\n          parsedUrlUpdated.query = {\n            ...parsedUrlUpdated.query,\n            ...result.query,\n          }\n        }\n\n        // check filesystem\n        if (testRoute.check === true) {\n          if (await applyCheckTrue(parsedUrlUpdated)) {\n            return true\n          }\n        }\n      }\n    }\n    return false\n  }\n}\n"], "names": [], "mappings": ";;;;;AAGsB,GAAuC,CAAvC,UAAuC;AACrB,GAAoC,CAApC,uBAAoC;AACxC,GAA0C,CAA1C,oBAA0C;AAErD,GAAgD,CAAhD,mBAAgD;;;;;;AAElE,KAAK,CAAC,KAAK,OANI,UAAuC;QAMhD,KAAK,GAAL,KAAK;AAiClB,KAAK,CAAC,gBAAgB,GAAG,GAAG,CAAC,GAAG;KAAE,OAAS;KAAE,QAAU;KAAE,MAAQ;;SAExD,eAAe,CAAC,QAAgB,EAAE,QAAgB,EAAE,CAAC;IAC5D,EAAuG,AAAvG,qGAAuG;WAChG,QAAQ,CAAE,OAAO,CAAC,QAAQ,UAAS,CAAG;AAC/C,CAAC;MAEoB,MAAM;kBAiBvB,QAAQ,OACR,OAAO,OACP,QAAQ,OACR,QAAQ;QACN,WAAW;QACX,UAAU;QACV,QAAQ;QAEV,SAAS,OACT,aAAa,GACb,aAAa,OACb,WAAW,GACX,yBAAyB,GACzB,OAAO,QAgBN,CAAC;aACG,QAAQ,GAAG,QAAQ;aACnB,OAAO,GAAG,OAAO;aACjB,QAAQ,GAAG,QAAQ;aACnB,QAAQ,GAAG,QAAQ;aACnB,SAAS,GAAG,SAAS;aACrB,WAAW,GAAG,WAAW;aACzB,aAAa,GAAG,aAAa;aAC7B,aAAa,GAAG,aAAa;aAC7B,yBAAyB,GAAG,yBAAyB;aACrD,OAAO,GAAG,OAAO;IACxB,CAAC;IAED,gBAAgB,CAAC,MAAqB,OAAO,CAAC;aACvC,aAAa,GAAG,MAAM;IAC7B,CAAC;IAED,UAAU,CAAC,OAAc,EAAE,CAAC;aACrB,QAAQ,CAAC,OAAO,CAAC,OAAO;IAC/B,CAAC;UAEK,OAAO,CACX,GAAoB,EACpB,GAAmB,EACnB,SAA6B,EACX,CAAC;QACnB,EAAkE,AAAlE,gEAAkE;QAClE,KAAK,CAAC,UAAU;;QAChB,KAAK,CAAC,mBAAmB,UAAU,CAAS,GAAuB,CAAC;YAClE,CAAC,OAvH6B,oBAA0C,sBAuHhD,CAAC,OAAO,OAAO,EAAE,QAAQ;YAEjD,EAAE,EAAE,UAAU,CAAC,CAAC,GAAG,CAAC;uBACX,UAAU,CAAC,CAAC;YACrB,CAAC;YACD,KAAK,CAAC,MAAM,QAAQ,WAAW,CAAC,CAAC;YACjC,UAAU,CAAC,CAAC,IAAI,MAAM;mBACf,MAAM;QACf,CAAC;QAED,GAAG,CAAC,gBAAgB,GAAG,SAAS;QAEhC,KAAK,CAAC,cAAc,UAAU,cAAkC,GAAK,CAAC;YACpE,KAAK,CAAC,kBAAkB,GAAG,cAAc,CAAC,QAAQ;YAClD,KAAK,CAAC,UAAU,GAAG,eAAe,MAAM,QAAQ,EAAE,kBAAkB;iBAE/D,KAAK,CAAC,OAAO,SAAS,QAAQ,CAAE,CAAC;gBACpC,KAAK,CAAC,QAAQ,GAAG,OAAO,CAAC,KAAK,CAAC,UAAU;gBAEzC,EAAE,EAAE,QAAQ,EAAE,CAAC;oBACb,cAAc,CAAC,QAAQ,GAAG,UAAU;oBAEpC,KAAK,CAAC,QAAQ,SAAS,OAAO,CAAC,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,QAAQ,EAAE,cAAc;oBAEpE,EAAE,EAAE,QAAQ,CAAC,QAAQ,EAAE,CAAC;+BACf,IAAI;oBACb,CAAC;oBAED,cAAc,CAAC,QAAQ,GAAG,kBAAkB;gBAC9C,CAAC;YACH,CAAC;YACD,GAAG,CAAC,WAAW,SAAS,mBAAmB,CAAC,UAAU;YAEtD,EAAiD,AAAjD,+CAAiD;YACjD,EAAE,GAAG,WAAW,EAAE,CAAC;gBACjB,KAAK,CAAC,oBAAoB,OA1JE,oBAA0C,sBA2JpE,UAAU,OACL,OAAO,EACZ,QAAQ;qBAEL,KAAK,CAAC,YAAY,SAAS,aAAa,CAAE,CAAC;oBAC9C,EAAE,EAAE,YAAY,CAAC,KAAK,CAAC,oBAAoB,GAAG,CAAC;wBAC7C,WAAW,GAAG,IAAI;oBACpB,CAAC;gBACH,CAAC;YACH,CAAC;YAED,EAAmE,AAAnE,iEAAmE;YACnE,EAAE,EAAE,WAAW,EAAE,CAAC;gBAChB,KAAK,CAAC,UAAU,QAAQ,aAAa,CAAC,KAAK,CAAC,cAAc,CAAC,QAAQ;gBACnE,cAAc,CAAC,QAAQ,GAAG,UAAU;gBACpC,cAAc,CAAC,KAAK,CAAC,qBAAqB,IAAG,CAAG;gBAEhD,KAAK,CAAC,MAAM,cAAc,aAAa,CAAC,EAAE,CACxC,GAAG,EACH,GAAG,EACH,UAAU,EACV,cAAc;uBAET,MAAM,CAAC,QAAQ;YACxB,CAAC;QACH,CAAC;QAED,EAME,AANF;;;;;;IAME,AANF,EAME,CAEF,KAAK,CAAC,SAAS;oBACL,OAAO;oBACP,SAAS;oBACT,QAAQ,CAAC,WAAW;oBACpB,QAAQ;YAChB,EAAsE,AAAtE,oEAAsE;YACtE,EAAW,AAAX,SAAW;oBACF,yBAAyB;;oBAG1B,IAAI,GAAE,KAAO;oBACb,IAAI,GAAE,YAAc;oBACpB,eAAe,EAAE,KAAK;oBACtB,KAAK,EAAE,KAAK,EAAC,OAAS;oBACtB,EAAE,SAAS,UAAU,EAAE,UAAU,EAAE,MAAM,EAAE,gBAAgB,GAAK,CAAC;wBAC/D,GAAG,GAAG,QAAQ,MAAK,gBAAgB;wBACnC,QAAQ,OA/MgB,uBAAoC,0BA+MzB,QAAQ,KAAI,CAAG;wBAElD,EAAE,GAAG,QAAQ,EAAE,CAAC;;gCACL,QAAQ,EAAE,KAAK;;wBAC1B,CAAC;wBAED,EAAE,QAAQ,mBAAmB,CAAC,QAAQ,GAAG,CAAC;wCAC5B,aAAa,CAAC,EAAE,CAC1B,UAAU,EACV,UAAU,EACV,MAAM,EACN,gBAAgB;wBAEpB,CAAC;;4BACQ,QAAQ,EAAE,KAAK;;oBAC1B,CAAC;;;oBAID,QAAQ,CAAC,UAAU;oBAClB,QAAQ,CAAC,QAAQ,CAAC,MAAM;;oBAGzB,IAAI,GAAE,KAAO;oBACb,IAAI,GAAE,wBAA0B;oBAChC,eAAe,EAAE,KAAK;oBACtB,KAAK,EAAE,KAAK,EAAC,OAAS;oBACtB,EAAE,SACA,WAAW,EACX,WAAW,EACX,OAAO,EACP,gBAAgB,GACb,CAAC;;4BAEF,QAAQ,QAAQ,cAAc,CAAC,gBAAgB;;oBAEnD,CAAC;;wBAEK,QAAQ,CAAC,QAAQ;;YAI/B,EAAsE,AAAtE,oEAAsE;YACtE,EAAW,AAAX,SAAW;oBACF,yBAAyB;qBAAS,aAAa;;;QAE1D,KAAK,CAAC,qBAAqB,SACnB,QAAQ,IAAK,GAAG,CAAS,gBAAgB;aAE5C,KAAK,CAAC,SAAS,IAAI,SAAS,CAAE,CAAC;YAClC,EAAiE,AAAjE,+DAAiE;YACjE,EAAoE,AAApE,kEAAoE;YACpE,EAA0E,AAA1E,wEAA0E;YAC1E,EAAe,AAAf,aAAe;YACf,GAAG,CAAC,eAAe,GAAG,gBAAgB,CAAC,QAAQ;YAC/C,KAAK,CAAC,gBAAgB,GAAG,eAAe;YACxC,KAAK,CAAC,eAAe,GAAG,SAAS,CAAC,eAAe,KAAK,KAAK;YAC3D,KAAK,CAAC,aAAa,GAAG,gBAAgB,CAAC,GAAG,CAAC,SAAS,CAAC,IAAI;YACzD,KAAK,CAAC,sBAAsB,GAAG,SAAS,CAAC,IAAI,MAAK,sBAAwB;YAC1E,KAAK,CAAC,YAAY,GAAG,aAAa,IAAI,sBAAsB;YAC5D,KAAK,CAAC,UAAU,GAAG,aAAa;YAEhC,KAAK,CAAC,yBAAyB,GAAG,eAAe,MAC1C,QAAQ,EACb,eAAe;YAGjB,EAAE,GAAG,YAAY,EAAE,CAAC;gBAClB,eAAe,GAAG,yBAAyB;YAC7C,CAAC;YAED,KAAK,CAAC,gBAAgB,OArRQ,oBAA0C,sBAsRtE,yBAAyB,OACpB,OAAO;YAEd,KAAK,CAAC,cAAc,GAAG,YAAY,QAAQ,QAAQ;YAEnD,EAAE,EAAE,UAAU,EAAE,CAAC;gBACf,EAAE,GACC,SAAS,CAAC,QAAQ,IACnB,SAAS,CAAC,KAAK,CAAC,YAAY,KAC3B,gBAAgB,CAAC,cAAc,EAChC,CAAC;oBACD,eAAe,MAAM,cAAc,CAAC,CAAC,EAAE,SAAS,CAAC,KAAK,CAAC,YAAY,GACjE,yBAAyB,MAAK,CAAG,SAAQ,yBAAyB;gBAEtE,CAAC;gBAED,EAAE,EACC,GAAG,CAAS,sBAAsB,KAClC,eAAe,CAAC,QAAQ,EAAC,CAAG,IAC7B,CAAC;oBACD,eAAe,KAAI,CAAG;gBACxB,CAAC;YACH,CAAC,MAAM,CAAC;gBACN,eAAe,MACZ,GAAG,CAAS,gBAAgB,GAAG,cAAc,QAE9C,cAAc,IAAI,gBAAgB,CAAC,QAAQ,MAAK,CAAG,SAE/C,gBAAgB,CAAC,QAAQ;YAEjC,CAAC;YAED,GAAG,CAAC,SAAS,GAAG,SAAS,CAAC,KAAK,CAAC,eAAe;YAE/C,EAAE,EAAE,SAAS,CAAC,GAAG,IAAI,SAAS,EAAE,CAAC;gBAC/B,KAAK,CAAC,SAAS,OAvTE,mBAAgD,WAuTtC,GAAG,EAAE,SAAS,CAAC,GAAG,EAAE,gBAAgB,CAAC,KAAK;gBAErE,EAAE,EAAE,SAAS,EAAE,CAAC;oBACd,MAAM,CAAC,MAAM,CAAC,SAAS,EAAE,SAAS;gBACpC,CAAC,MAAM,CAAC;oBACN,SAAS,GAAG,KAAK;gBACnB,CAAC;YACH,CAAC;YAED,EAAsC,AAAtC,oCAAsC;YACtC,EAAE,EAAE,SAAS,EAAE,CAAC;gBACd,EAAgE,AAAhE,8DAAgE;gBAChE,EAAuC,AAAvC,qCAAuC;gBACvC,EAAE,GAAG,YAAY,EAAE,CAAC;oBAClB,EAAE,GAAG,qBAAqB,KAAM,GAAG,CAAS,eAAe,EAAE,CAAC;wBAC5D,EAAE,EAAE,eAAe,EAAE,CAAC;4BACpB,EAA+C,AAA/C,6CAA+C;mCACxC,KAAK;wBACd,CAAC;;oBAIH,CAAC;oBAED,gBAAgB,CAAC,QAAQ,GAAG,eAAe;gBAC7C,CAAC;gBAED,KAAK,CAAC,MAAM,SAAS,SAAS,CAAC,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,SAAS,EAAE,gBAAgB;gBAEvE,EAA2B,AAA3B,yBAA2B;gBAC3B,EAAE,EAAE,MAAM,CAAC,QAAQ,EAAE,CAAC;2BACb,IAAI;gBACb,CAAC;gBAED,EAAiE,AAAjE,+DAAiE;gBACjE,EAA0D,AAA1D,wDAA0D;gBAC1D,EAAE,GAAG,YAAY,EAAE,CAAC;oBAClB,gBAAgB,CAAC,QAAQ,GAAG,gBAAgB;gBAC9C,CAAC;gBAED,EAAE,EAAE,MAAM,CAAC,QAAQ,EAAE,CAAC;oBACpB,gBAAgB,CAAC,QAAQ,GAAG,MAAM,CAAC,QAAQ;gBAC7C,CAAC;gBAED,EAAE,EAAE,MAAM,CAAC,KAAK,EAAE,CAAC;oBACjB,gBAAgB,CAAC,KAAK;2BACjB,gBAAgB,CAAC,KAAK;2BACtB,MAAM,CAAC,KAAK;;gBAEnB,CAAC;gBAED,EAAmB,AAAnB,iBAAmB;gBACnB,EAAE,EAAE,SAAS,CAAC,KAAK,KAAK,IAAI,EAAE,CAAC;oBAC7B,EAAE,QAAQ,cAAc,CAAC,gBAAgB,GAAG,CAAC;+BACpC,IAAI;oBACb,CAAC;gBACH,CAAC;YACH,CAAC;QACH,CAAC;eACM,KAAK;IACd,CAAC;;kBAzUkB,MAAM"}