{"version": 3, "sources": ["../../lib/recursive-copy.ts"], "sourcesContent": ["import path from 'path'\nimport { promises, constants, Dirent, Stats } from 'fs'\nimport { Sema } from 'next/dist/compiled/async-sema'\n\nconst COPYFILE_EXCL = constants.COPYFILE_EXCL\n\nexport async function recursiveCopy(\n  source: string,\n  dest: string,\n  {\n    concurrency = 32,\n    overwrite = false,\n    filter = () => true,\n  }: {\n    concurrency?: number\n    overwrite?: boolean\n    filter?(filePath: string): boolean\n  } = {}\n): Promise<void> {\n  const cwdPath = process.cwd()\n  const from = path.resolve(cwdPath, source)\n  const to = path.resolve(cwdPath, dest)\n\n  const sema = new Sema(concurrency)\n\n  // deep copy the file/directory\n  async function _copy(item: string, lstats?: Stats | Dirent): Promise<void> {\n    const target = item.replace(from, to)\n\n    await sema.acquire()\n\n    if (!lstats) {\n      // after lock on first run\n      lstats = await promises.lstat(from)\n    }\n\n    // readdir & lstat do not follow symbolic links\n    // if part is a symbolic link, follow it with stat\n    let isFile = lstats.isFile()\n    let isDirectory = lstats.isDirectory()\n    if (lstats.isSymbolicLink()) {\n      const stats = await promises.stat(item)\n      isFile = stats.isFile()\n      isDirectory = stats.isDirectory()\n    }\n\n    if (isDirectory) {\n      try {\n        await promises.mkdir(target)\n      } catch (err) {\n        // do not throw `folder already exists` errors\n        if (err.code !== 'EEXIST') {\n          throw err\n        }\n      }\n      sema.release()\n      const files = await promises.readdir(item, { withFileTypes: true })\n      await Promise.all(\n        files.map((file) => _copy(path.join(item, file.name), file))\n      )\n    } else if (\n      isFile &&\n      // before we send the path to filter\n      // we remove the base path (from) and replace \\ by / (windows)\n      filter(item.replace(from, '').replace(/\\\\/g, '/'))\n    ) {\n      await promises.copyFile(\n        item,\n        target,\n        overwrite ? undefined : COPYFILE_EXCL\n      )\n      sema.release()\n    } else {\n      sema.release()\n    }\n  }\n\n  await _copy(from)\n}\n"], "names": [], "mappings": ";;;;QAMsB,aAAa,GAAb,aAAa;AANlB,GAAM,CAAN,KAAM;AAC4B,GAAI,CAAJ,GAAI;AAClC,GAA+B,CAA/B,UAA+B;;;;;;AAEpD,KAAK,CAAC,aAAa,GAHgC,GAAI,WAGvB,aAAa;eAEvB,aAAa,CACjC,MAAc,EACd,IAAY,IAEV,WAAW,EAAG,EAAE,GAChB,SAAS,EAAG,KAAK,GACjB,MAAM,MAAS,IAAI;;GAMN,CAAC;IAChB,KAAK,CAAC,OAAO,GAAG,OAAO,CAAC,GAAG;IAC3B,KAAK,CAAC,IAAI,GApBK,KAAM,SAoBH,OAAO,CAAC,OAAO,EAAE,MAAM;IACzC,KAAK,CAAC,EAAE,GArBO,KAAM,SAqBL,OAAO,CAAC,OAAO,EAAE,IAAI;IAErC,KAAK,CAAC,IAAI,GAAG,GAAG,CArBG,UAA+B,MAqB5B,WAAW;IAEjC,EAA+B,AAA/B,6BAA+B;mBAChB,KAAK,CAAC,IAAY,EAAE,MAAuB,EAAiB,CAAC;QAC1E,KAAK,CAAC,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,EAAE;cAE9B,IAAI,CAAC,OAAO;QAElB,EAAE,GAAG,MAAM,EAAE,CAAC;YACZ,EAA0B,AAA1B,wBAA0B;YAC1B,MAAM,SAhCuC,GAAI,UAgCzB,KAAK,CAAC,IAAI;QACpC,CAAC;QAED,EAA+C,AAA/C,6CAA+C;QAC/C,EAAkD,AAAlD,gDAAkD;QAClD,GAAG,CAAC,MAAM,GAAG,MAAM,CAAC,MAAM;QAC1B,GAAG,CAAC,WAAW,GAAG,MAAM,CAAC,WAAW;QACpC,EAAE,EAAE,MAAM,CAAC,cAAc,IAAI,CAAC;YAC5B,KAAK,CAAC,KAAK,SAxCkC,GAAI,UAwCpB,IAAI,CAAC,IAAI;YACtC,MAAM,GAAG,KAAK,CAAC,MAAM;YACrB,WAAW,GAAG,KAAK,CAAC,WAAW;QACjC,CAAC;QAED,EAAE,EAAE,WAAW,EAAE,CAAC;gBACZ,CAAC;sBA9CwC,GAAI,UA+ChC,KAAK,CAAC,MAAM;YAC7B,CAAC,QAAQ,GAAG,EAAE,CAAC;gBACb,EAA8C,AAA9C,4CAA8C;gBAC9C,EAAE,EAAE,GAAG,CAAC,IAAI,MAAK,MAAQ,GAAE,CAAC;oBAC1B,KAAK,CAAC,GAAG;gBACX,CAAC;YACH,CAAC;YACD,IAAI,CAAC,OAAO;YACZ,KAAK,CAAC,KAAK,SAvDkC,GAAI,UAuDpB,OAAO,CAAC,IAAI;gBAAI,aAAa,EAAE,IAAI;;kBAC1D,OAAO,CAAC,GAAG,CACf,KAAK,CAAC,GAAG,EAAE,IAAI,GAAK,KAAK,CA1DhB,KAAM,SA0DgB,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,GAAG,IAAI;;QAE9D,CAAC,MAAM,EAAE,EACP,MAAM,IACN,EAAoC,AAApC,kCAAoC;QACpC,EAA8D,AAA9D,4DAA8D;QAC9D,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,MAAM,OAAO,SAAQ,CAAG,KAChD,CAAC;kBAhE4C,GAAI,UAiElC,QAAQ,CACrB,IAAI,EACJ,MAAM,EACN,SAAS,GAAG,SAAS,GAAG,aAAa;YAEvC,IAAI,CAAC,OAAO;QACd,CAAC,MAAM,CAAC;YACN,IAAI,CAAC,OAAO;QACd,CAAC;IACH,CAAC;UAEK,KAAK,CAAC,IAAI;AAClB,CAAC"}