{"version": 3, "sources": ["../../../../telemetry/trace/report/to-json.ts"], "sourcesContent": ["import { randomBytes } from 'crypto'\nimport { batcher } from './to-zipkin'\nimport { traceGlobals } from '../shared'\nimport fs from 'fs'\nimport path from 'path'\n\nlet writeStream: fs.WriteStream\nlet traceId: string\nlet batch: ReturnType<typeof batcher> | undefined\n\nconst reportToLocalHost = (\n  name: string,\n  duration: number,\n  timestamp: number,\n  id: string,\n  parentId?: string,\n  attrs?: Object\n) => {\n  const distDir = traceGlobals.get('distDir')\n  if (!distDir) {\n    return\n  }\n\n  if (!traceId) {\n    traceId = process.env.TRACE_ID || randomBytes(8).toString('hex')\n  }\n\n  if (!batch) {\n    batch = batcher(async (events) => {\n      if (!writeStream) {\n        const tracesDir = path.join(distDir, 'traces')\n        await fs.promises.mkdir(tracesDir, { recursive: true })\n        const file = path.join(distDir, 'trace')\n        writeStream = fs.createWriteStream(file, {\n          flags: 'a',\n          encoding: 'utf8',\n        })\n      }\n      const eventsJson = JSON.stringify(events)\n      try {\n        await new Promise<void>((resolve, reject) => {\n          writeStream.write(eventsJson + '\\n', 'utf8', (err) => {\n            err ? reject(err) : resolve()\n          })\n        })\n      } catch (err) {\n        console.log(err)\n      }\n    })\n  }\n\n  batch.report({\n    traceId,\n    parentId,\n    name,\n    id,\n    timestamp,\n    duration,\n    tags: attrs,\n  })\n}\n\nexport default {\n  flushAll: () =>\n    batch\n      ? batch.flushAll().then(() => {\n          writeStream.end('', 'utf8')\n        })\n      : undefined,\n  report: reportToLocalHost,\n}\n"], "names": [], "mappings": ";;;;;AAA4B,GAAQ,CAAR,OAAQ;AACZ,GAAa,CAAb,SAAa;AACR,GAAW,CAAX,OAAW;AACzB,GAAI,CAAJ,GAAI;AACF,GAAM,CAAN,KAAM;;;;;;AAEvB,GAAG,CAAC,WAAW;AACf,GAAG,CAAC,OAAO;AACX,GAAG,CAAC,KAAK;AAET,KAAK,CAAC,iBAAiB,IACrB,IAAY,EACZ,QAAgB,EAChB,SAAiB,EACjB,EAAU,EACV,QAAiB,EACjB,KAAc,GACX,CAAC;IACJ,KAAK,CAAC,OAAO,GAhBc,OAAW,cAgBT,GAAG,EAAC,OAAS;IAC1C,EAAE,GAAG,OAAO,EAAE,CAAC;;IAEf,CAAC;IAED,EAAE,GAAG,OAAO,EAAE,CAAC;QACb,OAAO,GAAG,OAAO,CAAC,GAAG,CAAC,QAAQ,QAxBN,OAAQ,cAwBc,CAAC,EAAE,QAAQ,EAAC,GAAK;IACjE,CAAC;IAED,EAAE,GAAG,KAAK,EAAE,CAAC;QACX,KAAK,OA3Be,SAAa,iBA2BV,MAAM,GAAK,CAAC;YACjC,EAAE,GAAG,WAAW,EAAE,CAAC;gBACjB,KAAK,CAAC,SAAS,GA1BN,KAAM,SA0BQ,IAAI,CAAC,OAAO,GAAE,MAAQ;sBA3BtC,GAAI,SA4BF,QAAQ,CAAC,KAAK,CAAC,SAAS;oBAAI,SAAS,EAAE,IAAI;;gBACpD,KAAK,CAAC,IAAI,GA5BD,KAAM,SA4BG,IAAI,CAAC,OAAO,GAAE,KAAO;gBACvC,WAAW,GA9BJ,GAAI,SA8BM,iBAAiB,CAAC,IAAI;oBACrC,KAAK,GAAE,CAAG;oBACV,QAAQ,GAAE,IAAM;;YAEpB,CAAC;YACD,KAAK,CAAC,UAAU,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM;gBACpC,CAAC;sBACG,GAAG,CAAC,OAAO,EAAQ,OAAO,EAAE,MAAM,GAAK,CAAC;oBAC5C,WAAW,CAAC,KAAK,CAAC,UAAU,IAAG,EAAI,IAAE,IAAM,IAAG,GAAG,GAAK,CAAC;wBACrD,GAAG,GAAG,MAAM,CAAC,GAAG,IAAI,OAAO;oBAC7B,CAAC;gBACH,CAAC;YACH,CAAC,QAAQ,GAAG,EAAE,CAAC;gBACb,OAAO,CAAC,GAAG,CAAC,GAAG;YACjB,CAAC;QACH,CAAC;IACH,CAAC;IAED,KAAK,CAAC,MAAM;QACV,OAAO;QACP,QAAQ;QACR,IAAI;QACJ,EAAE;QACF,SAAS;QACT,QAAQ;QACR,IAAI,EAAE,KAAK;;AAEf,CAAC;;IAGC,QAAQ,MACN,KAAK,GACD,KAAK,CAAC,QAAQ,GAAG,IAAI,KAAO,CAAC;YAC3B,WAAW,CAAC,GAAG,MAAK,IAAM;QAC5B,CAAC,IACD,SAAS;;IACf,MAAM,EAAE,iBAAiB"}