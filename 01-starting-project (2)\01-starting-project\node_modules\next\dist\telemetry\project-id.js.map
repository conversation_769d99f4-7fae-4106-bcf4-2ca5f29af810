{"version": 3, "sources": ["../../telemetry/project-id.ts"], "sourcesContent": ["import { execSync } from 'child_process'\n\n// Q: Why does Next.js need a project ID? Why is it looking at my git remote?\n// A:\n// Next.js' telemetry is and always will be completely anonymous. Because of\n// this, we need a way to differentiate different projects to track feature\n// usage accurately. For example, to prevent a feature from appearing to be\n// constantly `used` and then `unused` when switching between local projects.\n// To reiterate,\n// we **never** can read your actual git remote. The value is hashed one-way\n// with random salt data, making it impossible for us to reverse or try to\n// guess the remote by re-computing hashes.\n\nfunction _getProjectIdByGit() {\n  try {\n    const originBuffer = execSync(\n      `git config --local --get remote.origin.url`,\n      {\n        timeout: 1000,\n        stdio: `pipe`,\n      }\n    )\n\n    return String(originBuffer).trim()\n  } catch (_) {\n    return null\n  }\n}\n\nexport function getRawProjectId(): string {\n  return _getProjectIdByGit() || process.env.REPOSITORY_URL || process.cwd()\n}\n"], "names": [], "mappings": ";;;;QA6BgB,eAAe,GAAf,eAAe;AA7BN,GAAe,CAAf,aAAe;AAExC,EAA6E,AAA7E,2EAA6E;AAC7E,EAAK,AAAL,GAAK;AACL,EAA4E,AAA5E,0EAA4E;AAC5E,EAA2E,AAA3E,yEAA2E;AAC3E,EAA2E,AAA3E,yEAA2E;AAC3E,EAA6E,AAA7E,2EAA6E;AAC7E,EAAgB,AAAhB,cAAgB;AAChB,EAA4E,AAA5E,0EAA4E;AAC5E,EAA0E,AAA1E,wEAA0E;AAC1E,EAA2C,AAA3C,yCAA2C;SAElC,kBAAkB,GAAG,CAAC;QACzB,CAAC;QACH,KAAK,CAAC,YAAY,OAfG,aAAe,YAgBjC,0CAA0C;YAEzC,OAAO,EAAE,IAAI;YACb,KAAK,GAAG,IAAI;;eAIT,MAAM,CAAC,YAAY,EAAE,IAAI;IAClC,CAAC,QAAQ,CAAC,EAAE,CAAC;eACJ,IAAI;IACb,CAAC;AACH,CAAC;SAEe,eAAe,GAAW,CAAC;WAClC,kBAAkB,MAAM,OAAO,CAAC,GAAG,CAAC,cAAc,IAAI,OAAO,CAAC,GAAG;AAC1E,CAAC"}