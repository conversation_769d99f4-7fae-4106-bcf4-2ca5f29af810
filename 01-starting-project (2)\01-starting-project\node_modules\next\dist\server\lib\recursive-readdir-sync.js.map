{"version": 3, "sources": ["../../../server/lib/recursive-readdir-sync.ts"], "sourcesContent": ["import fs from 'fs'\nimport { join } from 'path'\n\n/**\n * Recursively read directory\n * @param  {string[]=[]} arr This doesn't have to be provided, it's used for the recursion\n * @param  {string=dir`} rootDir Used to replace the initial path, only the relative path is left, it's faster than path.relative.\n * @returns Array holding all relative paths\n */\nexport function recursiveReadDirSync(\n  dir: string,\n  arr: string[] = [],\n  rootDir = dir\n): string[] {\n  const result = fs.readdirSync(dir)\n\n  result.forEach((part: string) => {\n    const absolutePath = join(dir, part)\n    const pathStat = fs.statSync(absolutePath)\n\n    if (pathStat.isDirectory()) {\n      recursiveReadDirSync(absolutePath, arr, rootDir)\n      return\n    }\n    arr.push(absolutePath.replace(rootDir, ''))\n  })\n\n  return arr\n}\n"], "names": [], "mappings": ";;;;QASgB,oBAAoB,GAApB,oBAAoB;AATrB,GAAI,CAAJ,GAAI;AACE,GAAM,CAAN,KAAM;;;;;;SAQX,oBAAoB,CAClC,GAAW,EACX,GAAa,OACb,OAAO,GAAG,GAAG,EACH,CAAC;IACX,KAAK,CAAC,MAAM,GAdC,GAAI,SAcC,WAAW,CAAC,GAAG;IAEjC,MAAM,CAAC,OAAO,EAAE,IAAY,GAAK,CAAC;QAChC,KAAK,CAAC,YAAY,OAhBD,KAAM,OAgBG,GAAG,EAAE,IAAI;QACnC,KAAK,CAAC,QAAQ,GAlBH,GAAI,SAkBK,QAAQ,CAAC,YAAY;QAEzC,EAAE,EAAE,QAAQ,CAAC,WAAW,IAAI,CAAC;YAC3B,oBAAoB,CAAC,YAAY,EAAE,GAAG,EAAE,OAAO;;QAEjD,CAAC;QACD,GAAG,CAAC,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,OAAO;IACvC,CAAC;WAEM,GAAG;AACZ,CAAC"}