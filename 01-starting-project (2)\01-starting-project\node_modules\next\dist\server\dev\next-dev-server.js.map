{"version": 3, "sources": ["../../../server/dev/next-dev-server.ts"], "sourcesContent": ["import crypto from 'crypto'\nimport fs from 'fs'\nimport chalk from 'chalk'\nimport { IncomingMessage, ServerResponse } from 'http'\nimport { Worker } from 'jest-worker'\nimport AmpHtmlValidator from 'next/dist/compiled/amphtml-validator'\nimport findUp from 'next/dist/compiled/find-up'\nimport { join as pathJoin, relative, resolve as pathResolve, sep } from 'path'\nimport React from 'react'\nimport { UrlWithParsedQuery } from 'url'\nimport Watchpack from 'watchpack'\nimport { ampValidation } from '../../build/output'\nimport { PUBLIC_DIR_MIDDLEWARE_CONFLICT } from '../../lib/constants'\nimport { fileExists } from '../../lib/file-exists'\nimport { findPagesDir } from '../../lib/find-pages-dir'\nimport loadCustomRoutes, { CustomRoutes } from '../../lib/load-custom-routes'\nimport { verifyTypeScriptSetup } from '../../lib/verifyTypeScriptSetup'\nimport {\n  PHASE_DEVELOPMENT_SERVER,\n  CLIENT_STATIC_FILES_PATH,\n  DEV_CLIENT_PAGES_MANIFEST,\n} from '../../shared/lib/constants'\nimport {\n  getRouteMatcher,\n  getRouteRegex,\n  getSortedRoutes,\n  isDynamicRoute,\n} from '../../shared/lib/router/utils'\nimport { __ApiPreviewProps } from '../api-utils'\nimport Server, {\n  WrappedBuildError,\n  ServerConstructor,\n  FindComponentsResult,\n} from '../next-server'\nimport { normalizePagePath } from '../normalize-page-path'\nimport Router, { Params, route } from '../router'\nimport { eventCliSession } from '../../telemetry/events'\nimport { Telemetry } from '../../telemetry/storage'\nimport { setGlobal } from '../../telemetry/trace'\nimport HotReloader from './hot-reloader'\nimport { findPageFile } from '../lib/find-page-file'\nimport { getNodeOptionsWithoutInspect } from '../lib/utils'\nimport { withCoalescedInvoke } from '../../lib/coalesced-function'\nimport { NextConfig } from '../config'\nimport { ParsedUrlQuery } from 'querystring'\nimport {\n  LoadComponentsReturnType,\n  loadDefaultErrorComponents,\n} from '../load-components'\nimport { DecodeError } from '../../shared/lib/utils'\nimport { parseStack } from '@next/react-dev-overlay/lib/internal/helpers/parseStack'\nimport {\n  createOriginalStackFrame,\n  getSourceById,\n} from '@next/react-dev-overlay/lib/middleware'\nimport * as Log from '../../build/output/log'\n\n// Load ReactDevOverlay only when needed\nlet ReactDevOverlayImpl: React.FunctionComponent\nconst ReactDevOverlay = (props: any) => {\n  if (ReactDevOverlayImpl === undefined) {\n    ReactDevOverlayImpl =\n      require('@next/react-dev-overlay/lib/client').ReactDevOverlay\n  }\n  return ReactDevOverlayImpl(props)\n}\n\nexport default class DevServer extends Server {\n  private devReady: Promise<void>\n  private setDevReady?: Function\n  private webpackWatcher?: Watchpack | null\n  private hotReloader?: HotReloader\n  private isCustomServer: boolean\n  protected sortedRoutes?: string[]\n\n  protected staticPathsWorker: import('jest-worker').Worker & {\n    loadStaticPaths: typeof import('./static-paths-worker').loadStaticPaths\n  }\n\n  constructor(\n    options: ServerConstructor & {\n      conf: NextConfig\n      isNextDevCommand?: boolean\n    }\n  ) {\n    super({ ...options, dev: true })\n    this.renderOpts.dev = true\n    ;(this.renderOpts as any).ErrorDebug = ReactDevOverlay\n    this.devReady = new Promise((resolve) => {\n      this.setDevReady = resolve\n    })\n    ;(this.renderOpts as any).ampSkipValidation =\n      this.nextConfig.experimental?.amp?.skipValidation ?? false\n    ;(this.renderOpts as any).ampValidator = (\n      html: string,\n      pathname: string\n    ) => {\n      const validatorPath =\n        this.nextConfig.experimental &&\n        this.nextConfig.experimental.amp &&\n        this.nextConfig.experimental.amp.validator\n      return AmpHtmlValidator.getInstance(validatorPath).then((validator) => {\n        const result = validator.validateString(html)\n        ampValidation(\n          pathname,\n          result.errors\n            .filter((e) => e.severity === 'ERROR')\n            .filter((e) => this._filterAmpDevelopmentScript(html, e)),\n          result.errors.filter((e) => e.severity !== 'ERROR')\n        )\n      })\n    }\n    if (fs.existsSync(pathJoin(this.dir, 'static'))) {\n      console.warn(\n        `The static directory has been deprecated in favor of the public directory. https://nextjs.org/docs/messages/static-dir-deprecated`\n      )\n    }\n    this.isCustomServer = !options.isNextDevCommand\n    this.pagesDir = findPagesDir(this.dir)\n    this.staticPathsWorker = new Worker(\n      require.resolve('./static-paths-worker'),\n      {\n        maxRetries: 1,\n        numWorkers: this.nextConfig.experimental.cpus,\n        enableWorkerThreads: this.nextConfig.experimental.workerThreads,\n        forkOptions: {\n          env: {\n            ...process.env,\n            // discard --inspect/--inspect-brk flags from process.env.NODE_OPTIONS. Otherwise multiple Node.js debuggers\n            // would be started if user launch Next.js in debugging mode. The number of debuggers is linked to\n            // the number of workers Next.js tries to launch. The only worker users are interested in debugging\n            // is the main Next.js one\n            NODE_OPTIONS: getNodeOptionsWithoutInspect(),\n          },\n        },\n      }\n    ) as Worker & {\n      loadStaticPaths: typeof import('./static-paths-worker').loadStaticPaths\n    }\n\n    this.staticPathsWorker.getStdout().pipe(process.stdout)\n    this.staticPathsWorker.getStderr().pipe(process.stderr)\n  }\n\n  protected readBuildId(): string {\n    return 'development'\n  }\n\n  async addExportPathMapRoutes() {\n    // Makes `next export` exportPathMap work in development mode.\n    // So that the user doesn't have to define a custom server reading the exportPathMap\n    if (this.nextConfig.exportPathMap) {\n      console.log('Defining routes from exportPathMap')\n      const exportPathMap = await this.nextConfig.exportPathMap(\n        {},\n        {\n          dev: true,\n          dir: this.dir,\n          outDir: null,\n          distDir: this.distDir,\n          buildId: this.buildId,\n        }\n      ) // In development we can't give a default path mapping\n      for (const path in exportPathMap) {\n        const { page, query = {} } = exportPathMap[path]\n\n        // We use unshift so that we're sure the routes is defined before Next's default routes\n        this.router.addFsRoute({\n          match: route(path),\n          type: 'route',\n          name: `${path} exportpathmap route`,\n          fn: async (req, res, _params, parsedUrl) => {\n            const { query: urlQuery } = parsedUrl\n\n            Object.keys(urlQuery)\n              .filter((key) => query[key] === undefined)\n              .forEach((key) =>\n                console.warn(\n                  `Url '${path}' defines a query parameter '${key}' that is missing in exportPathMap`\n                )\n              )\n\n            const mergedQuery = { ...urlQuery, ...query }\n\n            await this.render(req, res, page, mergedQuery, parsedUrl)\n            return {\n              finished: true,\n            }\n          },\n        })\n      }\n    }\n  }\n\n  async startWatcher(): Promise<void> {\n    if (this.webpackWatcher) {\n      return\n    }\n\n    const regexPageExtension = new RegExp(\n      `\\\\.+(?:${this.nextConfig.pageExtensions.join('|')})$`\n    )\n\n    let resolved = false\n    return new Promise((resolve, reject) => {\n      const pagesDir = this.pagesDir\n\n      // Watchpack doesn't emit an event for an empty directory\n      fs.readdir(pagesDir!, (_, files) => {\n        if (files?.length) {\n          return\n        }\n\n        if (!resolved) {\n          resolve()\n          resolved = true\n        }\n      })\n\n      let wp = (this.webpackWatcher = new Watchpack())\n      wp.watch([], [pagesDir!], 0)\n\n      wp.on('aggregated', () => {\n        const routedPages = []\n        const knownFiles = wp.getTimeInfoEntries()\n        for (const [fileName, { accuracy }] of knownFiles) {\n          if (accuracy === undefined || !regexPageExtension.test(fileName)) {\n            continue\n          }\n\n          let pageName =\n            '/' + relative(pagesDir!, fileName).replace(/\\\\+/g, '/')\n          pageName = pageName.replace(regexPageExtension, '')\n          pageName = pageName.replace(/\\/index$/, '') || '/'\n\n          routedPages.push(pageName)\n        }\n\n        try {\n          // we serve a separate manifest with all pages for the client in\n          // dev mode so that we can match a page after a rewrite on the client\n          // before it has been built and is populated in the _buildManifest\n          const sortedRoutes = getSortedRoutes(routedPages)\n\n          if (\n            !this.sortedRoutes?.every((val, idx) => val === sortedRoutes[idx])\n          ) {\n            // emit the change so clients fetch the update\n            this.hotReloader!.send(undefined, { devPagesManifest: true })\n          }\n          this.sortedRoutes = sortedRoutes\n\n          this.dynamicRoutes = this.sortedRoutes\n            .filter(isDynamicRoute)\n            .map((page) => ({\n              page,\n              match: getRouteMatcher(getRouteRegex(page)),\n            }))\n\n          this.router.setDynamicRoutes(this.dynamicRoutes)\n\n          if (!resolved) {\n            resolve()\n            resolved = true\n          }\n        } catch (e) {\n          if (!resolved) {\n            reject(e)\n            resolved = true\n          } else {\n            console.warn('Failed to reload dynamic routes:', e)\n          }\n        }\n      })\n    })\n  }\n\n  async stopWatcher(): Promise<void> {\n    if (!this.webpackWatcher) {\n      return\n    }\n\n    this.webpackWatcher.close()\n    this.webpackWatcher = null\n  }\n\n  async prepare(): Promise<void> {\n    await verifyTypeScriptSetup(\n      this.dir,\n      this.pagesDir!,\n      false,\n      !this.nextConfig.images.disableStaticImages\n    )\n\n    this.customRoutes = await loadCustomRoutes(this.nextConfig)\n\n    // reload router\n    const { redirects, rewrites, headers } = this.customRoutes\n\n    if (\n      rewrites.beforeFiles.length ||\n      rewrites.afterFiles.length ||\n      rewrites.fallback.length ||\n      redirects.length ||\n      headers.length\n    ) {\n      this.router = new Router(this.generateRoutes())\n    }\n\n    this.hotReloader = new HotReloader(this.dir, {\n      pagesDir: this.pagesDir!,\n      config: this.nextConfig,\n      previewProps: this.getPreviewProps(),\n      buildId: this.buildId,\n      rewrites,\n    })\n    await super.prepare()\n    await this.addExportPathMapRoutes()\n    await this.hotReloader.start()\n    await this.startWatcher()\n    this.setDevReady!()\n\n    const telemetry = new Telemetry({ distDir: this.distDir })\n    telemetry.record(\n      eventCliSession(PHASE_DEVELOPMENT_SERVER, this.distDir, {\n        webpackVersion: this.hotReloader.isWebpack5 ? 5 : 4,\n        cliCommand: 'dev',\n        isSrcDir: relative(this.dir, this.pagesDir!).startsWith('src'),\n        hasNowJson: !!(await findUp('now.json', { cwd: this.dir })),\n        isCustomServer: this.isCustomServer,\n      })\n    )\n    // This is required by the tracing subsystem.\n    setGlobal('telemetry', telemetry)\n\n    process.on('unhandledRejection', (reason) => {\n      this.logErrorWithOriginalStack(reason, 'unhandledRejection').catch(\n        () => {}\n      )\n    })\n    process.on('uncaughtException', (err) => {\n      this.logErrorWithOriginalStack(err, 'uncaughtException').catch(() => {})\n    })\n  }\n\n  protected async close(): Promise<void> {\n    await this.stopWatcher()\n    await this.staticPathsWorker.end()\n    if (this.hotReloader) {\n      await this.hotReloader.stop()\n    }\n  }\n\n  protected async hasPage(pathname: string): Promise<boolean> {\n    let normalizedPath: string\n\n    try {\n      normalizedPath = normalizePagePath(pathname)\n    } catch (err) {\n      console.error(err)\n      // if normalizing the page fails it means it isn't valid\n      // so it doesn't exist so don't throw and return false\n      // to ensure we return 404 instead of 500\n      return false\n    }\n\n    const pageFile = await findPageFile(\n      this.pagesDir!,\n      normalizedPath,\n      this.nextConfig.pageExtensions\n    )\n    return !!pageFile\n  }\n\n  protected async _beforeCatchAllRender(\n    req: IncomingMessage,\n    res: ServerResponse,\n    params: Params,\n    parsedUrl: UrlWithParsedQuery\n  ): Promise<boolean> {\n    const { pathname } = parsedUrl\n    const pathParts = params.path || []\n    const path = `/${pathParts.join('/')}`\n    // check for a public file, throwing error if there's a\n    // conflicting page\n    let decodedPath: string\n\n    try {\n      decodedPath = decodeURIComponent(path)\n    } catch (_) {\n      throw new DecodeError('failed to decode param')\n    }\n\n    if (await this.hasPublicFile(decodedPath)) {\n      if (await this.hasPage(pathname!)) {\n        const err = new Error(\n          `A conflicting public file and page file was found for path ${pathname} https://nextjs.org/docs/messages/conflicting-public-file-page`\n        )\n        res.statusCode = 500\n        await this.renderError(err, req, res, pathname!, {})\n        return true\n      }\n      await this.servePublic(req, res, pathParts)\n      return true\n    }\n\n    return false\n  }\n\n  async run(\n    req: IncomingMessage,\n    res: ServerResponse,\n    parsedUrl: UrlWithParsedQuery\n  ): Promise<void> {\n    await this.devReady\n\n    const { basePath } = this.nextConfig\n    let originalPathname: string | null = null\n\n    if (basePath && parsedUrl.pathname?.startsWith(basePath)) {\n      // strip basePath before handling dev bundles\n      // If replace ends up replacing the full url it'll be `undefined`, meaning we have to default it to `/`\n      originalPathname = parsedUrl.pathname\n      parsedUrl.pathname = parsedUrl.pathname!.slice(basePath.length) || '/'\n    }\n\n    const { pathname } = parsedUrl\n\n    if (pathname!.startsWith('/_next')) {\n      if (await fileExists(pathJoin(this.publicDir, '_next'))) {\n        throw new Error(PUBLIC_DIR_MIDDLEWARE_CONFLICT)\n      }\n    }\n\n    const { finished = false } = await this.hotReloader!.run(\n      req,\n      res,\n      parsedUrl\n    )\n\n    if (finished) {\n      return\n    }\n\n    if (originalPathname) {\n      // restore the path before continuing so that custom-routes can accurately determine\n      // if they should match against the basePath or not\n      parsedUrl.pathname = originalPathname\n    }\n    try {\n      return await super.run(req, res, parsedUrl)\n    } catch (err) {\n      res.statusCode = 500\n      try {\n        this.logErrorWithOriginalStack(err).catch(() => {})\n        return await this.renderError(err, req, res, pathname!, {\n          __NEXT_PAGE: err?.page || pathname,\n        })\n      } catch (internalErr) {\n        console.error(internalErr)\n        res.end('Internal Server Error')\n      }\n    }\n  }\n\n  private async logErrorWithOriginalStack(\n    possibleError?: any,\n    type?: 'unhandledRejection' | 'uncaughtException'\n  ) {\n    let usedOriginalStack = false\n\n    if (possibleError?.name && possibleError?.stack && possibleError?.message) {\n      const err: Error & { stack: string } = possibleError\n      try {\n        const frames = parseStack(err.stack)\n        const frame = frames[0]\n\n        if (frame.lineNumber && frame?.file) {\n          const compilation = this.hotReloader?.serverStats?.compilation\n          const moduleId = frame.file!.replace(\n            /^(webpack-internal:\\/\\/\\/|file:\\/\\/)/,\n            ''\n          )\n\n          const source = await getSourceById(\n            !!frame.file?.startsWith(sep) || !!frame.file?.startsWith('file:'),\n            moduleId,\n            compilation,\n            this.hotReloader!.isWebpack5\n          )\n\n          const originalFrame = await createOriginalStackFrame({\n            line: frame.lineNumber!,\n            column: frame.column,\n            source,\n            frame,\n            modulePath: moduleId,\n            rootDirectory: this.dir,\n          })\n\n          if (originalFrame) {\n            const { originalCodeFrame, originalStackFrame } = originalFrame\n            const { file, lineNumber, column, methodName } = originalStackFrame\n\n            console.error(\n              chalk.red('error') +\n                ' - ' +\n                `${file} (${lineNumber}:${column}) @ ${methodName}`\n            )\n            console.error(`${chalk.red(err.name)}: ${err.message}`)\n            console.error(originalCodeFrame)\n            usedOriginalStack = true\n          }\n        }\n      } catch (_) {\n        // failed to load original stack using source maps\n        // this un-actionable by users so we don't show the\n        // internal error and only show the provided stack\n      }\n    }\n\n    if (!usedOriginalStack) {\n      if (type) {\n        Log.error(`${type}:`, possibleError)\n      } else {\n        Log.error(possibleError)\n      }\n    }\n  }\n\n  // override production loading of routes-manifest\n  protected getCustomRoutes(): CustomRoutes {\n    // actual routes will be loaded asynchronously during .prepare()\n    return {\n      redirects: [],\n      rewrites: { beforeFiles: [], afterFiles: [], fallback: [] },\n      headers: [],\n    }\n  }\n\n  private _devCachedPreviewProps: __ApiPreviewProps | undefined\n  protected getPreviewProps() {\n    if (this._devCachedPreviewProps) {\n      return this._devCachedPreviewProps\n    }\n    return (this._devCachedPreviewProps = {\n      previewModeId: crypto.randomBytes(16).toString('hex'),\n      previewModeSigningKey: crypto.randomBytes(32).toString('hex'),\n      previewModeEncryptionKey: crypto.randomBytes(32).toString('hex'),\n    })\n  }\n\n  generateRoutes() {\n    const { fsRoutes, ...otherRoutes } = super.generateRoutes()\n\n    // In development we expose all compiled files for react-error-overlay's line show feature\n    // We use unshift so that we're sure the routes is defined before Next's default routes\n    fsRoutes.unshift({\n      match: route('/_next/development/:path*'),\n      type: 'route',\n      name: '_next/development catchall',\n      fn: async (req, res, params) => {\n        const p = pathJoin(this.distDir, ...(params.path || []))\n        await this.serveStatic(req, res, p)\n        return {\n          finished: true,\n        }\n      },\n    })\n\n    fsRoutes.unshift({\n      match: route(\n        `/_next/${CLIENT_STATIC_FILES_PATH}/${this.buildId}/${DEV_CLIENT_PAGES_MANIFEST}`\n      ),\n      type: 'route',\n      name: `_next/${CLIENT_STATIC_FILES_PATH}/${this.buildId}/${DEV_CLIENT_PAGES_MANIFEST}`,\n      fn: async (_req, res) => {\n        res.statusCode = 200\n        res.setHeader('Content-Type', 'application/json; charset=utf-8')\n        res.end(\n          JSON.stringify({\n            pages: this.sortedRoutes,\n          })\n        )\n        return {\n          finished: true,\n        }\n      },\n    })\n\n    fsRoutes.push({\n      match: route('/:path*'),\n      type: 'route',\n      requireBasePath: false,\n      name: 'catchall public directory route',\n      fn: async (req, res, params, parsedUrl) => {\n        const { pathname } = parsedUrl\n        if (!pathname) {\n          throw new Error('pathname is undefined')\n        }\n\n        // Used in development to check public directory paths\n        if (await this._beforeCatchAllRender(req, res, params, parsedUrl)) {\n          return {\n            finished: true,\n          }\n        }\n\n        return {\n          finished: false,\n        }\n      },\n    })\n\n    return { fsRoutes, ...otherRoutes }\n  }\n\n  // In development public files are not added to the router but handled as a fallback instead\n  protected generatePublicRoutes(): never[] {\n    return []\n  }\n\n  // In development dynamic routes cannot be known ahead of time\n  protected getDynamicRoutes(): never[] {\n    return []\n  }\n\n  _filterAmpDevelopmentScript(\n    html: string,\n    event: { line: number; col: number; code: string }\n  ): boolean {\n    if (event.code !== 'DISALLOWED_SCRIPT_TAG') {\n      return true\n    }\n\n    const snippetChunks = html.split('\\n')\n\n    let snippet\n    if (\n      !(snippet = html.split('\\n')[event.line - 1]) ||\n      !(snippet = snippet.substring(event.col))\n    ) {\n      return true\n    }\n\n    snippet = snippet + snippetChunks.slice(event.line).join('\\n')\n    snippet = snippet.substring(0, snippet.indexOf('</script>'))\n\n    return !snippet.includes('data-amp-development-mode-only')\n  }\n\n  protected async getStaticPaths(pathname: string): Promise<{\n    staticPaths: string[] | undefined\n    fallbackMode: false | 'static' | 'blocking'\n  }> {\n    // we lazy load the staticPaths to prevent the user\n    // from waiting on them for the page to load in dev mode\n\n    const __getStaticPaths = async () => {\n      const { publicRuntimeConfig, serverRuntimeConfig, httpAgentOptions } =\n        this.nextConfig\n      const { locales, defaultLocale } = this.nextConfig.i18n || {}\n\n      const paths = await this.staticPathsWorker.loadStaticPaths(\n        this.distDir,\n        pathname,\n        !this.renderOpts.dev && this._isLikeServerless,\n        {\n          publicRuntimeConfig,\n          serverRuntimeConfig,\n        },\n        httpAgentOptions,\n        locales,\n        defaultLocale\n      )\n      return paths\n    }\n    const { paths: staticPaths, fallback } = (\n      await withCoalescedInvoke(__getStaticPaths)(`staticPaths-${pathname}`, [])\n    ).value\n\n    return {\n      staticPaths,\n      fallbackMode:\n        fallback === 'blocking'\n          ? 'blocking'\n          : fallback === true\n          ? 'static'\n          : false,\n    }\n  }\n\n  protected async ensureApiPage(pathname: string) {\n    return this.hotReloader!.ensurePage(pathname)\n  }\n\n  protected async findPageComponents(\n    pathname: string,\n    query: ParsedUrlQuery = {},\n    params: Params | null = null\n  ): Promise<FindComponentsResult | null> {\n    await this.devReady\n    const compilationErr = await this.getCompilationError(pathname)\n    if (compilationErr) {\n      // Wrap build errors so that they don't get logged again\n      throw new WrappedBuildError(compilationErr)\n    }\n    try {\n      await this.hotReloader!.ensurePage(pathname)\n      return super.findPageComponents(pathname, query, params)\n    } catch (err) {\n      if ((err as any).code !== 'ENOENT') {\n        throw err\n      }\n      return null\n    }\n  }\n\n  protected async getFallbackErrorComponents(): Promise<LoadComponentsReturnType | null> {\n    await this.hotReloader!.buildFallbackError()\n    // Build the error page to ensure the fallback is built too.\n    // TODO: See if this can be moved into hotReloader or removed.\n    await this.hotReloader!.ensurePage('/_error')\n    return await loadDefaultErrorComponents(this.distDir)\n  }\n\n  protected setImmutableAssetCacheControl(res: ServerResponse): void {\n    res.setHeader('Cache-Control', 'no-store, must-revalidate')\n  }\n\n  private servePublic(\n    req: IncomingMessage,\n    res: ServerResponse,\n    pathParts: string[]\n  ): Promise<void> {\n    const p = pathJoin(this.publicDir, ...pathParts)\n    return this.serveStatic(req, res, p)\n  }\n\n  async hasPublicFile(path: string): Promise<boolean> {\n    try {\n      const info = await fs.promises.stat(pathJoin(this.publicDir, path))\n      return info.isFile()\n    } catch (_) {\n      return false\n    }\n  }\n\n  async getCompilationError(page: string): Promise<any> {\n    const errors = await this.hotReloader!.getCompilationErrors(page)\n    if (errors.length === 0) return\n\n    // Return the very first error we found.\n    return errors[0]\n  }\n\n  protected isServeableUrl(untrustedFileUrl: string): boolean {\n    // This method mimics what the version of `send` we use does:\n    // 1. decodeURIComponent:\n    //    https://github.com/pillarjs/send/blob/0.17.1/index.js#L989\n    //    https://github.com/pillarjs/send/blob/0.17.1/index.js#L518-L522\n    // 2. resolve:\n    //    https://github.com/pillarjs/send/blob/de073ed3237ade9ff71c61673a34474b30e5d45b/index.js#L561\n\n    let decodedUntrustedFilePath: string\n    try {\n      // (1) Decode the URL so we have the proper file name\n      decodedUntrustedFilePath = decodeURIComponent(untrustedFileUrl)\n    } catch {\n      return false\n    }\n\n    // (2) Resolve \"up paths\" to determine real request\n    const untrustedFilePath = pathResolve(decodedUntrustedFilePath)\n\n    // don't allow null bytes anywhere in the file path\n    if (untrustedFilePath.indexOf('\\0') !== -1) {\n      return false\n    }\n\n    // During development mode, files can be added while the server is running.\n    // Checks for .next/static, .next/server, static and public.\n    // Note that in development .next/server is available for error reporting purposes.\n    // see `packages/next/server/next-server.ts` for more details.\n    if (\n      untrustedFilePath.startsWith(pathJoin(this.distDir, 'static') + sep) ||\n      untrustedFilePath.startsWith(pathJoin(this.distDir, 'server') + sep) ||\n      untrustedFilePath.startsWith(pathJoin(this.dir, 'static') + sep) ||\n      untrustedFilePath.startsWith(pathJoin(this.dir, 'public') + sep)\n    ) {\n      return true\n    }\n\n    return false\n  }\n}\n"], "names": [], "mappings": ";;;;;AAAmB,GAAQ,CAAR,OAAQ;AACZ,GAAI,CAAJ,GAAI;AACD,GAAO,CAAP,MAAO;AAEF,GAAa,CAAb,WAAa;AACP,GAAsC,CAAtC,iBAAsC;AAChD,GAA4B,CAA5B,OAA4B;AACyB,GAAM,CAAN,KAAM;AAGxD,GAAW,CAAX,UAAW;AACH,GAAoB,CAApB,OAAoB;AACH,GAAqB,CAArB,UAAqB;AACzC,GAAuB,CAAvB,WAAuB;AACrB,GAA0B,CAA1B,aAA0B;AACR,GAA8B,CAA9B,iBAA8B;AACvC,GAAiC,CAAjC,sBAAiC;AAKhE,GAA4B,CAA5B,WAA4B;AAM5B,GAA+B,CAA/B,MAA+B;AAM/B,GAAgB,CAAhB,WAAgB;AACW,GAAwB,CAAxB,kBAAwB;AACpB,GAAW,CAAX,OAAW;AACjB,GAAwB,CAAxB,OAAwB;AAC9B,GAAyB,CAAzB,QAAyB;AACzB,GAAuB,CAAvB,MAAuB;AACzB,GAAgB,CAAhB,YAAgB;AACX,GAAuB,CAAvB,aAAuB;AACP,GAAc,CAAd,OAAc;AACvB,GAA8B,CAA9B,kBAA8B;AAM3D,GAAoB,CAApB,eAAoB;AACC,GAAwB,CAAxB,OAAwB;AACzB,GAAyD,CAAzD,WAAyD;AAI7E,GAAwC,CAAxC,WAAwC;AACnC,GAAG,CAAH,GAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEf,EAAwC,AAAxC,sCAAwC;AACxC,GAAG,CAAC,mBAAmB;AACvB,KAAK,CAAC,eAAe,IAAI,KAAU,GAAK,CAAC;IACvC,EAAE,EAAE,mBAAmB,KAAK,SAAS,EAAE,CAAC;QACtC,mBAAmB,GACjB,OAAO,EAAC,kCAAoC,GAAE,eAAe;IACjE,CAAC;WACM,mBAAmB,CAAC,KAAK;AAClC,CAAC;MAEoB,SAAS,SAlCvB,WAAgB;gBA+CnB,OAGC,CACD,CAAC;YAQC,GAA4B;QAP9B,KAAK;eAAM,OAAO;YAAE,GAAG,EAAE,IAAI;;aACxB,UAAU,CAAC,GAAG,GAAG,IAAI;aACnB,UAAU,CAAS,UAAU,GAAG,eAAe;aACjD,QAAQ,GAAG,GAAG,CAAC,OAAO,EAAE,OAAO,GAAK,CAAC;iBACnC,WAAW,GAAG,OAAO;QAC5B,CAAC;YAEC,IAAiD;aAD5C,UAAU,CAAS,iBAAiB,IACzC,IAAiD,IAAjD,GAA4B,QAAvB,UAAU,CAAC,YAAY,cAA5B,GAA4B,UAA5B,CAAiC,QAAjC,CAAiC,WAAjC,GAA4B,CAAE,GAAG,4BAAjC,CAAiC,QAAjC,CAAiC,QAAE,cAAc,cAAjD,IAAiD,cAAjD,IAAiD,GAAI,KAAK;aACrD,UAAU,CAAS,YAAY,IACpC,IAAY,EACZ,QAAgB,GACb,CAAC;YACJ,KAAK,CAAC,aAAa,QACZ,UAAU,CAAC,YAAY,SACvB,UAAU,CAAC,YAAY,CAAC,GAAG,SAC3B,UAAU,CAAC,YAAY,CAAC,GAAG,CAAC,SAAS;mBA/FrB,iBAAsC,SAgGrC,WAAW,CAAC,aAAa,EAAE,IAAI,EAAE,SAAS,GAAK,CAAC;gBACtE,KAAK,CAAC,MAAM,GAAG,SAAS,CAAC,cAAc,CAAC,IAAI;oBA3FtB,OAAoB,gBA6FxC,QAAQ,EACR,MAAM,CAAC,MAAM,CACV,MAAM,EAAE,CAAC,GAAK,CAAC,CAAC,QAAQ,MAAK,KAAO;kBACpC,MAAM,EAAE,CAAC,QAAU,2BAA2B,CAAC,IAAI,EAAE,CAAC;mBACzD,MAAM,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC,GAAK,CAAC,CAAC,QAAQ,MAAK,KAAO;;YAEtD,CAAC;QACH,CAAC;QACD,EAAE,EA/GS,GAAI,SA+GR,UAAU,KAzGmD,KAAM,YAyG1C,GAAG,GAAE,MAAQ,KAAI,CAAC;YAChD,OAAO,CAAC,IAAI,EACT,iIAAiI;QAEtI,CAAC;aACI,cAAc,IAAI,OAAO,CAAC,gBAAgB;aAC1C,QAAQ,OAxGY,aAA0B,oBAwGjB,GAAG;aAChC,iBAAiB,GAAG,GAAG,CAnHT,WAAa,QAoH9B,OAAO,CAAC,OAAO,EAAC,qBAAuB;YAErC,UAAU,EAAE,CAAC;YACb,UAAU,OAAO,UAAU,CAAC,YAAY,CAAC,IAAI;YAC7C,mBAAmB,OAAO,UAAU,CAAC,YAAY,CAAC,aAAa;YAC/D,WAAW;gBACT,GAAG;uBACE,OAAO,CAAC,GAAG;oBACd,EAA4G,AAA5G,0GAA4G;oBAC5G,EAAkG,AAAlG,gGAAkG;oBAClG,EAAmG,AAAnG,iGAAmG;oBACnG,EAA0B,AAA1B,wBAA0B;oBAC1B,YAAY,MA3FqB,OAAc;;;;aAmGlD,iBAAiB,CAAC,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM;aACjD,iBAAiB,CAAC,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM;IACxD,CAAC;IAES,WAAW,GAAW,CAAC;gBACxB,WAAa;IACtB,CAAC;UAEK,sBAAsB,GAAG,CAAC;QAC9B,EAA8D,AAA9D,4DAA8D;QAC9D,EAAoF,AAApF,kFAAoF;QACpF,EAAE,OAAO,UAAU,CAAC,aAAa,EAAE,CAAC;YAClC,OAAO,CAAC,GAAG,EAAC,kCAAoC;YAChD,KAAK,CAAC,aAAa,cAAc,UAAU,CAAC,aAAa;;gBAGrD,GAAG,EAAE,IAAI;gBACT,GAAG,OAAO,GAAG;gBACb,MAAM,EAAE,IAAI;gBACZ,OAAO,OAAO,OAAO;gBACrB,OAAO,OAAO,OAAO;cAEvB,CAAsD,AAAtD,EAAsD,AAAtD,oDAAsD;;gBACnD,KAAK,CAAC,IAAI,IAAI,aAAa,CAAE,CAAC;gBACjC,KAAK,GAAG,IAAI,GAAE,KAAK;uBAAU,aAAa,CAAC,IAAI;gBAE/C,EAAuF,AAAvF,qFAAuF;qBAClF,MAAM,CAAC,UAAU;oBACpB,KAAK,MArIuB,OAAW,QAqI1B,IAAI;oBACjB,IAAI,GAAE,KAAO;oBACb,IAAI,KAAK,IAAI,CAAC,oBAAoB;oBAClC,EAAE,SAAS,GAAG,EAAE,GAAG,EAAE,OAAO,EAAE,SAAS,GAAK,CAAC;wBAC3C,KAAK,GAAG,KAAK,EAAE,QAAQ,MAAK,SAAS;wBAErC,MAAM,CAAC,IAAI,CAAC,QAAQ,EACjB,MAAM,EAAE,GAAG,GAAK,KAAK,CAAC,GAAG,MAAM,SAAS;0BACxC,OAAO,EAAE,GAAG,GACX,OAAO,CAAC,IAAI,EACT,KAAK,EAAE,IAAI,CAAC,6BAA6B,EAAE,GAAG,CAAC,kCAAkC;;wBAIxF,KAAK,CAAC,WAAW;+BAAQ,QAAQ;+BAAK,KAAK;;mCAEhC,MAAM,CAAC,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,WAAW,EAAE,SAAS;;4BAEtD,QAAQ,EAAE,IAAI;;oBAElB,CAAC;;YAEL,CAAC;QACH,CAAC;IACH,CAAC;UAEK,YAAY,GAAkB,CAAC;QACnC,EAAE,OAAO,cAAc,EAAE,CAAC;;QAE1B,CAAC;QAED,KAAK,CAAC,kBAAkB,GAAG,GAAG,CAAC,MAAM,EAClC,OAAO,OAAO,UAAU,CAAC,cAAc,CAAC,IAAI,EAAC,CAAG,GAAE,EAAE;QAGvD,GAAG,CAAC,QAAQ,GAAG,KAAK;eACb,GAAG,CAAC,OAAO,EAAE,OAAO,EAAE,MAAM,GAAK,CAAC;YACvC,KAAK,CAAC,QAAQ,QAAQ,QAAQ;YAE9B,EAAyD,AAAzD,uDAAyD;YA9MhD,GAAI,SA+MV,OAAO,CAAC,QAAQ,GAAI,CAAC,EAAE,KAAK,GAAK,CAAC;gBACnC,EAAE,EAAE,KAAK,aAAL,KAAK,UAAL,CAAa,QAAb,CAAa,GAAb,KAAK,CAAE,MAAM,EAAE,CAAC;;gBAEpB,CAAC;gBAED,EAAE,GAAG,QAAQ,EAAE,CAAC;oBACd,OAAO;oBACP,QAAQ,GAAG,IAAI;gBACjB,CAAC;YACH,CAAC;YAED,GAAG,CAAC,EAAE,QAAS,cAAc,GAAG,GAAG,CAjNnB,UAAW;YAkN3B,EAAE,CAAC,KAAK;gBAAM,QAAQ;eAAI,CAAC;YAE3B,EAAE,CAAC,EAAE,EAAC,UAAY,OAAQ,CAAC;gBACzB,KAAK,CAAC,WAAW;gBACjB,KAAK,CAAC,UAAU,GAAG,EAAE,CAAC,kBAAkB;qBACnC,KAAK,EAAE,QAAQ,IAAI,QAAQ,QAAO,UAAU,CAAE,CAAC;oBAClD,EAAE,EAAE,QAAQ,KAAK,SAAS,KAAK,kBAAkB,CAAC,IAAI,CAAC,QAAQ,GAAG,CAAC;;oBAEnE,CAAC;oBAED,GAAG,CAAC,QAAQ,IACV,CAAG,QAhOyD,KAAM,WAgOnD,QAAQ,EAAG,QAAQ,EAAE,OAAO,UAAS,CAAG;oBACzD,QAAQ,GAAG,QAAQ,CAAC,OAAO,CAAC,kBAAkB;oBAC9C,QAAQ,GAAG,QAAQ,CAAC,OAAO,qBAAoB,CAAG;oBAElD,WAAW,CAAC,IAAI,CAAC,QAAQ;gBAC3B,CAAC;oBAEG,CAAC;wBAOA,IAAiB;oBANpB,EAAgE,AAAhE,8DAAgE;oBAChE,EAAqE,AAArE,mEAAqE;oBACrE,EAAkE,AAAlE,gEAAkE;oBAClE,KAAK,CAAC,YAAY,OAvNrB,MAA+B,kBAuNS,WAAW;oBAEhD,EAAE,KACC,IAAiB,QAAZ,YAAY,cAAjB,IAAiB,UAAjB,CAAwB,QAAxB,CAAwB,GAAxB,IAAiB,CAAE,KAAK,EAAE,GAAG,EAAE,GAAG,GAAK,GAAG,KAAK,YAAY,CAAC,GAAG;wBAChE,CAAC;wBACD,EAA8C,AAA9C,4CAA8C;6BACzC,WAAW,CAAE,IAAI,CAAC,SAAS;4BAAI,gBAAgB,EAAE,IAAI;;oBAC5D,CAAC;yBACI,YAAY,GAAG,YAAY;yBAE3B,aAAa,QAAQ,YAAY,CACnC,MAAM,CAlOZ,MAA+B,iBAmOzB,GAAG,EAAE,IAAI;4BACR,IAAI;4BACJ,KAAK,MArOZ,MAA+B,sBAA/B,MAA+B,gBAqOa,IAAI;;;yBAGxC,MAAM,CAAC,gBAAgB,MAAM,aAAa;oBAE/C,EAAE,GAAG,QAAQ,EAAE,CAAC;wBACd,OAAO;wBACP,QAAQ,GAAG,IAAI;oBACjB,CAAC;gBACH,CAAC,QAAQ,CAAC,EAAE,CAAC;oBACX,EAAE,GAAG,QAAQ,EAAE,CAAC;wBACd,MAAM,CAAC,CAAC;wBACR,QAAQ,GAAG,IAAI;oBACjB,CAAC,MAAM,CAAC;wBACN,OAAO,CAAC,IAAI,EAAC,gCAAkC,GAAE,CAAC;oBACpD,CAAC;gBACH,CAAC;YACH,CAAC;QACH,CAAC;IACH,CAAC;UAEK,WAAW,GAAkB,CAAC;QAClC,EAAE,QAAQ,cAAc,EAAE,CAAC;;QAE3B,CAAC;aAEI,cAAc,CAAC,KAAK;aACpB,cAAc,GAAG,IAAI;IAC5B,CAAC;UAEK,OAAO,GAAkB,CAAC;kBA9QI,sBAAiC,6BAgR5D,GAAG,OACH,QAAQ,EACb,KAAK,QACC,UAAU,CAAC,MAAM,CAAC,mBAAmB;aAGxC,YAAY,aAvR0B,iBAA8B,eAuRzB,UAAU;QAE1D,EAAgB,AAAhB,cAAgB;QAChB,KAAK,GAAG,SAAS,GAAE,QAAQ,GAAE,OAAO,WAAU,YAAY;QAE1D,EAAE,EACA,QAAQ,CAAC,WAAW,CAAC,MAAM,IAC3B,QAAQ,CAAC,UAAU,CAAC,MAAM,IAC1B,QAAQ,CAAC,QAAQ,CAAC,MAAM,IACxB,SAAS,CAAC,MAAM,IAChB,OAAO,CAAC,MAAM,EACd,CAAC;iBACI,MAAM,GAAG,GAAG,CA/Qe,OAAW,cA+Qb,cAAc;QAC9C,CAAC;aAEI,WAAW,GAAG,GAAG,CA9QF,YAAgB,cA8QI,GAAG;YACzC,QAAQ,OAAO,QAAQ;YACvB,MAAM,OAAO,UAAU;YACvB,YAAY,OAAO,eAAe;YAClC,OAAO,OAAO,OAAO;YACrB,QAAQ;;cAEJ,KAAK,CAAC,OAAO;mBACR,sBAAsB;mBACtB,WAAW,CAAC,KAAK;mBACjB,YAAY;aAClB,WAAW;QAEhB,KAAK,CAAC,SAAS,GAAG,GAAG,CA7RC,QAAyB;YA6Rb,OAAO,OAAO,OAAO;;QACvD,SAAS,CAAC,MAAM,KA/RY,OAAwB,kBAfjD,WAA4B,gCA+SkB,OAAO;YACpD,cAAc,OAAO,WAAW,CAAC,UAAU,GAAG,CAAC,GAAG,CAAC;YACnD,UAAU,GAAE,GAAK;YACjB,QAAQ,MAhUwD,KAAM,gBAgU9C,GAAG,OAAO,QAAQ,EAAG,UAAU,EAAC,GAAK;YAC7D,UAAU,cAlUC,OAA4B,WAkUX,QAAU;gBAAI,GAAG,OAAO,GAAG;;YACvD,cAAc,OAAO,cAAc;;QAGvC,EAA6C,AAA7C,2CAA6C;YAtSvB,MAAuB,aAuSnC,SAAW,GAAE,SAAS;QAEhC,OAAO,CAAC,EAAE,EAAC,kBAAoB,IAAG,MAAM,GAAK,CAAC;iBACvC,yBAAyB,CAAC,MAAM,GAAE,kBAAoB,GAAE,KAAK,KAC1D,CAAC;YAAA,CAAC;QAEZ,CAAC;QACD,OAAO,CAAC,EAAE,EAAC,iBAAmB,IAAG,GAAG,GAAK,CAAC;iBACnC,yBAAyB,CAAC,GAAG,GAAE,iBAAmB,GAAE,KAAK,KAAO,CAAC;YAAA,CAAC;QACzE,CAAC;IACH,CAAC;UAEe,KAAK,GAAkB,CAAC;mBAC3B,WAAW;mBACX,iBAAiB,CAAC,GAAG;QAChC,EAAE,OAAO,WAAW,EAAE,CAAC;uBACV,WAAW,CAAC,IAAI;QAC7B,CAAC;IACH,CAAC;UAEe,OAAO,CAAC,QAAgB,EAAoB,CAAC;QAC3D,GAAG,CAAC,cAAc;YAEd,CAAC;YACH,cAAc,OAnUc,kBAAwB,oBAmUjB,QAAQ;QAC7C,CAAC,QAAQ,GAAG,EAAE,CAAC;YACb,OAAO,CAAC,KAAK,CAAC,GAAG;YACjB,EAAwD,AAAxD,sDAAwD;YACxD,EAAsD,AAAtD,oDAAsD;YACtD,EAAyC,AAAzC,uCAAyC;mBAClC,KAAK;QACd,CAAC;QAED,KAAK,CAAC,QAAQ,aAtUW,aAAuB,oBAuUzC,QAAQ,EACb,cAAc,OACT,UAAU,CAAC,cAAc;iBAEvB,QAAQ;IACnB,CAAC;UAEe,qBAAqB,CACnC,GAAoB,EACpB,GAAmB,EACnB,MAAc,EACd,SAA6B,EACX,CAAC;QACnB,KAAK,GAAG,QAAQ,MAAK,SAAS;QAC9B,KAAK,CAAC,SAAS,GAAG,MAAM,CAAC,IAAI;QAC7B,KAAK,CAAC,IAAI,IAAI,CAAC,EAAE,SAAS,CAAC,IAAI,EAAC,CAAG;QACnC,EAAuD,AAAvD,qDAAuD;QACvD,EAAmB,AAAnB,iBAAmB;QACnB,GAAG,CAAC,WAAW;YAEX,CAAC;YACH,WAAW,GAAG,kBAAkB,CAAC,IAAI;QACvC,CAAC,QAAQ,CAAC,EAAE,CAAC;YACX,KAAK,CAAC,GAAG,CArVa,OAAwB,cAqVxB,sBAAwB;QAChD,CAAC;QAED,EAAE,aAAa,aAAa,CAAC,WAAW,GAAG,CAAC;YAC1C,EAAE,aAAa,OAAO,CAAC,QAAQ,GAAI,CAAC;gBAClC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,KAAK,EAClB,2DAA2D,EAAE,QAAQ,CAAC,8DAA8D;gBAEvI,GAAG,CAAC,UAAU,GAAG,GAAG;2BACT,WAAW,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,QAAQ;;uBACvC,IAAI;YACb,CAAC;uBACU,WAAW,CAAC,GAAG,EAAE,GAAG,EAAE,SAAS;mBACnC,IAAI;QACb,CAAC;eAEM,KAAK;IACd,CAAC;UAEK,GAAG,CACP,GAAoB,EACpB,GAAmB,EACnB,SAA6B,EACd,CAAC;YAMA,IAAkB;mBALvB,QAAQ;QAEnB,KAAK,GAAG,QAAQ,WAAU,UAAU;QACpC,GAAG,CAAC,gBAAgB,GAAkB,IAAI;QAE1C,EAAE,EAAE,QAAQ,MAAI,IAAkB,GAAlB,SAAS,CAAC,QAAQ,cAAlB,IAAkB,UAAlB,CAA8B,QAA9B,CAA8B,GAA9B,IAAkB,CAAE,UAAU,CAAC,QAAQ,IAAG,CAAC;YACzD,EAA6C,AAA7C,2CAA6C;YAC7C,EAAuG,AAAvG,qGAAuG;YACvG,gBAAgB,GAAG,SAAS,CAAC,QAAQ;YACrC,SAAS,CAAC,QAAQ,GAAG,SAAS,CAAC,QAAQ,CAAE,KAAK,CAAC,QAAQ,CAAC,MAAM,MAAK,CAAG;QACxE,CAAC;QAED,KAAK,GAAG,QAAQ,MAAK,SAAS;QAE9B,EAAE,EAAE,QAAQ,CAAE,UAAU,EAAC,MAAQ,IAAG,CAAC;YACnC,EAAE,YAhamB,WAAuB,iBANsB,KAAM,YAsarC,SAAS,GAAE,KAAO,KAAI,CAAC;gBACxD,KAAK,CAAC,GAAG,CAAC,KAAK,CAlawB,UAAqB;YAma9D,CAAC;QACH,CAAC;QAED,KAAK,GAAG,QAAQ,EAAG,KAAK,iBAAgB,WAAW,CAAE,GAAG,CACtD,GAAG,EACH,GAAG,EACH,SAAS;QAGX,EAAE,EAAE,QAAQ,EAAE,CAAC;;QAEf,CAAC;QAED,EAAE,EAAE,gBAAgB,EAAE,CAAC;YACrB,EAAoF,AAApF,kFAAoF;YACpF,EAAmD,AAAnD,iDAAmD;YACnD,SAAS,CAAC,QAAQ,GAAG,gBAAgB;QACvC,CAAC;YACG,CAAC;yBACU,KAAK,CAAC,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,SAAS;QAC5C,CAAC,QAAQ,GAAG,EAAE,CAAC;YACb,GAAG,CAAC,UAAU,GAAG,GAAG;gBAChB,CAAC;qBACE,yBAAyB,CAAC,GAAG,EAAE,KAAK,KAAO,CAAC;gBAAA,CAAC;kCAChC,WAAW,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,QAAQ;oBACnD,WAAW,GAAE,GAAG,aAAH,GAAG,UAAH,CAAS,QAAT,CAAS,GAAT,GAAG,CAAE,IAAI,KAAI,QAAQ;;YAEtC,CAAC,QAAQ,WAAW,EAAE,CAAC;gBACrB,OAAO,CAAC,KAAK,CAAC,WAAW;gBACzB,GAAG,CAAC,GAAG,EAAC,qBAAuB;YACjC,CAAC;QACH,CAAC;IACH,CAAC;UAEa,yBAAyB,CACrC,aAAmB,EACnB,IAAiD,EACjD,CAAC;QACD,GAAG,CAAC,iBAAiB,GAAG,KAAK;QAE7B,EAAE,GAAE,aAAa,aAAb,aAAa,UAAb,CAAmB,QAAnB,CAAmB,GAAnB,aAAa,CAAE,IAAI,MAAI,aAAa,aAAb,aAAa,UAAb,CAAoB,QAApB,CAAoB,GAApB,aAAa,CAAE,KAAK,MAAI,aAAa,aAAb,aAAa,UAAb,CAAsB,QAAtB,CAAsB,GAAtB,aAAa,CAAE,OAAO,GAAE,CAAC;YAC1E,KAAK,CAAC,GAAG,GAA8B,aAAa;gBAChD,CAAC;gBACH,KAAK,CAAC,MAAM,OAxaO,WAAyD,aAwalD,GAAG,CAAC,KAAK;gBACnC,KAAK,CAAC,KAAK,GAAG,MAAM,CAAC,CAAC;gBAEtB,EAAE,EAAE,KAAK,CAAC,UAAU,KAAI,KAAK,aAAL,KAAK,UAAL,CAAW,QAAX,CAAW,GAAX,KAAK,CAAE,IAAI,GAAE,CAAC;wBAChB,IAAgB,QAOhC,IAAU,EAAuB,IAAU;oBAP/C,KAAK,CAAC,WAAW,IAAG,IAAgB,QAAX,WAAW,cAAhB,IAAgB,UAAhB,CAA6B,QAA7B,CAA6B,WAA7B,IAAgB,CAAE,WAAW,4BAA7B,CAA6B,QAA7B,CAA6B,QAAE,WAAW;oBAC9D,KAAK,CAAC,QAAQ,GAAG,KAAK,CAAC,IAAI,CAAE,OAAO;oBAKpC,KAAK,CAAC,MAAM,aA9af,WAAwC,oBA+ajC,IAAU,GAAV,KAAK,CAAC,IAAI,cAAV,IAAU,UAAV,CAAsB,QAAtB,CAAsB,GAAtB,IAAU,CAAE,UAAU,CA9doC,KAAM,cA8d/B,IAAU,GAAV,KAAK,CAAC,IAAI,cAAV,IAAU,UAAV,CAAsB,QAAtB,CAAsB,GAAtB,IAAU,CAAE,UAAU,EAAC,KAAO,KACjE,QAAQ,EACR,WAAW,OACN,WAAW,CAAE,UAAU;oBAG9B,KAAK,CAAC,aAAa,aArbtB,WAAwC;wBAsbnC,IAAI,EAAE,KAAK,CAAC,UAAU;wBACtB,MAAM,EAAE,KAAK,CAAC,MAAM;wBACpB,MAAM;wBACN,KAAK;wBACL,UAAU,EAAE,QAAQ;wBACpB,aAAa,OAAO,GAAG;;oBAGzB,EAAE,EAAE,aAAa,EAAE,CAAC;wBAClB,KAAK,GAAG,iBAAiB,GAAE,kBAAkB,MAAK,aAAa;wBAC/D,KAAK,GAAG,IAAI,GAAE,UAAU,GAAE,MAAM,GAAE,UAAU,MAAK,kBAAkB;wBAEnE,OAAO,CAAC,KAAK,CAtfP,MAAO,SAufL,GAAG,EAAC,KAAO,MACf,GAAK,OACF,IAAI,CAAC,EAAE,EAAE,UAAU,CAAC,CAAC,EAAE,MAAM,CAAC,IAAI,EAAE,UAAU;wBAErD,OAAO,CAAC,KAAK,IA3fP,MAAO,SA2fU,GAAG,CAAC,GAAG,CAAC,IAAI,EAAE,EAAE,EAAE,GAAG,CAAC,OAAO;wBACpD,OAAO,CAAC,KAAK,CAAC,iBAAiB;wBAC/B,iBAAiB,GAAG,IAAI;oBAC1B,CAAC;gBACH,CAAC;YACH,CAAC,QAAQ,CAAC,EAAE,CAAC;YACX,EAAkD,AAAlD,gDAAkD;YAClD,EAAmD,AAAnD,iDAAmD;YACnD,EAAkD,AAAlD,gDAAkD;YACpD,CAAC;QACH,CAAC;QAED,EAAE,GAAG,iBAAiB,EAAE,CAAC;YACvB,EAAE,EAAE,IAAI,EAAE,CAAC;gBAndL,GAAG,CAodH,KAAK,IAAI,IAAI,CAAC,CAAC,GAAG,aAAa;YACrC,CAAC,MAAM,CAAC;gBArdF,GAAG,CAsdH,KAAK,CAAC,aAAa;YACzB,CAAC;QACH,CAAC;IACH,CAAC;IAED,EAAiD,AAAjD,+CAAiD;IACvC,eAAe,GAAiB,CAAC;QACzC,EAAgE,AAAhE,8DAAgE;;YAE9D,SAAS;YACT,QAAQ;gBAAI,WAAW;gBAAM,UAAU;gBAAM,QAAQ;;YACrD,OAAO;;IAEX,CAAC;IAGS,eAAe,GAAG,CAAC;QAC3B,EAAE,OAAO,sBAAsB,EAAE,CAAC;wBACpB,sBAAsB;QACpC,CAAC;oBACY,sBAAsB;YACjC,aAAa,EAliBA,OAAQ,SAkiBC,WAAW,CAAC,EAAE,EAAE,QAAQ,EAAC,GAAK;YACpD,qBAAqB,EAniBR,OAAQ,SAmiBS,WAAW,CAAC,EAAE,EAAE,QAAQ,EAAC,GAAK;YAC5D,wBAAwB,EApiBX,OAAQ,SAoiBY,WAAW,CAAC,EAAE,EAAE,QAAQ,EAAC,GAAK;;IAEnE,CAAC;IAED,cAAc,GAAG,CAAC;QAChB,KAAK,GAAG,QAAQ,MAAK,WAAW,KAAK,KAAK,CAAC,cAAc;QAEzD,EAA0F,AAA1F,wFAA0F;QAC1F,EAAuF,AAAvF,qFAAuF;QACvF,QAAQ,CAAC,OAAO;YACd,KAAK,MA3gB2B,OAAW,SA2gB9B,yBAA2B;YACxC,IAAI,GAAE,KAAO;YACb,IAAI,GAAE,0BAA4B;YAClC,EAAE,SAAS,GAAG,EAAE,GAAG,EAAE,MAAM,GAAK,CAAC;gBAC/B,KAAK,CAAC,CAAC,OA3iByD,KAAM,YA2iB9C,OAAO,KAAM,MAAM,CAAC,IAAI;2BACrC,WAAW,CAAC,GAAG,EAAE,GAAG,EAAE,CAAC;;oBAEhC,QAAQ,EAAE,IAAI;;YAElB,CAAC;;QAGH,QAAQ,CAAC,OAAO;YACd,KAAK,MAxhB2B,OAAW,SAyhBxC,OAAO,EAviBT,WAA4B,0BAuiBQ,CAAC,OAAO,OAAO,CAAC,CAAC,EAviBrD,WAA4B;YAyiB7B,IAAI,GAAE,KAAO;YACb,IAAI,GAAG,MAAM,EA1iBZ,WAA4B,0BA0iBW,CAAC,OAAO,OAAO,CAAC,CAAC,EA1iBxD,WAA4B;YA2iB7B,EAAE,SAAS,IAAI,EAAE,GAAG,GAAK,CAAC;gBACxB,GAAG,CAAC,UAAU,GAAG,GAAG;gBACpB,GAAG,CAAC,SAAS,EAAC,YAAc,IAAE,+BAAiC;gBAC/D,GAAG,CAAC,GAAG,CACL,IAAI,CAAC,SAAS;oBACZ,KAAK,OAAO,YAAY;;;oBAI1B,QAAQ,EAAE,IAAI;;YAElB,CAAC;;QAGH,QAAQ,CAAC,IAAI;YACX,KAAK,MA5iB2B,OAAW,SA4iB9B,OAAS;YACtB,IAAI,GAAE,KAAO;YACb,eAAe,EAAE,KAAK;YACtB,IAAI,GAAE,+BAAiC;YACvC,EAAE,SAAS,GAAG,EAAE,GAAG,EAAE,MAAM,EAAE,SAAS,GAAK,CAAC;gBAC1C,KAAK,GAAG,QAAQ,MAAK,SAAS;gBAC9B,EAAE,GAAG,QAAQ,EAAE,CAAC;oBACd,KAAK,CAAC,GAAG,CAAC,KAAK,EAAC,qBAAuB;gBACzC,CAAC;gBAED,EAAsD,AAAtD,oDAAsD;gBACtD,EAAE,aAAa,qBAAqB,CAAC,GAAG,EAAE,GAAG,EAAE,MAAM,EAAE,SAAS,GAAG,CAAC;;wBAEhE,QAAQ,EAAE,IAAI;;gBAElB,CAAC;;oBAGC,QAAQ,EAAE,KAAK;;YAEnB,CAAC;;;YAGM,QAAQ;eAAK,WAAW;;IACnC,CAAC;IAED,EAA4F,AAA5F,0FAA4F;IAClF,oBAAoB,GAAY,CAAC;;IAE3C,CAAC;IAED,EAA8D,AAA9D,4DAA8D;IACpD,gBAAgB,GAAY,CAAC;;IAEvC,CAAC;IAED,2BAA2B,CACzB,IAAY,EACZ,KAAkD,EACzC,CAAC;QACV,EAAE,EAAE,KAAK,CAAC,IAAI,MAAK,qBAAuB,GAAE,CAAC;mBACpC,IAAI;QACb,CAAC;QAED,KAAK,CAAC,aAAa,GAAG,IAAI,CAAC,KAAK,EAAC,EAAI;QAErC,GAAG,CAAC,OAAO;QACX,EAAE,IACE,OAAO,GAAG,IAAI,CAAC,KAAK,EAAC,EAAI,GAAE,KAAK,CAAC,IAAI,GAAG,CAAC,QACzC,OAAO,GAAG,OAAO,CAAC,SAAS,CAAC,KAAK,CAAC,GAAG,IACvC,CAAC;mBACM,IAAI;QACb,CAAC;QAED,OAAO,GAAG,OAAO,GAAG,aAAa,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,EAAC,EAAI;QAC7D,OAAO,GAAG,OAAO,CAAC,SAAS,CAAC,CAAC,EAAE,OAAO,CAAC,OAAO,EAAC,SAAW;gBAElD,OAAO,CAAC,QAAQ,EAAC,8BAAgC;IAC3D,CAAC;UAEe,cAAc,CAAC,QAAgB,EAG5C,CAAC;QACF,EAAmD,AAAnD,iDAAmD;QACnD,EAAwD,AAAxD,sDAAwD;QAExD,KAAK,CAAC,gBAAgB,aAAe,CAAC;YACpC,KAAK,GAAG,mBAAmB,GAAE,mBAAmB,GAAE,gBAAgB,WAC3D,UAAU;YACjB,KAAK,GAAG,OAAO,GAAE,aAAa,WAAU,UAAU,CAAC,IAAI;;YAEvD,KAAK,CAAC,KAAK,cAAc,iBAAiB,CAAC,eAAe,MACnD,OAAO,EACZ,QAAQ,QACF,UAAU,CAAC,GAAG,SAAS,iBAAiB;gBAE5C,mBAAmB;gBACnB,mBAAmB;eAErB,gBAAgB,EAChB,OAAO,EACP,aAAa;mBAER,KAAK;QACd,CAAC;QACD,KAAK,GAAG,KAAK,EAAE,WAAW,GAAE,QAAQ,iBA3nBJ,kBAA8B,sBA4nBlC,gBAAgB,GAAG,YAAY,EAAE,QAAQ,SACnE,KAAK;;YAGL,WAAW;YACX,YAAY,EACV,QAAQ,MAAK,QAAU,KACnB,QAAU,IACV,QAAQ,KAAK,IAAI,IACjB,MAAQ,IACR,KAAK;;IAEf,CAAC;UAEe,aAAa,CAAC,QAAgB,EAAE,CAAC;oBACnC,WAAW,CAAE,UAAU,CAAC,QAAQ;IAC9C,CAAC;UAEe,kBAAkB,CAChC,QAAgB,EAChB,KAAqB;OACrB,MAAqB,GAAG,IAAI,EACU,CAAC;mBAC5B,QAAQ;QACnB,KAAK,CAAC,cAAc,cAAc,mBAAmB,CAAC,QAAQ;QAC9D,EAAE,EAAE,cAAc,EAAE,CAAC;YACnB,EAAwD,AAAxD,sDAAwD;YACxD,KAAK,CAAC,GAAG,CAhqBR,WAAgB,mBAgqBW,cAAc;QAC5C,CAAC;YACG,CAAC;uBACQ,WAAW,CAAE,UAAU,CAAC,QAAQ;mBACpC,KAAK,CAAC,kBAAkB,CAAC,QAAQ,EAAE,KAAK,EAAE,MAAM;QACzD,CAAC,QAAQ,GAAG,EAAE,CAAC;YACb,EAAE,EAAG,GAAG,CAAS,IAAI,MAAK,MAAQ,GAAE,CAAC;gBACnC,KAAK,CAAC,GAAG;YACX,CAAC;mBACM,IAAI;QACb,CAAC;IACH,CAAC;UAEe,0BAA0B,GAA6C,CAAC;mBAC3E,WAAW,CAAE,kBAAkB;QAC1C,EAA4D,AAA5D,0DAA4D;QAC5D,EAA8D,AAA9D,4DAA8D;mBACnD,WAAW,CAAE,UAAU,EAAC,OAAS;yBAlqBzC,eAAoB,kCAmqBsB,OAAO;IACtD,CAAC;IAES,6BAA6B,CAAC,GAAmB,EAAQ,CAAC;QAClE,GAAG,CAAC,SAAS,EAAC,aAAe,IAAE,yBAA2B;IAC5D,CAAC;IAEO,WAAW,CACjB,GAAoB,EACpB,GAAmB,EACnB,SAAmB,EACJ,CAAC;QAChB,KAAK,CAAC,CAAC,OAxtB6D,KAAM,YAwtBlD,SAAS,KAAK,SAAS;oBACnC,WAAW,CAAC,GAAG,EAAE,GAAG,EAAE,CAAC;IACrC,CAAC;UAEK,aAAa,CAAC,IAAY,EAAoB,CAAC;YAC/C,CAAC;YACH,KAAK,CAAC,IAAI,SApuBD,GAAI,SAouBS,QAAQ,CAAC,IAAI,KA9tB+B,KAAM,YA8tBtB,SAAS,EAAE,IAAI;mBAC1D,IAAI,CAAC,MAAM;QACpB,CAAC,QAAQ,CAAC,EAAE,CAAC;mBACJ,KAAK;QACd,CAAC;IACH,CAAC;UAEK,mBAAmB,CAAC,IAAY,EAAgB,CAAC;QACrD,KAAK,CAAC,MAAM,cAAc,WAAW,CAAE,oBAAoB,CAAC,IAAI;QAChE,EAAE,EAAE,MAAM,CAAC,MAAM,KAAK,CAAC;QAEvB,EAAwC,AAAxC,sCAAwC;eACjC,MAAM,CAAC,CAAC;IACjB,CAAC;IAES,cAAc,CAAC,gBAAwB,EAAW,CAAC;QAC3D,EAA6D,AAA7D,2DAA6D;QAC7D,EAAyB,AAAzB,uBAAyB;QACzB,EAAgE,AAAhE,8DAAgE;QAChE,EAAqE,AAArE,mEAAqE;QACrE,EAAc,AAAd,YAAc;QACd,EAAkG,AAAlG,gGAAkG;QAElG,GAAG,CAAC,wBAAwB;YACxB,CAAC;YACH,EAAqD,AAArD,mDAAqD;YACrD,wBAAwB,GAAG,kBAAkB,CAAC,gBAAgB;QAChE,CAAC,QAAO,CAAC;mBACA,KAAK;QACd,CAAC;QAED,EAAmD,AAAnD,iDAAmD;QACnD,KAAK,CAAC,iBAAiB,OA9vB6C,KAAM,UA8vBpC,wBAAwB;QAE9D,EAAmD,AAAnD,iDAAmD;QACnD,EAAE,EAAE,iBAAiB,CAAC,OAAO,EAAC,IAAI,QAAO,CAAC,EAAE,CAAC;mBACpC,KAAK;QACd,CAAC;QAED,EAA2E,AAA3E,yEAA2E;QAC3E,EAA4D,AAA5D,0DAA4D;QAC5D,EAAmF,AAAnF,iFAAmF;QACnF,EAA8D,AAA9D,4DAA8D;QAC9D,EAAE,EACA,iBAAiB,CAAC,UAAU,KA1wBsC,KAAM,YA0wB7B,OAAO,GAAE,MAAQ,KA1wBM,KAAM,SA2wBxE,iBAAiB,CAAC,UAAU,KA3wBsC,KAAM,YA2wB7B,OAAO,GAAE,MAAQ,KA3wBM,KAAM,SA4wBxE,iBAAiB,CAAC,UAAU,KA5wBsC,KAAM,YA4wB7B,GAAG,GAAE,MAAQ,KA5wBU,KAAM,SA6wBxE,iBAAiB,CAAC,UAAU,KA7wBsC,KAAM,YA6wB7B,GAAG,GAAE,MAAQ,KA7wBU,KAAM,OA8wBxE,CAAC;mBACM,IAAI;QACb,CAAC;eAEM,KAAK;IACd,CAAC;;kBAvtBkB,SAAS"}