{"version": 3, "sources": ["../../client/script.tsx"], "sourcesContent": ["import React, { useEffect, useContext } from 'react'\nimport { ScriptHTMLAttributes } from 'react'\nimport { HeadManagerContext } from '../shared/lib/head-manager-context'\nimport { DOMAttributeNames } from './head-manager'\nimport { requestIdleCallback } from './request-idle-callback'\n\nconst ScriptCache = new Map()\nconst LoadCache = new Set()\n\nexport interface ScriptProps extends ScriptHTMLAttributes<HTMLScriptElement> {\n  strategy?: 'afterInteractive' | 'lazyOnload' | 'beforeInteractive'\n  id?: string\n  onLoad?: (e: any) => void\n  onError?: (e: any) => void\n  children?: React.ReactNode\n}\n\n/**\n * @deprecated Use `ScriptProps` instead.\n */\nexport type Props = ScriptProps\n\nconst ignoreProps = [\n  'onLoad',\n  'dangerouslySetInnerHTML',\n  'children',\n  'onError',\n  'strategy',\n]\n\nconst loadScript = (props: ScriptProps): void => {\n  const {\n    src,\n    id,\n    onLoad = () => {},\n    dangerouslySetInnerHTML,\n    children = '',\n    strategy = 'afterInteractive',\n    onError,\n  } = props\n\n  const cacheKey = id || src\n\n  // Script has already loaded\n  if (cacheKey && LoadCache.has(cacheKey)) {\n    return\n  }\n\n  // Contents of this script are already loading/loaded\n  if (ScriptCache.has(src)) {\n    LoadCache.add(cacheKey)\n    // Execute onLoad since the script loading has begun\n    ScriptCache.get(src).then(onLoad, onError)\n    return\n  }\n\n  const el = document.createElement('script')\n\n  const loadPromise = new Promise<void>((resolve, reject) => {\n    el.addEventListener('load', function (e) {\n      resolve()\n      if (onLoad) {\n        onLoad.call(this, e)\n      }\n    })\n    el.addEventListener('error', function (e) {\n      reject(e)\n    })\n  }).catch(function (e) {\n    if (onError) {\n      onError(e)\n    }\n  })\n\n  if (src) {\n    ScriptCache.set(src, loadPromise)\n  }\n  LoadCache.add(cacheKey)\n\n  if (dangerouslySetInnerHTML) {\n    el.innerHTML = dangerouslySetInnerHTML.__html || ''\n  } else if (children) {\n    el.textContent =\n      typeof children === 'string'\n        ? children\n        : Array.isArray(children)\n        ? children.join('')\n        : ''\n  } else if (src) {\n    el.src = src\n  }\n\n  for (const [k, value] of Object.entries(props)) {\n    if (value === undefined || ignoreProps.includes(k)) {\n      continue\n    }\n\n    const attr = DOMAttributeNames[k] || k.toLowerCase()\n    el.setAttribute(attr, value)\n  }\n\n  el.setAttribute('data-nscript', strategy)\n\n  document.body.appendChild(el)\n}\n\nfunction handleClientScriptLoad(props: ScriptProps) {\n  const { strategy = 'afterInteractive' } = props\n  if (strategy === 'afterInteractive') {\n    loadScript(props)\n  } else if (strategy === 'lazyOnload') {\n    window.addEventListener('load', () => {\n      requestIdleCallback(() => loadScript(props))\n    })\n  }\n}\n\nfunction loadLazyScript(props: ScriptProps) {\n  if (document.readyState === 'complete') {\n    requestIdleCallback(() => loadScript(props))\n  } else {\n    window.addEventListener('load', () => {\n      requestIdleCallback(() => loadScript(props))\n    })\n  }\n}\n\nexport function initScriptLoader(scriptLoaderItems: ScriptProps[]) {\n  scriptLoaderItems.forEach(handleClientScriptLoad)\n}\n\nfunction Script(props: ScriptProps): JSX.Element | null {\n  const {\n    src = '',\n    onLoad = () => {},\n    dangerouslySetInnerHTML,\n    strategy = 'afterInteractive',\n    onError,\n    ...restProps\n  } = props\n\n  // Context is available only during SSR\n  const { updateScripts, scripts, getIsSsr } = useContext(HeadManagerContext)\n\n  useEffect(() => {\n    if (strategy === 'afterInteractive') {\n      loadScript(props)\n    } else if (strategy === 'lazyOnload') {\n      loadLazyScript(props)\n    }\n  }, [props, strategy])\n\n  if (strategy === 'beforeInteractive') {\n    if (updateScripts) {\n      scripts.beforeInteractive = (scripts.beforeInteractive || []).concat([\n        {\n          src,\n          onLoad,\n          onError,\n          ...restProps,\n        },\n      ])\n      updateScripts(scripts)\n    } else if (getIsSsr && getIsSsr()) {\n      // Script has already loaded during SSR\n      LoadCache.add(restProps.id || src)\n    } else if (getIsSsr && !getIsSsr()) {\n      loadScript(props)\n    }\n  }\n\n  return null\n}\n\nexport default Script\n"], "names": [], "mappings": ";;;;QA+HgB,gBAAgB,GAAhB,gBAAgB;;AA/Ha,GAAO,CAAP,MAAO;AAEjB,GAAoC,CAApC,mBAAoC;AACrC,GAAgB,CAAhB,YAAgB;AACd,GAAyB,CAAzB,oBAAyB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAE7D,KAAK,CAAC,WAAW,GAAG,GAAG,CAAC,GAAG;AAC3B,KAAK,CAAC,SAAS,GAAG,GAAG,CAAC,GAAG;AAezB,KAAK,CAAC,WAAW;KACf,MAAQ;KACR,uBAAyB;KACzB,QAAU;KACV,OAAS;KACT,QAAU;;AAGZ,KAAK,CAAC,UAAU,IAAI,KAAkB,GAAW,CAAC;IAChD,KAAK,GACH,GAAG,GACH,EAAE,GACF,MAAM,MAAS,CAAC;IAAA,CAAC,GACjB,uBAAuB,GACvB,QAAQ,OACR,QAAQ,GAAG,gBAAkB,IAC7B,OAAO,QACL,KAAK;IAET,KAAK,CAAC,QAAQ,GAAG,EAAE,IAAI,GAAG;IAE1B,EAA4B,AAA5B,0BAA4B;IAC5B,EAAE,EAAE,QAAQ,IAAI,SAAS,CAAC,GAAG,CAAC,QAAQ,GAAG,CAAC;;IAE1C,CAAC;IAED,EAAqD,AAArD,mDAAqD;IACrD,EAAE,EAAE,WAAW,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC;QACzB,SAAS,CAAC,GAAG,CAAC,QAAQ;QACtB,EAAoD,AAApD,kDAAoD;QACpD,WAAW,CAAC,GAAG,CAAC,GAAG,EAAE,IAAI,CAAC,MAAM,EAAE,OAAO;;IAE3C,CAAC;IAED,KAAK,CAAC,EAAE,GAAG,QAAQ,CAAC,aAAa,EAAC,MAAQ;IAE1C,KAAK,CAAC,WAAW,GAAG,GAAG,CAAC,OAAO,EAAQ,OAAO,EAAE,MAAM,GAAK,CAAC;QAC1D,EAAE,CAAC,gBAAgB,EAAC,IAAM,YAAY,CAAC,EAAE,CAAC;YACxC,OAAO;YACP,EAAE,EAAE,MAAM,EAAE,CAAC;gBACX,MAAM,CAAC,IAAI,OAAO,CAAC;YACrB,CAAC;QACH,CAAC;QACD,EAAE,CAAC,gBAAgB,EAAC,KAAO,YAAY,CAAC,EAAE,CAAC;YACzC,MAAM,CAAC,CAAC;QACV,CAAC;IACH,CAAC,EAAE,KAAK,UAAW,CAAC,EAAE,CAAC;QACrB,EAAE,EAAE,OAAO,EAAE,CAAC;YACZ,OAAO,CAAC,CAAC;QACX,CAAC;IACH,CAAC;IAED,EAAE,EAAE,GAAG,EAAE,CAAC;QACR,WAAW,CAAC,GAAG,CAAC,GAAG,EAAE,WAAW;IAClC,CAAC;IACD,SAAS,CAAC,GAAG,CAAC,QAAQ;IAEtB,EAAE,EAAE,uBAAuB,EAAE,CAAC;QAC5B,EAAE,CAAC,SAAS,GAAG,uBAAuB,CAAC,MAAM;IAC/C,CAAC,MAAM,EAAE,EAAE,QAAQ,EAAE,CAAC;QACpB,EAAE,CAAC,WAAW,UACL,QAAQ,MAAK,MAAQ,IACxB,QAAQ,GACR,KAAK,CAAC,OAAO,CAAC,QAAQ,IACtB,QAAQ,CAAC,IAAI;IAErB,CAAC,MAAM,EAAE,EAAE,GAAG,EAAE,CAAC;QACf,EAAE,CAAC,GAAG,GAAG,GAAG;IACd,CAAC;SAEI,KAAK,EAAE,CAAC,EAAE,KAAK,KAAK,MAAM,CAAC,OAAO,CAAC,KAAK,EAAG,CAAC;QAC/C,EAAE,EAAE,KAAK,KAAK,SAAS,IAAI,WAAW,CAAC,QAAQ,CAAC,CAAC,GAAG,CAAC;;QAErD,CAAC;QAED,KAAK,CAAC,IAAI,GA9FoB,YAAgB,mBA8Ff,CAAC,KAAK,CAAC,CAAC,WAAW;QAClD,EAAE,CAAC,YAAY,CAAC,IAAI,EAAE,KAAK;IAC7B,CAAC;IAED,EAAE,CAAC,YAAY,EAAC,YAAc,GAAE,QAAQ;IAExC,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,EAAE;AAC9B,CAAC;SAEQ,sBAAsB,CAAC,KAAkB,EAAE,CAAC;IACnD,KAAK,GAAG,QAAQ,GAAG,gBAAkB,OAAK,KAAK;IAC/C,EAAE,EAAE,QAAQ,MAAK,gBAAkB,GAAE,CAAC;QACpC,UAAU,CAAC,KAAK;IAClB,CAAC,MAAM,EAAE,EAAE,QAAQ,MAAK,UAAY,GAAE,CAAC;QACrC,MAAM,CAAC,gBAAgB,EAAC,IAAM,OAAQ,CAAC;gBA3GP,oBAAyB,0BA4G7B,UAAU,CAAC,KAAK;;QAC5C,CAAC;IACH,CAAC;AACH,CAAC;SAEQ,cAAc,CAAC,KAAkB,EAAE,CAAC;IAC3C,EAAE,EAAE,QAAQ,CAAC,UAAU,MAAK,QAAU,GAAE,CAAC;YAlHP,oBAAyB,0BAmH/B,UAAU,CAAC,KAAK;;IAC5C,CAAC,MAAM,CAAC;QACN,MAAM,CAAC,gBAAgB,EAAC,IAAM,OAAQ,CAAC;gBArHP,oBAAyB,0BAsH7B,UAAU,CAAC,KAAK;;QAC5C,CAAC;IACH,CAAC;AACH,CAAC;SAEe,gBAAgB,CAAC,iBAAgC,EAAE,CAAC;IAClE,iBAAiB,CAAC,OAAO,CAAC,sBAAsB;AAClD,CAAC;SAEQ,MAAM,CAAC,KAAkB,EAAsB,CAAC;IACvD,KAAK,GACH,GAAG,OACH,MAAM,MAAS,CAAC;IAAA,CAAC,GACjB,uBAAuB,GACvB,QAAQ,GAAG,gBAAkB,IAC7B,OAAO,MAEL,KAAK,EADJ,SAAS,4BACV,KAAK,IANP,GAAG,IACH,MAAM,IACN,uBAAuB,IACvB,QAAQ,IACR,OAAO;IAIT,EAAuC,AAAvC,qCAAuC;IACvC,KAAK,GAAG,aAAa,GAAE,OAAO,GAAE,QAAQ,UA9IG,MAAO,aAEjB,mBAAoC;QAF1B,MAAO,gBAgJlC,CAAC;QACf,EAAE,EAAE,QAAQ,MAAK,gBAAkB,GAAE,CAAC;YACpC,UAAU,CAAC,KAAK;QAClB,CAAC,MAAM,EAAE,EAAE,QAAQ,MAAK,UAAY,GAAE,CAAC;YACrC,cAAc,CAAC,KAAK;QACtB,CAAC;IACH,CAAC;QAAG,KAAK;QAAE,QAAQ;;IAEnB,EAAE,EAAE,QAAQ,MAAK,iBAAmB,GAAE,CAAC;QACrC,EAAE,EAAE,aAAa,EAAE,CAAC;YAClB,OAAO,CAAC,iBAAiB,IAAI,OAAO,CAAC,iBAAiB,QAAQ,MAAM;;oBAEhE,GAAG;oBACH,MAAM;oBACN,OAAO;mBACJ,SAAS;;YAGhB,aAAa,CAAC,OAAO;QACvB,CAAC,MAAM,EAAE,EAAE,QAAQ,IAAI,QAAQ,IAAI,CAAC;YAClC,EAAuC,AAAvC,qCAAuC;YACvC,SAAS,CAAC,GAAG,CAAC,SAAS,CAAC,EAAE,IAAI,GAAG;QACnC,CAAC,MAAM,EAAE,EAAE,QAAQ,KAAK,QAAQ,IAAI,CAAC;YACnC,UAAU,CAAC,KAAK;QAClB,CAAC;IACH,CAAC;WAEM,IAAI;AACb,CAAC;eAEc,MAAM"}