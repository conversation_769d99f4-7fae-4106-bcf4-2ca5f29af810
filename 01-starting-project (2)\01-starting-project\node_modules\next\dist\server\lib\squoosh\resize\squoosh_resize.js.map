{"version": 3, "sources": ["../../../../../server/lib/squoosh/resize/squoosh_resize.js"], "sourcesContent": ["let wasm\n\nlet cachegetUint8Memory0 = null\nfunction getUint8Memory0() {\n  if (\n    cachegetUint8Memory0 === null ||\n    cachegetUint8Memory0.buffer !== wasm.memory.buffer\n  ) {\n    cachegetUint8Memory0 = new Uint8Array(wasm.memory.buffer)\n  }\n  return cachegetUint8Memory0\n}\n\nlet WASM_VECTOR_LEN = 0\n\nfunction passArray8ToWasm0(arg, malloc) {\n  const ptr = malloc(arg.length * 1)\n  getUint8Memory0().set(arg, ptr / 1)\n  WASM_VECTOR_LEN = arg.length\n  return ptr\n}\n\nlet cachegetInt32Memory0 = null\nfunction getInt32Memory0() {\n  if (\n    cachegetInt32Memory0 === null ||\n    cachegetInt32Memory0.buffer !== wasm.memory.buffer\n  ) {\n    cachegetInt32Memory0 = new Int32Array(wasm.memory.buffer)\n  }\n  return cachegetInt32Memory0\n}\n\nfunction getArrayU8FromWasm0(ptr, len) {\n  return getUint8Memory0().subarray(ptr / 1, ptr / 1 + len)\n}\n/**\n * @param {Uint8Array} input_image\n * @param {number} input_width\n * @param {number} input_height\n * @param {number} output_width\n * @param {number} output_height\n * @param {number} typ_idx\n * @param {boolean} premultiply\n * @param {boolean} color_space_conversion\n * @returns {Uint8Array}\n */\nexport function resize(\n  input_image,\n  input_width,\n  input_height,\n  output_width,\n  output_height,\n  typ_idx,\n  premultiply,\n  color_space_conversion\n) {\n  var ptr0 = passArray8ToWasm0(input_image, wasm.__wbindgen_malloc)\n  var len0 = WASM_VECTOR_LEN\n  wasm.resize(\n    8,\n    ptr0,\n    len0,\n    input_width,\n    input_height,\n    output_width,\n    output_height,\n    typ_idx,\n    premultiply,\n    color_space_conversion\n  )\n  var r0 = getInt32Memory0()[8 / 4 + 0]\n  var r1 = getInt32Memory0()[8 / 4 + 1]\n  var v1 = getArrayU8FromWasm0(r0, r1).slice()\n  wasm.__wbindgen_free(r0, r1 * 1)\n  return v1\n}\n\nasync function load(module, imports) {\n  if (typeof Response === 'function' && module instanceof Response) {\n    if (typeof WebAssembly.instantiateStreaming === 'function') {\n      try {\n        return await WebAssembly.instantiateStreaming(module, imports)\n      } catch (e) {\n        if (module.headers.get('Content-Type') !== 'application/wasm') {\n          console.warn(\n            '`WebAssembly.instantiateStreaming` failed because your server does not serve wasm with `application/wasm` MIME type. Falling back to `WebAssembly.instantiate` which is slower. Original error:\\n',\n            e\n          )\n        } else {\n          throw e\n        }\n      }\n    }\n\n    const bytes = await module.arrayBuffer()\n    return await WebAssembly.instantiate(bytes, imports)\n  } else {\n    const instance = await WebAssembly.instantiate(module, imports)\n\n    if (instance instanceof WebAssembly.Instance) {\n      return { instance, module }\n    } else {\n      return instance\n    }\n  }\n}\n\nasync function init(input) {\n  if (typeof input === 'undefined') {\n    // input = import.meta.url.replace(/\\.js$/, '_bg.wasm')\n    throw new Error('invariant')\n  }\n  const imports = {}\n\n  if (\n    typeof input === 'string' ||\n    (typeof Request === 'function' && input instanceof Request) ||\n    (typeof URL === 'function' && input instanceof URL)\n  ) {\n    input = fetch(input)\n  }\n\n  const { instance, module } = await load(await input, imports)\n\n  wasm = instance.exports\n  init.__wbindgen_wasm_module = module\n\n  return wasm\n}\n\nexport default init\n\n// Manually remove the wasm and memory references to trigger GC\nexport function cleanup() {\n  wasm = null\n  cachegetUint8Memory0 = null\n  cachegetInt32Memory0 = null\n}\n"], "names": [], "mappings": ";;;;QA+CgB,MAAM,GAAN,MAAM;QAuFN,OAAO,GAAP,OAAO;;AAtIvB,GAAG,CAAC,IAAI;AAER,GAAG,CAAC,oBAAoB,GAAG,IAAI;SACtB,eAAe,GAAG,CAAC;IAC1B,EAAE,EACA,oBAAoB,KAAK,IAAI,IAC7B,oBAAoB,CAAC,MAAM,KAAK,IAAI,CAAC,MAAM,CAAC,MAAM,EAClD,CAAC;QACD,oBAAoB,GAAG,GAAG,CAAC,UAAU,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM;IAC1D,CAAC;WACM,oBAAoB;AAC7B,CAAC;AAED,GAAG,CAAC,eAAe,GAAG,CAAC;SAEd,iBAAiB,CAAC,GAAG,EAAE,MAAM,EAAE,CAAC;IACvC,KAAK,CAAC,GAAG,GAAG,MAAM,CAAC,GAAG,CAAC,MAAM,GAAG,CAAC;IACjC,eAAe,GAAG,GAAG,CAAC,GAAG,EAAE,GAAG,GAAG,CAAC;IAClC,eAAe,GAAG,GAAG,CAAC,MAAM;WACrB,GAAG;AACZ,CAAC;AAED,GAAG,CAAC,oBAAoB,GAAG,IAAI;SACtB,eAAe,GAAG,CAAC;IAC1B,EAAE,EACA,oBAAoB,KAAK,IAAI,IAC7B,oBAAoB,CAAC,MAAM,KAAK,IAAI,CAAC,MAAM,CAAC,MAAM,EAClD,CAAC;QACD,oBAAoB,GAAG,GAAG,CAAC,UAAU,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM;IAC1D,CAAC;WACM,oBAAoB;AAC7B,CAAC;SAEQ,mBAAmB,CAAC,GAAG,EAAE,GAAG,EAAE,CAAC;WAC/B,eAAe,GAAG,QAAQ,CAAC,GAAG,GAAG,CAAC,EAAE,GAAG,GAAG,CAAC,GAAG,GAAG;AAC1D,CAAC;SAYe,MAAM,CACpB,WAAW,EACX,WAAW,EACX,YAAY,EACZ,YAAY,EACZ,aAAa,EACb,OAAO,EACP,WAAW,EACX,sBAAsB,EACtB,CAAC;IACD,GAAG,CAAC,IAAI,GAAG,iBAAiB,CAAC,WAAW,EAAE,IAAI,CAAC,iBAAiB;IAChE,GAAG,CAAC,IAAI,GAAG,eAAe;IAC1B,IAAI,CAAC,MAAM,CACT,CAAC,EACD,IAAI,EACJ,IAAI,EACJ,WAAW,EACX,YAAY,EACZ,YAAY,EACZ,aAAa,EACb,OAAO,EACP,WAAW,EACX,sBAAsB;IAExB,GAAG,CAAC,EAAE,GAAG,eAAe,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC;IACpC,GAAG,CAAC,EAAE,GAAG,eAAe,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC;IACpC,GAAG,CAAC,EAAE,GAAG,mBAAmB,CAAC,EAAE,EAAE,EAAE,EAAE,KAAK;IAC1C,IAAI,CAAC,eAAe,CAAC,EAAE,EAAE,EAAE,GAAG,CAAC;WACxB,EAAE;AACX,CAAC;eAEc,IAAI,CAAC,MAAM,EAAE,OAAO,EAAE,CAAC;IACpC,EAAE,SAAS,QAAQ,MAAK,QAAU,KAAI,MAAM,YAAY,QAAQ,EAAE,CAAC;QACjE,EAAE,SAAS,WAAW,CAAC,oBAAoB,MAAK,QAAU,GAAE,CAAC;gBACvD,CAAC;6BACU,WAAW,CAAC,oBAAoB,CAAC,MAAM,EAAE,OAAO;YAC/D,CAAC,QAAQ,CAAC,EAAE,CAAC;gBACX,EAAE,EAAE,MAAM,CAAC,OAAO,CAAC,GAAG,EAAC,YAAc,QAAM,gBAAkB,GAAE,CAAC;oBAC9D,OAAO,CAAC,IAAI,EACV,iMAAmM,GACnM,CAAC;gBAEL,CAAC,MAAM,CAAC;oBACN,KAAK,CAAC,CAAC;gBACT,CAAC;YACH,CAAC;QACH,CAAC;QAED,KAAK,CAAC,KAAK,SAAS,MAAM,CAAC,WAAW;qBACzB,WAAW,CAAC,WAAW,CAAC,KAAK,EAAE,OAAO;IACrD,CAAC,MAAM,CAAC;QACN,KAAK,CAAC,QAAQ,SAAS,WAAW,CAAC,WAAW,CAAC,MAAM,EAAE,OAAO;QAE9D,EAAE,EAAE,QAAQ,YAAY,WAAW,CAAC,QAAQ,EAAE,CAAC;;gBACpC,QAAQ;gBAAE,MAAM;;QAC3B,CAAC,MAAM,CAAC;mBACC,QAAQ;QACjB,CAAC;IACH,CAAC;AACH,CAAC;eAEc,IAAI,CAAC,KAAK,EAAE,CAAC;IAC1B,EAAE,SAAS,KAAK,MAAK,SAAW,GAAE,CAAC;QACjC,EAAuD,AAAvD,qDAAuD;QACvD,KAAK,CAAC,GAAG,CAAC,KAAK,EAAC,SAAW;IAC7B,CAAC;IACD,KAAK,CAAC,OAAO;;IAEb,EAAE,SACO,KAAK,MAAK,MAAQ,YACjB,OAAO,MAAK,QAAU,KAAI,KAAK,YAAY,OAAO,WAClD,GAAG,MAAK,QAAU,KAAI,KAAK,YAAY,GAAG,EAClD,CAAC;QACD,KAAK,GAAG,KAAK,CAAC,KAAK;IACrB,CAAC;IAED,KAAK,GAAG,QAAQ,GAAE,MAAM,YAAW,IAAI,OAAO,KAAK,EAAE,OAAO;IAE5D,IAAI,GAAG,QAAQ,CAAC,OAAO;IACvB,IAAI,CAAC,sBAAsB,GAAG,MAAM;WAE7B,IAAI;AACb,CAAC;eAEc,IAAI;;SAGH,OAAO,GAAG,CAAC;IACzB,IAAI,GAAG,IAAI;IACX,oBAAoB,GAAG,IAAI;IAC3B,oBAAoB,GAAG,IAAI;AAC7B,CAAC"}