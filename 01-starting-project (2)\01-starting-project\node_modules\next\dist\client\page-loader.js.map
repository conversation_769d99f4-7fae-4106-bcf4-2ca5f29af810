{"version": 3, "sources": ["../../client/page-loader.ts"], "sourcesContent": ["import { ComponentType } from 'react'\nimport { ClientSsgManifest } from '../build'\nimport {\n  addBasePath,\n  addLocale,\n  interpolateAs,\n} from '../shared/lib/router/router'\nimport getAssetPathFromRoute from '../shared/lib/router/utils/get-asset-path-from-route'\nimport { isDynamicRoute } from '../shared/lib/router/utils/is-dynamic'\nimport { parseRelativeUrl } from '../shared/lib/router/utils/parse-relative-url'\nimport { removePathTrailingSlash } from './normalize-trailing-slash'\nimport {\n  createRouteLoader,\n  getClientBuildManifest,\n  RouteLoader,\n} from './route-loader'\n\nfunction normalizeRoute(route: string): string {\n  if (route[0] !== '/') {\n    throw new Error(`Route name should start with a \"/\", got \"${route}\"`)\n  }\n\n  if (route === '/') return route\n  return route.replace(/\\/$/, '')\n}\n\nexport type StyleSheetTuple = { href: string; text: string }\nexport type GoodPageCache = {\n  page: ComponentType\n  mod: any\n  styleSheets: StyleSheetTuple[]\n}\n\nexport default class PageLoader {\n  private buildId: string\n  private assetPrefix: string\n\n  private promisedSsgManifest?: Promise<ClientSsgManifest>\n  private promisedDevPagesManifest?: Promise<any>\n  public routeLoader: RouteLoader\n\n  constructor(buildId: string, assetPrefix: string) {\n    this.routeLoader = createRouteLoader(assetPrefix)\n\n    this.buildId = buildId\n    this.assetPrefix = assetPrefix\n\n    /** @type {Promise<Set<string>>} */\n    this.promisedSsgManifest = new Promise((resolve) => {\n      if ((window as any).__SSG_MANIFEST) {\n        resolve((window as any).__SSG_MANIFEST)\n      } else {\n        ;(window as any).__SSG_MANIFEST_CB = () => {\n          resolve((window as any).__SSG_MANIFEST)\n        }\n      }\n    })\n  }\n\n  getPageList() {\n    if (process.env.NODE_ENV === 'production') {\n      return getClientBuildManifest().then((manifest) => manifest.sortedPages)\n    } else {\n      if ((window as any).__DEV_PAGES_MANIFEST) {\n        return (window as any).__DEV_PAGES_MANIFEST.pages\n      } else {\n        if (!this.promisedDevPagesManifest) {\n          this.promisedDevPagesManifest = fetch(\n            `${this.assetPrefix}/_next/static/development/_devPagesManifest.json`\n          )\n            .then((res) => res.json())\n            .then((manifest) => {\n              ;(window as any).__DEV_PAGES_MANIFEST = manifest\n              return manifest.pages\n            })\n            .catch((err) => {\n              console.log(`Failed to fetch devPagesManifest`, err)\n            })\n        }\n        return this.promisedDevPagesManifest\n      }\n    }\n  }\n\n  /**\n   * @param {string} href the route href (file-system path)\n   * @param {string} asPath the URL as shown in browser (virtual path); used for dynamic routes\n   * @returns {string}\n   */\n  getDataHref(\n    href: string,\n    asPath: string,\n    ssg: boolean,\n    locale?: string | false\n  ): string {\n    const { pathname: hrefPathname, query, search } = parseRelativeUrl(href)\n    const { pathname: asPathname } = parseRelativeUrl(asPath)\n    const route = normalizeRoute(hrefPathname)\n\n    const getHrefForSlug = (path: string) => {\n      const dataRoute = getAssetPathFromRoute(\n        removePathTrailingSlash(addLocale(path, locale)),\n        '.json'\n      )\n      return addBasePath(\n        `/_next/data/${this.buildId}${dataRoute}${ssg ? '' : search}`\n      )\n    }\n\n    const isDynamic: boolean = isDynamicRoute(route)\n    const interpolatedRoute = isDynamic\n      ? interpolateAs(hrefPathname, asPathname, query).result\n      : ''\n\n    return isDynamic\n      ? interpolatedRoute && getHrefForSlug(interpolatedRoute)\n      : getHrefForSlug(route)\n  }\n\n  /**\n   * @param {string} route - the route (file-system path)\n   */\n  _isSsg(route: string): Promise<boolean> {\n    return this.promisedSsgManifest!.then((s: ClientSsgManifest) =>\n      s.has(route)\n    )\n  }\n\n  loadPage(route: string): Promise<GoodPageCache> {\n    return this.routeLoader.loadRoute(route).then((res) => {\n      if ('component' in res) {\n        return {\n          page: res.component,\n          mod: res.exports,\n          styleSheets: res.styles.map((o) => ({\n            href: o.href,\n            text: o.content,\n          })),\n        }\n      }\n      throw res.error\n    })\n  }\n\n  prefetch(route: string): Promise<void> {\n    return this.routeLoader.prefetch(route)\n  }\n}\n"], "names": [], "mappings": ";;;;;AAMO,GAA6B,CAA7B,OAA6B;AACF,GAAsD,CAAtD,sBAAsD;AACzD,GAAuC,CAAvC,UAAuC;AACrC,GAA+C,CAA/C,iBAA+C;AACxC,GAA4B,CAA5B,uBAA4B;AAK7D,GAAgB,CAAhB,YAAgB;;;;;;SAEd,cAAc,CAAC,KAAa,EAAU,CAAC;IAC9C,EAAE,EAAE,KAAK,CAAC,CAAC,OAAM,CAAG,GAAE,CAAC;QACrB,KAAK,CAAC,GAAG,CAAC,KAAK,EAAE,yCAAyC,EAAE,KAAK,CAAC,CAAC;IACrE,CAAC;IAED,EAAE,EAAE,KAAK,MAAK,CAAG,UAAS,KAAK;WACxB,KAAK,CAAC,OAAO;AACtB,CAAC;MASoB,UAAU;IA0B7B,WAAW,GAAG,CAAC;QACb,EAAE,EAAE,OAAO,CAAC,GAAG,CAAC,QAAQ,MAAK,UAAY,GAAE,CAAC;uBA7CzC,YAAgB,2BA8Ce,IAAI,EAAE,QAAQ,GAAK,QAAQ,CAAC,WAAW;;QACzE,CAAC,MAAM,CAAC;YACN,EAAE,EAAG,MAAM,CAAS,oBAAoB,EAAE,CAAC;uBACjC,MAAM,CAAS,oBAAoB,CAAC,KAAK;YACnD,CAAC,MAAM,CAAC;gBACN,EAAE,QAAQ,wBAAwB,EAAE,CAAC;yBAC9B,wBAAwB,GAAG,KAAK,SAC3B,WAAW,CAAC,gDAAgD,GAEnE,IAAI,EAAE,GAAG,GAAK,GAAG,CAAC,IAAI;sBACtB,IAAI,EAAE,QAAQ,GAAK,CAAC;wBACjB,MAAM,CAAS,oBAAoB,GAAG,QAAQ;+BACzC,QAAQ,CAAC,KAAK;oBACvB,CAAC,EACA,KAAK,EAAE,GAAG,GAAK,CAAC;wBACf,OAAO,CAAC,GAAG,EAAE,gCAAgC,GAAG,GAAG;oBACrD,CAAC;gBACL,CAAC;4BACW,wBAAwB;YACtC,CAAC;QACH,CAAC;IACH,CAAC;IAED,EAIG,AAJH;;;;GAIG,AAJH,EAIG,CACH,WAAW,CACT,IAAY,EACZ,MAAc,EACd,GAAY,EACZ,MAAuB,EACf,CAAC;QACT,KAAK,GAAG,QAAQ,EAAE,YAAY,GAAE,KAAK,GAAE,MAAM,UAtFhB,iBAA+C,mBAsFT,IAAI;QACvE,KAAK,GAAG,QAAQ,EAAE,UAAU,UAvFC,iBAA+C,mBAuF1B,MAAM;QACxD,KAAK,CAAC,KAAK,GAAG,cAAc,CAAC,YAAY;QAEzC,KAAK,CAAC,cAAc,IAAI,IAAY,GAAK,CAAC;YACxC,KAAK,CAAC,SAAS,OA7Fa,sBAAsD,cAGhD,uBAA4B,8BAJ7D,OAA6B,YA+FM,IAAI,EAAE,MAAM,KAC9C,KAAO;uBAhGR,OAA6B,eAmG3B,YAAY,OAAO,OAAO,GAAG,SAAS,GAAG,GAAG,QAAQ,MAAM;QAE/D,CAAC;QAED,KAAK,CAAC,SAAS,OArGY,UAAuC,iBAqGxB,KAAK;QAC/C,KAAK,CAAC,iBAAiB,GAAG,SAAS,OAxGhC,OAA6B,gBAyGd,YAAY,EAAE,UAAU,EAAE,KAAK,EAAE,MAAM;eAGlD,SAAS,GACZ,iBAAiB,IAAI,cAAc,CAAC,iBAAiB,IACrD,cAAc,CAAC,KAAK;IAC1B,CAAC;IAED,EAEG,AAFH;;GAEG,AAFH,EAEG,CACH,MAAM,CAAC,KAAa,EAAoB,CAAC;oBAC3B,mBAAmB,CAAE,IAAI,EAAE,CAAoB,GACzD,CAAC,CAAC,GAAG,CAAC,KAAK;;IAEf,CAAC;IAED,QAAQ,CAAC,KAAa,EAA0B,CAAC;oBACnC,WAAW,CAAC,SAAS,CAAC,KAAK,EAAE,IAAI,EAAE,GAAG,GAAK,CAAC;YACtD,EAAE,GAAE,SAAW,KAAI,GAAG,EAAE,CAAC;;oBAErB,IAAI,EAAE,GAAG,CAAC,SAAS;oBACnB,GAAG,EAAE,GAAG,CAAC,OAAO;oBAChB,WAAW,EAAE,GAAG,CAAC,MAAM,CAAC,GAAG,EAAE,CAAC;4BAC5B,IAAI,EAAE,CAAC,CAAC,IAAI;4BACZ,IAAI,EAAE,CAAC,CAAC,OAAO;;;;YAGrB,CAAC;YACD,KAAK,CAAC,GAAG,CAAC,KAAK;QACjB,CAAC;IACH,CAAC;IAED,QAAQ,CAAC,KAAa,EAAiB,CAAC;oBAC1B,WAAW,CAAC,QAAQ,CAAC,KAAK;IACxC,CAAC;gBAzGW,OAAe,EAAE,WAAmB,CAAE,CAAC;aAC5C,WAAW,OA3Bb,YAAgB,oBA2BkB,WAAW;aAE3C,OAAO,GAAG,OAAO;aACjB,WAAW,GAAG,WAAW;QAE9B,EAAmC,AAAnC,+BAAmC,AAAnC,EAAmC,MAC9B,mBAAmB,GAAG,GAAG,CAAC,OAAO,EAAE,OAAO,GAAK,CAAC;YACnD,EAAE,EAAG,MAAM,CAAS,cAAc,EAAE,CAAC;gBACnC,OAAO,CAAE,MAAM,CAAS,cAAc;YACxC,CAAC,MAAM,CAAC;gBACJ,MAAM,CAAS,iBAAiB,OAAS,CAAC;oBAC1C,OAAO,CAAE,MAAM,CAAS,cAAc;gBACxC,CAAC;YACH,CAAC;QACH,CAAC;IACH,CAAC;;kBAxBkB,UAAU"}