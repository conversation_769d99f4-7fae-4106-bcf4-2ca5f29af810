{"version": 3, "sources": ["../../../../telemetry/trace/report/to-console.ts"], "sourcesContent": ["const idToName = new Map<string, string>()\n\nconst reportToConsole = (\n  spanName: string,\n  duration: number,\n  _timestamp: number,\n  id: string,\n  parentId?: string,\n  attrs?: Object\n) => {\n  idToName.set(id, spanName)\n\n  const parentStr =\n    parentId && idToName.has(parentId)\n      ? `, parent: ${idToName.get(parentId)}`\n      : ''\n  const attrsStr = attrs\n    ? `, ${Object.entries(attrs)\n        .map(([key, val]) => `${key}: ${val}`)\n        .join(', ')}`\n    : ''\n\n  console.log(`[trace] ${spanName} took ${duration} μs${parentStr}${attrsStr}`)\n}\n\nexport default {\n  flushAll: () => {},\n  report: reportToConsole,\n}\n"], "names": [], "mappings": ";;;;;AAAA,KAAK,CAAC,QAAQ,GAAG,GAAG,CAAC,GAAG;AAExB,KAAK,CAAC,eAAe,IACnB,QAAgB,EAChB,QAAgB,EAChB,UAAkB,EAClB,EAAU,EACV,QAAiB,EACjB,KAAc,GACX,CAAC;IACJ,QAAQ,CAAC,GAAG,CAAC,EAAE,EAAE,QAAQ;IAEzB,KAAK,CAAC,SAAS,GACb,QAAQ,IAAI,QAAQ,CAAC,GAAG,CAAC,QAAQ,KAC5B,UAAU,EAAE,QAAQ,CAAC,GAAG,CAAC,QAAQ;IAExC,KAAK,CAAC,QAAQ,GAAG,KAAK,IACjB,EAAE,EAAE,MAAM,CAAC,OAAO,CAAC,KAAK,EACtB,GAAG,GAAG,GAAG,EAAE,GAAG,OAAS,GAAG,CAAC,EAAE,EAAE,GAAG;MAClC,IAAI,EAAC,EAAI;IAGhB,OAAO,CAAC,GAAG,EAAE,QAAQ,EAAE,QAAQ,CAAC,MAAM,EAAE,QAAQ,CAAC,IAAG,EAAE,SAAS,GAAG,QAAQ;AAC5E,CAAC;;IAGC,QAAQ,MAAQ,CAAC;IAAA,CAAC;IAClB,MAAM,EAAE,eAAe"}