{"version": 3, "sources": ["../../../lib/typescript/writeConfigurationDefaults.ts"], "sourcesContent": ["import { promises as fs } from 'fs'\nimport chalk from 'chalk'\nimport * as Comment<PERSON>son from 'next/dist/compiled/comment-json'\nimport os from 'os'\nimport { getTypeScriptConfiguration } from './getTypeScriptConfiguration'\n\ntype DesiredCompilerOptionsShape = {\n  [key: string]:\n    | { suggested: any }\n    | {\n        parsedValue?: any\n        parsedValues?: Array<any>\n        value: any\n        reason: string\n      }\n}\n\nfunction getDesiredCompilerOptions(\n  ts: typeof import('typescript')\n): DesiredCompilerOptionsShape {\n  const o: DesiredCompilerOptionsShape = {\n    // These are suggested values and will be set when not present in the\n    // tsconfig.json\n    target: { suggested: 'es5' },\n    lib: { suggested: ['dom', 'dom.iterable', 'esnext'] },\n    allowJs: { suggested: true },\n    skipLibCheck: { suggested: true },\n    strict: { suggested: false },\n    forceConsistentCasingInFileNames: { suggested: true },\n    noEmit: { suggested: true },\n\n    // These values are required and cannot be changed by the user\n    // Keep this in sync with the webpack config\n    // 'parsedValue' matches the output value from ts.parseJsonConfigFileContent()\n    esModuleInterop: {\n      value: true,\n      reason: 'requirement for babel',\n    },\n    module: {\n      parsedValue: ts.ModuleKind.ESNext,\n      // All of these values work:\n      parsedValues: [\n        ts.ModuleKind.ES2020,\n        ts.ModuleKind.ESNext,\n        ts.ModuleKind.CommonJS,\n        ts.ModuleKind.AMD,\n      ],\n      value: 'esnext',\n      reason: 'for dynamic import() support',\n    },\n    moduleResolution: {\n      parsedValue: ts.ModuleResolutionKind.NodeJs,\n      value: 'node',\n      reason: 'to match webpack resolution',\n    },\n    resolveJsonModule: { value: true, reason: 'to match webpack resolution' },\n    isolatedModules: {\n      value: true,\n      reason: 'requirement for babel',\n    },\n    jsx: {\n      parsedValue: ts.JsxEmit.Preserve,\n      value: 'preserve',\n      reason: 'next.js implements its own optimized jsx transform',\n    },\n  }\n\n  return o\n}\n\nexport function getRequiredConfiguration(\n  ts: typeof import('typescript')\n): Partial<import('typescript').CompilerOptions> {\n  const res: Partial<import('typescript').CompilerOptions> = {}\n\n  const desiredCompilerOptions = getDesiredCompilerOptions(ts)\n  for (const optionKey of Object.keys(desiredCompilerOptions)) {\n    const ev = desiredCompilerOptions[optionKey]\n    if (!('value' in ev)) {\n      continue\n    }\n    res[optionKey] = ev.parsedValue ?? ev.value\n  }\n\n  return res\n}\n\nexport async function writeConfigurationDefaults(\n  ts: typeof import('typescript'),\n  tsConfigPath: string,\n  isFirstTimeSetup: boolean\n): Promise<void> {\n  if (isFirstTimeSetup) {\n    await fs.writeFile(tsConfigPath, '{}' + os.EOL)\n  }\n\n  const desiredCompilerOptions = getDesiredCompilerOptions(ts)\n  const { options: tsOptions, raw: rawConfig } =\n    await getTypeScriptConfiguration(ts, tsConfigPath, true)\n\n  const userTsConfigContent = await fs.readFile(tsConfigPath, {\n    encoding: 'utf8',\n  })\n  const userTsConfig = CommentJson.parse(userTsConfigContent)\n  if (userTsConfig.compilerOptions == null && !('extends' in rawConfig)) {\n    userTsConfig.compilerOptions = {}\n    isFirstTimeSetup = true\n  }\n\n  const suggestedActions: string[] = []\n  const requiredActions: string[] = []\n  for (const optionKey of Object.keys(desiredCompilerOptions)) {\n    const check = desiredCompilerOptions[optionKey]\n    if ('suggested' in check) {\n      if (!(optionKey in tsOptions)) {\n        userTsConfig.compilerOptions[optionKey] = check.suggested\n        suggestedActions.push(\n          chalk.cyan(optionKey) + ' was set to ' + chalk.bold(check.suggested)\n        )\n      }\n    } else if ('value' in check) {\n      const ev = tsOptions[optionKey]\n      if (\n        !('parsedValues' in check\n          ? check.parsedValues?.includes(ev)\n          : 'parsedValue' in check\n          ? check.parsedValue === ev\n          : check.value === ev)\n      ) {\n        userTsConfig.compilerOptions[optionKey] = check.value\n        requiredActions.push(\n          chalk.cyan(optionKey) +\n            ' was set to ' +\n            chalk.bold(check.value) +\n            ` (${check.reason})`\n        )\n      }\n    } else {\n      // eslint-disable-next-line @typescript-eslint/no-unused-vars\n      const _: never = check\n    }\n  }\n\n  if (!('include' in rawConfig)) {\n    userTsConfig.include = ['next-env.d.ts', '**/*.ts', '**/*.tsx']\n    suggestedActions.push(\n      chalk.cyan('include') +\n        ' was set to ' +\n        chalk.bold(`['next-env.d.ts', '**/*.ts', '**/*.tsx']`)\n    )\n  }\n\n  if (!('exclude' in rawConfig)) {\n    userTsConfig.exclude = ['node_modules']\n    suggestedActions.push(\n      chalk.cyan('exclude') + ' was set to ' + chalk.bold(`['node_modules']`)\n    )\n  }\n\n  if (suggestedActions.length < 1 && requiredActions.length < 1) {\n    return\n  }\n\n  await fs.writeFile(\n    tsConfigPath,\n    CommentJson.stringify(userTsConfig, null, 2) + os.EOL\n  )\n\n  if (isFirstTimeSetup) {\n    console.log(\n      chalk.green(\n        `We detected TypeScript in your project and created a ${chalk.bold(\n          'tsconfig.json'\n        )} file for you.`\n      ) + '\\n'\n    )\n    return\n  }\n\n  console.log(\n    chalk.green(\n      `We detected TypeScript in your project and reconfigured your ${chalk.bold(\n        'tsconfig.json'\n      )} file for you. Strict-mode is set to ${chalk.bold('false')} by default.`\n    ) + '\\n'\n  )\n  if (suggestedActions.length) {\n    console.log(\n      `The following suggested values were added to your ${chalk.cyan(\n        'tsconfig.json'\n      )}. These values ${chalk.bold(\n        'can be changed'\n      )} to fit your project's needs:\\n`\n    )\n\n    suggestedActions.forEach((action) => console.log(`\\t- ${action}`))\n\n    console.log('')\n  }\n\n  if (requiredActions.length) {\n    console.log(\n      `The following ${chalk.bold(\n        'mandatory changes'\n      )} were made to your ${chalk.cyan('tsconfig.json')}:\\n`\n    )\n\n    requiredActions.forEach((action) => console.log(`\\t- ${action}`))\n\n    console.log('')\n  }\n}\n"], "names": [], "mappings": ";;;;QAsEgB,wBAAwB,GAAxB,wBAAwB;QAiBlB,0BAA0B,GAA1B,0BAA0B;AAvFjB,GAAI,CAAJ,GAAI;AACjB,GAAO,CAAP,MAAO;AACb,GAAW,CAAX,WAAW;AACR,GAAI,CAAJ,GAAI;AACwB,GAA8B,CAA9B,2BAA8B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;SAahE,yBAAyB,CAChC,EAA+B,EACF,CAAC;IAC9B,KAAK,CAAC,CAAC;QACL,EAAqE,AAArE,mEAAqE;QACrE,EAAgB,AAAhB,cAAgB;QAChB,MAAM;YAAI,SAAS,GAAE,GAAK;;QAC1B,GAAG;YAAI,SAAS;iBAAG,GAAK;iBAAE,YAAc;iBAAE,MAAQ;;;QAClD,OAAO;YAAI,SAAS,EAAE,IAAI;;QAC1B,YAAY;YAAI,SAAS,EAAE,IAAI;;QAC/B,MAAM;YAAI,SAAS,EAAE,KAAK;;QAC1B,gCAAgC;YAAI,SAAS,EAAE,IAAI;;QACnD,MAAM;YAAI,SAAS,EAAE,IAAI;;QAEzB,EAA8D,AAA9D,4DAA8D;QAC9D,EAA4C,AAA5C,0CAA4C;QAC5C,EAA8E,AAA9E,4EAA8E;QAC9E,eAAe;YACb,KAAK,EAAE,IAAI;YACX,MAAM,GAAE,qBAAuB;;QAEjC,MAAM;YACJ,WAAW,EAAE,EAAE,CAAC,UAAU,CAAC,MAAM;YACjC,EAA4B,AAA5B,0BAA4B;YAC5B,YAAY;gBACV,EAAE,CAAC,UAAU,CAAC,MAAM;gBACpB,EAAE,CAAC,UAAU,CAAC,MAAM;gBACpB,EAAE,CAAC,UAAU,CAAC,QAAQ;gBACtB,EAAE,CAAC,UAAU,CAAC,GAAG;;YAEnB,KAAK,GAAE,MAAQ;YACf,MAAM,GAAE,4BAA8B;;QAExC,gBAAgB;YACd,WAAW,EAAE,EAAE,CAAC,oBAAoB,CAAC,MAAM;YAC3C,KAAK,GAAE,IAAM;YACb,MAAM,GAAE,2BAA6B;;QAEvC,iBAAiB;YAAI,KAAK,EAAE,IAAI;YAAE,MAAM,GAAE,2BAA6B;;QACvE,eAAe;YACb,KAAK,EAAE,IAAI;YACX,MAAM,GAAE,qBAAuB;;QAEjC,GAAG;YACD,WAAW,EAAE,EAAE,CAAC,OAAO,CAAC,QAAQ;YAChC,KAAK,GAAE,QAAU;YACjB,MAAM,GAAE,kDAAoD;;;WAIzD,CAAC;AACV,CAAC;SAEe,wBAAwB,CACtC,EAA+B,EACgB,CAAC;IAChD,KAAK,CAAC,GAAG;;IAET,KAAK,CAAC,sBAAsB,GAAG,yBAAyB,CAAC,EAAE;SACtD,KAAK,CAAC,SAAS,IAAI,MAAM,CAAC,IAAI,CAAC,sBAAsB,EAAG,CAAC;QAC5D,KAAK,CAAC,EAAE,GAAG,sBAAsB,CAAC,SAAS;QAC3C,EAAE,KAAI,KAAO,KAAI,EAAE,GAAG,CAAC;;QAEvB,CAAC;YACgB,YAAc;QAA/B,GAAG,CAAC,SAAS,KAAI,YAAc,GAAd,EAAE,CAAC,WAAW,cAAd,YAAc,cAAd,YAAc,GAAI,EAAE,CAAC,KAAK;IAC7C,CAAC;WAEM,GAAG;AACZ,CAAC;eAEqB,0BAA0B,CAC9C,EAA+B,EAC/B,YAAoB,EACpB,gBAAyB,EACV,CAAC;IAChB,EAAE,EAAE,gBAAgB,EAAE,CAAC;cA5FM,GAAI,UA6FtB,SAAS,CAAC,YAAY,GAAE,EAAI,IA1F1B,GAAI,SA0F4B,GAAG;IAChD,CAAC;IAED,KAAK,CAAC,sBAAsB,GAAG,yBAAyB,CAAC,EAAE;IAC3D,KAAK,GAAG,OAAO,EAAE,SAAS,GAAE,GAAG,EAAE,SAAS,gBA7FD,2BAA8B,6BA8FpC,EAAE,EAAE,YAAY,EAAE,IAAI;IAEzD,KAAK,CAAC,mBAAmB,SApGI,GAAI,UAoGI,QAAQ,CAAC,YAAY;QACxD,QAAQ,GAAE,IAAM;;IAElB,KAAK,CAAC,YAAY,GArGR,WAAW,CAqGY,KAAK,CAAC,mBAAmB;IAC1D,EAAE,EAAE,YAAY,CAAC,eAAe,IAAI,IAAI,OAAM,OAAS,KAAI,SAAS,GAAG,CAAC;QACtE,YAAY,CAAC,eAAe;;QAC5B,gBAAgB,GAAG,IAAI;IACzB,CAAC;IAED,KAAK,CAAC,gBAAgB;IACtB,KAAK,CAAC,eAAe;SAChB,KAAK,CAAC,SAAS,IAAI,MAAM,CAAC,IAAI,CAAC,sBAAsB,EAAG,CAAC;QAC5D,KAAK,CAAC,KAAK,GAAG,sBAAsB,CAAC,SAAS;QAC9C,EAAE,GAAE,SAAW,KAAI,KAAK,EAAE,CAAC;YACzB,EAAE,IAAI,SAAS,IAAI,SAAS,GAAG,CAAC;gBAC9B,YAAY,CAAC,eAAe,CAAC,SAAS,IAAI,KAAK,CAAC,SAAS;gBACzD,gBAAgB,CAAC,IAAI,CAnHX,MAAO,SAoHT,IAAI,CAAC,SAAS,KAAI,YAAc,IApH9B,MAAO,SAoHgC,IAAI,CAAC,KAAK,CAAC,SAAS;YAEvE,CAAC;QACH,CAAC,MAAM,EAAE,GAAE,KAAO,KAAI,KAAK,EAAE,CAAC;gBAItB,GAAkB;YAHxB,KAAK,CAAC,EAAE,GAAG,SAAS,CAAC,SAAS;YAC9B,EAAE,KACE,YAAc,KAAI,KAAK,IACrB,GAAkB,GAAlB,KAAK,CAAC,YAAY,cAAlB,GAAkB,UAAlB,CAA4B,QAA5B,CAA4B,GAA5B,GAAkB,CAAE,QAAQ,CAAC,EAAE,KAC/B,WAAa,KAAI,KAAK,GACtB,KAAK,CAAC,WAAW,KAAK,EAAE,GACxB,KAAK,CAAC,KAAK,KAAK,EAAE,GACtB,CAAC;gBACD,YAAY,CAAC,eAAe,CAAC,SAAS,IAAI,KAAK,CAAC,KAAK;gBACrD,eAAe,CAAC,IAAI,CAjIV,MAAO,SAkIT,IAAI,CAAC,SAAS,KAClB,YAAc,IAnIR,MAAO,SAoIP,IAAI,CAAC,KAAK,CAAC,KAAK,KACrB,EAAE,EAAE,KAAK,CAAC,MAAM,CAAC,CAAC;YAEzB,CAAC;QACH,CAAC,MAAM,CAAC;YACN,EAA6D,AAA7D,2DAA6D;YAC7D,KAAK,CAAC,CAAC,GAAU,KAAK;QACxB,CAAC;IACH,CAAC;IAED,EAAE,KAAI,OAAS,KAAI,SAAS,GAAG,CAAC;QAC9B,YAAY,CAAC,OAAO;aAAI,aAAe;aAAE,OAAS;aAAE,QAAU;;QAC9D,gBAAgB,CAAC,IAAI,CAhJP,MAAO,SAiJb,IAAI,EAAC,OAAS,MAClB,YAAc,IAlJJ,MAAO,SAmJX,IAAI,EAAE,wCAAwC;IAE1D,CAAC;IAED,EAAE,KAAI,OAAS,KAAI,SAAS,GAAG,CAAC;QAC9B,YAAY,CAAC,OAAO;aAAI,YAAc;;QACtC,gBAAgB,CAAC,IAAI,CAzJP,MAAO,SA0Jb,IAAI,EAAC,OAAS,MAAI,YAAc,IA1J1B,MAAO,SA0J4B,IAAI,EAAE,gBAAgB;IAEzE,CAAC;IAED,EAAE,EAAE,gBAAgB,CAAC,MAAM,GAAG,CAAC,IAAI,eAAe,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;;IAEhE,CAAC;UAjK4B,GAAI,UAmKxB,SAAS,CAChB,YAAY,EAlKJ,WAAW,CAmKP,SAAS,CAAC,YAAY,EAAE,IAAI,EAAE,CAAC,IAlKhC,GAAI,SAkKmC,GAAG;IAGvD,EAAE,EAAE,gBAAgB,EAAE,CAAC;QACrB,OAAO,CAAC,GAAG,CAxKG,MAAO,SAyKb,KAAK,EACR,qDAAqD,EA1K5C,MAAO,SA0K6C,IAAI,EAChE,aAAe,GACf,cAAc,MACd,EAAI;;IAGZ,CAAC;IAED,OAAO,CAAC,GAAG,CAlLK,MAAO,SAmLf,KAAK,EACR,6DAA6D,EApLlD,MAAO,SAoLmD,IAAI,EACxE,aAAe,GACf,qCAAqC,EAtL3B,MAAO,SAsL4B,IAAI,EAAC,KAAO,GAAE,YAAY,MACvE,EAAI;IAEV,EAAE,EAAE,gBAAgB,CAAC,MAAM,EAAE,CAAC;QAC5B,OAAO,CAAC,GAAG,EACR,kDAAkD,EA3LvC,MAAO,SA2LwC,IAAI,EAC7D,aAAe,GACf,eAAe,EA7LL,MAAO,SA6LM,IAAI,EAC3B,cAAgB,GAChB,+BAA+B;QAGnC,gBAAgB,CAAC,OAAO,EAAE,MAAM,GAAK,OAAO,CAAC,GAAG,EAAE,IAAI,EAAE,MAAM;;QAE9D,OAAO,CAAC,GAAG;IACb,CAAC;IAED,EAAE,EAAE,eAAe,CAAC,MAAM,EAAE,CAAC;QAC3B,OAAO,CAAC,GAAG,EACR,cAAc,EAzMH,MAAO,SAyMI,IAAI,EACzB,iBAAmB,GACnB,mBAAmB,EA3MT,MAAO,SA2MU,IAAI,EAAC,aAAe,GAAE,GAAG;QAGxD,eAAe,CAAC,OAAO,EAAE,MAAM,GAAK,OAAO,CAAC,GAAG,EAAE,IAAI,EAAE,MAAM;;QAE7D,OAAO,CAAC,GAAG;IACb,CAAC;AACH,CAAC"}