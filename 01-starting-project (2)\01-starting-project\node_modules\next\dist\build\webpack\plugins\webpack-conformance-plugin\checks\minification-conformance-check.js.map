{"version": 3, "sources": ["../../../../../../build/webpack/plugins/webpack-conformance-plugin/checks/minification-conformance-check.ts"], "sourcesContent": ["import {\n  IWebpackConformanceTest,\n  IConformanceTestResult,\n  IConformanceTestStatus,\n} from '../TestInterface'\nimport { CONFORMANCE_ERROR_PREFIX } from '../constants'\nconst EARLY_EXIT_RESULT: IConformanceTestResult = {\n  result: IConformanceTestStatus.SUCCESS,\n}\n\nexport class MinificationConformanceCheck implements IWebpackConformanceTest {\n  public buildStared(options: any): IConformanceTestResult {\n    if (options.output.path.endsWith('/server')) {\n      return EARLY_EXIT_RESULT\n    }\n    // TODO(prateekbh@): Implement warning for using Terser maybe?\n    const { optimization } = options\n    if (\n      optimization &&\n      (optimization.minimize !== true ||\n        (optimization.minimizer && optimization.minimizer.length === 0))\n    ) {\n      return {\n        result: IConformanceTestStatus.FAILED,\n        errors: [\n          {\n            message: `${CONFORMANCE_ERROR_PREFIX}: Minification is disabled for this build.\\nDisabling minification can result in serious performance degradation.`,\n          },\n        ],\n      }\n    } else {\n      return EARLY_EXIT_RESULT\n    }\n  }\n}\n"], "names": [], "mappings": ";;;;AAIO,GAAkB,CAAlB,cAAkB;AACgB,GAAc,CAAd,UAAc;AACvD,KAAK,CAAC,iBAAiB;IACrB,MAAM,EAHD,cAAkB,wBAGQ,OAAO;;MAG3B,4BAA4B;IAChC,WAAW,CAAC,OAAY,EAA0B,CAAC;QACxD,EAAE,EAAE,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,EAAC,OAAS,IAAG,CAAC;mBACrC,iBAAiB;QAC1B,CAAC;QACD,EAA8D,AAA9D,4DAA8D;QAC9D,KAAK,GAAG,YAAY,MAAK,OAAO;QAChC,EAAE,EACA,YAAY,KACX,YAAY,CAAC,QAAQ,KAAK,IAAI,IAC5B,YAAY,CAAC,SAAS,IAAI,YAAY,CAAC,SAAS,CAAC,MAAM,KAAK,CAAC,GAChE,CAAC;;gBAEC,MAAM,EAnBP,cAAkB,wBAmBc,MAAM;gBACrC,MAAM;;wBAEF,OAAO,KArBsB,UAAc,0BAqBN,iHAAiH;;;;QAI9J,CAAC,MAAM,CAAC;mBACC,iBAAiB;QAC1B,CAAC;IACH,CAAC;;QAvBU,4BAA4B,GAA5B,4BAA4B"}