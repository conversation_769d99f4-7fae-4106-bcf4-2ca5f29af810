{"version": 3, "sources": ["../../telemetry/storage.ts"], "sourcesContent": ["import chalk from 'chalk'\nimport Conf from 'next/dist/compiled/conf'\nimport { BinaryLike, createHash, randomBytes } from 'crypto'\nimport isDockerFunction from 'next/dist/compiled/is-docker'\nimport path from 'path'\n\nimport { getAnonymousMeta } from './anonymous-meta'\nimport * as ciEnvironment from './ci-info'\nimport { _postPayload } from './post-payload'\nimport { getRawProjectId } from './project-id'\n\n// This is the key that stores whether or not telemetry is enabled or disabled.\nconst TELEMETRY_KEY_ENABLED = 'telemetry.enabled'\n\n// This is the key that specifies when the user was informed about anonymous\n// telemetry collection.\nconst TELEMETRY_KEY_NOTIFY_DATE = 'telemetry.notifiedAt'\n\n// This is a quasi-persistent identifier used to dedupe recurring events. It's\n// generated from random data and completely anonymous.\nconst TELEMETRY_KEY_ID = `telemetry.anonymousId`\n\n// This is the cryptographic salt that is included within every hashed value.\n// This salt value is never sent to us, ensuring privacy and the one-way nature\n// of the hash (prevents dictionary lookups of pre-computed hashes).\n// See the `oneWayHash` function.\nconst TELEMETRY_KEY_SALT = `telemetry.salt`\n\ntype TelemetryEvent = { eventName: string; payload: object }\ntype EventContext = {\n  anonymousId: string\n  projectId: string\n  sessionId: string\n}\ntype EventMeta = { [key: string]: unknown }\ntype EventBatchShape = {\n  eventName: string\n  fields: object\n}\n\ntype RecordObject = {\n  isFulfilled: boolean\n  isRejected: boolean\n  value?: any\n  reason?: any\n}\n\nexport class Telemetry {\n  private conf: Conf<any> | null\n  private sessionId: string\n  private rawProjectId: string\n  private NEXT_TELEMETRY_DISABLED: any\n  private NEXT_TELEMETRY_DEBUG: any\n\n  private queue: Set<Promise<RecordObject>>\n\n  constructor({ distDir }: { distDir: string }) {\n    // Read in the constructor so that .env can be loaded before reading\n    const { NEXT_TELEMETRY_DISABLED, NEXT_TELEMETRY_DEBUG } = process.env\n    this.NEXT_TELEMETRY_DISABLED = NEXT_TELEMETRY_DISABLED\n    this.NEXT_TELEMETRY_DEBUG = NEXT_TELEMETRY_DEBUG\n    const storageDirectory = getStorageDirectory(distDir)\n\n    try {\n      // `conf` incorrectly throws a permission error during initialization\n      // instead of waiting for first use. We need to handle it, otherwise the\n      // process may crash.\n      this.conf = new Conf({ projectName: 'nextjs', cwd: storageDirectory })\n    } catch (_) {\n      this.conf = null\n    }\n    this.sessionId = randomBytes(32).toString('hex')\n    this.rawProjectId = getRawProjectId()\n\n    this.queue = new Set()\n\n    this.notify()\n  }\n\n  private notify = () => {\n    if (this.isDisabled || !this.conf) {\n      return\n    }\n\n    // The end-user has already been notified about our telemetry integration. We\n    // don't need to constantly annoy them about it.\n    // We will re-inform users about the telemetry if significant changes are\n    // ever made.\n    if (this.conf.get(TELEMETRY_KEY_NOTIFY_DATE, '')) {\n      return\n    }\n    this.conf.set(TELEMETRY_KEY_NOTIFY_DATE, Date.now().toString())\n\n    console.log(\n      `${chalk.magenta.bold(\n        'Attention'\n      )}: Next.js now collects completely anonymous telemetry regarding usage.`\n    )\n    console.log(\n      `This information is used to shape Next.js' roadmap and prioritize features.`\n    )\n    console.log(\n      `You can learn more, including how to opt-out if you'd not like to participate in this anonymous program, by visiting the following URL:`\n    )\n    console.log(chalk.cyan('https://nextjs.org/telemetry'))\n    console.log()\n  }\n\n  get anonymousId(): string {\n    const val = this.conf && this.conf.get(TELEMETRY_KEY_ID)\n    if (val) {\n      return val\n    }\n\n    const generated = randomBytes(32).toString('hex')\n    this.conf && this.conf.set(TELEMETRY_KEY_ID, generated)\n    return generated\n  }\n\n  get salt(): string {\n    const val = this.conf && this.conf.get(TELEMETRY_KEY_SALT)\n    if (val) {\n      return val\n    }\n\n    const generated = randomBytes(16).toString('hex')\n    this.conf && this.conf.set(TELEMETRY_KEY_SALT, generated)\n    return generated\n  }\n\n  private get isDisabled(): boolean {\n    if (!!this.NEXT_TELEMETRY_DISABLED || !this.conf) {\n      return true\n    }\n    return this.conf.get(TELEMETRY_KEY_ENABLED, true) === false\n  }\n\n  setEnabled = (_enabled: boolean) => {\n    const enabled = !!_enabled\n    this.conf && this.conf.set(TELEMETRY_KEY_ENABLED, enabled)\n  }\n\n  get isEnabled(): boolean {\n    return !!this.conf && this.conf.get(TELEMETRY_KEY_ENABLED, true) !== false\n  }\n\n  oneWayHash = (payload: BinaryLike): string => {\n    const hash = createHash('sha256')\n\n    // Always prepend the payload value with salt. This ensures the hash is truly\n    // one-way.\n    hash.update(this.salt)\n\n    // Update is an append operation, not a replacement. The salt from the prior\n    // update is still present!\n    hash.update(payload)\n    return hash.digest('hex')\n  }\n\n  private get projectId(): string {\n    return this.oneWayHash(this.rawProjectId)\n  }\n\n  record = (\n    _events: TelemetryEvent | TelemetryEvent[]\n  ): Promise<RecordObject> => {\n    const _this = this\n    // pseudo try-catch\n    async function wrapper() {\n      return await _this.submitRecord(_events)\n    }\n\n    const prom = wrapper()\n      .then((value) => ({\n        isFulfilled: true,\n        isRejected: false,\n        value,\n      }))\n      .catch((reason) => ({\n        isFulfilled: false,\n        isRejected: true,\n        reason,\n      }))\n      // Acts as `Promise#finally` because `catch` transforms the error\n      .then((res) => {\n        // Clean up the event to prevent unbounded `Set` growth\n        this.queue.delete(prom)\n        return res\n      })\n\n    // Track this `Promise` so we can flush pending events\n    this.queue.add(prom)\n\n    return prom\n  }\n\n  flush = async () => Promise.all(this.queue).catch(() => null)\n\n  private submitRecord = (\n    _events: TelemetryEvent | TelemetryEvent[]\n  ): Promise<any> => {\n    let events: TelemetryEvent[]\n    if (Array.isArray(_events)) {\n      events = _events\n    } else {\n      events = [_events]\n    }\n\n    if (events.length < 1) {\n      return Promise.resolve()\n    }\n\n    if (this.NEXT_TELEMETRY_DEBUG) {\n      // Print to standard error to simplify selecting the output\n      events.forEach(({ eventName, payload }) =>\n        console.error(\n          `[telemetry] ` + JSON.stringify({ eventName, payload }, null, 2)\n        )\n      )\n      // Do not send the telemetry data if debugging. Users may use this feature\n      // to preview what data would be sent.\n      return Promise.resolve()\n    }\n\n    // Skip recording telemetry if the feature is disabled\n    if (this.isDisabled) {\n      return Promise.resolve()\n    }\n\n    const context: EventContext = {\n      anonymousId: this.anonymousId,\n      projectId: this.projectId,\n      sessionId: this.sessionId,\n    }\n    const meta: EventMeta = getAnonymousMeta()\n    return _postPayload(`https://telemetry.nextjs.org/api/v1/record`, {\n      context,\n      meta,\n      events: events.map(({ eventName, payload }) => ({\n        eventName,\n        fields: payload,\n      })) as Array<EventBatchShape>,\n    })\n  }\n}\n\nfunction getStorageDirectory(distDir: string): string | undefined {\n  const isLikelyEphemeral = ciEnvironment.isCI || isDockerFunction()\n\n  if (isLikelyEphemeral) {\n    return path.join(distDir, 'cache')\n  }\n\n  return undefined\n}\n"], "names": [], "mappings": ";;;;AAAkB,GAAO,CAAP,MAAO;AACR,GAAyB,CAAzB,KAAyB;AACU,GAAQ,CAAR,OAAQ;AAC/B,GAA8B,CAA9B,SAA8B;AAC1C,GAAM,CAAN,KAAM;AAEU,GAAkB,CAAlB,cAAkB;AACvC,GAAa,CAAb,aAAa;AACI,GAAgB,CAAhB,YAAgB;AACb,GAAc,CAAd,UAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAE9C,EAA+E,AAA/E,6EAA+E;AAC/E,KAAK,CAAC,qBAAqB,IAAG,iBAAmB;AAEjD,EAA4E,AAA5E,0EAA4E;AAC5E,EAAwB,AAAxB,sBAAwB;AACxB,KAAK,CAAC,yBAAyB,IAAG,oBAAsB;AAExD,EAA8E,AAA9E,4EAA8E;AAC9E,EAAuD,AAAvD,qDAAuD;AACvD,KAAK,CAAC,gBAAgB,IAAI,qBAAqB;AAE/C,EAA6E,AAA7E,2EAA6E;AAC7E,EAA+E,AAA/E,6EAA+E;AAC/E,EAAoE,AAApE,kEAAoE;AACpE,EAAiC,AAAjC,+BAAiC;AACjC,KAAK,CAAC,kBAAkB,IAAI,cAAc;MAqB7B,SAAS;kBASN,OAAO,IAAyB,CAAC;aAuBvC,MAAM,OAAS,CAAC;YACtB,EAAE,OAAO,UAAU,UAAU,IAAI,EAAE,CAAC;;YAEpC,CAAC;YAED,EAA6E,AAA7E,2EAA6E;YAC7E,EAAgD,AAAhD,8CAAgD;YAChD,EAAyE,AAAzE,uEAAyE;YACzE,EAAa,AAAb,WAAa;YACb,EAAE,OAAO,IAAI,CAAC,GAAG,CAAC,yBAAyB,OAAO,CAAC;;YAEnD,CAAC;iBACI,IAAI,CAAC,GAAG,CAAC,yBAAyB,EAAE,IAAI,CAAC,GAAG,GAAG,QAAQ;YAE5D,OAAO,CAAC,GAAG,IA7FG,MAAO,SA8FV,OAAO,CAAC,IAAI,EACnB,SAAW,GACX,sEAAsE;YAE1E,OAAO,CAAC,GAAG,EACR,2EAA2E;YAE9E,OAAO,CAAC,GAAG,EACR,uIAAuI;YAE1I,OAAO,CAAC,GAAG,CAxGG,MAAO,SAwGH,IAAI,EAAC,4BAA8B;YACrD,OAAO,CAAC,GAAG;QACb,CAAC;aA+BD,UAAU,IAAI,QAAiB,GAAK,CAAC;YACnC,KAAK,CAAC,OAAO,KAAK,QAAQ;iBACrB,IAAI,SAAS,IAAI,CAAC,GAAG,CAAC,qBAAqB,EAAE,OAAO;QAC3D,CAAC;aAMD,UAAU,IAAI,OAAmB,GAAa,CAAC;YAC7C,KAAK,CAAC,IAAI,OAjJsC,OAAQ,cAiJhC,MAAQ;YAEhC,EAA6E,AAA7E,2EAA6E;YAC7E,EAAW,AAAX,SAAW;YACX,IAAI,CAAC,MAAM,MAAM,IAAI;YAErB,EAA4E,AAA5E,0EAA4E;YAC5E,EAA2B,AAA3B,yBAA2B;YAC3B,IAAI,CAAC,MAAM,CAAC,OAAO;mBACZ,IAAI,CAAC,MAAM,EAAC,GAAK;QAC1B,CAAC;aAMD,MAAM,IACJ,OAA0C,GAChB,CAAC;YAC3B,KAAK,CAAC,KAAK;YACX,EAAmB,AAAnB,iBAAmB;2BACJ,OAAO,GAAG,CAAC;6BACX,KAAK,CAAC,YAAY,CAAC,OAAO;YACzC,CAAC;YAED,KAAK,CAAC,IAAI,GAAG,OAAO,GACjB,IAAI,EAAE,KAAK;oBACV,WAAW,EAAE,IAAI;oBACjB,UAAU,EAAE,KAAK;oBACjB,KAAK;;cAEN,KAAK,EAAE,MAAM;oBACZ,WAAW,EAAE,KAAK;oBAClB,UAAU,EAAE,IAAI;oBAChB,MAAM;;aAER,EAAiE,AAAjE,+DAAiE;aAChE,IAAI,EAAE,GAAG,GAAK,CAAC;gBACd,EAAuD,AAAvD,qDAAuD;qBAClD,KAAK,CAAC,MAAM,CAAC,IAAI;uBACf,GAAG;YACZ,CAAC;YAEH,EAAsD,AAAtD,oDAAsD;iBACjD,KAAK,CAAC,GAAG,CAAC,IAAI;mBAEZ,IAAI;QACb,CAAC;aAED,KAAK,aAAe,OAAO,CAAC,GAAG,MAAM,KAAK,EAAE,KAAK,KAAO,IAAI;;;aAEpD,YAAY,IAClB,OAA0C,GACzB,CAAC;YAClB,GAAG,CAAC,MAAM;YACV,EAAE,EAAE,KAAK,CAAC,OAAO,CAAC,OAAO,GAAG,CAAC;gBAC3B,MAAM,GAAG,OAAO;YAClB,CAAC,MAAM,CAAC;gBACN,MAAM;oBAAI,OAAO;;YACnB,CAAC;YAED,EAAE,EAAE,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;uBACf,OAAO,CAAC,OAAO;YACxB,CAAC;YAED,EAAE,OAAO,oBAAoB,EAAE,CAAC;gBAC9B,EAA2D,AAA3D,yDAA2D;gBAC3D,MAAM,CAAC,OAAO,IAAI,SAAS,GAAE,OAAO,MAClC,OAAO,CAAC,KAAK,EACV,YAAY,IAAI,IAAI,CAAC,SAAS;wBAAG,SAAS;wBAAE,OAAO;uBAAI,IAAI,EAAE,CAAC;;gBAGnE,EAA0E,AAA1E,wEAA0E;gBAC1E,EAAsC,AAAtC,oCAAsC;uBAC/B,OAAO,CAAC,OAAO;YACxB,CAAC;YAED,EAAsD,AAAtD,oDAAsD;YACtD,EAAE,OAAO,UAAU,EAAE,CAAC;uBACb,OAAO,CAAC,OAAO;YACxB,CAAC;YAED,KAAK,CAAC,OAAO;gBACX,WAAW,OAAO,WAAW;gBAC7B,SAAS,OAAO,SAAS;gBACzB,SAAS,OAAO,SAAS;;YAE3B,KAAK,CAAC,IAAI,OApOmB,cAAkB;uBAEtB,YAAgB,gBAmOpB,0CAA0C;gBAC7D,OAAO;gBACP,IAAI;gBACJ,MAAM,EAAE,MAAM,CAAC,GAAG,IAAI,SAAS,GAAE,OAAO;wBACtC,SAAS;wBACT,MAAM,EAAE,OAAO;;;;QAGrB,CAAC;QA1LC,EAAoE,AAApE,kEAAoE;QACpE,KAAK,GAAG,uBAAuB,GAAE,oBAAoB,MAAK,OAAO,CAAC,GAAG;aAChE,uBAAuB,GAAG,uBAAuB;aACjD,oBAAoB,GAAG,oBAAoB;QAChD,KAAK,CAAC,gBAAgB,GAAG,mBAAmB,CAAC,OAAO;YAEhD,CAAC;YACH,EAAqE,AAArE,mEAAqE;YACrE,EAAwE,AAAxE,sEAAwE;YACxE,EAAqB,AAArB,mBAAqB;iBAChB,IAAI,GAAG,GAAG,CAlEJ,KAAyB;gBAkEb,WAAW,GAAE,MAAQ;gBAAE,GAAG,EAAE,gBAAgB;;QACrE,CAAC,QAAQ,CAAC,EAAE,CAAC;iBACN,IAAI,GAAG,IAAI;QAClB,CAAC;aACI,SAAS,OArEkC,OAAQ,cAqE3B,EAAE,EAAE,QAAQ,EAAC,GAAK;aAC1C,YAAY,OA/DW,UAAc;aAiErC,KAAK,GAAG,GAAG,CAAC,GAAG;aAEf,MAAM;IACb,CAAC;QA+BG,WAAW,GAAW,CAAC;QACzB,KAAK,CAAC,GAAG,QAAQ,IAAI,SAAS,IAAI,CAAC,GAAG,CAAC,gBAAgB;QACvD,EAAE,EAAE,GAAG,EAAE,CAAC;mBACD,GAAG;QACZ,CAAC;QAED,KAAK,CAAC,SAAS,OAhHiC,OAAQ,cAgH1B,EAAE,EAAE,QAAQ,EAAC,GAAK;aAC3C,IAAI,SAAS,IAAI,CAAC,GAAG,CAAC,gBAAgB,EAAE,SAAS;eAC/C,SAAS;IAClB,CAAC;QAEG,IAAI,GAAW,CAAC;QAClB,KAAK,CAAC,GAAG,QAAQ,IAAI,SAAS,IAAI,CAAC,GAAG,CAAC,kBAAkB;QACzD,EAAE,EAAE,GAAG,EAAE,CAAC;mBACD,GAAG;QACZ,CAAC;QAED,KAAK,CAAC,SAAS,OA3HiC,OAAQ,cA2H1B,EAAE,EAAE,QAAQ,EAAC,GAAK;aAC3C,IAAI,SAAS,IAAI,CAAC,GAAG,CAAC,kBAAkB,EAAE,SAAS;eACjD,SAAS;IAClB,CAAC;QAEW,UAAU,GAAY,CAAC;QACjC,EAAE,SAAS,uBAAuB,UAAU,IAAI,EAAE,CAAC;mBAC1C,IAAI;QACb,CAAC;oBACW,IAAI,CAAC,GAAG,CAAC,qBAAqB,EAAE,IAAI,MAAM,KAAK;IAC7D,CAAC;QAOG,SAAS,GAAY,CAAC;sBACV,IAAI,SAAS,IAAI,CAAC,GAAG,CAAC,qBAAqB,EAAE,IAAI,MAAM,KAAK;IAC5E,CAAC;QAeW,SAAS,GAAW,CAAC;oBACnB,UAAU,MAAM,YAAY;IAC1C,CAAC;;QAlHU,SAAS,GAAT,SAAS;SAuMb,mBAAmB,CAAC,QAAe,EAAsB,CAAC;IACjE,KAAK,CAAC,iBAAiB,GAhPb,aAAa,CAgPiB,IAAI,QApPjB,SAA8B;IAsPzD,EAAE,EAAE,iBAAiB,EAAE,CAAC;eArPT,KAAM,SAsPP,IAAI,CAAC,QAAO,GAAE,KAAO;IACnC,CAAC;WAEM,SAAS;AAClB,CAAC"}