{"version": 3, "sources": ["../../telemetry/post-payload.ts"], "sourcesContent": ["import retry from 'next/dist/compiled/async-retry'\nimport fetch from 'node-fetch'\n\nexport function _postPayload(endpoint: string, body: object) {\n  return (\n    retry(\n      () =>\n        fetch(endpoint, {\n          method: 'POST',\n          body: JSON.stringify(body),\n          headers: { 'content-type': 'application/json' },\n          timeout: 5000,\n        }).then((res) => {\n          if (!res.ok) {\n            const err = new Error(res.statusText)\n            ;(err as any).response = res\n            throw err\n          }\n        }),\n      { minTimeout: 500, retries: 1, factor: 1 }\n    )\n      .catch(() => {\n        // We swallow errors when telemetry cannot be sent\n      })\n      // Ensure promise is voided\n      .then(\n        () => {},\n        () => {}\n      )\n  )\n}\n"], "names": [], "mappings": ";;;;QAGgB,YAAY,GAAZ,YAAY;AAHV,GAAgC,CAAhC,WAAgC;AAChC,GAAY,CAAZ,UAAY;;;;;;SAEd,YAAY,CAAC,QAAgB,EAAE,IAAY,EAAE,CAAC;eAH5C,WAAgC,kBAChC,UAAY,UAMhB,QAAQ;YACZ,MAAM,GAAE,IAAM;YACd,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI;YACzB,OAAO;iBAAI,YAAc,IAAE,gBAAkB;;YAC7C,OAAO,EAAE,IAAI;WACZ,IAAI,EAAE,GAAG,GAAK,CAAC;YAChB,EAAE,GAAG,GAAG,CAAC,EAAE,EAAE,CAAC;gBACZ,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,UAAU;gBAClC,GAAG,CAAS,QAAQ,GAAG,GAAG;gBAC5B,KAAK,CAAC,GAAG;YACX,CAAC;QACH,CAAC;;QACD,UAAU,EAAE,GAAG;QAAE,OAAO,EAAE,CAAC;QAAE,MAAM,EAAE,CAAC;OAEvC,KAAK,KAAO,CAAC;IACZ,EAAkD,AAAlD,gDAAkD;IACpD,CAAC,CACD,EAA2B,AAA3B,yBAA2B;KAC1B,IAAI,KACG,CAAC;IAAA,CAAC,MACF,CAAC;IAAA,CAAC;AAGhB,CAAC"}