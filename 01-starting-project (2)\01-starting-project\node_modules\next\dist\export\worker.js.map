{"version": 3, "sources": ["../../export/worker.ts"], "sourcesContent": ["import url from 'url'\nimport { extname, join, dirname, sep } from 'path'\nimport { renderToHTML } from '../server/render'\nimport { promises } from 'fs'\nimport AmpHtmlValidator from 'next/dist/compiled/amphtml-validator'\nimport Observable from 'next/dist/compiled/zen-observable'\nimport { loadComponents } from '../server/load-components'\nimport { isDynamicRoute } from '../shared/lib/router/utils/is-dynamic'\nimport { getRouteMatcher } from '../shared/lib/router/utils/route-matcher'\nimport { getRouteRegex } from '../shared/lib/router/utils/route-regex'\nimport { normalizePagePath } from '../server/normalize-page-path'\nimport { SERVER_PROPS_EXPORT_ERROR } from '../lib/constants'\nimport '../server/node-polyfill-fetch'\nimport { IncomingMessage, ServerResponse } from 'http'\nimport { ComponentType } from 'react'\nimport { GetStaticProps } from '../types'\nimport { requireFontManifest } from '../server/require'\nimport { FontManifest } from '../server/font-utils'\nimport { normalizeLocalePath } from '../shared/lib/i18n/normalize-locale-path'\nimport { trace } from '../telemetry/trace'\nimport { isInAmpMode } from '../shared/lib/amp'\nimport { resultsToString } from '../server/utils'\nimport { NextConfigComplete } from '../server/config-shared'\nimport { setHttpAgentOptions } from '../server/config'\n\nconst envConfig = require('../shared/lib/runtime-config')\n\n;(global as any).__NEXT_DATA__ = {\n  nextExport: true,\n}\n\ninterface AmpValidation {\n  page: string\n  result: {\n    errors: AmpHtmlValidator.ValidationError[]\n    warnings: AmpHtmlValidator.ValidationError[]\n  }\n}\n\ninterface PathMap {\n  page: string\n  query?: { [key: string]: string | string[] }\n}\n\ninterface ExportPageInput {\n  path: string\n  pathMap: PathMap\n  distDir: string\n  outDir: string\n  pagesDataDir: string\n  renderOpts: RenderOpts\n  buildExport?: boolean\n  serverRuntimeConfig: { [key: string]: any }\n  subFolders?: boolean\n  serverless: boolean\n  optimizeFonts: boolean\n  optimizeImages?: boolean\n  optimizeCss: any\n  disableOptimizedLoading: any\n  parentSpanId: any\n  httpAgentOptions: NextConfigComplete['httpAgentOptions']\n}\n\ninterface ExportPageResults {\n  ampValidations: AmpValidation[]\n  fromBuildExportRevalidate?: number\n  error?: boolean\n  ssgNotFound?: boolean\n  duration: number\n}\n\ninterface RenderOpts {\n  runtimeConfig?: { [key: string]: any }\n  params?: { [key: string]: string | string[] }\n  ampPath?: string\n  ampValidatorPath?: string\n  ampSkipValidation?: boolean\n  optimizeFonts?: boolean\n  optimizeImages?: boolean\n  disableOptimizedLoading?: boolean\n  optimizeCss?: any\n  fontManifest?: FontManifest\n  locales?: string[]\n  locale?: string\n  defaultLocale?: string\n  trailingSlash?: boolean\n}\n\ntype ComponentModule = ComponentType<{}> & {\n  renderReqToHTML: typeof renderToHTML\n  getStaticProps?: GetStaticProps\n}\n\nexport default async function exportPage({\n  parentSpanId,\n  path,\n  pathMap,\n  distDir,\n  outDir,\n  pagesDataDir,\n  renderOpts,\n  buildExport,\n  serverRuntimeConfig,\n  subFolders,\n  serverless,\n  optimizeFonts,\n  optimizeImages,\n  optimizeCss,\n  disableOptimizedLoading,\n  httpAgentOptions,\n}: ExportPageInput): Promise<ExportPageResults> {\n  setHttpAgentOptions(httpAgentOptions)\n  const exportPageSpan = trace('export-page-worker', parentSpanId)\n\n  return exportPageSpan.traceAsyncFn(async () => {\n    const start = Date.now()\n    let results: Omit<ExportPageResults, 'duration'> = {\n      ampValidations: [],\n    }\n\n    try {\n      const { query: originalQuery = {} } = pathMap\n      const { page } = pathMap\n      const filePath = normalizePagePath(path)\n      const isDynamic = isDynamicRoute(page)\n      const ampPath = `${filePath}.amp`\n      let renderAmpPath = ampPath\n      let query = { ...originalQuery }\n      let params: { [key: string]: string | string[] } | undefined\n\n      let updatedPath = (query.__nextSsgPath as string) || path\n      let locale = query.__nextLocale || renderOpts.locale\n      delete query.__nextLocale\n      delete query.__nextSsgPath\n\n      if (renderOpts.locale) {\n        const localePathResult = normalizeLocalePath(path, renderOpts.locales)\n\n        if (localePathResult.detectedLocale) {\n          updatedPath = localePathResult.pathname\n          locale = localePathResult.detectedLocale\n\n          if (locale === renderOpts.defaultLocale) {\n            renderAmpPath = `${normalizePagePath(updatedPath)}.amp`\n          }\n        }\n      }\n\n      // We need to show a warning if they try to provide query values\n      // for an auto-exported page since they won't be available\n      const hasOrigQueryValues = Object.keys(originalQuery).length > 0\n      const queryWithAutoExportWarn = () => {\n        if (hasOrigQueryValues) {\n          throw new Error(\n            `\\nError: you provided query values for ${path} which is an auto-exported page. These can not be applied since the page can no longer be re-rendered on the server. To disable auto-export for this page add \\`getInitialProps\\`\\n`\n          )\n        }\n      }\n\n      // Check if the page is a specified dynamic route\n      const nonLocalizedPath = normalizeLocalePath(\n        path,\n        renderOpts.locales\n      ).pathname\n\n      if (isDynamic && page !== nonLocalizedPath) {\n        params = getRouteMatcher(getRouteRegex(page))(updatedPath) || undefined\n        if (params) {\n          // we have to pass these separately for serverless\n          if (!serverless) {\n            query = {\n              ...query,\n              ...params,\n            }\n          }\n        } else {\n          throw new Error(\n            `The provided export path '${updatedPath}' doesn't match the '${page}' page.\\nRead more: https://nextjs.org/docs/messages/export-path-mismatch`\n          )\n        }\n      }\n\n      const headerMocks = {\n        headers: {},\n        getHeader: () => ({}),\n        setHeader: () => {},\n        hasHeader: () => false,\n        removeHeader: () => {},\n        getHeaderNames: () => [],\n      }\n\n      const req = {\n        url: updatedPath,\n        ...headerMocks,\n      } as unknown as IncomingMessage\n      const res = {\n        ...headerMocks,\n      } as unknown as ServerResponse\n\n      if (path === '/500' && page === '/_error') {\n        res.statusCode = 500\n      }\n\n      if (renderOpts.trailingSlash && !req.url?.endsWith('/')) {\n        req.url += '/'\n      }\n\n      envConfig.setConfig({\n        serverRuntimeConfig,\n        publicRuntimeConfig: renderOpts.runtimeConfig,\n      })\n\n      const getHtmlFilename = (_path: string) =>\n        subFolders ? `${_path}${sep}index.html` : `${_path}.html`\n      let htmlFilename = getHtmlFilename(filePath)\n\n      const pageExt = extname(page)\n      const pathExt = extname(path)\n      // Make sure page isn't a folder with a dot in the name e.g. `v1.2`\n      if (pageExt !== pathExt && pathExt !== '') {\n        const isBuiltinPaths = ['/500', '/404'].some(\n          (p) => p === path || p === path + '.html'\n        )\n        // If the ssg path has .html extension, and it's not builtin paths, use it directly\n        // Otherwise, use that as the filename instead\n        const isHtmlExtPath =\n          !serverless && !isBuiltinPaths && path.endsWith('.html')\n        htmlFilename = isHtmlExtPath ? getHtmlFilename(path) : path\n      } else if (path === '/') {\n        // If the path is the root, just use index.html\n        htmlFilename = 'index.html'\n      }\n\n      const baseDir = join(outDir, dirname(htmlFilename))\n      let htmlFilepath = join(outDir, htmlFilename)\n\n      await promises.mkdir(baseDir, { recursive: true })\n      let renderResult\n      let curRenderOpts: RenderOpts = {}\n      let renderMethod = renderToHTML\n      let inAmpMode = false,\n        hybridAmp = false\n\n      const renderedDuringBuild = (getStaticProps: any) => {\n        return !buildExport && getStaticProps && !isDynamicRoute(path)\n      }\n\n      if (serverless) {\n        const curUrl = url.parse(req.url!, true)\n        req.url = url.format({\n          ...curUrl,\n          query: {\n            ...curUrl.query,\n            ...query,\n          },\n        })\n        const {\n          Component: mod,\n          getServerSideProps,\n          pageConfig,\n        } = await loadComponents(distDir, page, serverless)\n        const ampState = {\n          ampFirst: pageConfig?.amp === true,\n          hasQuery: Boolean(query.amp),\n          hybrid: pageConfig?.amp === 'hybrid',\n        }\n        inAmpMode = isInAmpMode(ampState)\n        hybridAmp = ampState.hybrid\n\n        if (getServerSideProps) {\n          throw new Error(\n            `Error for page ${page}: ${SERVER_PROPS_EXPORT_ERROR}`\n          )\n        }\n\n        // if it was auto-exported the HTML is loaded here\n        if (typeof mod === 'string') {\n          renderResult = Observable.of(mod)\n          queryWithAutoExportWarn()\n        } else {\n          // for non-dynamic SSG pages we should have already\n          // prerendered the file\n          if (renderedDuringBuild((mod as ComponentModule).getStaticProps))\n            return { ...results, duration: Date.now() - start }\n\n          if (\n            (mod as ComponentModule).getStaticProps &&\n            !htmlFilepath.endsWith('.html')\n          ) {\n            // make sure it ends with .html if the name contains a dot\n            htmlFilename += '.html'\n            htmlFilepath += '.html'\n          }\n\n          renderMethod = (mod as ComponentModule).renderReqToHTML\n          const result = await renderMethod(\n            req,\n            res,\n            'export',\n            {\n              ampPath: renderAmpPath,\n              /// @ts-ignore\n              optimizeFonts,\n              /// @ts-ignore\n              optimizeImages,\n              /// @ts-ignore\n              optimizeCss,\n              disableOptimizedLoading,\n              distDir,\n              fontManifest: optimizeFonts\n                ? requireFontManifest(distDir, serverless)\n                : null,\n              locale: locale!,\n              locales: renderOpts.locales!,\n            },\n            // @ts-ignore\n            params\n          )\n          curRenderOpts = (result as any).renderOpts || {}\n          renderResult = (result as any).html\n        }\n\n        if (!renderResult && !(curRenderOpts as any).isNotFound) {\n          throw new Error(`Failed to render serverless page`)\n        }\n      } else {\n        const components = await loadComponents(distDir, page, serverless)\n        const ampState = {\n          ampFirst: components.pageConfig?.amp === true,\n          hasQuery: Boolean(query.amp),\n          hybrid: components.pageConfig?.amp === 'hybrid',\n        }\n        inAmpMode = isInAmpMode(ampState)\n        hybridAmp = ampState.hybrid\n\n        if (components.getServerSideProps) {\n          throw new Error(\n            `Error for page ${page}: ${SERVER_PROPS_EXPORT_ERROR}`\n          )\n        }\n\n        // for non-dynamic SSG pages we should have already\n        // prerendered the file\n        if (renderedDuringBuild(components.getStaticProps)) {\n          return { ...results, duration: Date.now() - start }\n        }\n\n        // TODO: de-dupe the logic here between serverless and server mode\n        if (components.getStaticProps && !htmlFilepath.endsWith('.html')) {\n          // make sure it ends with .html if the name contains a dot\n          htmlFilepath += '.html'\n          htmlFilename += '.html'\n        }\n\n        if (typeof components.Component === 'string') {\n          renderResult = Observable.of(components.Component)\n          queryWithAutoExportWarn()\n        } else {\n          /**\n           * This sets environment variable to be used at the time of static export by head.tsx.\n           * Using this from process.env allows targeting both serverless and SSR by calling\n           * `process.env.__NEXT_OPTIMIZE_FONTS`.\n           * TODO(prateekbh@): Remove this when experimental.optimizeFonts are being cleaned up.\n           */\n          if (optimizeFonts) {\n            process.env.__NEXT_OPTIMIZE_FONTS = JSON.stringify(true)\n          }\n          if (optimizeImages) {\n            process.env.__NEXT_OPTIMIZE_IMAGES = JSON.stringify(true)\n          }\n          if (optimizeCss) {\n            process.env.__NEXT_OPTIMIZE_CSS = JSON.stringify(true)\n          }\n          curRenderOpts = {\n            ...components,\n            ...renderOpts,\n            ampPath: renderAmpPath,\n            params,\n            optimizeFonts,\n            optimizeImages,\n            optimizeCss,\n            disableOptimizedLoading,\n            fontManifest: optimizeFonts\n              ? requireFontManifest(distDir, serverless)\n              : null,\n            locale: locale as string,\n          }\n          renderResult = await renderMethod(\n            req,\n            res,\n            page,\n            query,\n            // @ts-ignore\n            curRenderOpts\n          )\n        }\n      }\n      results.ssgNotFound = (curRenderOpts as any).isNotFound\n\n      const validateAmp = async (\n        rawAmpHtml: string,\n        ampPageName: string,\n        validatorPath?: string\n      ) => {\n        const validator = await AmpHtmlValidator.getInstance(validatorPath)\n        const result = validator.validateString(rawAmpHtml)\n        const errors = result.errors.filter((e) => e.severity === 'ERROR')\n        const warnings = result.errors.filter((e) => e.severity !== 'ERROR')\n\n        if (warnings.length || errors.length) {\n          results.ampValidations.push({\n            page: ampPageName,\n            result: {\n              errors,\n              warnings,\n            },\n          })\n        }\n      }\n\n      const html = renderResult ? await resultsToString([renderResult]) : ''\n      if (inAmpMode && !curRenderOpts.ampSkipValidation) {\n        if (!results.ssgNotFound) {\n          await validateAmp(html, path, curRenderOpts.ampValidatorPath)\n        }\n      } else if (hybridAmp) {\n        // we need to render the AMP version\n        let ampHtmlFilename = `${ampPath}${sep}index.html`\n        if (!subFolders) {\n          ampHtmlFilename = `${ampPath}.html`\n        }\n        const ampBaseDir = join(outDir, dirname(ampHtmlFilename))\n        const ampHtmlFilepath = join(outDir, ampHtmlFilename)\n\n        try {\n          await promises.access(ampHtmlFilepath)\n        } catch (_) {\n          // make sure it doesn't exist from manual mapping\n          let ampRenderResult\n          if (serverless) {\n            req.url += (req.url!.includes('?') ? '&' : '?') + 'amp=1'\n            // @ts-ignore\n            ampRenderResult = (\n              await (renderMethod as any)(\n                req,\n                res,\n                'export',\n                curRenderOpts,\n                params\n              )\n            ).html\n          } else {\n            ampRenderResult = await renderMethod(\n              req,\n              res,\n              page,\n              // @ts-ignore\n              { ...query, amp: '1' },\n              curRenderOpts as any\n            )\n          }\n\n          const ampHtml = ampRenderResult\n            ? await resultsToString([ampRenderResult])\n            : ''\n          if (!curRenderOpts.ampSkipValidation) {\n            await validateAmp(ampHtml, page + '?amp=1')\n          }\n          await promises.mkdir(ampBaseDir, { recursive: true })\n          await promises.writeFile(ampHtmlFilepath, ampHtml, 'utf8')\n        }\n      }\n\n      if ((curRenderOpts as any).pageData) {\n        const dataFile = join(\n          pagesDataDir,\n          htmlFilename.replace(/\\.html$/, '.json')\n        )\n\n        await promises.mkdir(dirname(dataFile), { recursive: true })\n        await promises.writeFile(\n          dataFile,\n          JSON.stringify((curRenderOpts as any).pageData),\n          'utf8'\n        )\n\n        if (hybridAmp) {\n          await promises.writeFile(\n            dataFile.replace(/\\.json$/, '.amp.json'),\n            JSON.stringify((curRenderOpts as any).pageData),\n            'utf8'\n          )\n        }\n      }\n      results.fromBuildExportRevalidate = (curRenderOpts as any).revalidate\n\n      if (!results.ssgNotFound) {\n        // don't attempt writing to disk if getStaticProps returned not found\n        await promises.writeFile(htmlFilepath, html, 'utf8')\n      }\n    } catch (error) {\n      console.error(\n        `\\nError occurred prerendering page \"${path}\". Read more: https://nextjs.org/docs/messages/prerender-error\\n` +\n          error.stack\n      )\n      results.error = true\n    }\n    return { ...results, duration: Date.now() - start }\n  })\n}\n"], "names": [], "mappings": ";;;;kBA6F8B,UAAU;AA7FxB,GAAK,CAAL,IAAK;AACuB,GAAM,CAAN,KAAM;AACrB,GAAkB,CAAlB,OAAkB;AACtB,GAAI,CAAJ,GAAI;AACA,GAAsC,CAAtC,iBAAsC;AAC5C,GAAmC,CAAnC,cAAmC;AAC3B,GAA2B,CAA3B,eAA2B;AAC3B,GAAuC,CAAvC,UAAuC;AACtC,GAA0C,CAA1C,aAA0C;AAC5C,GAAwC,CAAxC,WAAwC;AACpC,GAA+B,CAA/B,kBAA+B;AACvB,GAAkB,CAAlB,UAAkB;;AAKxB,GAAmB,CAAnB,QAAmB;AAEnB,GAA0C,CAA1C,oBAA0C;AACxD,GAAoB,CAApB,MAAoB;AACd,GAAmB,CAAnB,IAAmB;AACf,GAAiB,CAAjB,MAAiB;AAEb,GAAkB,CAAlB,OAAkB;;;;;;AAEtD,KAAK,CAAC,SAAS,GAAG,OAAO,EAAC,4BAA8B;AAEtD,MAAM,CAAS,aAAa;IAC5B,UAAU,EAAE,IAAI;;eAiEY,UAAU,GACtC,YAAY,GACZ,IAAI,GACJ,OAAO,GACP,OAAO,GACP,MAAM,GACN,YAAY,GACZ,UAAU,GACV,WAAW,GACX,mBAAmB,GACnB,UAAU,GACV,UAAU,GACV,aAAa,GACb,cAAc,GACd,WAAW,GACX,uBAAuB,GACvB,gBAAgB,KAC8B,CAAC;QAvFb,OAAkB,sBAwFhC,gBAAgB;IACpC,KAAK,CAAC,cAAc,OA7FA,MAAoB,SA6FX,kBAAoB,GAAE,YAAY;WAExD,cAAc,CAAC,YAAY,WAAa,CAAC;QAC9C,KAAK,CAAC,KAAK,GAAG,IAAI,CAAC,GAAG;QACtB,GAAG,CAAC,OAAO;YACT,cAAc;;YAGZ,CAAC;gBAmF8B,GAAO;YAlFxC,KAAK,GAAG,KAAK,EAAE,aAAa;mBAAU,OAAO;YAC7C,KAAK,GAAG,IAAI,MAAK,OAAO;YACxB,KAAK,CAAC,QAAQ,OAjHc,kBAA+B,oBAiHxB,IAAI;YACvC,KAAK,CAAC,SAAS,OArHU,UAAuC,iBAqH/B,IAAI;YACrC,KAAK,CAAC,OAAO,MAAM,QAAQ,CAAC,IAAI;YAChC,GAAG,CAAC,aAAa,GAAG,OAAO;YAC3B,GAAG,CAAC,KAAK;mBAAQ,aAAa;;YAC9B,GAAG,CAAC,MAAM;YAEV,GAAG,CAAC,WAAW,GAAI,KAAK,CAAC,aAAa,IAAe,IAAI;YACzD,GAAG,CAAC,MAAM,GAAG,KAAK,CAAC,YAAY,IAAI,UAAU,CAAC,MAAM;mBAC7C,KAAK,CAAC,YAAY;mBAClB,KAAK,CAAC,aAAa;YAE1B,EAAE,EAAE,UAAU,CAAC,MAAM,EAAE,CAAC;gBACtB,KAAK,CAAC,gBAAgB,OAtHM,oBAA0C,sBAsHzB,IAAI,EAAE,UAAU,CAAC,OAAO;gBAErE,EAAE,EAAE,gBAAgB,CAAC,cAAc,EAAE,CAAC;oBACpC,WAAW,GAAG,gBAAgB,CAAC,QAAQ;oBACvC,MAAM,GAAG,gBAAgB,CAAC,cAAc;oBAExC,EAAE,EAAE,MAAM,KAAK,UAAU,CAAC,aAAa,EAAE,CAAC;wBACxC,aAAa,UArIS,kBAA+B,oBAqIhB,WAAW,EAAE,IAAI;oBACxD,CAAC;gBACH,CAAC;YACH,CAAC;YAED,EAAgE,AAAhE,8DAAgE;YAChE,EAA0D,AAA1D,wDAA0D;YAC1D,KAAK,CAAC,kBAAkB,GAAG,MAAM,CAAC,IAAI,CAAC,aAAa,EAAE,MAAM,GAAG,CAAC;YAChE,KAAK,CAAC,uBAAuB,OAAS,CAAC;gBACrC,EAAE,EAAE,kBAAkB,EAAE,CAAC;oBACvB,KAAK,CAAC,GAAG,CAAC,KAAK,EACZ,uCAAuC,EAAE,IAAI,CAAC,mLAAmL;gBAEtO,CAAC;YACH,CAAC;YAED,EAAiD,AAAjD,+CAAiD;YACjD,KAAK,CAAC,gBAAgB,OA9IQ,oBAA0C,sBA+ItE,IAAI,EACJ,UAAU,CAAC,OAAO,EAClB,QAAQ;YAEV,EAAE,EAAE,SAAS,IAAI,IAAI,KAAK,gBAAgB,EAAE,CAAC;gBAC3C,MAAM,OA9JkB,aAA0C,sBAC5C,WAAwC,gBA6JvB,IAAI,GAAG,WAAW,KAAK,SAAS;gBACvE,EAAE,EAAE,MAAM,EAAE,CAAC;oBACX,EAAkD,AAAlD,gDAAkD;oBAClD,EAAE,GAAG,UAAU,EAAE,CAAC;wBAChB,KAAK;+BACA,KAAK;+BACL,MAAM;;oBAEb,CAAC;gBACH,CAAC,MAAM,CAAC;oBACN,KAAK,CAAC,GAAG,CAAC,KAAK,EACZ,0BAA0B,EAAE,WAAW,CAAC,qBAAqB,EAAE,IAAI,CAAC,yEAAyE;gBAElJ,CAAC;YACH,CAAC;YAED,KAAK,CAAC,WAAW;gBACf,OAAO;;gBACP,SAAS;;;gBACT,SAAS,MAAQ,CAAC;gBAAA,CAAC;gBACnB,SAAS,MAAQ,KAAK;;gBACtB,YAAY,MAAQ,CAAC;gBAAA,CAAC;gBACtB,cAAc;;YAGhB,KAAK,CAAC,GAAG;gBACP,GAAG,EAAE,WAAW;mBACb,WAAW;;YAEhB,KAAK,CAAC,GAAG;mBACJ,WAAW;;YAGhB,EAAE,EAAE,IAAI,MAAK,IAAM,KAAI,IAAI,MAAK,OAAS,GAAE,CAAC;gBAC1C,GAAG,CAAC,UAAU,GAAG,GAAG;YACtB,CAAC;YAED,EAAE,EAAE,UAAU,CAAC,aAAa,OAAK,GAAO,GAAP,GAAG,CAAC,GAAG,cAAP,GAAO,UAAP,CAAiB,QAAjB,CAAiB,GAAjB,GAAO,CAAE,QAAQ,EAAC,CAAG,KAAG,CAAC;gBACxD,GAAG,CAAC,GAAG,KAAI,CAAG;YAChB,CAAC;YAED,SAAS,CAAC,SAAS;gBACjB,mBAAmB;gBACnB,mBAAmB,EAAE,UAAU,CAAC,aAAa;;YAG/C,KAAK,CAAC,eAAe,IAAI,MAAa,GACpC,UAAU,MAAM,MAAK,GApNe,KAAM,KAoNd,UAAU,OAAO,MAAK,CAAC,KAAK;;YAC1D,GAAG,CAAC,YAAY,GAAG,eAAe,CAAC,QAAQ;YAE3C,KAAK,CAAC,OAAO,OAvNyB,KAAM,UAuNpB,IAAI;YAC5B,KAAK,CAAC,OAAO,OAxNyB,KAAM,UAwNpB,IAAI;YAC5B,EAAmE,AAAnE,iEAAmE;YACnE,EAAE,EAAE,OAAO,KAAK,OAAO,IAAI,OAAO,SAAS,CAAC;gBAC1C,KAAK,CAAC,cAAc;qBAAI,IAAM;qBAAE,IAAM;kBAAE,IAAI,EACzC,CAAC,GAAK,CAAC,KAAK,IAAI,IAAI,CAAC,KAAK,IAAI,IAAG,KAAO;;gBAE3C,EAAmF,AAAnF,iFAAmF;gBACnF,EAA8C,AAA9C,4CAA8C;gBAC9C,KAAK,CAAC,aAAa,IAChB,UAAU,KAAK,cAAc,IAAI,IAAI,CAAC,QAAQ,EAAC,KAAO;gBACzD,YAAY,GAAG,aAAa,GAAG,eAAe,CAAC,IAAI,IAAI,IAAI;YAC7D,CAAC,MAAM,EAAE,EAAE,IAAI,MAAK,CAAG,GAAE,CAAC;gBACxB,EAA+C,AAA/C,6CAA+C;gBAC/C,YAAY,IAAG,UAAY;YAC7B,CAAC;YAED,KAAK,CAAC,OAAO,OAxOyB,KAAM,OAwOvB,MAAM,MAxOW,KAAM,UAwOP,YAAY;YACjD,GAAG,CAAC,YAAY,OAzOsB,KAAM,OAyOpB,MAAM,EAAE,YAAY;kBAvOzB,GAAI,UAyOR,KAAK,CAAC,OAAO;gBAAI,SAAS,EAAE,IAAI;;YAC/C,GAAG,CAAC,YAAY;YAChB,GAAG,CAAC,aAAa;;YACjB,GAAG,CAAC,YAAY,GA7OO,OAAkB;YA8OzC,GAAG,CAAC,SAAS,GAAG,KAAK,EACnB,SAAS,GAAG,KAAK;YAEnB,KAAK,CAAC,mBAAmB,IAAI,cAAmB,GAAK,CAAC;wBAC5C,WAAW,IAAI,cAAc,SA7Od,UAAuC,iBA6OL,IAAI;YAC/D,CAAC;YAED,EAAE,EAAE,UAAU,EAAE,CAAC;gBACf,KAAK,CAAC,MAAM,GAxPJ,IAAK,SAwPM,KAAK,CAAC,GAAG,CAAC,GAAG,EAAG,IAAI;gBACvC,GAAG,CAAC,GAAG,GAzPC,IAAK,SAyPC,MAAM;uBACf,MAAM;oBACT,KAAK;2BACA,MAAM,CAAC,KAAK;2BACZ,KAAK;;;gBAGZ,KAAK,GACH,SAAS,EAAE,GAAG,GACd,kBAAkB,GAClB,UAAU,kBA7PW,eAA2B,iBA8PzB,OAAO,EAAE,IAAI,EAAE,UAAU;gBAClD,KAAK,CAAC,QAAQ;oBACZ,QAAQ,GAAE,UAAU,aAAV,UAAU,UAAV,CAAe,QAAf,CAAe,GAAf,UAAU,CAAE,GAAG,MAAK,IAAI;oBAClC,QAAQ,EAAE,OAAO,CAAC,KAAK,CAAC,GAAG;oBAC3B,MAAM,GAAE,UAAU,aAAV,UAAU,UAAV,CAAe,QAAf,CAAe,GAAf,UAAU,CAAE,GAAG,OAAK,MAAQ;;gBAEtC,SAAS,OAtPW,IAAmB,cAsPf,QAAQ;gBAChC,SAAS,GAAG,QAAQ,CAAC,MAAM;gBAE3B,EAAE,EAAE,kBAAkB,EAAE,CAAC;oBACvB,KAAK,CAAC,GAAG,CAAC,KAAK,EACZ,eAAe,EAAE,IAAI,CAAC,EAAE,EApQK,UAAkB;gBAsQpD,CAAC;gBAED,EAAkD,AAAlD,gDAAkD;gBAClD,EAAE,SAAS,GAAG,MAAK,MAAQ,GAAE,CAAC;oBAC5B,YAAY,GAhRC,cAAmC,SAgRtB,EAAE,CAAC,GAAG;oBAChC,uBAAuB;gBACzB,CAAC,MAAM,CAAC;oBACN,EAAmD,AAAnD,iDAAmD;oBACnD,EAAuB,AAAvB,qBAAuB;oBACvB,EAAE,EAAE,mBAAmB,CAAE,GAAG,CAAqB,cAAc;2BACjD,OAAO;wBAAE,QAAQ,EAAE,IAAI,CAAC,GAAG,KAAK,KAAK;;oBAEnD,EAAE,EACC,GAAG,CAAqB,cAAc,KACtC,YAAY,CAAC,QAAQ,EAAC,KAAO,IAC9B,CAAC;wBACD,EAA0D,AAA1D,wDAA0D;wBAC1D,YAAY,KAAI,KAAO;wBACvB,YAAY,KAAI,KAAO;oBACzB,CAAC;oBAED,YAAY,GAAI,GAAG,CAAqB,eAAe;oBACvD,KAAK,CAAC,MAAM,SAAS,YAAY,CAC/B,GAAG,EACH,GAAG,GACH,MAAQ;wBAEN,OAAO,EAAE,aAAa;wBACtB,EAAc,AAAd,YAAc;wBACd,aAAa;wBACb,EAAc,AAAd,YAAc;wBACd,cAAc;wBACd,EAAc,AAAd,YAAc;wBACd,WAAW;wBACX,uBAAuB;wBACvB,OAAO;wBACP,YAAY,EAAE,aAAa,OArSL,QAAmB,sBAsSjB,OAAO,EAAE,UAAU,IACvC,IAAI;wBACR,MAAM,EAAE,MAAM;wBACd,OAAO,EAAE,UAAU,CAAC,OAAO;uBAE7B,EAAa,AAAb,WAAa;oBACb,MAAM;oBAER,aAAa,GAAI,MAAM,CAAS,UAAU;;oBAC1C,YAAY,GAAI,MAAM,CAAS,IAAI;gBACrC,CAAC;gBAED,EAAE,GAAG,YAAY,KAAM,aAAa,CAAS,UAAU,EAAE,CAAC;oBACxD,KAAK,CAAC,GAAG,CAAC,KAAK,EAAE,gCAAgC;gBACnD,CAAC;YACH,CAAC,MAAM,CAAC;oBAGM,IAAqB,EAEvB,IAAqB;gBAJ/B,KAAK,CAAC,UAAU,aAhUO,eAA2B,iBAgUV,OAAO,EAAE,IAAI,EAAE,UAAU;gBACjE,KAAK,CAAC,QAAQ;oBACZ,QAAQ,IAAE,IAAqB,GAArB,UAAU,CAAC,UAAU,cAArB,IAAqB,UAArB,CAA0B,QAA1B,CAA0B,GAA1B,IAAqB,CAAE,GAAG,MAAK,IAAI;oBAC7C,QAAQ,EAAE,OAAO,CAAC,KAAK,CAAC,GAAG;oBAC3B,MAAM,IAAE,IAAqB,GAArB,UAAU,CAAC,UAAU,cAArB,IAAqB,UAArB,CAA0B,QAA1B,CAA0B,GAA1B,IAAqB,CAAE,GAAG,OAAK,MAAQ;;gBAEjD,SAAS,OAxTW,IAAmB,cAwTf,QAAQ;gBAChC,SAAS,GAAG,QAAQ,CAAC,MAAM;gBAE3B,EAAE,EAAE,UAAU,CAAC,kBAAkB,EAAE,CAAC;oBAClC,KAAK,CAAC,GAAG,CAAC,KAAK,EACZ,eAAe,EAAE,IAAI,CAAC,EAAE,EAtUK,UAAkB;gBAwUpD,CAAC;gBAED,EAAmD,AAAnD,iDAAmD;gBACnD,EAAuB,AAAvB,qBAAuB;gBACvB,EAAE,EAAE,mBAAmB,CAAC,UAAU,CAAC,cAAc,GAAG,CAAC;;2BACvC,OAAO;wBAAE,QAAQ,EAAE,IAAI,CAAC,GAAG,KAAK,KAAK;;gBACnD,CAAC;gBAED,EAAkE,AAAlE,gEAAkE;gBAClE,EAAE,EAAE,UAAU,CAAC,cAAc,KAAK,YAAY,CAAC,QAAQ,EAAC,KAAO,IAAG,CAAC;oBACjE,EAA0D,AAA1D,wDAA0D;oBAC1D,YAAY,KAAI,KAAO;oBACvB,YAAY,KAAI,KAAO;gBACzB,CAAC;gBAED,EAAE,SAAS,UAAU,CAAC,SAAS,MAAK,MAAQ,GAAE,CAAC;oBAC7C,YAAY,GA9VC,cAAmC,SA8VtB,EAAE,CAAC,UAAU,CAAC,SAAS;oBACjD,uBAAuB;gBACzB,CAAC,MAAM,CAAC;oBACN,EAKG,AALH;;;;;WAKG,AALH,EAKG,CACH,EAAE,EAAE,aAAa,EAAE,CAAC;wBAClB,OAAO,CAAC,GAAG,CAAC,qBAAqB,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI;oBACzD,CAAC;oBACD,EAAE,EAAE,cAAc,EAAE,CAAC;wBACnB,OAAO,CAAC,GAAG,CAAC,sBAAsB,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI;oBAC1D,CAAC;oBACD,EAAE,EAAE,WAAW,EAAE,CAAC;wBAChB,OAAO,CAAC,GAAG,CAAC,mBAAmB,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI;oBACvD,CAAC;oBACD,aAAa;2BACR,UAAU;2BACV,UAAU;wBACb,OAAO,EAAE,aAAa;wBACtB,MAAM;wBACN,aAAa;wBACb,cAAc;wBACd,WAAW;wBACX,uBAAuB;wBACvB,YAAY,EAAE,aAAa,OA9WH,QAAmB,sBA+WnB,OAAO,EAAE,UAAU,IACvC,IAAI;wBACR,MAAM,EAAE,MAAM;;oBAEhB,YAAY,SAAS,YAAY,CAC/B,GAAG,EACH,GAAG,EACH,IAAI,EACJ,KAAK,EACL,EAAa,AAAb,WAAa;oBACb,aAAa;gBAEjB,CAAC;YACH,CAAC;YACD,OAAO,CAAC,WAAW,GAAI,aAAa,CAAS,UAAU;YAEvD,KAAK,CAAC,WAAW,UACf,UAAkB,EAClB,WAAmB,EACnB,aAAsB,GACnB,CAAC;gBACJ,KAAK,CAAC,SAAS,SAhZM,iBAAsC,SAgZlB,WAAW,CAAC,aAAa;gBAClE,KAAK,CAAC,MAAM,GAAG,SAAS,CAAC,cAAc,CAAC,UAAU;gBAClD,KAAK,CAAC,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC,GAAK,CAAC,CAAC,QAAQ,MAAK,KAAO;;gBACjE,KAAK,CAAC,QAAQ,GAAG,MAAM,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC,GAAK,CAAC,CAAC,QAAQ,MAAK,KAAO;;gBAEnE,EAAE,EAAE,QAAQ,CAAC,MAAM,IAAI,MAAM,CAAC,MAAM,EAAE,CAAC;oBACrC,OAAO,CAAC,cAAc,CAAC,IAAI;wBACzB,IAAI,EAAE,WAAW;wBACjB,MAAM;4BACJ,MAAM;4BACN,QAAQ;;;gBAGd,CAAC;YACH,CAAC;YAED,KAAK,CAAC,IAAI,GAAG,YAAY,aA/YC,MAAiB;gBA+YQ,YAAY;;YAC/D,EAAE,EAAE,SAAS,KAAK,aAAa,CAAC,iBAAiB,EAAE,CAAC;gBAClD,EAAE,GAAG,OAAO,CAAC,WAAW,EAAE,CAAC;0BACnB,WAAW,CAAC,IAAI,EAAE,IAAI,EAAE,aAAa,CAAC,gBAAgB;gBAC9D,CAAC;YACH,CAAC,MAAM,EAAE,EAAE,SAAS,EAAE,CAAC;gBACrB,EAAoC,AAApC,kCAAoC;gBACpC,GAAG,CAAC,eAAe,MAAM,OAAO,GA1aI,KAAM,KA0aH,UAAU;gBACjD,EAAE,GAAG,UAAU,EAAE,CAAC;oBAChB,eAAe,MAAM,OAAO,CAAC,KAAK;gBACpC,CAAC;gBACD,KAAK,CAAC,UAAU,OA9aoB,KAAM,OA8alB,MAAM,MA9aM,KAAM,UA8aF,eAAe;gBACvD,KAAK,CAAC,eAAe,OA/ae,KAAM,OA+ab,MAAM,EAAE,eAAe;oBAEhD,CAAC;0BA/aY,GAAI,UAgbJ,MAAM,CAAC,eAAe;gBACvC,CAAC,QAAQ,CAAC,EAAE,CAAC;oBACX,EAAiD,AAAjD,+CAAiD;oBACjD,GAAG,CAAC,eAAe;oBACnB,EAAE,EAAE,UAAU,EAAE,CAAC;wBACf,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,GAAG,CAAE,QAAQ,EAAC,CAAG,MAAI,CAAG,KAAG,CAAG,MAAI,KAAO;wBACzD,EAAa,AAAb,WAAa;wBACb,eAAe,UACN,YAAY,CACjB,GAAG,EACH,GAAG,GACH,MAAQ,GACR,aAAa,EACb,MAAM,GAER,IAAI;oBACR,CAAC,MAAM,CAAC;wBACN,eAAe,SAAS,YAAY,CAClC,GAAG,EACH,GAAG,EACH,IAAI,EACJ,EAAa,AAAb,WAAa;;+BACR,KAAK;4BAAE,GAAG,GAAE,CAAG;2BACpB,aAAa;oBAEjB,CAAC;oBAED,KAAK,CAAC,OAAO,GAAG,eAAe,aAzbT,MAAiB;wBA0bZ,eAAe;;oBAE1C,EAAE,GAAG,aAAa,CAAC,iBAAiB,EAAE,CAAC;8BAC/B,WAAW,CAAC,OAAO,EAAE,IAAI,IAAG,MAAQ;oBAC5C,CAAC;0BAhdc,GAAI,UAidJ,KAAK,CAAC,UAAU;wBAAI,SAAS,EAAE,IAAI;;0BAjdnC,GAAI,UAkdJ,SAAS,CAAC,eAAe,EAAE,OAAO,GAAE,IAAM;gBAC3D,CAAC;YACH,CAAC;YAED,EAAE,EAAG,aAAa,CAAS,QAAQ,EAAE,CAAC;gBACpC,KAAK,CAAC,QAAQ,OAzdsB,KAAM,OA0dxC,YAAY,EACZ,YAAY,CAAC,OAAO,aAAY,KAAO;sBAzdxB,GAAI,UA4dN,KAAK,KA9dgB,KAAM,UA8db,QAAQ;oBAAK,SAAS,EAAE,IAAI;;sBA5dxC,GAAI,UA6dN,SAAS,CACtB,QAAQ,EACR,IAAI,CAAC,SAAS,CAAE,aAAa,CAAS,QAAQ,IAC9C,IAAM;gBAGR,EAAE,EAAE,SAAS,EAAE,CAAC;0BAneC,GAAI,UAoeJ,SAAS,CACtB,QAAQ,CAAC,OAAO,aAAY,SAAW,IACvC,IAAI,CAAC,SAAS,CAAE,aAAa,CAAS,QAAQ,IAC9C,IAAM;gBAEV,CAAC;YACH,CAAC;YACD,OAAO,CAAC,yBAAyB,GAAI,aAAa,CAAS,UAAU;YAErE,EAAE,GAAG,OAAO,CAAC,WAAW,EAAE,CAAC;gBACzB,EAAqE,AAArE,mEAAqE;sBA9epD,GAAI,UA+eN,SAAS,CAAC,YAAY,EAAE,IAAI,GAAE,IAAM;YACrD,CAAC;QACH,CAAC,QAAQ,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,EACV,oCAAoC,EAAE,IAAI,CAAC,gEAAgE,IAC1G,KAAK,CAAC,KAAK;YAEf,OAAO,CAAC,KAAK,GAAG,IAAI;QACtB,CAAC;;eACW,OAAO;YAAE,QAAQ,EAAE,IAAI,CAAC,GAAG,KAAK,KAAK;;IACnD,CAAC;AACH,CAAC"}