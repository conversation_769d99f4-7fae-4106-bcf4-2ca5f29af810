{"version": 3, "sources": ["../../cli/next-export.ts"], "sourcesContent": ["#!/usr/bin/env node\nimport { resolve, join } from 'path'\nimport { existsSync } from 'fs'\nimport arg from 'next/dist/compiled/arg/index.js'\nimport exportApp from '../export'\nimport { printAndExit } from '../server/lib/utils'\nimport { cliCommand } from '../bin/next'\n\nconst nextExport: cliCommand = (argv) => {\n  const validArgs: arg.Spec = {\n    // Types\n    '--help': Boolean,\n    '--silent': Boolean,\n    '--outdir': String,\n    '--threads': Number,\n\n    // Aliases\n    '-h': '--help',\n    '-s': '--silent',\n    '-o': '--outdir',\n  }\n  let args: arg.Result<arg.Spec>\n  try {\n    args = arg(validArgs, { argv })\n  } catch (error) {\n    if (error.code === 'ARG_UNKNOWN_OPTION') {\n      return printAndExit(error.message, 1)\n    }\n    throw error\n  }\n  if (args['--help']) {\n    console.log(`\n      Description\n        Exports the application for production deployment\n\n      Usage\n        $ next export [options] <dir>\n\n      <dir> represents the directory of the Next.js application.\n      If no directory is provided, the current directory will be used.\n\n      Options\n        -h - list this help\n        -o - set the output dir (defaults to 'out')\n        -s - do not print any messages to console\n    `)\n    process.exit(0)\n  }\n\n  const dir = resolve(args._[0] || '.')\n\n  // Check if pages dir exists and warn if not\n  if (!existsSync(dir)) {\n    printAndExit(`> No such directory exists as the project root: ${dir}`)\n  }\n\n  const options = {\n    silent: args['--silent'] || false,\n    threads: args['--threads'],\n    outdir: args['--outdir'] ? resolve(args['--outdir']) : join(dir, 'out'),\n  }\n\n  exportApp(dir, options)\n    .then(() => {\n      printAndExit(`Export successful. Files written to ${options.outdir}`, 0)\n    })\n    .catch((err) => {\n      printAndExit(err)\n    })\n}\n\nexport { nextExport }\n"], "names": [], "mappings": ";;;;;;AAC8B,GAAM,CAAN,KAAM;AACT,GAAI,CAAJ,GAAI;AACf,GAAiC,CAAjC,QAAiC;AAC3B,GAAW,CAAX,OAAW;AACJ,GAAqB,CAArB,MAAqB;;;;;;AAGlD,KAAK,CAAC,UAAU,IAAgB,IAAI,GAAK,CAAC;IACxC,KAAK,CAAC,SAAS;QACb,EAAQ,AAAR,MAAQ;SACR,MAAQ,GAAE,OAAO;SACjB,QAAU,GAAE,OAAO;SACnB,QAAU,GAAE,MAAM;SAClB,SAAW,GAAE,MAAM;QAEnB,EAAU,AAAV,QAAU;SACV,EAAI,IAAE,MAAQ;SACd,EAAI,IAAE,QAAU;SAChB,EAAI,IAAE,QAAU;;IAElB,GAAG,CAAC,IAAI;QACJ,CAAC;QACH,IAAI,OApBQ,QAAiC,UAoBlC,SAAS;YAAI,IAAI;;IAC9B,CAAC,QAAQ,KAAK,EAAE,CAAC;QACf,EAAE,EAAE,KAAK,CAAC,IAAI,MAAK,kBAAoB,GAAE,CAAC;uBApBjB,MAAqB,eAqBxB,KAAK,CAAC,OAAO,EAAE,CAAC;QACtC,CAAC;QACD,KAAK,CAAC,KAAK;IACb,CAAC;IACD,EAAE,EAAE,IAAI,EAAC,MAAQ,IAAG,CAAC;QACnB,OAAO,CAAC,GAAG,EAAE,8ZAcb;QACA,OAAO,CAAC,IAAI,CAAC,CAAC;IAChB,CAAC;IAED,KAAK,CAAC,GAAG,OAhDmB,KAAM,UAgDd,IAAI,CAAC,CAAC,CAAC,CAAC,MAAK,CAAG;IAEpC,EAA4C,AAA5C,0CAA4C;IAC5C,EAAE,OAlDuB,GAAI,aAkDb,GAAG,GAAG,CAAC;YA/CI,MAAqB,gBAgDhC,gDAAgD,EAAE,GAAG;IACrE,CAAC;IAED,KAAK,CAAC,OAAO;QACX,MAAM,EAAE,IAAI,EAAC,QAAU,MAAK,KAAK;QACjC,OAAO,EAAE,IAAI,EAAC,SAAW;QACzB,MAAM,EAAE,IAAI,EAAC,QAAU,SA1DG,KAAM,UA0DG,IAAI,EAAC,QAAU,UA1DxB,KAAM,OA0D4B,GAAG,GAAE,GAAK;;QAvDpD,OAAW,UA0DrB,GAAG,EAAE,OAAO,EACnB,IAAI,KAAO,CAAC;YA1DY,MAAqB,gBA2D9B,oCAAoC,EAAE,OAAO,CAAC,MAAM,IAAI,CAAC;IACzE,CAAC,EACA,KAAK,EAAE,GAAG,GAAK,CAAC;YA7DQ,MAAqB,eA8D/B,GAAG;IAClB,CAAC;AACL,CAAC;QAEQ,UAAU,GAAV,UAAU"}