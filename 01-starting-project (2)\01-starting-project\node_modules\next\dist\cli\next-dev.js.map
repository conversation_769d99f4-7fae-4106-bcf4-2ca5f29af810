{"version": 3, "sources": ["../../cli/next-dev.ts"], "sourcesContent": ["#!/usr/bin/env node\nimport { resolve } from 'path'\nimport arg from 'next/dist/compiled/arg/index.js'\nimport { existsSync } from 'fs'\nimport startServer from '../server/lib/start-server'\nimport { printAndExit } from '../server/lib/utils'\nimport * as Log from '../build/output/log'\nimport { startedDevelopmentServer } from '../build/output'\nimport { cliCommand } from '../bin/next'\n\nconst nextDev: cliCommand = (argv) => {\n  const validArgs: arg.Spec = {\n    // Types\n    '--help': Boolean,\n    '--port': Number,\n    '--hostname': String,\n\n    // Aliases\n    '-h': '--help',\n    '-p': '--port',\n    '-H': '--hostname',\n  }\n  let args: arg.Result<arg.Spec>\n  try {\n    args = arg(validArgs, { argv })\n  } catch (error) {\n    if (error.code === 'ARG_UNKNOWN_OPTION') {\n      return printAndExit(error.message, 1)\n    }\n    throw error\n  }\n  if (args['--help']) {\n    console.log(`\n      Description\n        Starts the application in development mode (hot-code reloading, error\n        reporting, etc)\n\n      Usage\n        $ next dev <dir> -p <port number>\n\n      <dir> represents the directory of the Next.js application.\n      If no directory is provided, the current directory will be used.\n\n      Options\n        --port, -p      A port number on which to start the application\n        --hostname, -H  Hostname on which to start the application (default: 0.0.0.0)\n        --help, -h      Displays this message\n    `)\n    process.exit(0)\n  }\n\n  const dir = resolve(args._[0] || '.')\n\n  // Check if pages dir exists and warn if not\n  if (!existsSync(dir)) {\n    printAndExit(`> No such directory exists as the project root: ${dir}`)\n  }\n\n  async function preflight() {\n    const { getPackageVersion } = await import('../lib/get-package-version')\n    const [sassVersion, nodeSassVersion] = await Promise.all([\n      getPackageVersion({ cwd: dir, name: 'sass' }),\n      getPackageVersion({ cwd: dir, name: 'node-sass' }),\n    ])\n    if (sassVersion && nodeSassVersion) {\n      Log.warn(\n        'Your project has both `sass` and `node-sass` installed as dependencies, but should only use one or the other. ' +\n          'Please remove the `node-sass` dependency from your project. ' +\n          ' Read more: https://nextjs.org/docs/messages/duplicate-sass'\n      )\n    }\n  }\n\n  const port =\n    args['--port'] || (process.env.PORT && parseInt(process.env.PORT)) || 3000\n\n  // We do not set a default host value here to prevent breaking\n  // some set-ups that rely on listening on other interfaces\n  const host = args['--hostname']\n  const appUrl = `http://${\n    !host || host === '0.0.0.0' ? 'localhost' : host\n  }:${port}`\n\n  startServer({ dir, dev: true, isNextDevCommand: true }, port, host)\n    .then(async (app) => {\n      startedDevelopmentServer(appUrl, `${host || '0.0.0.0'}:${port}`)\n      // Start preflight after server is listening and ignore errors:\n      preflight().catch(() => {})\n      // Finalize server bootup:\n      await app.prepare()\n    })\n    .catch((err) => {\n      if (err.code === 'EADDRINUSE') {\n        let errorMessage = `Port ${port} is already in use.`\n        const pkgAppPath = require('next/dist/compiled/find-up').sync(\n          'package.json',\n          {\n            cwd: dir,\n          }\n        )\n        const appPackage = require(pkgAppPath)\n        if (appPackage.scripts) {\n          const nextScript = Object.entries(appPackage.scripts).find(\n            (scriptLine) => scriptLine[1] === 'next'\n          )\n          if (nextScript) {\n            errorMessage += `\\nUse \\`npm run ${nextScript[0]} -- -p <some other port>\\`.`\n          }\n        }\n        console.error(errorMessage)\n      } else {\n        console.error(err)\n      }\n      process.nextTick(() => process.exit(1))\n    })\n}\n\nexport { nextDev }\n"], "names": [], "mappings": ";;;;;;AACwB,GAAM,CAAN,KAAM;AACd,GAAiC,CAAjC,QAAiC;AACtB,GAAI,CAAJ,GAAI;AACP,GAA4B,CAA5B,YAA4B;AACvB,GAAqB,CAArB,MAAqB;AACtC,GAAG,CAAH,GAAG;AAC0B,GAAiB,CAAjB,OAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAG1D,KAAK,CAAC,OAAO,IAAgB,IAAI,GAAK,CAAC;IACrC,KAAK,CAAC,SAAS;QACb,EAAQ,AAAR,MAAQ;SACR,MAAQ,GAAE,OAAO;SACjB,MAAQ,GAAE,MAAM;SAChB,UAAY,GAAE,MAAM;QAEpB,EAAU,AAAV,QAAU;SACV,EAAI,IAAE,MAAQ;SACd,EAAI,IAAE,MAAQ;SACd,EAAI,IAAE,UAAY;;IAEpB,GAAG,CAAC,IAAI;QACJ,CAAC;QACH,IAAI,OAtBQ,QAAiC,UAsBlC,SAAS;YAAI,IAAI;;IAC9B,CAAC,QAAQ,KAAK,EAAE,CAAC;QACf,EAAE,EAAE,KAAK,CAAC,IAAI,MAAK,kBAAoB,GAAE,CAAC;uBArBjB,MAAqB,eAsBxB,KAAK,CAAC,OAAO,EAAE,CAAC;QACtC,CAAC;QACD,KAAK,CAAC,KAAK;IACb,CAAC;IACD,EAAE,EAAE,IAAI,EAAC,MAAQ,IAAG,CAAC;QACnB,OAAO,CAAC,GAAG,EAAE,whBAeb;QACA,OAAO,CAAC,IAAI,CAAC,CAAC;IAChB,CAAC;IAED,KAAK,CAAC,GAAG,OAlDa,KAAM,UAkDR,IAAI,CAAC,CAAC,CAAC,CAAC,MAAK,CAAG;IAEpC,EAA4C,AAA5C,0CAA4C;IAC5C,EAAE,OAnDuB,GAAI,aAmDb,GAAG,GAAG,CAAC;YAjDI,MAAqB,gBAkDhC,gDAAgD,EAAE,GAAG;IACrE,CAAC;mBAEc,SAAS,GAAG,CAAC;QAC1B,KAAK,GAAG,iBAAiB;oDAAkB,0BAA4B;;QACvE,KAAK,EAAE,WAAW,EAAE,eAAe,UAAU,OAAO,CAAC,GAAG;YACtD,iBAAiB;gBAAG,GAAG,EAAE,GAAG;gBAAE,IAAI,GAAE,IAAM;;YAC1C,iBAAiB;gBAAG,GAAG,EAAE,GAAG;gBAAE,IAAI,GAAE,SAAW;;;QAEjD,EAAE,EAAE,WAAW,IAAI,eAAe,EAAE,CAAC;YA1D7B,GAAG,CA2DL,IAAI,EACN,8GAAgH,KAC9G,4DAA8D,KAC9D,2DAA6D;QAEnE,CAAC;IACH,CAAC;IAED,KAAK,CAAC,IAAI,GACR,IAAI,EAAC,MAAQ,MAAM,OAAO,CAAC,GAAG,CAAC,IAAI,IAAI,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,KAAM,IAAI;IAE5E,EAA8D,AAA9D,4DAA8D;IAC9D,EAA0D,AAA1D,wDAA0D;IAC1D,KAAK,CAAC,IAAI,GAAG,IAAI,EAAC,UAAY;IAC9B,KAAK,CAAC,MAAM,IAAI,OAAO,GACpB,IAAI,IAAI,IAAI,MAAK,OAAS,KAAG,SAAW,IAAG,IAAI,CACjD,CAAC,EAAE,IAAI;QA7Ec,YAA4B;QA+EpC,GAAG;QAAE,GAAG,EAAE,IAAI;QAAE,gBAAgB,EAAE,IAAI;OAAI,IAAI,EAAE,IAAI,EAC/D,IAAI,QAAQ,GAAG,GAAK,CAAC;YA7Ee,OAAiB,2BA8E3B,MAAM,KAAK,IAAI,KAAI,OAAS,EAAC,CAAC,EAAE,IAAI;QAC7D,EAA+D,AAA/D,6DAA+D;QAC/D,SAAS,GAAG,KAAK,KAAO,CAAC;QAAA,CAAC;QAC1B,EAA0B,AAA1B,wBAA0B;cACpB,GAAG,CAAC,OAAO;IACnB,CAAC,EACA,KAAK,EAAE,GAAG,GAAK,CAAC;QACf,EAAE,EAAE,GAAG,CAAC,IAAI,MAAK,UAAY,GAAE,CAAC;YAC9B,GAAG,CAAC,YAAY,IAAI,KAAK,EAAE,IAAI,CAAC,mBAAmB;YACnD,KAAK,CAAC,UAAU,GAAG,OAAO,EAAC,0BAA4B,GAAE,IAAI,EAC3D,YAAc;gBAEZ,GAAG,EAAE,GAAG;;YAGZ,KAAK,CAAC,UAAU,GAAG,OAAO,CAAC,UAAU;YACrC,EAAE,EAAE,UAAU,CAAC,OAAO,EAAE,CAAC;gBACvB,KAAK,CAAC,UAAU,GAAG,MAAM,CAAC,OAAO,CAAC,UAAU,CAAC,OAAO,EAAE,IAAI,EACvD,UAAU,GAAK,UAAU,CAAC,CAAC,OAAM,IAAM;;gBAE1C,EAAE,EAAE,UAAU,EAAE,CAAC;oBACf,YAAY,KAAK,gBAAgB,EAAE,UAAU,CAAC,CAAC,EAAE,2BAA2B;gBAC9E,CAAC;YACH,CAAC;YACD,OAAO,CAAC,KAAK,CAAC,YAAY;QAC5B,CAAC,MAAM,CAAC;YACN,OAAO,CAAC,KAAK,CAAC,GAAG;QACnB,CAAC;QACD,OAAO,CAAC,QAAQ,KAAO,OAAO,CAAC,IAAI,CAAC,CAAC;;IACvC,CAAC;AACL,CAAC;QAEQ,OAAO,GAAP,OAAO"}