{"version": 3, "sources": ["../../../telemetry/events/plugins.ts"], "sourcesContent": ["import findUp from 'next/dist/compiled/find-up'\n\nconst EVENT_PLUGIN_PRESENT = 'NEXT_PACKAGE_DETECTED'\ntype NextPluginsEvent = {\n  eventName: string\n  payload: {\n    packageName: string\n    packageVersion: string\n  }\n}\n\nexport async function eventNextPlugins(\n  dir: string\n): Promise<Array<NextPluginsEvent>> {\n  try {\n    const packageJsonPath = await findUp('package.json', { cwd: dir })\n    if (!packageJsonPath) {\n      return []\n    }\n\n    const { dependencies = {}, devDependencies = {} } = require(packageJsonPath)\n\n    const deps = { ...devDependencies, ...dependencies }\n\n    return Object.keys(deps).reduce(\n      (events: NextPluginsEvent[], plugin: string): NextPluginsEvent[] => {\n        const version = deps[plugin]\n        // Don't add deps without a version set\n        if (!version) {\n          return events\n        }\n\n        events.push({\n          eventName: EVENT_PLUGIN_PRESENT,\n          payload: {\n            packageName: plugin,\n            packageVersion: version,\n          },\n        })\n\n        return events\n      },\n      []\n    )\n  } catch (_) {\n    return []\n  }\n}\n"], "names": [], "mappings": ";;;;QAWsB,gBAAgB,GAAhB,gBAAgB;AAXnB,GAA4B,CAA5B,OAA4B;;;;;;AAE/C,KAAK,CAAC,oBAAoB,IAAG,qBAAuB;eAS9B,gBAAgB,CACpC,GAAW,EACuB,CAAC;QAC/B,CAAC;QACH,KAAK,CAAC,eAAe,aAfN,OAA4B,WAeN,YAAc;YAAI,GAAG,EAAE,GAAG;;QAC/D,EAAE,GAAG,eAAe,EAAE,CAAC;;QAEvB,CAAC;QAED,KAAK,GAAG,YAAY;YAAO,eAAe;eAAU,OAAO,CAAC,eAAe;QAE3E,KAAK,CAAC,IAAI;eAAQ,eAAe;eAAK,YAAY;;eAE3C,MAAM,CAAC,IAAI,CAAC,IAAI,EAAE,MAAM,EAC5B,MAA0B,EAAE,MAAc,GAAyB,CAAC;YACnE,KAAK,CAAC,OAAO,GAAG,IAAI,CAAC,MAAM;YAC3B,EAAuC,AAAvC,qCAAuC;YACvC,EAAE,GAAG,OAAO,EAAE,CAAC;uBACN,MAAM;YACf,CAAC;YAED,MAAM,CAAC,IAAI;gBACT,SAAS,EAAE,oBAAoB;gBAC/B,OAAO;oBACL,WAAW,EAAE,MAAM;oBACnB,cAAc,EAAE,OAAO;;;mBAIpB,MAAM;QACf,CAAC;IAGL,CAAC,QAAQ,CAAC,EAAE,CAAC;;IAEb,CAAC;AACH,CAAC"}