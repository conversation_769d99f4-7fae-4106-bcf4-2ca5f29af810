{"version": 3, "sources": ["../../../shared/lib/post-process.ts"], "sourcesContent": ["import escapeRegexp from 'next/dist/compiled/escape-string-regexp'\nimport { parse, HTMLElement } from 'node-html-parser'\nimport { OPTIMIZED_FONT_PROVIDERS } from './constants'\n\n// const MIDDLEWARE_TIME_BUDGET = parseInt(process.env.__POST_PROCESS_MIDDLEWARE_TIME_BUDGET || '', 10) || 10\nconst MAXIMUM_IMAGE_PRELOADS = 2\nconst IMAGE_PRELOAD_SIZE_THRESHOLD = 2500\n\ntype postProcessOptions = {\n  optimizeFonts: boolean\n  optimizeImages: boolean\n}\n\ntype renderOptions = {\n  getFontDefinition?: (url: string) => string\n}\ninterface PostProcessMiddleware {\n  inspect: (originalDom: HTMLElement, options: renderOptions) => any\n  mutate: (markup: string, data: any, options: renderOptions) => Promise<string>\n}\n\ntype middlewareSignature = {\n  name: string\n  middleware: PostProcessMiddleware\n  condition: ((options: postProcessOptions) => boolean) | null\n}\n\nconst middlewareRegistry: Array<middlewareSignature> = []\n\nfunction registerPostProcessor(\n  name: string,\n  middleware: PostProcessMiddleware,\n  condition?: (options: postProcessOptions) => boolean\n) {\n  middlewareRegistry.push({ name, middleware, condition: condition || null })\n}\n\nasync function processHTML(\n  html: string,\n  data: renderOptions,\n  options: postProcessOptions\n): Promise<string> {\n  // Don't parse unless there's at least one processor middleware\n  if (!middlewareRegistry[0]) {\n    return html\n  }\n  const root: HTMLElement = parse(html)\n  let document = html\n  // Calls the middleware, with some instrumentation and logging\n  async function callMiddleWare(middleware: PostProcessMiddleware) {\n    // let timer = Date.now()\n    const inspectData = middleware.inspect(root, data)\n    document = await middleware.mutate(document, inspectData, data)\n    // timer = Date.now() - timer\n    // if (timer > MIDDLEWARE_TIME_BUDGET) {\n    // TODO: Identify a correct upper limit for the postprocess step\n    // and add a warning to disable the optimization\n    // }\n    return\n  }\n\n  for (let i = 0; i < middlewareRegistry.length; i++) {\n    let middleware = middlewareRegistry[i]\n    if (!middleware.condition || middleware.condition(options)) {\n      await callMiddleWare(middlewareRegistry[i].middleware)\n    }\n  }\n\n  return document\n}\n\nclass FontOptimizerMiddleware implements PostProcessMiddleware {\n  inspect(originalDom: HTMLElement, options: renderOptions) {\n    if (!options.getFontDefinition) {\n      return\n    }\n    const fontDefinitions: (string | undefined)[][] = []\n    // collecting all the requested font definitions\n    originalDom\n      .querySelectorAll('link')\n      .filter(\n        (tag: HTMLElement) =>\n          tag.getAttribute('rel') === 'stylesheet' &&\n          tag.hasAttribute('data-href') &&\n          OPTIMIZED_FONT_PROVIDERS.some(({ url }) => {\n            const dataHref = tag.getAttribute('data-href')\n            return dataHref ? dataHref.startsWith(url) : false\n          })\n      )\n      .forEach((element: HTMLElement) => {\n        const url = element.getAttribute('data-href')\n        const nonce = element.getAttribute('nonce')\n\n        if (url) {\n          fontDefinitions.push([url, nonce])\n        }\n      })\n\n    return fontDefinitions\n  }\n  mutate = async (\n    markup: string,\n    fontDefinitions: string[][],\n    options: renderOptions\n  ) => {\n    let result = markup\n    let preconnectUrls = new Set<string>()\n\n    if (!options.getFontDefinition) {\n      return markup\n    }\n\n    fontDefinitions.forEach((fontDef) => {\n      const [url, nonce] = fontDef\n      const fallBackLinkTag = `<link rel=\"stylesheet\" href=\"${url}\"/>`\n      if (\n        result.indexOf(`<style data-href=\"${url}\">`) > -1 ||\n        result.indexOf(fallBackLinkTag) > -1\n      ) {\n        // The font is already optimized and probably the response is cached\n        return\n      }\n      const fontContent = options.getFontDefinition\n        ? options.getFontDefinition(url as string)\n        : null\n      if (!fontContent) {\n        /**\n         * In case of unreachable font definitions, fallback to default link tag.\n         */\n        result = result.replace('</head>', `${fallBackLinkTag}</head>`)\n      } else {\n        const nonceStr = nonce ? ` nonce=\"${nonce}\"` : ''\n        result = result.replace(\n          '</head>',\n          `<style data-href=\"${url}\"${nonceStr}>${fontContent}</style></head>`\n        )\n\n        const provider = OPTIMIZED_FONT_PROVIDERS.find((p) =>\n          url.startsWith(p.url)\n        )\n\n        if (provider) {\n          preconnectUrls.add(provider.preconnect)\n        }\n      }\n    })\n\n    let preconnectTag = ''\n    preconnectUrls.forEach((url) => {\n      preconnectTag += `<link rel=\"preconnect\" href=\"${url}\" crossorigin />`\n    })\n\n    result = result.replace(\n      '<meta name=\"next-font-preconnect\"/>',\n      preconnectTag\n    )\n\n    return result\n  }\n}\n\nclass ImageOptimizerMiddleware implements PostProcessMiddleware {\n  inspect(originalDom: HTMLElement) {\n    const imgPreloads = []\n    const imgElements = originalDom.querySelectorAll('img')\n    let eligibleImages: Array<HTMLElement> = []\n    for (let i = 0; i < imgElements.length; i++) {\n      if (isImgEligible(imgElements[i])) {\n        eligibleImages.push(imgElements[i])\n      }\n      if (eligibleImages.length >= MAXIMUM_IMAGE_PRELOADS) {\n        break\n      }\n    }\n\n    for (const imgEl of eligibleImages) {\n      const src = imgEl.getAttribute('src')\n      if (src) {\n        imgPreloads.push(src)\n      }\n    }\n\n    return imgPreloads\n  }\n  mutate = async (markup: string, imgPreloads: string[]) => {\n    let result = markup\n    let imagePreloadTags = imgPreloads\n      .filter((imgHref) => !preloadTagAlreadyExists(markup, imgHref))\n      .reduce(\n        (acc, imgHref) =>\n          acc + `<link rel=\"preload\" href=\"${imgHref}\" as=\"image\"/>`,\n        ''\n      )\n    return result.replace('<meta name=\"next-image-preload\"/>', imagePreloadTags)\n  }\n}\n\nfunction isImgEligible(imgElement: HTMLElement): boolean {\n  let imgSrc = imgElement.getAttribute('src')\n  return (\n    !!imgSrc &&\n    sourceIsSupportedType(imgSrc) &&\n    imageIsNotTooSmall(imgElement) &&\n    imageIsNotHidden(imgElement)\n  )\n}\n\nfunction preloadTagAlreadyExists(html: string, href: string) {\n  const escapedHref = escapeRegexp(href)\n  const regex = new RegExp(`<link[^>]*href[^>]*${escapedHref}`)\n  return html.match(regex)\n}\n\nfunction imageIsNotTooSmall(imgElement: HTMLElement): boolean {\n  // Skip images without both height and width--we don't know enough to say if\n  // they are too small\n  if (\n    !(imgElement.hasAttribute('height') && imgElement.hasAttribute('width'))\n  ) {\n    return true\n  }\n  try {\n    const heightAttr = imgElement.getAttribute('height')\n    const widthAttr = imgElement.getAttribute('width')\n    if (!heightAttr || !widthAttr) {\n      return true\n    }\n\n    if (\n      parseInt(heightAttr) * parseInt(widthAttr) <=\n      IMAGE_PRELOAD_SIZE_THRESHOLD\n    ) {\n      return false\n    }\n  } catch (err) {\n    return true\n  }\n  return true\n}\n\n// Traverse up the dom from each image to see if it or any of it's\n// ancestors have the hidden attribute.\nfunction imageIsNotHidden(imgElement: HTMLElement): boolean {\n  let activeElement = imgElement\n  while (activeElement.parentNode) {\n    if (activeElement.hasAttribute('hidden')) {\n      return false\n    }\n    activeElement = activeElement.parentNode as HTMLElement\n  }\n  return true\n}\n\n// Currently only filters out svg images--could be made more specific in the future.\nfunction sourceIsSupportedType(imgSrc: string): boolean {\n  return !imgSrc.includes('.svg')\n}\n\n// Initialization\nregisterPostProcessor(\n  'Inline-Fonts',\n  new FontOptimizerMiddleware(),\n  // Using process.env because passing Experimental flag through loader is not possible.\n  // @ts-ignore\n  (options) => options.optimizeFonts || process.env.__NEXT_OPTIMIZE_FONTS\n)\n\nregisterPostProcessor(\n  'Preload Images',\n  new ImageOptimizerMiddleware(),\n  // @ts-ignore\n  (options) => options.optimizeImages || process.env.__NEXT_OPTIMIZE_IMAGES\n)\n\nexport default processHTML\n"], "names": [], "mappings": ";;;;;AAAyB,GAAyC,CAAzC,mBAAyC;AAC/B,GAAkB,CAAlB,eAAkB;AACZ,GAAa,CAAb,UAAa;;;;;;AAEtD,EAA6G,AAA7G,2GAA6G;AAC7G,KAAK,CAAC,sBAAsB,GAAG,CAAC;AAChC,KAAK,CAAC,4BAA4B,GAAG,IAAI;AAqBzC,KAAK,CAAC,kBAAkB;SAEf,qBAAqB,CAC5B,IAAY,EACZ,UAAiC,EACjC,SAAoD,EACpD,CAAC;IACD,kBAAkB,CAAC,IAAI;QAAG,IAAI;QAAE,UAAU;QAAE,SAAS,EAAE,SAAS,IAAI,IAAI;;AAC1E,CAAC;eAEc,WAAW,CACxB,IAAY,EACZ,IAAmB,EACnB,OAA2B,EACV,CAAC;IAClB,EAA+D,AAA/D,6DAA+D;IAC/D,EAAE,GAAG,kBAAkB,CAAC,CAAC,GAAG,CAAC;eACpB,IAAI;IACb,CAAC;IACD,KAAK,CAAC,IAAI,OA7CuB,eAAkB,QA6CnB,IAAI;IACpC,GAAG,CAAC,QAAQ,GAAG,IAAI;IACnB,EAA8D,AAA9D,4DAA8D;mBAC/C,cAAc,CAAC,UAAiC,EAAE,CAAC;QAChE,EAAyB,AAAzB,uBAAyB;QACzB,KAAK,CAAC,WAAW,GAAG,UAAU,CAAC,OAAO,CAAC,IAAI,EAAE,IAAI;QACjD,QAAQ,SAAS,UAAU,CAAC,MAAM,CAAC,QAAQ,EAAE,WAAW,EAAE,IAAI;QAC9D,EAA6B,AAA7B,2BAA6B;QAC7B,EAAwC,AAAxC,sCAAwC;QACxC,EAAgE,AAAhE,8DAAgE;QAChE,EAAgD,AAAhD,8CAAgD;QAChD,EAAI,AAAJ,EAAI;;IAEN,CAAC;QAEI,GAAG,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,kBAAkB,CAAC,MAAM,EAAE,CAAC,GAAI,CAAC;QACnD,GAAG,CAAC,UAAU,GAAG,kBAAkB,CAAC,CAAC;QACrC,EAAE,GAAG,UAAU,CAAC,SAAS,IAAI,UAAU,CAAC,SAAS,CAAC,OAAO,GAAG,CAAC;kBACrD,cAAc,CAAC,kBAAkB,CAAC,CAAC,EAAE,UAAU;QACvD,CAAC;IACH,CAAC;WAEM,QAAQ;AACjB,CAAC;MAEK,uBAAuB;IAC3B,OAAO,CAAC,WAAwB,EAAE,OAAsB,EAAE,CAAC;QACzD,EAAE,GAAG,OAAO,CAAC,iBAAiB,EAAE,CAAC;;QAEjC,CAAC;QACD,KAAK,CAAC,eAAe;QACrB,EAAgD,AAAhD,8CAAgD;QAChD,WAAW,CACR,gBAAgB,EAAC,IAAM,GACvB,MAAM,EACJ,GAAgB,GACf,GAAG,CAAC,YAAY,EAAC,GAAK,QAAM,UAAY,KACxC,GAAG,CAAC,YAAY,EAAC,SAAW,MAjFG,UAAa,0BAkFnB,IAAI,IAAI,GAAG,MAAO,CAAC;gBAC1C,KAAK,CAAC,QAAQ,GAAG,GAAG,CAAC,YAAY,EAAC,SAAW;uBACtC,QAAQ,GAAG,QAAQ,CAAC,UAAU,CAAC,GAAG,IAAI,KAAK;YACpD,CAAC;UAEJ,OAAO,EAAE,OAAoB,GAAK,CAAC;YAClC,KAAK,CAAC,GAAG,GAAG,OAAO,CAAC,YAAY,EAAC,SAAW;YAC5C,KAAK,CAAC,KAAK,GAAG,OAAO,CAAC,YAAY,EAAC,KAAO;YAE1C,EAAE,EAAE,GAAG,EAAE,CAAC;gBACR,eAAe,CAAC,IAAI;oBAAE,GAAG;oBAAE,KAAK;;YAClC,CAAC;QACH,CAAC;eAEI,eAAe;IACxB,CAAC;;aACD,MAAM,UACJ,MAAc,EACd,eAA2B,EAC3B,OAAsB,GACnB,CAAC;YACJ,GAAG,CAAC,MAAM,GAAG,MAAM;YACnB,GAAG,CAAC,cAAc,GAAG,GAAG,CAAC,GAAG;YAE5B,EAAE,GAAG,OAAO,CAAC,iBAAiB,EAAE,CAAC;uBACxB,MAAM;YACf,CAAC;YAED,eAAe,CAAC,OAAO,EAAE,OAAO,GAAK,CAAC;gBACpC,KAAK,EAAE,GAAG,EAAE,KAAK,IAAI,OAAO;gBAC5B,KAAK,CAAC,eAAe,IAAI,6BAA6B,EAAE,GAAG,CAAC,GAAG;gBAC/D,EAAE,EACA,MAAM,CAAC,OAAO,EAAE,kBAAkB,EAAE,GAAG,CAAC,EAAE,MAAM,CAAC,IACjD,MAAM,CAAC,OAAO,CAAC,eAAe,KAAK,CAAC,EACpC,CAAC;oBACD,EAAoE,AAApE,kEAAoE;;gBAEtE,CAAC;gBACD,KAAK,CAAC,WAAW,GAAG,OAAO,CAAC,iBAAiB,GACzC,OAAO,CAAC,iBAAiB,CAAC,GAAG,IAC7B,IAAI;gBACR,EAAE,GAAG,WAAW,EAAE,CAAC;oBACjB,EAEG,AAFH;;SAEG,AAFH,EAEG,CACH,MAAM,GAAG,MAAM,CAAC,OAAO,EAAC,OAAS,MAAK,eAAe,CAAC,OAAO;gBAC/D,CAAC,MAAM,CAAC;oBACN,KAAK,CAAC,QAAQ,GAAG,KAAK,IAAI,QAAQ,EAAE,KAAK,CAAC,CAAC;oBAC3C,MAAM,GAAG,MAAM,CAAC,OAAO,EACrB,OAAS,IACR,kBAAkB,EAAE,GAAG,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC,EAAE,WAAW,CAAC,eAAe;oBAGrE,KAAK,CAAC,QAAQ,GAvImB,UAAa,0BAuIJ,IAAI,EAAE,CAAC,GAC/C,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC,GAAG;;oBAGtB,EAAE,EAAE,QAAQ,EAAE,CAAC;wBACb,cAAc,CAAC,GAAG,CAAC,QAAQ,CAAC,UAAU;oBACxC,CAAC;gBACH,CAAC;YACH,CAAC;YAED,GAAG,CAAC,aAAa;YACjB,cAAc,CAAC,OAAO,EAAE,GAAG,GAAK,CAAC;gBAC/B,aAAa,KAAK,6BAA6B,EAAE,GAAG,CAAC,gBAAgB;YACvE,CAAC;YAED,MAAM,GAAG,MAAM,CAAC,OAAO,EACrB,mCAAqC,GACrC,aAAa;mBAGR,MAAM;QACf,CAAC;;;MAGG,wBAAwB;IAC5B,OAAO,CAAC,WAAwB,EAAE,CAAC;QACjC,KAAK,CAAC,WAAW;QACjB,KAAK,CAAC,WAAW,GAAG,WAAW,CAAC,gBAAgB,EAAC,GAAK;QACtD,GAAG,CAAC,cAAc;YACb,GAAG,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,WAAW,CAAC,MAAM,EAAE,CAAC,GAAI,CAAC;YAC5C,EAAE,EAAE,aAAa,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC;gBAClC,cAAc,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YACnC,CAAC;YACD,EAAE,EAAE,cAAc,CAAC,MAAM,IAAI,sBAAsB,EAAE,CAAC;;YAEtD,CAAC;QACH,CAAC;aAEI,KAAK,CAAC,KAAK,IAAI,cAAc,CAAE,CAAC;YACnC,KAAK,CAAC,GAAG,GAAG,KAAK,CAAC,YAAY,EAAC,GAAK;YACpC,EAAE,EAAE,GAAG,EAAE,CAAC;gBACR,WAAW,CAAC,IAAI,CAAC,GAAG;YACtB,CAAC;QACH,CAAC;eAEM,WAAW;IACpB,CAAC;;aACD,MAAM,UAAU,MAAc,EAAE,WAAqB,GAAK,CAAC;YACzD,GAAG,CAAC,MAAM,GAAG,MAAM;YACnB,GAAG,CAAC,gBAAgB,GAAG,WAAW,CAC/B,MAAM,EAAE,OAAO,IAAM,uBAAuB,CAAC,MAAM,EAAE,OAAO;cAC5D,MAAM,EACJ,GAAG,EAAE,OAAO,GACX,GAAG,IAAI,0BAA0B,EAAE,OAAO,CAAC,cAAc;;mBAGxD,MAAM,CAAC,OAAO,EAAC,iCAAmC,GAAE,gBAAgB;QAC7E,CAAC;;;SAGM,aAAa,CAAC,UAAuB,EAAW,CAAC;IACxD,GAAG,CAAC,MAAM,GAAG,UAAU,CAAC,YAAY,EAAC,GAAK;aAEtC,MAAM,IACR,qBAAqB,CAAC,MAAM,KAC5B,kBAAkB,CAAC,UAAU,KAC7B,gBAAgB,CAAC,UAAU;AAE/B,CAAC;SAEQ,uBAAuB,CAAC,IAAY,EAAE,IAAY,EAAE,CAAC;IAC5D,KAAK,CAAC,WAAW,OAhNM,mBAAyC,UAgN/B,IAAI;IACrC,KAAK,CAAC,KAAK,GAAG,GAAG,CAAC,MAAM,EAAE,mBAAmB,EAAE,WAAW;WACnD,IAAI,CAAC,KAAK,CAAC,KAAK;AACzB,CAAC;SAEQ,kBAAkB,CAAC,UAAuB,EAAW,CAAC;IAC7D,EAA4E,AAA5E,0EAA4E;IAC5E,EAAqB,AAArB,mBAAqB;IACrB,EAAE,IACE,UAAU,CAAC,YAAY,EAAC,MAAQ,MAAK,UAAU,CAAC,YAAY,EAAC,KAAO,KACtE,CAAC;eACM,IAAI;IACb,CAAC;QACG,CAAC;QACH,KAAK,CAAC,UAAU,GAAG,UAAU,CAAC,YAAY,EAAC,MAAQ;QACnD,KAAK,CAAC,SAAS,GAAG,UAAU,CAAC,YAAY,EAAC,KAAO;QACjD,EAAE,GAAG,UAAU,KAAK,SAAS,EAAE,CAAC;mBACvB,IAAI;QACb,CAAC;QAED,EAAE,EACA,QAAQ,CAAC,UAAU,IAAI,QAAQ,CAAC,SAAS,KACzC,4BAA4B,EAC5B,CAAC;mBACM,KAAK;QACd,CAAC;IACH,CAAC,QAAQ,GAAG,EAAE,CAAC;eACN,IAAI;IACb,CAAC;WACM,IAAI;AACb,CAAC;AAED,EAAkE,AAAlE,gEAAkE;AAClE,EAAuC,AAAvC,qCAAuC;SAC9B,gBAAgB,CAAC,UAAuB,EAAW,CAAC;IAC3D,GAAG,CAAC,aAAa,GAAG,UAAU;UACvB,aAAa,CAAC,UAAU,CAAE,CAAC;QAChC,EAAE,EAAE,aAAa,CAAC,YAAY,EAAC,MAAQ,IAAG,CAAC;mBAClC,KAAK;QACd,CAAC;QACD,aAAa,GAAG,aAAa,CAAC,UAAU;IAC1C,CAAC;WACM,IAAI;AACb,CAAC;AAED,EAAoF,AAApF,kFAAoF;SAC3E,qBAAqB,CAAC,MAAc,EAAW,CAAC;YAC/C,MAAM,CAAC,QAAQ,EAAC,IAAM;AAChC,CAAC;AAED,EAAiB,AAAjB,eAAiB;AACjB,qBAAqB,EACnB,YAAc,GACd,GAAG,CAAC,uBAAuB,IAC3B,EAAsF,AAAtF,oFAAsF;AACtF,EAAa,AAAb,WAAa;CACZ,OAAO,GAAK,OAAO,CAAC,aAAa,IAAI,OAAO,CAAC,GAAG,CAAC,qBAAqB;;AAGzE,qBAAqB,EACnB,cAAgB,GAChB,GAAG,CAAC,wBAAwB,IAC5B,EAAa,AAAb,WAAa;CACZ,OAAO,GAAK,OAAO,CAAC,cAAc,IAAI,OAAO,CAAC,GAAG,CAAC,sBAAsB;;eAG5D,WAAW"}