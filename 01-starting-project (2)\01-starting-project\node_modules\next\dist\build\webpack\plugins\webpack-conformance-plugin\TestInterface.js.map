{"version": 3, "sources": ["../../../../../build/webpack/plugins/webpack-conformance-plugin/TestInterface.ts"], "sourcesContent": ["// eslint-disable-next-line import/no-extraneous-dependencies\nimport { NodePath } from 'ast-types/lib/node-path'\n\nexport interface IConformanceAnomaly {\n  message: string\n  stack_trace?: string\n}\n\n// eslint typescript has a bug with TS enums\n/* eslint-disable no-shadow */\nexport enum IConformanceTestStatus {\n  SUCCESS,\n  FAILED,\n}\nexport interface IConformanceTestResult {\n  result: IConformanceTestStatus\n  warnings?: Array<IConformanceAnomaly>\n  errors?: Array<IConformanceAnomaly>\n}\n\nexport interface IParsedModuleDetails {\n  request: string\n}\n\nexport type NodeInspector = (\n  node: NodePath,\n  details: IParsedModuleDetails\n) => IConformanceTestResult\n\nexport interface IGetAstNodeResult {\n  visitor: string\n  inspectNode: NodeInspector\n}\n\nexport interface IWebpackConformanceTest {\n  buildStared?: (options: any) => IConformanceTestResult\n  getAstNode?: () => IGetAstNodeResult[]\n  buildCompleted?: (assets: any) => IConformanceTestResult\n}\n"], "names": [], "mappings": ";;;;;;;UAUY,uBAAsB;IAAtB,uBAAsB,CAAtB,uBAAsB,EAChC,OAAO,KAAP,CAAO,KAAP,OAAO;IADG,uBAAsB,CAAtB,uBAAsB,EAEhC,MAAM,KAAN,CAAM,KAAN,MAAM;GAFI,sBAAsB,sCAAtB,sBAAsB"}