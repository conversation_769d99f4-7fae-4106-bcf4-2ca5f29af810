{"version": 3, "sources": ["../../../../../shared/lib/router/utils/parse-relative-url.ts"], "sourcesContent": ["import { getLocationOrigin } from '../../utils'\nimport { searchParamsToUrlQuery } from './querystring'\n\n/**\n * Parses path-relative urls (e.g. `/hello/world?foo=bar`). If url isn't path-relative\n * (e.g. `./hello`) then at least base must be.\n * Absolute urls are rejected with one exception, in the browser, absolute urls that are on\n * the current origin will be parsed as relative\n */\nexport function parseRelativeUrl(url: string, base?: string) {\n  const globalBase = new URL(\n    typeof window === 'undefined' ? 'http://n' : getLocationOrigin()\n  )\n  const resolvedBase = base ? new URL(base, globalBase) : globalBase\n  const { pathname, searchParams, search, hash, href, origin } = new URL(\n    url,\n    resolvedBase\n  )\n  if (origin !== globalBase.origin) {\n    throw new Error(`invariant: invalid relative URL, router received ${url}`)\n  }\n  return {\n    pathname,\n    query: searchParamsToUrlQuery(searchParams),\n    search,\n    hash,\n    href: href.slice(globalBase.origin.length),\n  }\n}\n"], "names": [], "mappings": ";;;;QASgB,gBAAgB,GAAhB,gBAAgB;AATE,GAAa,CAAb,MAAa;AACR,GAAe,CAAf,YAAe;SAQtC,gBAAgB,CAAC,GAAW,EAAE,IAAa,EAAE,CAAC;IAC5D,KAAK,CAAC,UAAU,GAAG,GAAG,CAAC,GAAG,QACjB,MAAM,MAAK,SAAW,KAAG,QAAU,QAXZ,MAAa;IAa7C,KAAK,CAAC,YAAY,GAAG,IAAI,GAAG,GAAG,CAAC,GAAG,CAAC,IAAI,EAAE,UAAU,IAAI,UAAU;IAClE,KAAK,GAAG,QAAQ,GAAE,YAAY,GAAE,MAAM,GAAE,IAAI,GAAE,IAAI,GAAE,MAAM,MAAK,GAAG,CAAC,GAAG,CACpE,GAAG,EACH,YAAY;IAEd,EAAE,EAAE,MAAM,KAAK,UAAU,CAAC,MAAM,EAAE,CAAC;QACjC,KAAK,CAAC,GAAG,CAAC,KAAK,EAAE,iDAAiD,EAAE,GAAG;IACzE,CAAC;;QAEC,QAAQ;QACR,KAAK,MAtB8B,YAAe,yBAsBpB,YAAY;QAC1C,MAAM;QACN,IAAI;QACJ,IAAI,EAAE,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,MAAM,CAAC,MAAM;;AAE7C,CAAC"}