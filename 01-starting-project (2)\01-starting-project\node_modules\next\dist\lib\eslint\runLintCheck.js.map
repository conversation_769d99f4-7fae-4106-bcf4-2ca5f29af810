{"version": 3, "sources": ["../../../lib/eslint/runLintCheck.ts"], "sourcesContent": ["import { promises as fs } from 'fs'\nimport chalk from 'chalk'\nimport path from 'path'\n\nimport findUp from 'next/dist/compiled/find-up'\nimport semver from 'next/dist/compiled/semver'\nimport * as CommentJson from 'next/dist/compiled/comment-json'\n\nimport { LintResult, formatResults } from './customFormatter'\nimport { writeDefaultConfig } from './writeDefaultConfig'\nimport { hasEslintConfiguration } from './hasEslintConfiguration'\n\nimport { ESLINT_PROMPT_VALUES } from '../constants'\nimport { existsSync, findPagesDir } from '../find-pages-dir'\nimport { installDependencies } from '../install-dependencies'\nimport { hasNecessaryDependencies } from '../has-necessary-dependencies'\nimport { isYarn } from '../is-yarn'\n\nimport * as Log from '../../build/output/log'\nimport { EventLintCheckCompleted } from '../../telemetry/events/build'\n\ntype Config = {\n  plugins: string[]\n  rules: { [key: string]: Array<number | string> }\n}\n\nconst requiredPackages = [\n  { file: 'eslint/lib/api.js', pkg: 'eslint' },\n  { file: 'eslint-config-next', pkg: 'eslint-config-next' },\n]\n\nasync function cliPrompt() {\n  console.log(\n    chalk.bold(\n      `${chalk.cyan(\n        '?'\n      )} How would you like to configure ESLint? https://nextjs.org/docs/basic-features/eslint`\n    )\n  )\n\n  try {\n    const cliSelect = (await import('next/dist/compiled/cli-select')).default\n    const { value } = await cliSelect({\n      values: ESLINT_PROMPT_VALUES,\n      valueRenderer: (\n        {\n          title,\n          recommended,\n        }: { title: string; recommended?: boolean; config: any },\n        selected: boolean\n      ) => {\n        const name = selected ? chalk.bold.underline.cyan(title) : title\n        return name + (recommended ? chalk.bold.yellow(' (recommended)') : '')\n      },\n      selected: chalk.cyan('❯ '),\n      unselected: '  ',\n    })\n\n    return { config: value?.config }\n  } catch {\n    return { config: null }\n  }\n}\n\nasync function lint(\n  baseDir: string,\n  lintDirs: string[],\n  eslintrcFile: string | null,\n  pkgJsonPath: string | null,\n  lintDuringBuild: boolean = false,\n  eslintOptions: any = null,\n  reportErrorsOnly: boolean = false,\n  maxWarnings: number = -1,\n  formatter: string | null = null\n): Promise<\n  | string\n  | null\n  | {\n      output: string | null\n      isError: boolean\n      eventInfo: EventLintCheckCompleted\n    }\n> {\n  try {\n    // Load ESLint after we're sure it exists:\n    const deps = await hasNecessaryDependencies(baseDir, requiredPackages)\n\n    if (deps.missing.some((dep) => dep.pkg === 'eslint')) {\n      Log.error(\n        `ESLint must be installed${\n          lintDuringBuild ? ' in order to run during builds:' : ':'\n        } ${chalk.bold.cyan(\n          isYarn(baseDir)\n            ? 'yarn add --dev eslint'\n            : 'npm install --save-dev eslint'\n        )}`\n      )\n      return null\n    }\n\n    const mod = await import(deps.resolved.get('eslint')!)\n\n    const { ESLint } = mod\n    let eslintVersion = ESLint?.version ?? mod?.CLIEngine?.version\n\n    if (!eslintVersion || semver.lt(eslintVersion, '7.0.0')) {\n      return `${chalk.red(\n        'error'\n      )} - Your project has an older version of ESLint installed${\n        eslintVersion ? ' (' + eslintVersion + ')' : ''\n      }. Please upgrade to ESLint version 7 or later`\n    }\n\n    let options: any = {\n      useEslintrc: true,\n      baseConfig: {},\n      errorOnUnmatchedPattern: false,\n      extensions: ['.js', '.jsx', '.ts', '.tsx'],\n      cache: true,\n      ...eslintOptions,\n    }\n\n    let eslint = new ESLint(options)\n\n    let nextEslintPluginIsEnabled = false\n    const pagesDirRules = ['@next/next/no-html-link-for-pages']\n\n    for (const configFile of [eslintrcFile, pkgJsonPath]) {\n      if (!configFile) continue\n\n      const completeConfig: Config = await eslint.calculateConfigForFile(\n        configFile\n      )\n\n      if (completeConfig.plugins?.includes('@next/next')) {\n        nextEslintPluginIsEnabled = true\n        break\n      }\n    }\n\n    const pagesDir = findPagesDir(baseDir)\n\n    if (nextEslintPluginIsEnabled) {\n      let updatedPagesDir = false\n\n      for (const rule of pagesDirRules) {\n        if (\n          !options.baseConfig!.rules?.[rule] &&\n          !options.baseConfig!.rules?.[\n            rule.replace('@next/next', '@next/babel-plugin-next')\n          ]\n        ) {\n          if (!options.baseConfig!.rules) {\n            options.baseConfig!.rules = {}\n          }\n          options.baseConfig!.rules[rule] = [1, pagesDir]\n          updatedPagesDir = true\n        }\n      }\n\n      if (updatedPagesDir) {\n        eslint = new ESLint(options)\n      }\n    } else {\n      Log.warn(\n        'The Next.js plugin was not detected in your ESLint configuration. See https://nextjs.org/docs/basic-features/eslint#migrating-existing-config'\n      )\n    }\n\n    const lintStart = process.hrtime()\n\n    let results = await eslint.lintFiles(lintDirs)\n    let selectedFormatter = null\n\n    if (options.fix) await ESLint.outputFixes(results)\n    if (reportErrorsOnly) results = await ESLint.getErrorResults(results) // Only return errors if --quiet flag is used\n\n    if (formatter) selectedFormatter = await eslint.loadFormatter(formatter)\n    const formattedResult = formatResults(\n      baseDir,\n      results,\n      selectedFormatter?.format\n    )\n    const lintEnd = process.hrtime(lintStart)\n    const totalWarnings = results.reduce(\n      (sum: number, file: LintResult) => sum + file.warningCount,\n      0\n    )\n\n    return {\n      output: formattedResult.output,\n      isError:\n        ESLint.getErrorResults(results)?.length > 0 ||\n        (maxWarnings >= 0 && totalWarnings > maxWarnings),\n      eventInfo: {\n        durationInSeconds: lintEnd[0],\n        eslintVersion: eslintVersion,\n        lintedFilesCount: results.length,\n        lintFix: !!options.fix,\n        nextEslintPluginVersion: nextEslintPluginIsEnabled\n          ? require(path.join(\n              path.dirname(deps.resolved.get('eslint-config-next')!),\n              'package.json'\n            )).version\n          : null,\n        nextEslintPluginErrorsCount: formattedResult.totalNextPluginErrorCount,\n        nextEslintPluginWarningsCount:\n          formattedResult.totalNextPluginWarningCount,\n      },\n    }\n  } catch (err) {\n    if (lintDuringBuild) {\n      Log.error(\n        `ESLint: ${err.message ? err.message.replace(/\\n/g, ' ') : err}`\n      )\n      return null\n    } else {\n      throw new Error(err)\n    }\n  }\n}\n\nexport async function runLintCheck(\n  baseDir: string,\n  lintDirs: string[],\n  lintDuringBuild: boolean = false,\n  eslintOptions: any = null,\n  reportErrorsOnly: boolean = false,\n  maxWarnings: number = -1,\n  formatter: string | null = null,\n  strict: boolean = false\n): ReturnType<typeof lint> {\n  try {\n    // Find user's .eslintrc file\n    const eslintrcFile =\n      (await findUp(\n        [\n          '.eslintrc.js',\n          '.eslintrc.yaml',\n          '.eslintrc.yml',\n          '.eslintrc.json',\n          '.eslintrc',\n        ],\n        {\n          cwd: baseDir,\n        }\n      )) ?? null\n\n    const pkgJsonPath = (await findUp('package.json', { cwd: baseDir })) ?? null\n    let packageJsonConfig = null\n    if (pkgJsonPath) {\n      const pkgJsonContent = await fs.readFile(pkgJsonPath, {\n        encoding: 'utf8',\n      })\n      packageJsonConfig = CommentJson.parse(pkgJsonContent)\n    }\n\n    const config = await hasEslintConfiguration(eslintrcFile, packageJsonConfig)\n    let deps\n\n    if (config.exists) {\n      // Run if ESLint config exists\n      return await lint(\n        baseDir,\n        lintDirs,\n        eslintrcFile,\n        pkgJsonPath,\n        lintDuringBuild,\n        eslintOptions,\n        reportErrorsOnly,\n        maxWarnings,\n        formatter\n      )\n    } else {\n      // Display warning if no ESLint configuration is present during \"next build\"\n      if (lintDuringBuild) {\n        Log.warn(\n          `No ESLint configuration detected. Run ${chalk.bold.cyan(\n            'next lint'\n          )} to begin setup`\n        )\n        return null\n      } else {\n        // Ask user what config they would like to start with for first time \"next lint\" setup\n        const { config: selectedConfig } = strict\n          ? ESLINT_PROMPT_VALUES.find(\n              (opt: { title: string }) => opt.title === 'Strict'\n            )!\n          : await cliPrompt()\n\n        if (selectedConfig == null) {\n          // Show a warning if no option is selected in prompt\n          Log.warn(\n            'If you set up ESLint yourself, we recommend adding the Next.js ESLint plugin. See https://nextjs.org/docs/basic-features/eslint#migrating-existing-config'\n          )\n          return null\n        } else {\n          // Check if necessary deps installed, and install any that are missing\n          deps = await hasNecessaryDependencies(baseDir, requiredPackages)\n          if (deps.missing.length > 0)\n            await installDependencies(baseDir, deps.missing, true)\n\n          // Write default ESLint config.\n          // Check for /pages and src/pages is to make sure this happens in Next.js folder\n          if (\n            existsSync(path.join(baseDir, 'pages')) ||\n            existsSync(path.join(baseDir, 'src/pages'))\n          ) {\n            await writeDefaultConfig(\n              baseDir,\n              config,\n              selectedConfig,\n              eslintrcFile,\n              pkgJsonPath,\n              packageJsonConfig\n            )\n          }\n        }\n\n        Log.ready(\n          `ESLint has successfully been configured. Run ${chalk.bold.cyan(\n            'next lint'\n          )} again to view warnings and errors.`\n        )\n\n        return null\n      }\n    }\n  } catch (err) {\n    throw err\n  }\n}\n"], "names": [], "mappings": ";;;;QA8NsB,YAAY,GAAZ,YAAY;AA9NH,GAAI,CAAJ,GAAI;AACjB,GAAO,CAAP,MAAO;AACR,GAAM,CAAN,KAAM;AAEJ,GAA4B,CAA5B,OAA4B;AAC5B,GAA2B,CAA3B,OAA2B;AAClC,GAAW,CAAX,WAAW;AAEmB,GAAmB,CAAnB,gBAAmB;AAC1B,GAAsB,CAAtB,mBAAsB;AAClB,GAA0B,CAA1B,uBAA0B;AAE5B,GAAc,CAAd,UAAc;AACV,GAAmB,CAAnB,aAAmB;AACxB,GAAyB,CAAzB,oBAAyB;AACpB,GAA+B,CAA/B,yBAA+B;AACjD,GAAY,CAAZ,OAAY;AAEvB,GAAG,CAAH,GAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQf,KAAK,CAAC,gBAAgB;;QAClB,IAAI,GAAE,iBAAmB;QAAE,GAAG,GAAE,MAAQ;;;QACxC,IAAI,GAAE,kBAAoB;QAAE,GAAG,GAAE,kBAAoB;;;eAG1C,SAAS,GAAG,CAAC;IAC1B,OAAO,CAAC,GAAG,CA/BK,MAAO,SAgCf,IAAI,IAhCI,MAAO,SAiCV,IAAI,EACX,CAAG,GACH,sFAAsF;QAIxF,CAAC;QACH,KAAK,CAAC,SAAS;oDAAiB,6BAA+B;YAAG,OAAO;QACzE,KAAK,GAAG,KAAK,YAAW,SAAS;YAC/B,MAAM,EA/ByB,UAAc;YAgC7C,aAAa,KAET,KAAK,GACL,WAAW,KAEb,QAAiB,GACd,CAAC;gBACJ,KAAK,CAAC,IAAI,GAAG,QAAQ,GAlDX,MAAO,SAkDa,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,IAAI,KAAK;uBACzD,IAAI,IAAI,WAAW,GAnDhB,MAAO,SAmDkB,IAAI,CAAC,MAAM,EAAC,cAAgB;YACjE,CAAC;YACD,QAAQ,EArDI,MAAO,SAqDH,IAAI,EAAC,IAAI;YACzB,UAAU,GAAE,EAAI;;;YAGT,MAAM,EAAE,KAAK,aAAL,KAAK,UAAL,CAAa,QAAb,CAAa,GAAb,KAAK,CAAE,MAAM;;IAChC,CAAC,QAAO,CAAC;;YACE,MAAM,EAAE,IAAI;;IACvB,CAAC;AACH,CAAC;eAEc,IAAI,CACjB,OAAe,EACf,QAAkB,EAClB,YAA2B,EAC3B,WAA0B,EAC1B,eAAwB,GAAG,KAAK,EAChC,aAAkB,GAAG,IAAI,EACzB,gBAAyB,GAAG,KAAK,EACjC,WAAmB,IAAI,CAAC,EACxB,SAAwB,GAAG,IAAI,EAS/B,CAAC;QACG,CAAC;YAoBoC,GAAc,EAyFjD,IAA+B;QA5GnC,EAA0C,AAA1C,wCAA0C;QAC1C,KAAK,CAAC,IAAI,aAtE2B,yBAA+B,2BAsExB,OAAO,EAAE,gBAAgB;QAErE,EAAE,EAAE,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,GAAG,GAAK,GAAG,CAAC,GAAG,MAAK,MAAQ;WAAG,CAAC;YArE/C,GAAG,CAsEL,KAAK,EACN,wBAAwB,EACvB,eAAe,IAAG,+BAAiC,KAAG,CAAG,EAC1D,CAAC,EA1FQ,MAAO,SA0FP,IAAI,CAAC,IAAI,KA3EJ,OAAY,SA4ElB,OAAO,KACV,qBAAuB,KACvB,6BAA+B;mBAGhC,IAAI;QACb,CAAC;QAED,KAAK,CAAC,GAAG;mDAAgB,IAAI,CAAC,QAAQ,CAAC,GAAG,EAAC,MAAQ;;QAEnD,KAAK,GAAG,MAAM,MAAK,GAAG;YACF,IAAe;QAAnC,GAAG,CAAC,aAAa,IAAG,IAAe,GAAf,MAAM,aAAN,MAAM,UAAN,CAAe,QAAf,CAAe,GAAf,MAAM,CAAE,OAAO,cAAf,IAAe,cAAf,IAAe,GAAI,GAAG,aAAH,GAAG,UAAH,CAAc,QAAd,CAAc,IAAd,GAAc,GAAd,GAAG,CAAE,SAAS,cAAd,GAAc,UAAd,CAAc,QAAd,CAAc,GAAd,GAAc,CAAE,OAAO;QAE9D,EAAE,GAAG,aAAa,IApGH,OAA2B,SAoGb,EAAE,CAAC,aAAa,GAAE,KAAO,IAAG,CAAC;sBAxG5C,MAAO,SAyGH,GAAG,EACjB,KAAO,GACP,wDAAwD,EACxD,aAAa,IAAG,EAAI,IAAG,aAAa,IAAG,CAAG,OAC3C,6CAA6C;QAChD,CAAC;QAED,GAAG,CAAC,OAAO;YACT,WAAW,EAAE,IAAI;YACjB,UAAU;;YACV,uBAAuB,EAAE,KAAK;YAC9B,UAAU;iBAAG,GAAK;iBAAE,IAAM;iBAAE,GAAK;iBAAE,IAAM;;YACzC,KAAK,EAAE,IAAI;eACR,aAAa;;QAGlB,GAAG,CAAC,MAAM,GAAG,GAAG,CAAC,MAAM,CAAC,OAAO;QAE/B,GAAG,CAAC,yBAAyB,GAAG,KAAK;QACrC,KAAK,CAAC,aAAa;aAAI,iCAAmC;;aAErD,KAAK,CAAC,UAAU;YAAK,YAAY;YAAE,WAAW;UAAG,CAAC;gBAOjD,IAAsB;YAN1B,EAAE,GAAG,UAAU;YAEf,KAAK,CAAC,cAAc,SAAiB,MAAM,CAAC,sBAAsB,CAChE,UAAU;YAGZ,EAAE,GAAE,IAAsB,GAAtB,cAAc,CAAC,OAAO,cAAtB,IAAsB,UAAtB,CAAgC,QAAhC,CAAgC,GAAhC,IAAsB,CAAE,QAAQ,EAAC,UAAY,IAAG,CAAC;gBACnD,yBAAyB,GAAG,IAAI;;YAElC,CAAC;QACH,CAAC;QAED,KAAK,CAAC,QAAQ,OA/HuB,aAAmB,eA+H1B,OAAO;QAErC,EAAE,EAAE,yBAAyB,EAAE,CAAC;YAC9B,GAAG,CAAC,eAAe,GAAG,KAAK;iBAEtB,KAAK,CAAC,IAAI,IAAI,aAAa,CAAE,CAAC;oBAE9B,IAAyB,EACzB,IAAyB;gBAF5B,EAAE,KACC,IAAyB,GAAzB,OAAO,CAAC,UAAU,CAAE,KAAK,cAAzB,IAAyB,UAAzB,CAAiC,QAAjC,CAAiC,GAAjC,IAAyB,CAAG,IAAI,SAChC,IAAyB,GAAzB,OAAO,CAAC,UAAU,CAAE,KAAK,cAAzB,IAAyB,UAAzB,CAEA,QAFA,CAEA,GAFA,IAAyB,CACxB,IAAI,CAAC,OAAO,EAAC,UAAY,IAAE,uBAAyB,MAEtD,CAAC;oBACD,EAAE,GAAG,OAAO,CAAC,UAAU,CAAE,KAAK,EAAE,CAAC;wBAC/B,OAAO,CAAC,UAAU,CAAE,KAAK;;oBAC3B,CAAC;oBACD,OAAO,CAAC,UAAU,CAAE,KAAK,CAAC,IAAI;wBAAK,CAAC;wBAAE,QAAQ;;oBAC9C,eAAe,GAAG,IAAI;gBACxB,CAAC;YACH,CAAC;YAED,EAAE,EAAE,eAAe,EAAE,CAAC;gBACpB,MAAM,GAAG,GAAG,CAAC,MAAM,CAAC,OAAO;YAC7B,CAAC;QACH,CAAC,MAAM,CAAC;YAjJA,GAAG,CAkJL,IAAI,EACN,6IAA+I;QAEnJ,CAAC;QAED,KAAK,CAAC,SAAS,GAAG,OAAO,CAAC,MAAM;QAEhC,GAAG,CAAC,OAAO,SAAS,MAAM,CAAC,SAAS,CAAC,QAAQ;QAC7C,GAAG,CAAC,iBAAiB,GAAG,IAAI;QAE5B,EAAE,EAAE,OAAO,CAAC,GAAG,QAAQ,MAAM,CAAC,WAAW,CAAC,OAAO;QACjD,EAAE,EAAE,gBAAgB,EAAE,OAAO,SAAS,MAAM,CAAC,eAAe,CAAC,OAAO,CAAE,CAA6C,AAA7C,EAA6C,AAA7C,2CAA6C;;QAEnH,EAAE,EAAE,SAAS,EAAE,iBAAiB,SAAS,MAAM,CAAC,aAAa,CAAC,SAAS;QACvE,KAAK,CAAC,eAAe,OA1KiB,gBAAmB,gBA2KvD,OAAO,EACP,OAAO,EACP,iBAAiB,aAAjB,iBAAiB,UAAjB,CAAyB,QAAzB,CAAyB,GAAzB,iBAAiB,CAAE,MAAM;QAE3B,KAAK,CAAC,OAAO,GAAG,OAAO,CAAC,MAAM,CAAC,SAAS;QACxC,KAAK,CAAC,aAAa,GAAG,OAAO,CAAC,MAAM,EACjC,GAAW,EAAE,IAAgB,GAAK,GAAG,GAAG,IAAI,CAAC,YAAY;UAC1D,CAAC;;YAID,MAAM,EAAE,eAAe,CAAC,MAAM;YAC9B,OAAO,IACL,IAA+B,GAA/B,MAAM,CAAC,eAAe,CAAC,OAAO,eAA9B,IAA+B,UAA/B,CAAuC,QAAvC,CAAuC,GAAvC,IAA+B,CAAE,MAAM,IAAG,CAAC,IAC1C,WAAW,IAAI,CAAC,IAAI,aAAa,GAAG,WAAW;YAClD,SAAS;gBACP,iBAAiB,EAAE,OAAO,CAAC,CAAC;gBAC5B,aAAa,EAAE,aAAa;gBAC5B,gBAAgB,EAAE,OAAO,CAAC,MAAM;gBAChC,OAAO,IAAI,OAAO,CAAC,GAAG;gBACtB,uBAAuB,EAAE,yBAAyB,GAC9C,OAAO,CAtMF,KAAM,SAsME,IAAI,CAtMZ,KAAM,SAuMJ,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,EAAC,kBAAoB,MACnD,YAAc,IACb,OAAO,GACV,IAAI;gBACR,2BAA2B,EAAE,eAAe,CAAC,yBAAyB;gBACtE,6BAA6B,EAC3B,eAAe,CAAC,2BAA2B;;;IAGnD,CAAC,QAAQ,GAAG,EAAE,CAAC;QACb,EAAE,EAAE,eAAe,EAAE,CAAC;YAjMd,GAAG,CAkML,KAAK,EACN,QAAQ,EAAE,GAAG,CAAC,OAAO,GAAG,GAAG,CAAC,OAAO,CAAC,OAAO,SAAQ,CAAG,KAAI,GAAG;mBAEzD,IAAI;QACb,CAAC,MAAM,CAAC;YACN,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG;QACrB,CAAC;IACH,CAAC;AACH,CAAC;eAEqB,YAAY,CAChC,OAAe,EACf,QAAkB,EAClB,eAAwB,GAAG,KAAK,EAChC,aAAkB,GAAG,IAAI,EACzB,gBAAyB,GAAG,KAAK,EACjC,WAAmB,IAAI,CAAC,EACxB,SAAwB,GAAG,IAAI,EAC/B,MAAe,GAAG,KAAK,EACE,CAAC;QACtB,CAAC;YAGD,IAWE;QAbJ,EAA6B,AAA7B,2BAA6B;QAC7B,KAAK,CAAC,YAAY,IAChB,IAWE,aAlPW,OAA4B;aAyOrC,YAAc;aACd,cAAgB;aAChB,aAAe;aACf,cAAgB;aAChB,SAAW;;YAGX,GAAG,EAAE,OAAO;wBAThB,IAWE,cAXF,IAWE,GAAI,IAAI;YAEQ,IAAgD;QAApE,KAAK,CAAC,WAAW,IAAG,IAAgD,aApPrD,OAA4B,WAoPT,YAAc;YAAI,GAAG,EAAE,OAAO;wBAA5C,IAAgD,cAAhD,IAAgD,GAAI,IAAI;QAC5E,GAAG,CAAC,iBAAiB,GAAG,IAAI;QAC5B,EAAE,EAAE,WAAW,EAAE,CAAC;YAChB,KAAK,CAAC,cAAc,SA3PK,GAAI,UA2PG,QAAQ,CAAC,WAAW;gBAClD,QAAQ,GAAE,IAAM;;YAElB,iBAAiB,GAxPX,WAAW,CAwPe,KAAK,CAAC,cAAc;QACtD,CAAC;QAED,KAAK,CAAC,MAAM,aAvPuB,uBAA0B,yBAuPjB,YAAY,EAAE,iBAAiB;QAC3E,GAAG,CAAC,IAAI;QAER,EAAE,EAAE,MAAM,CAAC,MAAM,EAAE,CAAC;YAClB,EAA8B,AAA9B,4BAA8B;yBACjB,IAAI,CACf,OAAO,EACP,QAAQ,EACR,YAAY,EACZ,WAAW,EACX,eAAe,EACf,aAAa,EACb,gBAAgB,EAChB,WAAW,EACX,SAAS;QAEb,CAAC,MAAM,CAAC;YACN,EAA4E,AAA5E,0EAA4E;YAC5E,EAAE,EAAE,eAAe,EAAE,CAAC;gBAjQhB,GAAG,CAkQH,IAAI,EACL,sCAAsC,EApR/B,MAAO,SAoRgC,IAAI,CAAC,IAAI,EACtD,SAAW,GACX,eAAe;uBAEZ,IAAI;YACb,CAAC,MAAM,CAAC;gBACN,EAAsF,AAAtF,oFAAsF;gBACtF,KAAK,GAAG,MAAM,EAAE,cAAc,MAAK,MAAM,GAhRZ,UAAc,sBAiRlB,IAAI,EACtB,GAAsB,GAAK,GAAG,CAAC,KAAK,MAAK,MAAQ;0BAE9C,SAAS;gBAEnB,EAAE,EAAE,cAAc,IAAI,IAAI,EAAE,CAAC;oBAC3B,EAAoD,AAApD,kDAAoD;oBAjRlD,GAAG,CAkRD,IAAI,EACN,yJAA2J;2BAEtJ,IAAI;gBACb,CAAC,MAAM,CAAC;oBACN,EAAsE,AAAtE,oEAAsE;oBACtE,IAAI,aA3R2B,yBAA+B,2BA2RxB,OAAO,EAAE,gBAAgB;oBAC/D,EAAE,EAAE,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,YA7RD,oBAAyB,sBA8RvB,OAAO,EAAE,IAAI,CAAC,OAAO,EAAE,IAAI;oBAEvD,EAA+B,AAA/B,6BAA+B;oBAC/B,EAAgF,AAAhF,8EAAgF;oBAChF,EAAE,MAnS6B,aAAmB,aAX3C,KAAM,SA+SK,IAAI,CAAC,OAAO,GAAE,KAAO,WApSR,aAAmB,aAX3C,KAAM,SAgTK,IAAI,CAAC,OAAO,GAAE,SAAW,KACzC,CAAC;kCA1SsB,mBAAsB,qBA4S3C,OAAO,EACP,MAAM,EACN,cAAc,EACd,YAAY,EACZ,WAAW,EACX,iBAAiB;oBAErB,CAAC;gBACH,CAAC;gBA3SG,GAAG,CA6SH,KAAK,EACN,6CAA6C,EA/TtC,MAAO,SA+TuC,IAAI,CAAC,IAAI,EAC7D,SAAW,GACX,mCAAmC;uBAGhC,IAAI;YACb,CAAC;QACH,CAAC;IACH,CAAC,QAAQ,GAAG,EAAE,CAAC;QACb,KAAK,CAAC,GAAG;IACX,CAAC;AACH,CAAC"}