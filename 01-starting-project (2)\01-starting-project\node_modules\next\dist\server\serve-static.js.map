{"version": 3, "sources": ["../../server/serve-static.ts"], "sourcesContent": ["import { IncomingMessage, ServerResponse } from 'http'\nimport send from 'next/dist/compiled/send'\n\nexport function serveStatic(\n  req: IncomingMessage,\n  res: ServerResponse,\n  path: string\n): Promise<void> {\n  return new Promise((resolve, reject) => {\n    send(req, path)\n      .on('directory', () => {\n        // We don't allow directories to be read.\n        const err: any = new Error('No directory access')\n        err.code = 'ENOENT'\n        reject(err)\n      })\n      .on('error', reject)\n      .pipe(res)\n      .on('finish', resolve)\n  })\n}\n\nexport function getContentType(extWithoutDot: string): string | null {\n  const { mime } = send\n  if ('getType' in mime) {\n    // 2.0\n    return mime.getType(extWithoutDot)\n  }\n  // 1.0\n  return (mime as any).lookup(extWithoutDot)\n}\n\nexport function getExtension(contentType: string): string | null {\n  const { mime } = send\n  if ('getExtension' in mime) {\n    // 2.0\n    return mime.getExtension(contentType)\n  }\n  // 1.0\n  return (mime as any).extension(contentType)\n}\n"], "names": [], "mappings": ";;;;QAGgB,WAAW,GAAX,WAAW;QAmBX,cAAc,GAAd,cAAc;QAUd,YAAY,GAAZ,YAAY;AA/BX,GAAyB,CAAzB,KAAyB;;;;;;SAE1B,WAAW,CACzB,GAAoB,EACpB,GAAmB,EACnB,IAAY,EACG,CAAC;WACT,GAAG,CAAC,OAAO,EAAE,OAAO,EAAE,MAAM,GAAK,CAAC;YAP1B,KAAyB,UAQjC,GAAG,EAAE,IAAI,EACX,EAAE,EAAC,SAAW,OAAQ,CAAC;YACtB,EAAyC,AAAzC,uCAAyC;YACzC,KAAK,CAAC,GAAG,GAAQ,GAAG,CAAC,KAAK,EAAC,mBAAqB;YAChD,GAAG,CAAC,IAAI,IAAG,MAAQ;YACnB,MAAM,CAAC,GAAG;QACZ,CAAC,EACA,EAAE,EAAC,KAAO,GAAE,MAAM,EAClB,IAAI,CAAC,GAAG,EACR,EAAE,EAAC,MAAQ,GAAE,OAAO;IACzB,CAAC;AACH,CAAC;SAEe,cAAc,CAAC,aAAqB,EAAiB,CAAC;IACpE,KAAK,GAAG,IAAI,MAtBG,KAAyB;IAuBxC,EAAE,GAAE,OAAS,KAAI,IAAI,EAAE,CAAC;QACtB,EAAM,AAAN,IAAM;eACC,IAAI,CAAC,OAAO,CAAC,aAAa;IACnC,CAAC;IACD,EAAM,AAAN,IAAM;WACE,IAAI,CAAS,MAAM,CAAC,aAAa;AAC3C,CAAC;SAEe,YAAY,CAAC,WAAmB,EAAiB,CAAC;IAChE,KAAK,GAAG,IAAI,MAhCG,KAAyB;IAiCxC,EAAE,GAAE,YAAc,KAAI,IAAI,EAAE,CAAC;QAC3B,EAAM,AAAN,IAAM;eACC,IAAI,CAAC,YAAY,CAAC,WAAW;IACtC,CAAC;IACD,EAAM,AAAN,IAAM;WACE,IAAI,CAAS,SAAS,CAAC,WAAW;AAC5C,CAAC"}