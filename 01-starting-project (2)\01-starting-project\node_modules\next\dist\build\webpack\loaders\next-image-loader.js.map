{"version": 3, "sources": ["../../../../build/webpack/loaders/next-image-loader.js"], "sourcesContent": ["import loaderUtils from 'next/dist/compiled/loader-utils'\nimport sizeOf from 'image-size'\nimport { resizeImage } from '../../../server/image-optimizer'\n\nconst BLUR_IMG_SIZE = 8\nconst BLUR_QUALITY = 70\nconst VALID_BLUR_EXT = ['jpeg', 'png', 'webp']\n\nfunction nextImageLoader(content) {\n  const imageLoaderSpan = this.currentTraceSpan.traceChild('next-image-loader')\n  return imageLoaderSpan.traceAsyncFn(async () => {\n    const { isServer, isDev, assetPrefix } = loaderUtils.getOptions(this)\n    const context = this.rootContext\n    const opts = { context, content }\n    const interpolatedName = loaderUtils.interpolateName(\n      this,\n      '/static/image/[path][name].[hash].[ext]',\n      opts\n    )\n    const outputPath = '/_next' + interpolatedName\n\n    let extension = loaderUtils.interpolateName(this, '[ext]', opts)\n    if (extension === 'jpg') {\n      extension = 'jpeg'\n    }\n\n    const imageSizeSpan = imageLoaderSpan.traceChild('image-size-calculation')\n    const imageSize = imageSizeSpan.traceFn(() => sizeOf(content))\n    let blurDataURL\n\n    if (VALID_BLUR_EXT.includes(extension)) {\n      if (isDev) {\n        const prefix = 'http://localhost'\n        const url = new URL('/_next/image', prefix)\n        url.searchParams.set('url', assetPrefix + outputPath)\n        url.searchParams.set('w', BLUR_IMG_SIZE)\n        url.searchParams.set('q', BLUR_QUALITY)\n        blurDataURL = url.href.slice(prefix.length)\n      } else {\n        // Shrink the image's largest dimension\n        const dimension =\n          imageSize.width >= imageSize.height ? 'width' : 'height'\n\n        const resizeImageSpan = imageLoaderSpan.traceChild('image-resize')\n        const resizedImage = await resizeImageSpan.traceAsyncFn(() =>\n          resizeImage(\n            content,\n            dimension,\n            BLUR_IMG_SIZE,\n            extension,\n            BLUR_QUALITY\n          )\n        )\n        const blurDataURLSpan = imageLoaderSpan.traceChild(\n          'image-base64-tostring'\n        )\n        blurDataURL = blurDataURLSpan.traceFn(\n          () =>\n            `data:image/${extension};base64,${resizedImage.toString('base64')}`\n        )\n      }\n    }\n\n    const stringifiedData = imageLoaderSpan\n      .traceChild('image-data-stringify')\n      .traceFn(() =>\n        JSON.stringify({\n          src: outputPath,\n          height: imageSize.height,\n          width: imageSize.width,\n          blurDataURL,\n        })\n      )\n\n    if (!isServer) {\n      this.emitFile(interpolatedName, content, null)\n    }\n\n    return `export default ${stringifiedData};`\n  })\n}\nexport const raw = true\nexport default nextImageLoader\n"], "names": [], "mappings": ";;;;;AAAwB,GAAiC,CAAjC,YAAiC;AACtC,GAAY,CAAZ,UAAY;AACH,GAAiC,CAAjC,eAAiC;;;;;;AAE7D,KAAK,CAAC,aAAa,GAAG,CAAC;AACvB,KAAK,CAAC,YAAY,GAAG,EAAE;AACvB,KAAK,CAAC,cAAc;KAAI,IAAM;KAAE,GAAK;KAAE,IAAM;;SAEpC,eAAe,CAAC,OAAO,EAAE,CAAC;IACjC,KAAK,CAAC,eAAe,QAAQ,gBAAgB,CAAC,UAAU,EAAC,iBAAmB;WACrE,eAAe,CAAC,YAAY,WAAa,CAAC;QAC/C,KAAK,GAAG,QAAQ,GAAE,KAAK,GAAE,WAAW,MAXhB,YAAiC,SAWA,UAAU;QAC/D,KAAK,CAAC,OAAO,QAAQ,WAAW;QAChC,KAAK,CAAC,IAAI;YAAK,OAAO;YAAE,OAAO;;QAC/B,KAAK,CAAC,gBAAgB,GAdF,YAAiC,SAchB,eAAe,QAElD,uCAAyC,GACzC,IAAI;QAEN,KAAK,CAAC,UAAU,IAAG,MAAQ,IAAG,gBAAgB;QAE9C,GAAG,CAAC,SAAS,GArBO,YAAiC,SAqBzB,eAAe,QAAO,KAAO,GAAE,IAAI;QAC/D,EAAE,EAAE,SAAS,MAAK,GAAK,GAAE,CAAC;YACxB,SAAS,IAAG,IAAM;QACpB,CAAC;QAED,KAAK,CAAC,aAAa,GAAG,eAAe,CAAC,UAAU,EAAC,sBAAwB;QACzE,KAAK,CAAC,SAAS,GAAG,aAAa,CAAC,OAAO,SA1BxB,UAAY,UA0B0B,OAAO;;QAC5D,GAAG,CAAC,WAAW;QAEf,EAAE,EAAE,cAAc,CAAC,QAAQ,CAAC,SAAS,GAAG,CAAC;YACvC,EAAE,EAAE,KAAK,EAAE,CAAC;gBACV,KAAK,CAAC,MAAM,IAAG,gBAAkB;gBACjC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,GAAG,EAAC,YAAc,GAAE,MAAM;gBAC1C,GAAG,CAAC,YAAY,CAAC,GAAG,EAAC,GAAK,GAAE,WAAW,GAAG,UAAU;gBACpD,GAAG,CAAC,YAAY,CAAC,GAAG,EAAC,CAAG,GAAE,aAAa;gBACvC,GAAG,CAAC,YAAY,CAAC,GAAG,EAAC,CAAG,GAAE,YAAY;gBACtC,WAAW,GAAG,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,MAAM;YAC5C,CAAC,MAAM,CAAC;gBACN,EAAuC,AAAvC,qCAAuC;gBACvC,KAAK,CAAC,SAAS,GACb,SAAS,CAAC,KAAK,IAAI,SAAS,CAAC,MAAM,IAAG,KAAO,KAAG,MAAQ;gBAE1D,KAAK,CAAC,eAAe,GAAG,eAAe,CAAC,UAAU,EAAC,YAAc;gBACjE,KAAK,CAAC,YAAY,SAAS,eAAe,CAAC,YAAY,SA1CnC,eAAiC,cA4CjD,OAAO,EACP,SAAS,EACT,aAAa,EACb,SAAS,EACT,YAAY;;gBAGhB,KAAK,CAAC,eAAe,GAAG,eAAe,CAAC,UAAU,EAChD,qBAAuB;gBAEzB,WAAW,GAAG,eAAe,CAAC,OAAO,MAEhC,WAAW,EAAE,SAAS,CAAC,QAAQ,EAAE,YAAY,CAAC,QAAQ,EAAC,MAAQ;;YAEtE,CAAC;QACH,CAAC;QAED,KAAK,CAAC,eAAe,GAAG,eAAe,CACpC,UAAU,EAAC,oBAAsB,GACjC,OAAO,KACN,IAAI,CAAC,SAAS;gBACZ,GAAG,EAAE,UAAU;gBACf,MAAM,EAAE,SAAS,CAAC,MAAM;gBACxB,KAAK,EAAE,SAAS,CAAC,KAAK;gBACtB,WAAW;;;QAIjB,EAAE,GAAG,QAAQ,EAAE,CAAC;iBACT,QAAQ,CAAC,gBAAgB,EAAE,OAAO,EAAE,IAAI;QAC/C,CAAC;gBAEO,eAAe,EAAE,eAAe,CAAC,CAAC;IAC5C,CAAC;AACH,CAAC;AACM,KAAK,CAAC,GAAG,GAAG,IAAI;QAAV,GAAG,GAAH,GAAG;eACD,eAAe"}