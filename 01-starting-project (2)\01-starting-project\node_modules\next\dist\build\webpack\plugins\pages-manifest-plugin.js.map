{"version": 3, "sources": ["../../../../build/webpack/plugins/pages-manifest-plugin.ts"], "sourcesContent": ["import {\n  webpack,\n  isWebpack5,\n  sources,\n} from 'next/dist/compiled/webpack/webpack'\nimport { PAGES_MANIFEST } from '../../../shared/lib/constants'\nimport getRouteFromEntrypoint from '../../../server/get-route-from-entrypoint'\n\nexport type PagesManifest = { [page: string]: string }\n\n// This plugin creates a pages-manifest.json from page entrypoints.\n// This is used for mapping paths like `/` to `.next/server/static/<buildid>/pages/index.js` when doing SSR\n// It's also used by next export to provide defaultPathMap\nexport default class PagesManifestPlugin implements webpack.Plugin {\n  serverless: boolean\n  dev: boolean\n\n  constructor({ serverless, dev }: { serverless: boolean; dev: boolean }) {\n    this.serverless = serverless\n    this.dev = dev\n  }\n\n  createAssets(compilation: any, assets: any) {\n    const entrypoints = compilation.entrypoints\n    const pages: PagesManifest = {}\n\n    for (const entrypoint of entrypoints.values()) {\n      const pagePath = getRouteFromEntrypoint(entrypoint.name)\n\n      if (!pagePath) {\n        continue\n      }\n\n      const files = entrypoint\n        .getFiles()\n        .filter(\n          (file: string) =>\n            !file.includes('webpack-runtime') && file.endsWith('.js')\n        )\n\n      if (!isWebpack5 && files.length > 1) {\n        console.log(\n          `Found more than one file in server entrypoint ${entrypoint.name}`,\n          files\n        )\n        continue\n      }\n\n      // Write filename, replace any backslashes in path (on windows) with forwardslashes for cross-platform consistency.\n      pages[pagePath] = files[files.length - 1]\n\n      if (isWebpack5 && !this.dev) {\n        pages[pagePath] = pages[pagePath].slice(3)\n      }\n      pages[pagePath] = pages[pagePath].replace(/\\\\/g, '/')\n    }\n\n    assets[`${isWebpack5 && !this.dev ? '../' : ''}` + PAGES_MANIFEST] =\n      new sources.RawSource(JSON.stringify(pages, null, 2))\n  }\n\n  apply(compiler: webpack.Compiler): void {\n    if (isWebpack5) {\n      compiler.hooks.make.tap('NextJsPagesManifest', (compilation) => {\n        // @ts-ignore TODO: Remove ignore when webpack 5 is stable\n        compilation.hooks.processAssets.tap(\n          {\n            name: 'NextJsPagesManifest',\n            // @ts-ignore TODO: Remove ignore when webpack 5 is stable\n            stage: webpack.Compilation.PROCESS_ASSETS_STAGE_ADDITIONS,\n          },\n          (assets: any) => {\n            this.createAssets(compilation, assets)\n          }\n        )\n      })\n      return\n    }\n\n    compiler.hooks.emit.tap('NextJsPagesManifest', (compilation: any) => {\n      this.createAssets(compilation, compilation.assets)\n    })\n  }\n}\n"], "names": [], "mappings": ";;;;;AAIO,GAAoC,CAApC,QAAoC;AACZ,GAA+B,CAA/B,UAA+B;AAC3B,GAA2C,CAA3C,uBAA2C;;;;;;MAOzD,mBAAmB;kBAIxB,UAAU,GAAE,GAAG,IAA2C,CAAC;aAClE,UAAU,GAAG,UAAU;aACvB,GAAG,GAAG,GAAG;IAChB,CAAC;IAED,YAAY,CAAC,WAAgB,EAAE,MAAW,EAAE,CAAC;QAC3C,KAAK,CAAC,WAAW,GAAG,WAAW,CAAC,WAAW;QAC3C,KAAK,CAAC,KAAK;;aAEN,KAAK,CAAC,UAAU,IAAI,WAAW,CAAC,MAAM,GAAI,CAAC;YAC9C,KAAK,CAAC,QAAQ,OArBe,uBAA2C,UAqBhC,UAAU,CAAC,IAAI;YAEvD,EAAE,GAAG,QAAQ,EAAE,CAAC;;YAEhB,CAAC;YAED,KAAK,CAAC,KAAK,GAAG,UAAU,CACrB,QAAQ,GACR,MAAM,EACJ,IAAY,IACV,IAAI,CAAC,QAAQ,EAAC,eAAiB,MAAK,IAAI,CAAC,QAAQ,EAAC,GAAK;;YAG9D,EAAE,GApCD,QAAoC,eAoClB,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBACpC,OAAO,CAAC,GAAG,EACR,8CAA8C,EAAE,UAAU,CAAC,IAAI,IAChE,KAAK;;YAGT,CAAC;YAED,EAAmH,AAAnH,iHAAmH;YACnH,KAAK,CAAC,QAAQ,IAAI,KAAK,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC;YAExC,EAAE,EA/CD,QAAoC,qBA+Cb,GAAG,EAAE,CAAC;gBAC5B,KAAK,CAAC,QAAQ,IAAI,KAAK,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC;YAC3C,CAAC;YACD,KAAK,CAAC,QAAQ,IAAI,KAAK,CAAC,QAAQ,EAAE,OAAO,SAAQ,CAAG;QACtD,CAAC;QAED,MAAM,IArDH,QAAoC,qBAqDT,GAAG,IAAG,GAAK,WApDd,UAA+B,mBAqDxD,GAAG,CAtDF,QAAoC,SAsDzB,SAAS,CAAC,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,IAAI,EAAE,CAAC;IACvD,CAAC;IAED,KAAK,CAAC,QAA0B,EAAQ,CAAC;QACvC,EAAE,EA1DC,QAAoC,aA0DvB,CAAC;YACf,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,EAAC,mBAAqB,IAAG,WAAW,GAAK,CAAC;gBAC/D,EAA0D,AAA1D,wDAA0D;gBAC1D,WAAW,CAAC,KAAK,CAAC,aAAa,CAAC,GAAG;oBAE/B,IAAI,GAAE,mBAAqB;oBAC3B,EAA0D,AAA1D,wDAA0D;oBAC1D,KAAK,EAjEV,QAAoC,SAiEhB,WAAW,CAAC,8BAA8B;oBAE1D,MAAW,GAAK,CAAC;yBACX,YAAY,CAAC,WAAW,EAAE,MAAM;gBACvC,CAAC;YAEL,CAAC;;QAEH,CAAC;QAED,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,EAAC,mBAAqB,IAAG,WAAgB,GAAK,CAAC;iBAC/D,YAAY,CAAC,WAAW,EAAE,WAAW,CAAC,MAAM;QACnD,CAAC;IACH,CAAC;;kBArEkB,mBAAmB"}