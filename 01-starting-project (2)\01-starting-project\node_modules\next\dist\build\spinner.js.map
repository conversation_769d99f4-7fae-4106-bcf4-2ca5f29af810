{"version": 3, "sources": ["../../build/spinner.ts"], "sourcesContent": ["import ora from 'next/dist/compiled/ora'\n\nconst dotsSpinner = {\n  frames: ['.', '..', '...'],\n  interval: 200,\n}\n\nexport default function createSpinner(\n  text: string | { prefixText: string },\n  options: ora.Options = {},\n  logFn: (...data: any[]) => void = console.log\n) {\n  let spinner: undefined | ora.Ora\n  let prefixText = text && typeof text === 'object' && text.prefixText\n\n  if (process.stdout.isTTY) {\n    spinner = ora({\n      text: typeof text === 'string' ? text : undefined,\n      prefixText: typeof prefixText === 'string' ? prefixText : undefined,\n      spinner: dotsSpinner,\n      stream: process.stdout,\n      ...options,\n    }).start()\n\n    // Add capturing of console.log/warn/error to allow pausing\n    // the spinner before logging and then restarting spinner after\n    const origLog = console.log\n    const origWarn = console.warn\n    const origError = console.error\n    const origStop = spinner.stop.bind(spinner)\n    const origStopAndPersist = spinner.stopAndPersist.bind(spinner)\n\n    const logHandle = (method: any, args: any[]) => {\n      origStop()\n      method(...args)\n      spinner!.start()\n    }\n\n    console.log = (...args: any) => logHandle(origLog, args)\n    console.warn = (...args: any) => logHandle(origWarn, args)\n    console.error = (...args: any) => logHandle(origError, args)\n\n    const resetLog = () => {\n      console.log = origLog\n      console.warn = origWarn\n      console.error = origError\n    }\n    spinner.stop = (): ora.Ora => {\n      origStop()\n      resetLog()\n      return spinner!\n    }\n    spinner.stopAndPersist = (): ora.Ora => {\n      origStopAndPersist()\n      resetLog()\n      return spinner!\n    }\n  } else if (prefixText || text) {\n    logFn(prefixText ? prefixText + '...' : text)\n  }\n\n  return spinner\n}\n"], "names": [], "mappings": ";;;;kBAOwB,aAAa;AAPrB,GAAwB,CAAxB,IAAwB;;;;;;AAExC,KAAK,CAAC,WAAW;IACf,MAAM;SAAG,CAAG;SAAE,EAAI;SAAE,GAAK;;IACzB,QAAQ,EAAE,GAAG;;SAGS,aAAa,CACnC,IAAqC,EACrC,OAAoB;GACpB,KAA+B,GAAG,OAAO,CAAC,GAAG,EAC7C,CAAC;IACD,GAAG,CAAC,OAAO;IACX,GAAG,CAAC,UAAU,GAAG,IAAI,WAAW,IAAI,MAAK,MAAQ,KAAI,IAAI,CAAC,UAAU;IAEpE,EAAE,EAAE,OAAO,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC;QACzB,OAAO,OAhBK,IAAwB;YAiBlC,IAAI,SAAS,IAAI,MAAK,MAAQ,IAAG,IAAI,GAAG,SAAS;YACjD,UAAU,SAAS,UAAU,MAAK,MAAQ,IAAG,UAAU,GAAG,SAAS;YACnE,OAAO,EAAE,WAAW;YACpB,MAAM,EAAE,OAAO,CAAC,MAAM;eACnB,OAAO;WACT,KAAK;QAER,EAA2D,AAA3D,yDAA2D;QAC3D,EAA+D,AAA/D,6DAA+D;QAC/D,KAAK,CAAC,OAAO,GAAG,OAAO,CAAC,GAAG;QAC3B,KAAK,CAAC,QAAQ,GAAG,OAAO,CAAC,IAAI;QAC7B,KAAK,CAAC,SAAS,GAAG,OAAO,CAAC,KAAK;QAC/B,KAAK,CAAC,QAAQ,GAAG,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO;QAC1C,KAAK,CAAC,kBAAkB,GAAG,OAAO,CAAC,cAAc,CAAC,IAAI,CAAC,OAAO;QAE9D,KAAK,CAAC,SAAS,IAAI,MAAW,EAAE,IAAW,GAAK,CAAC;YAC/C,QAAQ;YACR,MAAM,IAAI,IAAI;YACd,OAAO,CAAE,KAAK;QAChB,CAAC;QAED,OAAO,CAAC,GAAG,OAAO,IAAI,GAAU,SAAS,CAAC,OAAO,EAAE,IAAI;;QACvD,OAAO,CAAC,IAAI,OAAO,IAAI,GAAU,SAAS,CAAC,QAAQ,EAAE,IAAI;;QACzD,OAAO,CAAC,KAAK,OAAO,IAAI,GAAU,SAAS,CAAC,SAAS,EAAE,IAAI;;QAE3D,KAAK,CAAC,QAAQ,OAAS,CAAC;YACtB,OAAO,CAAC,GAAG,GAAG,OAAO;YACrB,OAAO,CAAC,IAAI,GAAG,QAAQ;YACvB,OAAO,CAAC,KAAK,GAAG,SAAS;QAC3B,CAAC;QACD,OAAO,CAAC,IAAI,OAAkB,CAAC;YAC7B,QAAQ;YACR,QAAQ;mBACD,OAAO;QAChB,CAAC;QACD,OAAO,CAAC,cAAc,OAAkB,CAAC;YACvC,kBAAkB;YAClB,QAAQ;mBACD,OAAO;QAChB,CAAC;IACH,CAAC,MAAM,EAAE,EAAE,UAAU,IAAI,IAAI,EAAE,CAAC;QAC9B,KAAK,CAAC,UAAU,GAAG,UAAU,IAAG,GAAK,IAAG,IAAI;IAC9C,CAAC;WAEM,OAAO;AAChB,CAAC"}