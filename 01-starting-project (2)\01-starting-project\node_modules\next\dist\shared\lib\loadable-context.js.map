{"version": 3, "sources": ["../../../shared/lib/loadable-context.ts"], "sourcesContent": ["import React from 'react'\n\ntype CaptureFn = (moduleName: string) => void\n\nexport const LoadableContext = React.createContext<CaptureFn | null>(null)\n\nif (process.env.NODE_ENV !== 'production') {\n  LoadableContext.displayName = 'LoadableContext'\n}\n"], "names": [], "mappings": ";;;;;AAAkB,GAAO,CAAP,MAAO;;;;;;AAIlB,KAAK,CAAC,eAAe,GAJV,MAAO,SAIY,aAAa,CAAmB,IAAI;QAA5D,eAAe,GAAf,eAAe;AAE5B,EAAE,EAAE,OAAO,CAAC,GAAG,CAAC,QAAQ,MAAK,UAAY,GAAE,CAAC;IAC1C,eAAe,CAAC,WAAW,IAAG,eAAiB;AACjD,CAAC"}