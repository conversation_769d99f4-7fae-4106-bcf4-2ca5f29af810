{"version": 3, "sources": ["../../../client/dev/on-demand-entries-utils.js"], "sourcesContent": ["/* global location */\nimport { getEventSourceWrapper } from './error-overlay/eventsource'\n\nlet evtSource\nexport let currentPage\n\nexport function closePing() {\n  if (evtSource) evtSource.close()\n  evtSource = null\n}\n\nexport function setupPing(assetPrefix, pathnameFn, retry) {\n  const pathname = pathnameFn()\n\n  // Make sure to only create new EventSource request if page has changed\n  if (pathname === currentPage && !retry) return\n  currentPage = pathname\n  // close current EventSource connection\n  closePing()\n\n  evtSource = getEventSourceWrapper({\n    path: `${assetPrefix}/_next/webpack-hmr?page=${encodeURIComponent(\n      currentPage\n    )}`,\n    timeout: 5000,\n  })\n\n  evtSource.addMessageListener((event) => {\n    if (event.data.indexOf('{') === -1) return\n    try {\n      const payload = JSON.parse(event.data)\n      // don't attempt fetching the page if we're already showing\n      // the dev overlay as this can cause the error to be triggered\n      // repeatedly\n      if (payload.invalid && !self.__NEXT_DATA__.err) {\n        // Payload can be invalid even if the page does not exist.\n        // So, we need to make sure it exists before reloading.\n        fetch(location.href, {\n          credentials: 'same-origin',\n        }).then((pageRes) => {\n          if (pageRes.status === 200) {\n            location.reload()\n          }\n        })\n      }\n    } catch (err) {\n      console.error('on-demand-entries failed to parse response', err)\n    }\n  })\n}\n"], "names": [], "mappings": ";;;;QAMgB,SAAS,GAAT,SAAS;QAKT,SAAS,GAAT,SAAS;;AAVa,GAA6B,CAA7B,YAA6B;AAEnE,GAAG,CAAC,SAAS;AACN,GAAG,CAAC,WAAW;QAAX,WAAW,GAAX,WAAW;SAEN,SAAS,GAAG,CAAC;IAC3B,EAAE,EAAE,SAAS,EAAE,SAAS,CAAC,KAAK;IAC9B,SAAS,GAAG,IAAI;AAClB,CAAC;SAEe,SAAS,CAAC,WAAW,EAAE,UAAU,EAAE,KAAK,EAAE,CAAC;IACzD,KAAK,CAAC,QAAQ,GAAG,UAAU;IAE3B,EAAuE,AAAvE,qEAAuE;IACvE,EAAE,EAAE,QAAQ,KAAK,WAAW,KAAK,KAAK;0BACtC,WAAW,GAAG,QAAQ;IACtB,EAAuC,AAAvC,qCAAuC;IACvC,SAAS;IAET,SAAS,OAnB2B,YAA6B;QAoB/D,IAAI,KAAK,WAAW,CAAC,wBAAwB,EAAE,kBAAkB,CAC/D,WAAW;QAEb,OAAO,EAAE,IAAI;;IAGf,SAAS,CAAC,kBAAkB,EAAE,KAAK,GAAK,CAAC;QACvC,EAAE,EAAE,KAAK,CAAC,IAAI,CAAC,OAAO,EAAC,CAAG,QAAO,CAAC;YAC9B,CAAC;YACH,KAAK,CAAC,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI;YACrC,EAA2D,AAA3D,yDAA2D;YAC3D,EAA8D,AAA9D,4DAA8D;YAC9D,EAAa,AAAb,WAAa;YACb,EAAE,EAAE,OAAO,CAAC,OAAO,KAAK,IAAI,CAAC,aAAa,CAAC,GAAG,EAAE,CAAC;gBAC/C,EAA0D,AAA1D,wDAA0D;gBAC1D,EAAuD,AAAvD,qDAAuD;gBACvD,KAAK,CAAC,QAAQ,CAAC,IAAI;oBACjB,WAAW,GAAE,WAAa;mBACzB,IAAI,EAAE,OAAO,GAAK,CAAC;oBACpB,EAAE,EAAE,OAAO,CAAC,MAAM,KAAK,GAAG,EAAE,CAAC;wBAC3B,QAAQ,CAAC,MAAM;oBACjB,CAAC;gBACH,CAAC;YACH,CAAC;QACH,CAAC,QAAQ,GAAG,EAAE,CAAC;YACb,OAAO,CAAC,KAAK,EAAC,0CAA4C,GAAE,GAAG;QACjE,CAAC;IACH,CAAC;AACH,CAAC"}