{"version": 3, "sources": ["../../../lib/helpers/install.ts"], "sourcesContent": ["/* eslint-disable import/no-extraneous-dependencies */\nimport chalk from 'chalk'\nimport spawn from 'next/dist/compiled/cross-spawn'\n\ninterface InstallArgs {\n  /**\n   * Indicate whether to install packages using Yarn.\n   */\n  useYarn: boolean\n  /**\n   * Indicate whether there is an active Internet connection.\n   */\n  isOnline: boolean\n  /**\n   * Indicate whether the given dependencies are devDependencies.\n   */\n  devDependencies?: boolean\n}\n\n/**\n * Spawn a package manager installation with either Yarn or NPM.\n *\n * @returns A Promise that resolves once the installation is finished.\n */\nexport function install(\n  root: string,\n  dependencies: string[] | null,\n  { useYarn, isOnline, devDependencies }: InstallArgs\n): Promise<void> {\n  /**\n   * NPM-specific command-line flags.\n   */\n  const npmFlags: string[] = []\n  /**\n   * Yarn-specific command-line flags.\n   */\n  const yarnFlags: string[] = []\n  /**\n   * Return a Promise that resolves once the installation is finished.\n   */\n  return new Promise((resolve, reject) => {\n    let args: string[]\n    let command: string = useYarn ? 'yarnpkg' : 'npm'\n\n    if (dependencies && dependencies.length) {\n      /**\n       * If there are dependencies, run a variation of `{displayCommand} add`.\n       */\n      if (useYarn) {\n        /**\n         * Call `yarn add --exact (--offline)? (-D)? ...`.\n         */\n        args = ['add', '--exact']\n        if (!isOnline) args.push('--offline')\n        args.push('--cwd', root)\n        if (devDependencies) args.push('--dev')\n        args.push(...dependencies)\n      } else {\n        /**\n         * Call `npm install [--save|--save-dev] ...`.\n         */\n        args = ['install', '--save-exact']\n        args.push(devDependencies ? '--save-dev' : '--save')\n        args.push(...dependencies)\n      }\n    } else {\n      /**\n       * If there are no dependencies, run a variation of `{displayCommand}\n       * install`.\n       */\n      args = ['install']\n      if (useYarn) {\n        if (!isOnline) {\n          console.log(chalk.yellow('You appear to be offline.'))\n          console.log(chalk.yellow('Falling back to the local Yarn cache.'))\n          console.log()\n          args.push('--offline')\n        }\n      } else {\n        if (!isOnline) {\n          console.log(chalk.yellow('You appear to be offline.'))\n          console.log()\n        }\n      }\n    }\n    /**\n     * Add any package manager-specific flags.\n     */\n    if (useYarn) {\n      args.push(...yarnFlags)\n    } else {\n      args.push(...npmFlags)\n    }\n    /**\n     * Spawn the installation process.\n     */\n    const child = spawn(command, args, {\n      stdio: 'inherit',\n      env: {\n        ...process.env,\n        NODE_ENV: devDependencies ? 'development' : 'production',\n        ADBLOCK: '1',\n        DISABLE_OPENCOLLECTIVE: '1',\n      },\n    })\n    child.on('close', (code) => {\n      if (code !== 0) {\n        reject({ command: `${command} ${args.join(' ')}` })\n        return\n      }\n      resolve()\n    })\n  })\n}\n"], "names": [], "mappings": ";;;;QAwBgB,OAAO,GAAP,OAAO;AAvBL,GAAO,CAAP,MAAO;AACP,GAAgC,CAAhC,WAAgC;;;;;;SAsBlC,OAAO,CACrB,IAAY,EACZ,YAA6B,IAC3B,OAAO,GAAE,QAAQ,GAAE,eAAe,KACrB,CAAC;IAChB,EAEG,AAFH;;GAEG,AAFH,EAEG,CACH,KAAK,CAAC,QAAQ;IACd,EAEG,AAFH;;GAEG,AAFH,EAEG,CACH,KAAK,CAAC,SAAS;IACf,EAEG,AAFH;;GAEG,AAFH,EAEG,QACI,GAAG,CAAC,OAAO,EAAE,OAAO,EAAE,MAAM,GAAK,CAAC;QACvC,GAAG,CAAC,IAAI;QACR,GAAG,CAAC,OAAO,GAAW,OAAO,IAAG,OAAS,KAAG,GAAK;QAEjD,EAAE,EAAE,YAAY,IAAI,YAAY,CAAC,MAAM,EAAE,CAAC;YACxC,EAEG,AAFH;;OAEG,AAFH,EAEG,CACH,EAAE,EAAE,OAAO,EAAE,CAAC;gBACZ,EAEG,AAFH;;SAEG,AAFH,EAEG,CACH,IAAI;qBAAI,GAAK;qBAAE,OAAS;;gBACxB,EAAE,GAAG,QAAQ,EAAE,IAAI,CAAC,IAAI,EAAC,SAAW;gBACpC,IAAI,CAAC,IAAI,EAAC,KAAO,GAAE,IAAI;gBACvB,EAAE,EAAE,eAAe,EAAE,IAAI,CAAC,IAAI,EAAC,KAAO;gBACtC,IAAI,CAAC,IAAI,IAAI,YAAY;YAC3B,CAAC,MAAM,CAAC;gBACN,EAEG,AAFH;;SAEG,AAFH,EAEG,CACH,IAAI;qBAAI,OAAS;qBAAE,YAAc;;gBACjC,IAAI,CAAC,IAAI,CAAC,eAAe,IAAG,UAAY,KAAG,MAAQ;gBACnD,IAAI,CAAC,IAAI,IAAI,YAAY;YAC3B,CAAC;QACH,CAAC,MAAM,CAAC;YACN,EAGG,AAHH;;;OAGG,AAHH,EAGG,CACH,IAAI;iBAAI,OAAS;;YACjB,EAAE,EAAE,OAAO,EAAE,CAAC;gBACZ,EAAE,GAAG,QAAQ,EAAE,CAAC;oBACd,OAAO,CAAC,GAAG,CAxEH,MAAO,SAwEG,MAAM,EAAC,yBAA2B;oBACpD,OAAO,CAAC,GAAG,CAzEH,MAAO,SAyEG,MAAM,EAAC,qCAAuC;oBAChE,OAAO,CAAC,GAAG;oBACX,IAAI,CAAC,IAAI,EAAC,SAAW;gBACvB,CAAC;YACH,CAAC,MAAM,CAAC;gBACN,EAAE,GAAG,QAAQ,EAAE,CAAC;oBACd,OAAO,CAAC,GAAG,CA/EH,MAAO,SA+EG,MAAM,EAAC,yBAA2B;oBACpD,OAAO,CAAC,GAAG;gBACb,CAAC;YACH,CAAC;QACH,CAAC;QACD,EAEG,AAFH;;KAEG,AAFH,EAEG,CACH,EAAE,EAAE,OAAO,EAAE,CAAC;YACZ,IAAI,CAAC,IAAI,IAAI,SAAS;QACxB,CAAC,MAAM,CAAC;YACN,IAAI,CAAC,IAAI,IAAI,QAAQ;QACvB,CAAC;QACD,EAEG,AAFH;;KAEG,AAFH,EAEG,CACH,KAAK,CAAC,KAAK,OA9FG,WAAgC,UA8F1B,OAAO,EAAE,IAAI;YAC/B,KAAK,GAAE,OAAS;YAChB,GAAG;mBACE,OAAO,CAAC,GAAG;gBACd,QAAQ,EAAE,eAAe,IAAG,WAAa,KAAG,UAAY;gBACxD,OAAO,GAAE,CAAG;gBACZ,sBAAsB,GAAE,CAAG;;;QAG/B,KAAK,CAAC,EAAE,EAAC,KAAO,IAAG,IAAI,GAAK,CAAC;YAC3B,EAAE,EAAE,IAAI,KAAK,CAAC,EAAE,CAAC;gBACf,MAAM;oBAAG,OAAO,KAAK,OAAO,CAAC,CAAC,EAAE,IAAI,CAAC,IAAI,EAAC,CAAG;;;YAE/C,CAAC;YACD,OAAO;QACT,CAAC;IACH,CAAC;AACH,CAAC"}