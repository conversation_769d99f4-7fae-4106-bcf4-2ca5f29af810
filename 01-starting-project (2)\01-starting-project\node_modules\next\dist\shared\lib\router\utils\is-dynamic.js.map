{"version": 3, "sources": ["../../../../../shared/lib/router/utils/is-dynamic.ts"], "sourcesContent": ["// Identify /[param]/ in route string\nconst TEST_ROUTE = /\\/\\[[^/]+?\\](?=\\/|$)/\n\nexport function isDynamicRoute(route: string): boolean {\n  return TEST_ROUTE.test(route)\n}\n"], "names": [], "mappings": ";;;;QAGgB,cAAc,GAAd,cAAc;AAH9B,EAAqC,AAArC,mCAAqC;AACrC,KAAK,CAAC,UAAU;SAEA,cAAc,CAAC,KAAa,EAAW,CAAC;WAC/C,UAAU,CAAC,IAAI,CAAC,KAAK;AAC9B,CAAC"}