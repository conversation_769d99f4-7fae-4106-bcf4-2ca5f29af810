{"version": 3, "sources": ["../../../../../../build/webpack/plugins/webpack-conformance-plugin/utils/utils.ts"], "sourcesContent": ["const assert = require('assert').strict\n\nexport function deepEqual(a: any, b: any) {\n  try {\n    assert.deepStrictEqual(a, b)\n    return true\n  } catch (_) {\n    return false\n  }\n}\n"], "names": [], "mappings": ";;;;QAEgB,SAAS,GAAT,SAAS;AAFzB,KAAK,CAAC,MAAM,GAAG,OAAO,EAAC,MAAQ,GAAE,MAAM;SAEvB,SAAS,CAAC,CAAM,EAAE,CAAM,EAAE,CAAC;QACrC,CAAC;QACH,MAAM,CAAC,eAAe,CAAC,CAAC,EAAE,CAAC;eACpB,IAAI;IACb,CAAC,QAAQ,CAAC,EAAE,CAAC;eACJ,KAAK;IACd,CAAC;AACH,CAAC"}