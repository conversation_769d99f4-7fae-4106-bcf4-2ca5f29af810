{"version": 3, "sources": ["../../server/response-cache.ts"], "sourcesContent": ["import Observable from 'next/dist/compiled/zen-observable'\nimport { IncrementalCache } from './incremental-cache'\nimport { RenderResult, resultsToString } from './utils'\n\ninterface CachedRedirectValue {\n  kind: 'REDIRECT'\n  props: Object\n}\n\ninterface CachedPageValue {\n  kind: 'PAGE'\n  html: RenderResult\n  pageData: Object\n}\n\nexport type ResponseCacheValue = CachedRedirectValue | CachedPageValue\n\nexport type ResponseCacheEntry = {\n  revalidate?: number | false\n  value: ResponseCacheValue | null\n}\n\ntype ResponseGenerator = (\n  hasResolved: boolean\n) => Promise<ResponseCacheEntry | null>\n\nexport default class ResponseCache {\n  incrementalCache: IncrementalCache\n  pendingResponses: Map<string, Promise<ResponseCacheEntry | null>>\n\n  constructor(incrementalCache: IncrementalCache) {\n    this.incrementalCache = incrementalCache\n    this.pendingResponses = new Map()\n  }\n\n  public get(\n    key: string | null,\n    responseGenerator: ResponseGenerator\n  ): Promise<ResponseCacheEntry | null> {\n    const pendingResponse = key ? this.pendingResponses.get(key) : null\n    if (pendingResponse) {\n      return pendingResponse\n    }\n\n    let resolver: (cacheEntry: ResponseCacheEntry | null) => void = () => {}\n    let rejecter: (error: Error) => void = () => {}\n    const promise: Promise<ResponseCacheEntry | null> = new Promise(\n      (resolve, reject) => {\n        resolver = resolve\n        rejecter = reject\n      }\n    )\n    if (key) {\n      this.pendingResponses.set(key, promise)\n    }\n\n    let resolved = false\n    const resolve = (cacheEntry: ResponseCacheEntry | null) => {\n      if (key) {\n        // Ensure all reads from the cache get the latest value.\n        this.pendingResponses.set(key, Promise.resolve(cacheEntry))\n      }\n      if (!resolved) {\n        resolved = true\n        resolver(cacheEntry)\n      }\n    }\n\n    // We wait to do any async work until after we've added our promise to\n    // `pendingResponses` to ensure that any any other calls will reuse the\n    // same promise until we've fully finished our work.\n    ;(async () => {\n      try {\n        const cachedResponse = key ? await this.incrementalCache.get(key) : null\n        if (cachedResponse) {\n          resolve({\n            revalidate: cachedResponse.curRevalidate,\n            value:\n              cachedResponse.value?.kind === 'PAGE'\n                ? {\n                    kind: 'PAGE',\n                    html: Observable.of(cachedResponse.value.html),\n                    pageData: cachedResponse.value.pageData,\n                  }\n                : cachedResponse.value,\n          })\n          if (!cachedResponse.isStale) {\n            // The cached value is still valid, so we don't need\n            // to update it yet.\n            return\n          }\n        }\n\n        const cacheEntry = await responseGenerator(resolved)\n        resolve(cacheEntry)\n\n        if (key && cacheEntry && typeof cacheEntry.revalidate !== 'undefined') {\n          await this.incrementalCache.set(\n            key,\n            cacheEntry.value?.kind === 'PAGE'\n              ? {\n                  kind: 'PAGE',\n                  html: await resultsToString([cacheEntry.value.html]),\n                  pageData: cacheEntry.value.pageData,\n                }\n              : cacheEntry.value,\n            cacheEntry.revalidate\n          )\n        }\n      } catch (err) {\n        rejecter(err)\n      } finally {\n        if (key) {\n          this.pendingResponses.delete(key)\n        }\n      }\n    })()\n    return promise\n  }\n}\n"], "names": [], "mappings": ";;;;;AAAuB,GAAmC,CAAnC,cAAmC;AAEZ,GAAS,CAAT,MAAS;;;;;;MAwBlC,aAAa;gBAIpB,gBAAkC,CAAE,CAAC;aAC1C,gBAAgB,GAAG,gBAAgB;aACnC,gBAAgB,GAAG,GAAG,CAAC,GAAG;IACjC,CAAC;IAEM,GAAG,CACR,GAAkB,EAClB,iBAAoC,EACA,CAAC;QACrC,KAAK,CAAC,eAAe,GAAG,GAAG,QAAQ,gBAAgB,CAAC,GAAG,CAAC,GAAG,IAAI,IAAI;QACnE,EAAE,EAAE,eAAe,EAAE,CAAC;mBACb,eAAe;QACxB,CAAC;QAED,GAAG,CAAC,QAAQ,OAA0D,CAAC;QAAA,CAAC;QACxE,GAAG,CAAC,QAAQ,OAAiC,CAAC;QAAA,CAAC;QAC/C,KAAK,CAAC,OAAO,GAAuC,GAAG,CAAC,OAAO,EAC5D,OAAO,EAAE,MAAM,GAAK,CAAC;YACpB,QAAQ,GAAG,OAAO;YAClB,QAAQ,GAAG,MAAM;QACnB,CAAC;QAEH,EAAE,EAAE,GAAG,EAAE,CAAC;iBACH,gBAAgB,CAAC,GAAG,CAAC,GAAG,EAAE,OAAO;QACxC,CAAC;QAED,GAAG,CAAC,QAAQ,GAAG,KAAK;QACpB,KAAK,CAAC,OAAO,IAAI,UAAqC,GAAK,CAAC;YAC1D,EAAE,EAAE,GAAG,EAAE,CAAC;gBACR,EAAwD,AAAxD,sDAAwD;qBACnD,gBAAgB,CAAC,GAAG,CAAC,GAAG,EAAE,OAAO,CAAC,OAAO,CAAC,UAAU;YAC3D,CAAC;YACD,EAAE,GAAG,QAAQ,EAAE,CAAC;gBACd,QAAQ,GAAG,IAAI;gBACf,QAAQ,CAAC,UAAU;YACrB,CAAC;QACH,CAAC;mBAKa,CAAC;gBACT,CAAC;gBACH,KAAK,CAAC,cAAc,GAAG,GAAG,cAAc,gBAAgB,CAAC,GAAG,CAAC,GAAG,IAAI,IAAI;gBACxE,EAAE,EAAE,cAAc,EAAE,CAAC;wBAIf,GAAoB;oBAHxB,OAAO;wBACL,UAAU,EAAE,cAAc,CAAC,aAAa;wBACxC,KAAK,IACH,GAAoB,GAApB,cAAc,CAAC,KAAK,cAApB,GAAoB,UAApB,CAA0B,QAA1B,CAA0B,GAA1B,GAAoB,CAAE,IAAI,OAAK,IAAM;4BAE/B,IAAI,GAAE,IAAM;4BACZ,IAAI,EAjFD,cAAmC,SAiFrB,EAAE,CAAC,cAAc,CAAC,KAAK,CAAC,IAAI;4BAC7C,QAAQ,EAAE,cAAc,CAAC,KAAK,CAAC,QAAQ;4BAEzC,cAAc,CAAC,KAAK;;oBAE5B,EAAE,GAAG,cAAc,CAAC,OAAO,EAAE,CAAC;wBAC5B,EAAoD,AAApD,kDAAoD;wBACpD,EAAoB,AAApB,kBAAoB;;oBAEtB,CAAC;gBACH,CAAC;gBAED,KAAK,CAAC,UAAU,SAAS,iBAAiB,CAAC,QAAQ;gBACnD,OAAO,CAAC,UAAU;gBAElB,EAAE,EAAE,GAAG,IAAI,UAAU,WAAW,UAAU,CAAC,UAAU,MAAK,SAAW,GAAE,CAAC;wBAGpE,GAAgB;+BAFP,gBAAgB,CAAC,GAAG,CAC7B,GAAG,IACH,GAAgB,GAAhB,UAAU,CAAC,KAAK,cAAhB,GAAgB,UAAhB,CAAsB,QAAtB,CAAsB,GAAtB,GAAgB,CAAE,IAAI,OAAK,IAAM;wBAE3B,IAAI,GAAE,IAAM;wBACZ,IAAI,YApGwB,MAAS;4BAoGR,UAAU,CAAC,KAAK,CAAC,IAAI;;wBAClD,QAAQ,EAAE,UAAU,CAAC,KAAK,CAAC,QAAQ;wBAErC,UAAU,CAAC,KAAK,EACpB,UAAU,CAAC,UAAU;gBAEzB,CAAC;YACH,CAAC,QAAQ,GAAG,EAAE,CAAC;gBACb,QAAQ,CAAC,GAAG;YACd,CAAC,QAAS,CAAC;gBACT,EAAE,EAAE,GAAG,EAAE,CAAC;yBACH,gBAAgB,CAAC,MAAM,CAAC,GAAG;gBAClC,CAAC;YACH,CAAC;QACH,CAAC;eACM,OAAO;IAChB,CAAC;;kBA5FkB,aAAa"}