{"version": 3, "sources": ["../../../../build/babel/plugins/next-page-disallow-re-export-all-exports.ts"], "sourcesContent": ["import { NodePath, PluginObj, types } from 'next/dist/compiled/babel/core'\n\nexport default function NextPageDisallowReExportAllExports(): PluginObj<any> {\n  return {\n    visitor: {\n      ExportAllDeclaration(path: NodePath<types.ExportAllDeclaration>) {\n        const err = new SyntaxError(\n          `Using \\`export * from '...'\\` in a page is disallowed. Please use \\`export { default } from '...'\\` instead.\\n` +\n            `Read more: https://nextjs.org/docs/messages/export-all-in-page`\n        )\n        ;(err as any).code = 'BABEL_PARSE_ERROR'\n        ;(err as any).loc =\n          path.node.loc?.start ?? path.node.loc?.end ?? path.node.loc\n        throw err\n      },\n    },\n  }\n}\n"], "names": [], "mappings": ";;;;kBAEwB,kCAAkC;SAAlC,kCAAkC,GAAmB,CAAC;;QAE1E,OAAO;YACL,oBAAoB,EAAC,IAA0C,EAAE,CAAC;oBAO9D,GAAa,EAAW,IAAa;gBANvC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,WAAW,EACxB,8GAA8G,KAC5G,8DAA8D;gBAEjE,GAAG,CAAS,IAAI,IAAG,iBAAmB;oBAEtC,IAAoB,EAApB,IAA0C;gBAD1C,GAAG,CAAS,GAAG,IACf,IAA0C,IAA1C,IAAoB,IAApB,GAAa,GAAb,IAAI,CAAC,IAAI,CAAC,GAAG,cAAb,GAAa,UAAb,CAAoB,QAApB,CAAoB,GAApB,GAAa,CAAE,KAAK,cAApB,IAAoB,cAApB,IAAoB,IAAI,IAAa,GAAb,IAAI,CAAC,IAAI,CAAC,GAAG,cAAb,IAAa,UAAb,CAAkB,QAAlB,CAAkB,GAAlB,IAAa,CAAE,GAAG,cAA1C,IAA0C,cAA1C,IAA0C,GAAI,IAAI,CAAC,IAAI,CAAC,GAAG;gBAC7D,KAAK,CAAC,GAAG;YACX,CAAC;;;AAGP,CAAC"}