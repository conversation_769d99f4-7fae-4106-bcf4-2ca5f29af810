{"version": 3, "sources": ["../../lib/verifyTypeScriptSetup.ts"], "sourcesContent": ["import chalk from 'chalk'\nimport path from 'path'\nimport {\n  hasNecessaryDependencies,\n  NecessaryDependencies,\n} from './has-necessary-dependencies'\nimport semver from 'next/dist/compiled/semver'\nimport { CompileError } from './compile-error'\nimport { FatalError } from './fatal-error'\nimport * as log from '../build/output/log'\n\nimport { getTypeScriptIntent } from './typescript/getTypeScriptIntent'\nimport { TypeCheckResult } from './typescript/runTypeCheck'\nimport { writeAppTypeDeclarations } from './typescript/writeAppTypeDeclarations'\nimport { writeConfigurationDefaults } from './typescript/writeConfigurationDefaults'\nimport { missingDepsError } from './typescript/missingDependencyError'\n\nconst requiredPackages = [\n  { file: 'typescript', pkg: 'typescript' },\n  { file: '@types/react/index.d.ts', pkg: '@types/react' },\n  { file: '@types/node/index.d.ts', pkg: '@types/node' },\n]\n\nexport async function verifyTypeScriptSetup(\n  dir: string,\n  pagesDir: string,\n  typeCheckPreflight: boolean,\n  imageImportsEnabled: boolean,\n  cacheDir?: string\n): Promise<{ result?: TypeCheckResult; version: string | null }> {\n  const tsConfigPath = path.join(dir, 'tsconfig.json')\n\n  try {\n    // Check if the project uses TypeScript:\n    const intent = await getTypeScriptIntent(dir, pagesDir)\n    if (!intent) {\n      return { version: null }\n    }\n\n    // Ensure TypeScript and necessary `@types/*` are installed:\n    const deps: NecessaryDependencies = await hasNecessaryDependencies(\n      dir,\n      requiredPackages\n    )\n\n    if (deps.missing?.length > 0) {\n      missingDepsError(dir, deps.missing)\n    }\n\n    // Load TypeScript after we're sure it exists:\n    const ts = (await import(\n      deps.resolved.get('typescript')!\n    )) as typeof import('typescript')\n\n    if (semver.lt(ts.version, '4.3.2')) {\n      log.warn(\n        `Minimum recommended TypeScript version is v4.3.2, older versions can potentially be incompatible with Next.js. Detected: ${ts.version}`\n      )\n    }\n\n    // Reconfigure (or create) the user's `tsconfig.json` for them:\n    await writeConfigurationDefaults(ts, tsConfigPath, intent.firstTimeSetup)\n    // Write out the necessary `next-env.d.ts` file to correctly register\n    // Next.js' types:\n    await writeAppTypeDeclarations(dir, imageImportsEnabled)\n\n    let result\n    if (typeCheckPreflight) {\n      const { runTypeCheck } = require('./typescript/runTypeCheck')\n\n      // Verify the project passes type-checking before we go to webpack phase:\n      result = await runTypeCheck(ts, dir, tsConfigPath, cacheDir)\n    }\n    return { result, version: ts.version }\n  } catch (err) {\n    // These are special errors that should not show a stack trace:\n    if (err instanceof CompileError) {\n      console.error(chalk.red('Failed to compile.\\n'))\n      console.error(err.message)\n      process.exit(1)\n    } else if (err instanceof FatalError) {\n      console.error(err.message)\n      process.exit(1)\n    }\n    throw err\n  }\n}\n"], "names": [], "mappings": ";;;;QAuBsB,qBAAqB,GAArB,qBAAqB;AAvBzB,GAAO,CAAP,MAAO;AACR,GAAM,CAAN,KAAM;AAIhB,GAA8B,CAA9B,yBAA8B;AAClB,GAA2B,CAA3B,OAA2B;AACjB,GAAiB,CAAjB,aAAiB;AACnB,GAAe,CAAf,WAAe;AAC9B,GAAG,CAAH,GAAG;AAEqB,GAAkC,CAAlC,oBAAkC;AAE7B,GAAuC,CAAvC,yBAAuC;AACrC,GAAyC,CAAzC,2BAAyC;AACnD,GAAqC,CAArC,uBAAqC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEtE,KAAK,CAAC,gBAAgB;;QAClB,IAAI,GAAE,UAAY;QAAE,GAAG,GAAE,UAAY;;;QACrC,IAAI,GAAE,uBAAyB;QAAE,GAAG,GAAE,YAAc;;;QACpD,IAAI,GAAE,sBAAwB;QAAE,GAAG,GAAE,WAAa;;;eAGhC,qBAAqB,CACzC,GAAW,EACX,QAAgB,EAChB,kBAA2B,EAC3B,mBAA4B,EAC5B,QAAiB,EAC8C,CAAC;IAChE,KAAK,CAAC,YAAY,GA7BH,KAAM,SA6BK,IAAI,CAAC,GAAG,GAAE,aAAe;QAE/C,CAAC;YAaC,GAAY;QAZhB,EAAwC,AAAxC,sCAAwC;QACxC,KAAK,CAAC,MAAM,aAvBoB,oBAAkC,sBAuBzB,GAAG,EAAE,QAAQ;QACtD,EAAE,GAAG,MAAM,EAAE,CAAC;;gBACH,OAAO,EAAE,IAAI;;QACxB,CAAC;QAED,EAA4D,AAA5D,0DAA4D;QAC5D,KAAK,CAAC,IAAI,aAnCP,yBAA8B,2BAoC/B,GAAG,EACH,gBAAgB;QAGlB,EAAE,IAAE,GAAY,GAAZ,IAAI,CAAC,OAAO,cAAZ,GAAY,UAAZ,CAAoB,QAApB,CAAoB,GAApB,GAAY,CAAE,MAAM,IAAG,CAAC,EAAE,CAAC;gBA9BF,uBAAqC,mBA+B/C,GAAG,EAAE,IAAI,CAAC,OAAO;QACpC,CAAC;QAED,EAA8C,AAA9C,4CAA8C;QAC9C,KAAK,CAAC,EAAE;mDACN,IAAI,CAAC,QAAQ,CAAC,GAAG,EAAC,UAAY;;QAGhC,EAAE,EAhDa,OAA2B,SAgD/B,EAAE,CAAC,EAAE,CAAC,OAAO,GAAE,KAAO,IAAG,CAAC;YA7C7B,GAAG,CA8CL,IAAI,EACL,yHAAyH,EAAE,EAAE,CAAC,OAAO;QAE1I,CAAC;QAED,EAA+D,AAA/D,6DAA+D;kBA9CxB,2BAAyC,6BA+C/C,EAAE,EAAE,YAAY,EAAE,MAAM,CAAC,cAAc;QACxE,EAAqE,AAArE,mEAAqE;QACrE,EAAkB,AAAlB,gBAAkB;kBAlDmB,yBAAuC,2BAmD7C,GAAG,EAAE,mBAAmB;QAEvD,GAAG,CAAC,MAAM;QACV,EAAE,EAAE,kBAAkB,EAAE,CAAC;YACvB,KAAK,GAAG,YAAY,MAAK,OAAO,EAAC,yBAA2B;YAE5D,EAAyE,AAAzE,uEAAyE;YACzE,MAAM,SAAS,YAAY,CAAC,EAAE,EAAE,GAAG,EAAE,YAAY,EAAE,QAAQ;QAC7D,CAAC;;YACQ,MAAM;YAAE,OAAO,EAAE,EAAE,CAAC,OAAO;;IACtC,CAAC,QAAQ,GAAG,EAAE,CAAC;QACb,EAA+D,AAA/D,6DAA+D;QAC/D,EAAE,EAAE,GAAG,YArEkB,aAAiB,eAqET,CAAC;YAChC,OAAO,CAAC,KAAK,CA7ED,MAAO,SA6EC,GAAG,EAAC,oBAAsB;YAC9C,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,OAAO;YACzB,OAAO,CAAC,IAAI,CAAC,CAAC;QAChB,CAAC,MAAM,EAAE,EAAE,GAAG,YAxES,WAAe,aAwEA,CAAC;YACrC,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,OAAO;YACzB,OAAO,CAAC,IAAI,CAAC,CAAC;QAChB,CAAC;QACD,KAAK,CAAC,GAAG;IACX,CAAC;AACH,CAAC"}