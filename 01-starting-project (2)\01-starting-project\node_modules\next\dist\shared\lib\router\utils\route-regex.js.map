{"version": 3, "sources": ["../../../../../shared/lib/router/utils/route-regex.ts"], "sourcesContent": ["interface Group {\n  pos: number\n  repeat: boolean\n  optional: boolean\n}\n\n// this isn't importing the escape-string-regex module\n// to reduce bytes\nfunction escapeRegex(str: string) {\n  return str.replace(/[|\\\\{}()[\\]^$+*?.-]/g, '\\\\$&')\n}\n\nfunction parseParameter(param: string) {\n  const optional = param.startsWith('[') && param.endsWith(']')\n  if (optional) {\n    param = param.slice(1, -1)\n  }\n  const repeat = param.startsWith('...')\n  if (repeat) {\n    param = param.slice(3)\n  }\n  return { key: param, repeat, optional }\n}\n\nexport function getParametrizedRoute(route: string) {\n  const segments = (route.replace(/\\/$/, '') || '/').slice(1).split('/')\n\n  const groups: { [groupName: string]: Group } = {}\n  let groupIndex = 1\n  const parameterizedRoute = segments\n    .map((segment) => {\n      if (segment.startsWith('[') && segment.endsWith(']')) {\n        const { key, optional, repeat } = parseParameter(segment.slice(1, -1))\n        groups[key] = { pos: groupIndex++, repeat, optional }\n        return repeat ? (optional ? '(?:/(.+?))?' : '/(.+?)') : '/([^/]+?)'\n      } else {\n        return `/${escapeRegex(segment)}`\n      }\n    })\n    .join('')\n\n  // dead code eliminate for browser since it's only needed\n  // while generating routes-manifest\n  if (typeof window === 'undefined') {\n    let routeKeyCharCode = 97\n    let routeKeyCharLength = 1\n\n    // builds a minimal routeKey using only a-z and minimal number of characters\n    const getSafeRouteKey = () => {\n      let routeKey = ''\n\n      for (let i = 0; i < routeKeyCharLength; i++) {\n        routeKey += String.fromCharCode(routeKeyCharCode)\n        routeKeyCharCode++\n\n        if (routeKeyCharCode > 122) {\n          routeKeyCharLength++\n          routeKeyCharCode = 97\n        }\n      }\n      return routeKey\n    }\n\n    const routeKeys: { [named: string]: string } = {}\n\n    let namedParameterizedRoute = segments\n      .map((segment) => {\n        if (segment.startsWith('[') && segment.endsWith(']')) {\n          const { key, optional, repeat } = parseParameter(segment.slice(1, -1))\n          // replace any non-word characters since they can break\n          // the named regex\n          let cleanedKey = key.replace(/\\W/g, '')\n          let invalidKey = false\n\n          // check if the key is still invalid and fallback to using a known\n          // safe key\n          if (cleanedKey.length === 0 || cleanedKey.length > 30) {\n            invalidKey = true\n          }\n          if (!isNaN(parseInt(cleanedKey.substr(0, 1)))) {\n            invalidKey = true\n          }\n\n          if (invalidKey) {\n            cleanedKey = getSafeRouteKey()\n          }\n\n          routeKeys[cleanedKey] = key\n          return repeat\n            ? optional\n              ? `(?:/(?<${cleanedKey}>.+?))?`\n              : `/(?<${cleanedKey}>.+?)`\n            : `/(?<${cleanedKey}>[^/]+?)`\n        } else {\n          return `/${escapeRegex(segment)}`\n        }\n      })\n      .join('')\n\n    return {\n      parameterizedRoute,\n      namedParameterizedRoute,\n      groups,\n      routeKeys,\n    }\n  }\n\n  return {\n    parameterizedRoute,\n    groups,\n  }\n}\n\nexport interface RouteRegex {\n  groups: { [groupName: string]: Group }\n  namedRegex?: string\n  re: RegExp\n  routeKeys?: { [named: string]: string }\n}\n\nexport function getRouteRegex(normalizedRoute: string): RouteRegex {\n  const result = getParametrizedRoute(normalizedRoute)\n  if ('routeKeys' in result) {\n    return {\n      re: new RegExp(`^${result.parameterizedRoute}(?:/)?$`),\n      groups: result.groups,\n      routeKeys: result.routeKeys,\n      namedRegex: `^${result.namedParameterizedRoute}(?:/)?$`,\n    }\n  }\n\n  return {\n    re: new RegExp(`^${result.parameterizedRoute}(?:/)?$`),\n    groups: result.groups,\n  }\n}\n"], "names": [], "mappings": ";;;;QAwBgB,oBAAoB,GAApB,oBAAoB;QAgGpB,aAAa,GAAb,aAAa;AAlH7B,EAAsD,AAAtD,oDAAsD;AACtD,EAAkB,AAAlB,gBAAkB;SACT,WAAW,CAAC,GAAW,EAAE,CAAC;WAC1B,GAAG,CAAC,OAAO,0BAAyB,IAAM;AACnD,CAAC;SAEQ,cAAc,CAAC,KAAa,EAAE,CAAC;IACtC,KAAK,CAAC,QAAQ,GAAG,KAAK,CAAC,UAAU,EAAC,CAAG,MAAK,KAAK,CAAC,QAAQ,EAAC,CAAG;IAC5D,EAAE,EAAE,QAAQ,EAAE,CAAC;QACb,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC;IAC3B,CAAC;IACD,KAAK,CAAC,MAAM,GAAG,KAAK,CAAC,UAAU,EAAC,GAAK;IACrC,EAAE,EAAE,MAAM,EAAE,CAAC;QACX,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC,CAAC;IACvB,CAAC;;QACQ,GAAG,EAAE,KAAK;QAAE,MAAM;QAAE,QAAQ;;AACvC,CAAC;SAEe,oBAAoB,CAAC,KAAa,EAAE,CAAC;IACnD,KAAK,CAAC,QAAQ,IAAI,KAAK,CAAC,OAAO,gBAAe,CAAG,GAAE,KAAK,CAAC,CAAC,EAAE,KAAK,EAAC,CAAG;IAErE,KAAK,CAAC,MAAM;;IACZ,GAAG,CAAC,UAAU,GAAG,CAAC;IAClB,KAAK,CAAC,kBAAkB,GAAG,QAAQ,CAChC,GAAG,EAAE,OAAO,GAAK,CAAC;QACjB,EAAE,EAAE,OAAO,CAAC,UAAU,EAAC,CAAG,MAAK,OAAO,CAAC,QAAQ,EAAC,CAAG,IAAG,CAAC;YACrD,KAAK,GAAG,GAAG,GAAE,QAAQ,GAAE,MAAM,MAAK,cAAc,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC;YACpE,MAAM,CAAC,GAAG;gBAAM,GAAG,EAAE,UAAU;gBAAI,MAAM;gBAAE,QAAQ;;mBAC5C,MAAM,GAAI,QAAQ,IAAG,WAAa,KAAG,MAAQ,KAAI,SAAW;QACrE,CAAC,MAAM,CAAC;oBACE,CAAC,EAAE,WAAW,CAAC,OAAO;QAChC,CAAC;IACH,CAAC,EACA,IAAI;IAEP,EAAyD,AAAzD,uDAAyD;IACzD,EAAmC,AAAnC,iCAAmC;IACnC,EAAE,SAAS,MAAM,MAAK,SAAW,GAAE,CAAC;QAClC,GAAG,CAAC,gBAAgB,GAAG,EAAE;QACzB,GAAG,CAAC,kBAAkB,GAAG,CAAC;QAE1B,EAA4E,AAA5E,0EAA4E;QAC5E,KAAK,CAAC,eAAe,OAAS,CAAC;YAC7B,GAAG,CAAC,QAAQ;gBAEP,GAAG,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,kBAAkB,EAAE,CAAC,GAAI,CAAC;gBAC5C,QAAQ,IAAI,MAAM,CAAC,YAAY,CAAC,gBAAgB;gBAChD,gBAAgB;gBAEhB,EAAE,EAAE,gBAAgB,GAAG,GAAG,EAAE,CAAC;oBAC3B,kBAAkB;oBAClB,gBAAgB,GAAG,EAAE;gBACvB,CAAC;YACH,CAAC;mBACM,QAAQ;QACjB,CAAC;QAED,KAAK,CAAC,SAAS;;QAEf,GAAG,CAAC,uBAAuB,GAAG,QAAQ,CACnC,GAAG,EAAE,OAAO,GAAK,CAAC;YACjB,EAAE,EAAE,OAAO,CAAC,UAAU,EAAC,CAAG,MAAK,OAAO,CAAC,QAAQ,EAAC,CAAG,IAAG,CAAC;gBACrD,KAAK,GAAG,GAAG,GAAE,QAAQ,GAAE,MAAM,MAAK,cAAc,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC;gBACpE,EAAuD,AAAvD,qDAAuD;gBACvD,EAAkB,AAAlB,gBAAkB;gBAClB,GAAG,CAAC,UAAU,GAAG,GAAG,CAAC,OAAO;gBAC5B,GAAG,CAAC,UAAU,GAAG,KAAK;gBAEtB,EAAkE,AAAlE,gEAAkE;gBAClE,EAAW,AAAX,SAAW;gBACX,EAAE,EAAE,UAAU,CAAC,MAAM,KAAK,CAAC,IAAI,UAAU,CAAC,MAAM,GAAG,EAAE,EAAE,CAAC;oBACtD,UAAU,GAAG,IAAI;gBACnB,CAAC;gBACD,EAAE,GAAG,KAAK,CAAC,QAAQ,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC;oBAC9C,UAAU,GAAG,IAAI;gBACnB,CAAC;gBAED,EAAE,EAAE,UAAU,EAAE,CAAC;oBACf,UAAU,GAAG,eAAe;gBAC9B,CAAC;gBAED,SAAS,CAAC,UAAU,IAAI,GAAG;uBACpB,MAAM,GACT,QAAQ,IACL,OAAO,EAAE,UAAU,CAAC,OAAO,KAC3B,IAAI,EAAE,UAAU,CAAC,KAAK,KACxB,IAAI,EAAE,UAAU,CAAC,QAAQ;YAChC,CAAC,MAAM,CAAC;wBACE,CAAC,EAAE,WAAW,CAAC,OAAO;YAChC,CAAC;QACH,CAAC,EACA,IAAI;;YAGL,kBAAkB;YAClB,uBAAuB;YACvB,MAAM;YACN,SAAS;;IAEb,CAAC;;QAGC,kBAAkB;QAClB,MAAM;;AAEV,CAAC;SASe,aAAa,CAAC,eAAuB,EAAc,CAAC;IAClE,KAAK,CAAC,MAAM,GAAG,oBAAoB,CAAC,eAAe;IACnD,EAAE,GAAE,SAAW,KAAI,MAAM,EAAE,CAAC;;YAExB,EAAE,EAAE,GAAG,CAAC,MAAM,EAAE,CAAC,EAAE,MAAM,CAAC,kBAAkB,CAAC,OAAO;YACpD,MAAM,EAAE,MAAM,CAAC,MAAM;YACrB,SAAS,EAAE,MAAM,CAAC,SAAS;YAC3B,UAAU,GAAG,CAAC,EAAE,MAAM,CAAC,uBAAuB,CAAC,OAAO;;IAE1D,CAAC;;QAGC,EAAE,EAAE,GAAG,CAAC,MAAM,EAAE,CAAC,EAAE,MAAM,CAAC,kBAAkB,CAAC,OAAO;QACpD,MAAM,EAAE,MAAM,CAAC,MAAM;;AAEzB,CAAC"}