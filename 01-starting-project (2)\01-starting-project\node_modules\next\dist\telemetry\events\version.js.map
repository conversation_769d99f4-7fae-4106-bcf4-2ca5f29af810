{"version": 3, "sources": ["../../../telemetry/events/version.ts"], "sourcesContent": ["import findUp from 'next/dist/compiled/find-up'\nimport path from 'path'\nimport {\n  CONFIG_FILE,\n  PHASE_DEVELOPMENT_SERVER,\n  PHASE_EXPORT,\n  PHASE_PRODUCTION_BUILD,\n} from '../../shared/lib/constants'\nimport { normalizeConfig } from '../../server/config'\n\nconst EVENT_VERSION = 'NEXT_CLI_SESSION_STARTED'\n\ntype EventCliSessionStarted = {\n  nextVersion: string\n  nodeVersion: string\n  cliCommand: string\n  isSrcDir: boolean | null\n  hasNowJson: boolean\n  isCustomServer: boolean | null\n  hasNextConfig: boolean\n  buildTarget: string\n  hasWebpackConfig: boolean\n  hasBabelConfig: boolean\n  basePathEnabled: boolean\n  i18nEnabled: boolean\n  imageEnabled: boolean\n  locales: string | null\n  localeDomainsCount: number | null\n  localeDetectionEnabled: boolean | null\n  imageDomainsCount: number | null\n  imageSizes: string | null\n  imageLoader: string | null\n  trailingSlashEnabled: boolean\n  reactStrictMode: boolean\n  webpackVersion: number | null\n}\n\nfunction hasBabelConfig(dir: string): boolean {\n  try {\n    const noopFile = path.join(dir, 'noop.js')\n    const res = require('next/dist/compiled/babel/core').loadPartialConfig({\n      cwd: dir,\n      filename: noopFile,\n      sourceFileName: noopFile,\n    }) as any\n    const isForTooling =\n      res.options?.presets?.every(\n        (e: any) => e?.file?.request === 'next/babel'\n      ) && res.options?.plugins?.length === 0\n    return res.hasFilesystemConfig() && !isForTooling\n  } catch {\n    return false\n  }\n}\n\ntype NextConfigurationPhase =\n  | typeof PHASE_DEVELOPMENT_SERVER\n  | typeof PHASE_PRODUCTION_BUILD\n  | typeof PHASE_EXPORT\n\nfunction getNextConfig(\n  phase: NextConfigurationPhase,\n  dir: string\n): { [key: string]: any } | null {\n  try {\n    const configurationPath = findUp.sync(CONFIG_FILE, {\n      cwd: dir,\n    })\n\n    if (configurationPath) {\n      // This should've already been loaded, and thus should be cached / won't\n      // be re-evaluated.\n      const configurationModule = require(configurationPath)\n\n      // Re-normalize the configuration.\n      return normalizeConfig(\n        phase,\n        configurationModule.default || configurationModule\n      )\n    }\n  } catch {\n    // ignored\n  }\n  return null\n}\n\nexport function eventCliSession(\n  phase: NextConfigurationPhase,\n  dir: string,\n  event: Omit<\n    EventCliSessionStarted,\n    | 'nextVersion'\n    | 'nodeVersion'\n    | 'hasNextConfig'\n    | 'buildTarget'\n    | 'hasWebpackConfig'\n    | 'hasBabelConfig'\n    | 'basePathEnabled'\n    | 'i18nEnabled'\n    | 'imageEnabled'\n    | 'locales'\n    | 'localeDomainsCount'\n    | 'localeDetectionEnabled'\n    | 'imageDomainsCount'\n    | 'imageSizes'\n    | 'imageLoader'\n    | 'trailingSlashEnabled'\n    | 'reactStrictMode'\n  >\n): { eventName: string; payload: EventCliSessionStarted }[] {\n  // This should be an invariant, if it fails our build tooling is broken.\n  if (typeof process.env.__NEXT_VERSION !== 'string') {\n    return []\n  }\n\n  const userConfiguration = getNextConfig(phase, dir)\n\n  const { images, i18n } = userConfiguration || {}\n\n  const payload: EventCliSessionStarted = {\n    nextVersion: process.env.__NEXT_VERSION,\n    nodeVersion: process.version,\n    cliCommand: event.cliCommand,\n    isSrcDir: event.isSrcDir,\n    hasNowJson: event.hasNowJson,\n    isCustomServer: event.isCustomServer,\n    hasNextConfig: !!userConfiguration,\n    buildTarget: userConfiguration?.target ?? 'default',\n    hasWebpackConfig: typeof userConfiguration?.webpack === 'function',\n    hasBabelConfig: hasBabelConfig(dir),\n    imageEnabled: !!images,\n    basePathEnabled: !!userConfiguration?.basePath,\n    i18nEnabled: !!i18n,\n    locales: i18n?.locales ? i18n.locales.join(',') : null,\n    localeDomainsCount: i18n?.domains ? i18n.domains.length : null,\n    localeDetectionEnabled: !i18n ? null : i18n.localeDetection !== false,\n    imageDomainsCount: images?.domains ? images.domains.length : null,\n    imageSizes: images?.sizes ? images.sizes.join(',') : null,\n    imageLoader: images?.loader,\n    trailingSlashEnabled: !!userConfiguration?.trailingSlash,\n    reactStrictMode: !!userConfiguration?.reactStrictMode,\n    webpackVersion: event.webpackVersion || null,\n  }\n  return [{ eventName: EVENT_VERSION, payload }]\n}\n"], "names": [], "mappings": ";;;;QAsFgB,eAAe,GAAf,eAAe;AAtFZ,GAA4B,CAA5B,OAA4B;AAC9B,GAAM,CAAN,KAAM;AAMhB,GAA4B,CAA5B,UAA4B;AACH,GAAqB,CAArB,OAAqB;;;;;;AAErD,KAAK,CAAC,aAAa,IAAG,wBAA0B;SA2BvC,cAAc,CAAC,GAAW,EAAW,CAAC;QACzC,CAAC;YAQD,GAAW,QAEN,IAAW;QATlB,KAAK,CAAC,QAAQ,GAtCD,KAAM,SAsCG,IAAI,CAAC,GAAG,GAAE,OAAS;QACzC,KAAK,CAAC,GAAG,GAAG,OAAO,EAAC,6BAA+B,GAAE,iBAAiB;YACpE,GAAG,EAAE,GAAG;YACR,QAAQ,EAAE,QAAQ;YAClB,cAAc,EAAE,QAAQ;;QAE1B,KAAK,CAAC,YAAY,KAChB,GAAW,GAAX,GAAG,CAAC,OAAO,cAAX,GAAW,UAAX,CAAoB,QAApB,CAAoB,WAApB,GAAW,CAAE,OAAO,4BAApB,CAAoB,QAApB,CAAoB,QAAE,KAAK,EACxB,CAAM;gBAAK,IAAO;oBAAP,CAAC,aAAD,CAAC,UAAD,CAAO,QAAP,CAAO,IAAP,IAAO,GAAP,CAAC,CAAE,IAAI,cAAP,IAAO,UAAP,CAAO,QAAP,CAAO,GAAP,IAAO,CAAE,OAAO,OAAK,UAAY;iBAC1C,IAAW,GAAX,GAAG,CAAC,OAAO,cAAX,IAAW,UAAX,CAAoB,QAApB,CAAoB,WAApB,IAAW,CAAE,OAAO,4BAApB,CAAoB,QAApB,CAAoB,QAAE,MAAM,MAAK,CAAC;eAClC,GAAG,CAAC,mBAAmB,OAAO,YAAY;IACnD,CAAC,QAAO,CAAC;eACA,KAAK;IACd,CAAC;AACH,CAAC;SAOQ,aAAa,CACpB,KAA6B,EAC7B,GAAW,EACoB,CAAC;QAC5B,CAAC;QACH,KAAK,CAAC,iBAAiB,GAjER,OAA4B,SAiEV,IAAI,CA1DlC,UAA4B;YA2D7B,GAAG,EAAE,GAAG;;QAGV,EAAE,EAAE,iBAAiB,EAAE,CAAC;YACtB,EAAwE,AAAxE,sEAAwE;YACxE,EAAmB,AAAnB,iBAAmB;YACnB,KAAK,CAAC,mBAAmB,GAAG,OAAO,CAAC,iBAAiB;YAErD,EAAkC,AAAlC,gCAAkC;uBAlER,OAAqB,kBAoE7C,KAAK,EACL,mBAAmB,CAAC,OAAO,IAAI,mBAAmB;QAEtD,CAAC;IACH,CAAC,QAAO,CAAC;IACP,EAAU,AAAV,QAAU;IACZ,CAAC;WACM,IAAI;AACb,CAAC;SAEe,eAAe,CAC7B,KAA6B,EAC7B,GAAW,EACX,KAmBC,EACyD,CAAC;IAC3D,EAAwE,AAAxE,sEAAwE;IACxE,EAAE,SAAS,OAAO,CAAC,GAAG,CAAC,cAAc,MAAK,MAAQ,GAAE,CAAC;;IAErD,CAAC;IAED,KAAK,CAAC,iBAAiB,GAAG,aAAa,CAAC,KAAK,EAAE,GAAG;IAElD,KAAK,GAAG,MAAM,GAAE,IAAI,MAAK,iBAAiB;;QAU3B,IAAyB;IARxC,KAAK,CAAC,OAAO;QACX,WAAW,EAAE,OAAO,CAAC,GAAG,CAAC,cAAc;QACvC,WAAW,EAAE,OAAO,CAAC,OAAO;QAC5B,UAAU,EAAE,KAAK,CAAC,UAAU;QAC5B,QAAQ,EAAE,KAAK,CAAC,QAAQ;QACxB,UAAU,EAAE,KAAK,CAAC,UAAU;QAC5B,cAAc,EAAE,KAAK,CAAC,cAAc;QACpC,aAAa,IAAI,iBAAiB;QAClC,WAAW,GAAE,IAAyB,GAAzB,iBAAiB,aAAjB,iBAAiB,UAAjB,CAAyB,QAAzB,CAAyB,GAAzB,iBAAiB,CAAE,MAAM,cAAzB,IAAyB,cAAzB,IAAyB,IAAI,OAAS;QACnD,gBAAgB,UAAS,iBAAiB,aAAjB,iBAAiB,UAAjB,CAA0B,QAA1B,CAA0B,GAA1B,iBAAiB,CAAE,OAAO,OAAK,QAAU;QAClE,cAAc,EAAE,cAAc,CAAC,GAAG;QAClC,YAAY,IAAI,MAAM;QACtB,eAAe,KAAI,iBAAiB,aAAjB,iBAAiB,UAAjB,CAA2B,QAA3B,CAA2B,GAA3B,iBAAiB,CAAE,QAAQ;QAC9C,WAAW,IAAI,IAAI;QACnB,OAAO,GAAE,IAAI,aAAJ,IAAI,UAAJ,CAAa,QAAb,CAAa,GAAb,IAAI,CAAE,OAAO,IAAG,IAAI,CAAC,OAAO,CAAC,IAAI,EAAC,CAAG,KAAI,IAAI;QACtD,kBAAkB,GAAE,IAAI,aAAJ,IAAI,UAAJ,CAAa,QAAb,CAAa,GAAb,IAAI,CAAE,OAAO,IAAG,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,IAAI;QAC9D,sBAAsB,GAAG,IAAI,GAAG,IAAI,GAAG,IAAI,CAAC,eAAe,KAAK,KAAK;QACrE,iBAAiB,GAAE,MAAM,aAAN,MAAM,UAAN,CAAe,QAAf,CAAe,GAAf,MAAM,CAAE,OAAO,IAAG,MAAM,CAAC,OAAO,CAAC,MAAM,GAAG,IAAI;QACjE,UAAU,GAAE,MAAM,aAAN,MAAM,UAAN,CAAa,QAAb,CAAa,GAAb,MAAM,CAAE,KAAK,IAAG,MAAM,CAAC,KAAK,CAAC,IAAI,EAAC,CAAG,KAAI,IAAI;QACzD,WAAW,EAAE,MAAM,aAAN,MAAM,UAAN,CAAc,QAAd,CAAc,GAAd,MAAM,CAAE,MAAM;QAC3B,oBAAoB,KAAI,iBAAiB,aAAjB,iBAAiB,UAAjB,CAAgC,QAAhC,CAAgC,GAAhC,iBAAiB,CAAE,aAAa;QACxD,eAAe,KAAI,iBAAiB,aAAjB,iBAAiB,UAAjB,CAAkC,QAAlC,CAAkC,GAAlC,iBAAiB,CAAE,eAAe;QACrD,cAAc,EAAE,KAAK,CAAC,cAAc,IAAI,IAAI;;;;YAEpC,SAAS,EAAE,aAAa;YAAE,OAAO;;;AAC7C,CAAC"}