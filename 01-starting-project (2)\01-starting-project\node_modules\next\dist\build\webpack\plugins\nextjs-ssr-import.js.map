{"version": 3, "sources": ["../../../../build/webpack/plugins/nextjs-ssr-import.ts"], "sourcesContent": ["import { join, resolve, relative, dirname } from 'path'\nimport { webpack } from 'next/dist/compiled/webpack/webpack'\n\n// This plugin modifies the require-ensure code generated by Webpack\n// to work with Next.js SSR\nexport default class NextJsSsrImportPlugin {\n  apply(compiler: webpack.Compiler) {\n    compiler.hooks.compilation.tap('NextJsSSRImport', (compilation: any) => {\n      compilation.mainTemplate.hooks.requireEnsure.tap(\n        'NextJsSSRImport',\n        (code: string, chunk: any) => {\n          // Update to load chunks from our custom chunks directory\n          const outputPath = resolve('/')\n          const pagePath = join('/', dirname(chunk.name))\n          const relativePathToBaseDir = relative(pagePath, outputPath)\n          // Make sure even in windows, the path looks like in unix\n          // Node.js require system will convert it accordingly\n          const relativePathToBaseDirNormalized = relativePathToBaseDir.replace(\n            /\\\\/g,\n            '/'\n          )\n          return code\n            .replace(\n              'require(\"./\"',\n              `require(\"${relativePathToBaseDirNormalized}/\"`\n            )\n            .replace(\n              'readFile(join(__dirname',\n              `readFile(join(__dirname, \"${relativePathToBaseDirNormalized}\"`\n            )\n        }\n      )\n    })\n  }\n}\n"], "names": [], "mappings": ";;;;;AAAiD,GAAM,CAAN,KAAM;MAKlC,qBAAqB;IACxC,KAAK,CAAC,QAA0B,EAAE,CAAC;QACjC,QAAQ,CAAC,KAAK,CAAC,WAAW,CAAC,GAAG,EAAC,eAAiB,IAAG,WAAgB,GAAK,CAAC;YACvE,WAAW,CAAC,YAAY,CAAC,KAAK,CAAC,aAAa,CAAC,GAAG,EAC9C,eAAiB,IAChB,IAAY,EAAE,KAAU,GAAK,CAAC;gBAC7B,EAAyD,AAAzD,uDAAyD;gBACzD,KAAK,CAAC,UAAU,OAZuB,KAAM,WAYlB,CAAG;gBAC9B,KAAK,CAAC,QAAQ,OAbyB,KAAM,QAavB,CAAG,OAbc,KAAM,UAaV,KAAK,CAAC,IAAI;gBAC7C,KAAK,CAAC,qBAAqB,OAdY,KAAM,WAcN,QAAQ,EAAE,UAAU;gBAC3D,EAAyD,AAAzD,uDAAyD;gBACzD,EAAqD,AAArD,mDAAqD;gBACrD,KAAK,CAAC,+BAA+B,GAAG,qBAAqB,CAAC,OAAO,SAEnE,CAAG;uBAEE,IAAI,CACR,OAAO,EACN,YAAc,IACb,SAAS,EAAE,+BAA+B,CAAC,EAAE,GAE/C,OAAO,EACN,uBAAyB,IACxB,0BAA0B,EAAE,+BAA+B,CAAC,CAAC;YAEpE,CAAC;QAEL,CAAC;IACH,CAAC;;kBA5BkB,qBAAqB"}