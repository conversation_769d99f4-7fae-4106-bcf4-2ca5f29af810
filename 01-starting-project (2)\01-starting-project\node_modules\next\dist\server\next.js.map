{"version": 3, "sources": ["../../server/next.ts"], "sourcesContent": ["import './node-polyfill-fetch'\nimport { default as Server, ServerConstructor } from './next-server'\nimport { NON_STANDARD_NODE_ENV } from '../lib/constants'\nimport * as log from '../build/output/log'\nimport loadConfig, { NextConfig } from './config'\nimport { resolve } from 'path'\nimport {\n  PHASE_DEVELOPMENT_SERVER,\n  PHASE_PRODUCTION_SERVER,\n} from '../shared/lib/constants'\nimport { IncomingMessage, ServerResponse } from 'http'\nimport { UrlWithParsedQuery } from 'url'\n\ntype NextServerConstructor = ServerConstructor & {\n  /**\n   * Whether to launch Next.js in dev mode - @default false\n   */\n  dev?: boolean\n}\n\nlet ServerImpl: typeof Server\n\nconst getServerImpl = async () => {\n  if (ServerImpl === undefined)\n    ServerImpl = (await import('./next-server')).default\n  return ServerImpl\n}\n\nexport class NextServer {\n  private serverPromise?: Promise<Server>\n  private server?: Server\n  private reqHandlerPromise?: Promise<any>\n  private preparedAssetPrefix?: string\n  options: NextServerConstructor\n\n  constructor(options: NextServerConstructor) {\n    this.options = options\n  }\n\n  getRequestHandler() {\n    return async (\n      req: IncomingMessage,\n      res: ServerResponse,\n      parsedUrl?: UrlWithParsedQuery\n    ) => {\n      const requestHandler = await this.getServerRequestHandler()\n      return requestHandler(req, res, parsedUrl)\n    }\n  }\n\n  setAssetPrefix(assetPrefix: string) {\n    if (this.server) {\n      this.server.setAssetPrefix(assetPrefix)\n    } else {\n      this.preparedAssetPrefix = assetPrefix\n    }\n  }\n\n  logError(...args: Parameters<Server['logError']>) {\n    if (this.server) {\n      this.server.logError(...args)\n    }\n  }\n\n  async render(...args: Parameters<Server['render']>) {\n    const server = await this.getServer()\n    return server.render(...args)\n  }\n\n  async renderToHTML(...args: Parameters<Server['renderToHTML']>) {\n    const server = await this.getServer()\n    return server.renderToHTML(...args)\n  }\n\n  async renderError(...args: Parameters<Server['renderError']>) {\n    const server = await this.getServer()\n    return server.renderError(...args)\n  }\n\n  async renderErrorToHTML(...args: Parameters<Server['renderErrorToHTML']>) {\n    const server = await this.getServer()\n    return server.renderErrorToHTML(...args)\n  }\n\n  async render404(...args: Parameters<Server['render404']>) {\n    const server = await this.getServer()\n    return server.render404(...args)\n  }\n\n  async serveStatic(...args: Parameters<Server['serveStatic']>) {\n    const server = await this.getServer()\n    return server.serveStatic(...args)\n  }\n\n  async prepare() {\n    const server = await this.getServer()\n    return server.prepare()\n  }\n\n  async close() {\n    const server = await this.getServer()\n    return (server as any).close()\n  }\n\n  private async createServer(\n    options: NextServerConstructor & {\n      conf: NextConfig\n      isNextDevCommand?: boolean\n    }\n  ): Promise<Server> {\n    if (options.dev) {\n      const DevServer = require('./dev/next-dev-server').default\n      return new DevServer(options)\n    }\n    const ServerImplementation = await getServerImpl()\n    return new ServerImplementation(options)\n  }\n\n  private async loadConfig() {\n    const phase = this.options.dev\n      ? PHASE_DEVELOPMENT_SERVER\n      : PHASE_PRODUCTION_SERVER\n    const dir = resolve(this.options.dir || '.')\n    const conf = await loadConfig(phase, dir, this.options.conf)\n    return conf\n  }\n\n  private async getServer() {\n    if (!this.serverPromise) {\n      setTimeout(getServerImpl, 10)\n      this.serverPromise = this.loadConfig().then(async (conf) => {\n        this.server = await this.createServer({\n          ...this.options,\n          conf,\n        })\n        if (this.preparedAssetPrefix) {\n          this.server.setAssetPrefix(this.preparedAssetPrefix)\n        }\n        return this.server\n      })\n    }\n    return this.serverPromise\n  }\n\n  private async getServerRequestHandler() {\n    // Memoize request handler creation\n    if (!this.reqHandlerPromise) {\n      this.reqHandlerPromise = this.getServer().then((server) =>\n        server.getRequestHandler().bind(server)\n      )\n    }\n    return this.reqHandlerPromise\n  }\n}\n\n// This file is used for when users run `require('next')`\nfunction createServer(options: NextServerConstructor): NextServer {\n  const standardEnv = ['production', 'development', 'test']\n\n  if (options == null) {\n    throw new Error(\n      'The server has not been instantiated properly. https://nextjs.org/docs/messages/invalid-server-options'\n    )\n  }\n\n  if (\n    !(options as any).isNextDevCommand &&\n    process.env.NODE_ENV &&\n    !standardEnv.includes(process.env.NODE_ENV)\n  ) {\n    log.warn(NON_STANDARD_NODE_ENV)\n  }\n\n  if (options.dev) {\n    if (typeof options.dev !== 'boolean') {\n      console.warn(\n        \"Warning: 'dev' is not a boolean which could introduce unexpected behavior. https://nextjs.org/docs/messages/invalid-server-options\"\n      )\n    }\n  }\n\n  return new NextServer(options)\n}\n\n// Support commonjs `require('next')`\nmodule.exports = createServer\nexports = module.exports\n\n// Support `import next from 'next'`\nexport default createServer\n"], "names": [], "mappings": ";;;;;;AAEsC,GAAkB,CAAlB,UAAkB;AAC5C,GAAG,CAAH,GAAG;AACwB,GAAU,CAAV,OAAU;AACzB,GAAM,CAAN,KAAM;AAIvB,GAAyB,CAAzB,WAAyB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWhC,GAAG,CAAC,UAAU;AAEd,KAAK,CAAC,aAAa,aAAe,CAAC;IACjC,EAAE,EAAE,UAAU,KAAK,SAAS,EAC1B,UAAU;gDAAiB,aAAe;QAAG,OAAO;WAC/C,UAAU;AACnB,CAAC;MAEY,UAAU;gBAOT,QAA8B,CAAE,CAAC;aACtC,OAAO,GAAG,QAAO;IACxB,CAAC;IAED,iBAAiB,GAAG,CAAC;sBAEjB,GAAoB,EACpB,GAAmB,EACnB,SAA8B,GAC3B,CAAC;YACJ,KAAK,CAAC,cAAc,cAAc,uBAAuB;mBAClD,cAAc,CAAC,GAAG,EAAE,GAAG,EAAE,SAAS;QAC3C,CAAC;IACH,CAAC;IAED,cAAc,CAAC,WAAmB,EAAE,CAAC;QACnC,EAAE,OAAO,MAAM,EAAE,CAAC;iBACX,MAAM,CAAC,cAAc,CAAC,WAAW;QACxC,CAAC,MAAM,CAAC;iBACD,mBAAmB,GAAG,WAAW;QACxC,CAAC;IACH,CAAC;IAED,QAAQ,IAAI,IAAI,EAAkC,CAAC;QACjD,EAAE,OAAO,MAAM,EAAE,CAAC;iBACX,MAAM,CAAC,QAAQ,IAAI,IAAI;QAC9B,CAAC;IACH,CAAC;UAEK,MAAM,IAAI,IAAI,EAAgC,CAAC;QACnD,KAAK,CAAC,MAAM,cAAc,SAAS;eAC5B,MAAM,CAAC,MAAM,IAAI,IAAI;IAC9B,CAAC;UAEK,YAAY,IAAI,IAAI,EAAsC,CAAC;QAC/D,KAAK,CAAC,MAAM,cAAc,SAAS;eAC5B,MAAM,CAAC,YAAY,IAAI,IAAI;IACpC,CAAC;UAEK,WAAW,IAAI,IAAI,EAAqC,CAAC;QAC7D,KAAK,CAAC,MAAM,cAAc,SAAS;eAC5B,MAAM,CAAC,WAAW,IAAI,IAAI;IACnC,CAAC;UAEK,iBAAiB,IAAI,IAAI,EAA2C,CAAC;QACzE,KAAK,CAAC,MAAM,cAAc,SAAS;eAC5B,MAAM,CAAC,iBAAiB,IAAI,IAAI;IACzC,CAAC;UAEK,SAAS,IAAI,IAAI,EAAmC,CAAC;QACzD,KAAK,CAAC,MAAM,cAAc,SAAS;eAC5B,MAAM,CAAC,SAAS,IAAI,IAAI;IACjC,CAAC;UAEK,WAAW,IAAI,IAAI,EAAqC,CAAC;QAC7D,KAAK,CAAC,MAAM,cAAc,SAAS;eAC5B,MAAM,CAAC,WAAW,IAAI,IAAI;IACnC,CAAC;UAEK,OAAO,GAAG,CAAC;QACf,KAAK,CAAC,MAAM,cAAc,SAAS;eAC5B,MAAM,CAAC,OAAO;IACvB,CAAC;UAEK,KAAK,GAAG,CAAC;QACb,KAAK,CAAC,MAAM,cAAc,SAAS;eAC3B,MAAM,CAAS,KAAK;IAC9B,CAAC;UAEa,YAAY,CACxB,OAGC,EACgB,CAAC;QAClB,EAAE,EAAE,OAAO,CAAC,GAAG,EAAE,CAAC;YAChB,KAAK,CAAC,SAAS,GAAG,OAAO,EAAC,qBAAuB,GAAE,OAAO;mBACnD,GAAG,CAAC,SAAS,CAAC,OAAO;QAC9B,CAAC;QACD,KAAK,CAAC,oBAAoB,SAAS,aAAa;eACzC,GAAG,CAAC,oBAAoB,CAAC,OAAO;IACzC,CAAC;UAEa,UAAU,GAAG,CAAC;QAC1B,KAAK,CAAC,KAAK,QAAQ,OAAO,CAAC,GAAG,GA9G3B,WAAyB,4BAAzB,WAAyB;QAiH5B,KAAK,CAAC,GAAG,OArHW,KAAM,eAqHD,OAAO,CAAC,GAAG,KAAI,CAAG;QAC3C,KAAK,CAAC,IAAI,aAvHyB,OAAU,UAuHf,KAAK,EAAE,GAAG,OAAO,OAAO,CAAC,IAAI;eACpD,IAAI;IACb,CAAC;UAEa,SAAS,GAAG,CAAC;QACzB,EAAE,QAAQ,aAAa,EAAE,CAAC;YACxB,UAAU,CAAC,aAAa,EAAE,EAAE;iBACvB,aAAa,QAAQ,UAAU,GAAG,IAAI,QAAQ,IAAI,GAAK,CAAC;qBACtD,MAAM,cAAc,YAAY;4BAC3B,OAAO;oBACf,IAAI;;gBAEN,EAAE,OAAO,mBAAmB,EAAE,CAAC;yBACxB,MAAM,CAAC,cAAc,MAAM,mBAAmB;gBACrD,CAAC;4BACW,MAAM;YACpB,CAAC;QACH,CAAC;oBACW,aAAa;IAC3B,CAAC;UAEa,uBAAuB,GAAG,CAAC;QACvC,EAAmC,AAAnC,iCAAmC;QACnC,EAAE,QAAQ,iBAAiB,EAAE,CAAC;iBACvB,iBAAiB,QAAQ,SAAS,GAAG,IAAI,EAAE,MAAM,GACpD,MAAM,CAAC,iBAAiB,GAAG,IAAI,CAAC,MAAM;;QAE1C,CAAC;oBACW,iBAAiB;IAC/B,CAAC;;QA5HU,UAAU,GAAV,UAAU;AA+HvB,EAAyD,AAAzD,uDAAyD;SAChD,YAAY,CAAC,QAA8B,EAAc,CAAC;IACjE,KAAK,CAAC,WAAW;SAAI,UAAY;SAAE,WAAa;SAAE,IAAM;;IAExD,EAAE,EAAE,QAAO,IAAI,IAAI,EAAE,CAAC;QACpB,KAAK,CAAC,GAAG,CAAC,KAAK,EACb,sGAAwG;IAE5G,CAAC;IAED,EAAE,GACE,QAAO,CAAS,gBAAgB,IAClC,OAAO,CAAC,GAAG,CAAC,QAAQ,KACnB,WAAW,CAAC,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,QAAQ,GAC1C,CAAC;QAtKO,GAAG,CAuKP,IAAI,CAxK0B,UAAkB;IAyKtD,CAAC;IAED,EAAE,EAAE,QAAO,CAAC,GAAG,EAAE,CAAC;QAChB,EAAE,SAAS,QAAO,CAAC,GAAG,MAAK,OAAS,GAAE,CAAC;YACrC,OAAO,CAAC,IAAI,EACV,kIAAoI;QAExI,CAAC;IACH,CAAC;WAEM,GAAG,CAAC,UAAU,CAAC,QAAO;AAC/B,CAAC;AAED,EAAqC,AAArC,mCAAqC;AACrC,MAAM,CAAC,OAAO,GAAG,YAAY;AAC7B,OAAO,GAAG,MAAM,CAAC,OAAO;eAGT,YAAY"}