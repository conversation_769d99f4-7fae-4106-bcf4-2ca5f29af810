{"version": 3, "sources": ["../../cli/next-lint.ts"], "sourcesContent": ["#!/usr/bin/env node\nimport { existsSync } from 'fs'\nimport arg from 'next/dist/compiled/arg/index.js'\nimport { resolve, join } from 'path'\nimport chalk from 'chalk'\n\nimport { cliCommand } from '../bin/next'\nimport { ESLINT_DEFAULT_DIRS } from '../lib/constants'\nimport { runLintCheck } from '../lib/eslint/runLintCheck'\nimport { printAndExit } from '../server/lib/utils'\nimport { Telemetry } from '../telemetry/storage'\nimport loadConfig from '../server/config'\nimport { PHASE_PRODUCTION_BUILD } from '../shared/lib/constants'\nimport { eventLintCheckCompleted } from '../telemetry/events'\nimport { CompileError } from '../lib/compile-error'\n\nconst eslintOptions = (args: arg.Spec, defaultCacheLocation: string) => ({\n  overrideConfigFile: args['--config'] || null,\n  extensions: args['--ext'] ?? ['.js', '.jsx', '.ts', '.tsx'],\n  resolvePluginsRelativeTo: args['--resolve-plugins-relative-to'] || null,\n  rulePaths: args['--rulesdir'] ?? [],\n  fix: args['--fix'] ?? false,\n  fixTypes: args['--fix-type'] ?? null,\n  ignorePath: args['--ignore-path'] || null,\n  ignore: !Boolean(args['--no-ignore']),\n  allowInlineConfig: !Boolean(args['--no-inline-config']),\n  reportUnusedDisableDirectives:\n    args['--report-unused-disable-directives'] || null,\n  cache: !Boolean(args['--no-cache']),\n  cacheLocation: args['--cache-location'] || defaultCacheLocation,\n  errorOnUnmatchedPattern: args['--error-on-unmatched-pattern']\n    ? Boolean(args['--error-on-unmatched-pattern'])\n    : false,\n})\n\nconst nextLint: cliCommand = async (argv) => {\n  const validArgs: arg.Spec = {\n    // Types\n    '--help': Boolean,\n    '--base-dir': String,\n    '--dir': [String],\n    '--strict': Boolean,\n\n    // Aliases\n    '-h': '--help',\n    '-b': '--base-dir',\n    '-d': '--dir',\n  }\n\n  const validEslintArgs: arg.Spec = {\n    // Types\n    '--config': String,\n    '--ext': [String],\n    '--resolve-plugins-relative-to': String,\n    '--rulesdir': [String],\n    '--fix': Boolean,\n    '--fix-type': [String],\n    '--ignore-path': String,\n    '--no-ignore': Boolean,\n    '--quiet': Boolean,\n    '--max-warnings': Number,\n    '--no-inline-config': Boolean,\n    '--report-unused-disable-directives': String,\n    '--cache': Boolean, // Although cache is enabled by default, this dummy flag still exists to not cause any breaking changes\n    '--no-cache': Boolean,\n    '--cache-location': String,\n    '--error-on-unmatched-pattern': Boolean,\n    '--format': String,\n\n    // Aliases\n    '-c': '--config',\n    '-f': '--format',\n  }\n\n  let args: arg.Result<arg.Spec>\n  try {\n    args = arg({ ...validArgs, ...validEslintArgs }, { argv })\n  } catch (error) {\n    if (error.code === 'ARG_UNKNOWN_OPTION') {\n      return printAndExit(error.message, 1)\n    }\n    throw error\n  }\n  if (args['--help']) {\n    printAndExit(\n      `\n      Description\n        Run ESLint on every file in specified directories. \n        If not configured, ESLint will be set up for the first time.\n\n      Usage\n        $ next lint <baseDir> [options]\n      \n      <baseDir> represents the directory of the Next.js application.\n      If no directory is provided, the current directory will be used.\n\n      Options\n        Basic configuration:\n          -h, --help                     List this help\n          -d, --dir Array                Set directory, or directories, to run ESLint - default: 'pages', 'components', and 'lib'\n          -c, --config path::String      Use this configuration file, overriding all other config options\n          --ext [String]                 Specify JavaScript file extensions - default: .js, .jsx, .ts, .tsx\n          --resolve-plugins-relative-to path::String  A folder where plugins should be resolved from, CWD by default\n\n        Initial setup:\n          --strict                       Creates an .eslintrc.json file using the Next.js strict configuration (only possible if no .eslintrc.json file is present)\n\n        Specifying rules:\n          --rulesdir [path::String]      Use additional rules from this directory\n\n        Fixing problems:\n          --fix                          Automatically fix problems\n          --fix-type Array               Specify the types of fixes to apply (problem, suggestion, layout)\n\n        Ignoring files:\n          --ignore-path path::String     Specify path of ignore file\n          --no-ignore                    Disable use of ignore files and patterns\n\n        Handling warnings:\n          --quiet                        Report errors only - default: false\n          --max-warnings Int             Number of warnings to trigger nonzero exit code - default: -1\n        \n        Output:\n          -f, --format String            Use a specific output format - default: Next.js custom formatter\n\n        Inline configuration comments:\n          --no-inline-config             Prevent comments from changing config or rules\n          --report-unused-disable-directives  Adds reported errors for unused eslint-disable directives (\"error\" | \"warn\" | \"off\")\n\n        Caching:\n          --no-cache                     Disable caching\n          --cache-location path::String  Path to the cache file or directory - default: .eslintcache\n        \n        Miscellaneous:\n          --error-on-unmatched-pattern   Show errors when any file patterns are unmatched - default: false\n          `,\n      0\n    )\n  }\n\n  const baseDir = resolve(args._[0] || '.')\n\n  // Check if the provided directory exists\n  if (!existsSync(baseDir)) {\n    printAndExit(`> No such directory exists as the project root: ${baseDir}`)\n  }\n\n  const nextConfig = await loadConfig(PHASE_PRODUCTION_BUILD, baseDir)\n\n  const dirs: string[] = args['--dir'] ?? nextConfig.eslint?.dirs\n  const lintDirs = (dirs ?? ESLINT_DEFAULT_DIRS).reduce(\n    (res: string[], d: string) => {\n      const currDir = join(baseDir, d)\n      if (!existsSync(currDir)) return res\n      res.push(currDir)\n      return res\n    },\n    []\n  )\n\n  const reportErrorsOnly = Boolean(args['--quiet'])\n  const maxWarnings = args['--max-warnings'] ?? -1\n  const formatter = args['--format'] || null\n  const strict = Boolean(args['--strict'])\n\n  const distDir = join(baseDir, nextConfig.distDir)\n  const defaultCacheLocation = join(distDir, 'cache', 'eslint/')\n\n  runLintCheck(\n    baseDir,\n    lintDirs,\n    false,\n    eslintOptions(args, defaultCacheLocation),\n    reportErrorsOnly,\n    maxWarnings,\n    formatter,\n    strict\n  )\n    .then(async (lintResults) => {\n      const lintOutput =\n        typeof lintResults === 'string' ? lintResults : lintResults?.output\n\n      if (typeof lintResults !== 'string' && lintResults?.eventInfo) {\n        const telemetry = new Telemetry({\n          distDir,\n        })\n        telemetry.record(\n          eventLintCheckCompleted({\n            ...lintResults.eventInfo,\n            buildLint: false,\n          })\n        )\n        await telemetry.flush()\n      }\n\n      if (\n        typeof lintResults !== 'string' &&\n        lintResults?.isError &&\n        lintOutput\n      ) {\n        throw new CompileError(lintOutput)\n      }\n\n      if (lintOutput) {\n        printAndExit(lintOutput, 0)\n      } else if (lintResults && !lintOutput) {\n        printAndExit(chalk.green('✔ No ESLint warnings or errors'), 0)\n      }\n    })\n    .catch((err) => {\n      printAndExit(err.message)\n    })\n}\n\nexport { nextLint }\n"], "names": [], "mappings": ";;;;;;AAC2B,GAAI,CAAJ,GAAI;AACf,GAAiC,CAAjC,QAAiC;AACnB,GAAM,CAAN,KAAM;AAClB,GAAO,CAAP,MAAO;AAGW,GAAkB,CAAlB,UAAkB;AACzB,GAA4B,CAA5B,aAA4B;AAC5B,GAAqB,CAArB,MAAqB;AACxB,GAAsB,CAAtB,QAAsB;AACzB,GAAkB,CAAlB,OAAkB;AACF,GAAyB,CAAzB,WAAyB;AACxB,GAAqB,CAArB,OAAqB;AAChC,GAAsB,CAAtB,aAAsB;;;;;;IAIrC,GAAa,EAEd,IAAkB,EACxB,IAAa,EACR,IAAkB;AAN9B,KAAK,CAAC,aAAa,IAAI,IAAc,EAAE,oBAA4B;QACjE,kBAAkB,EAAE,IAAI,EAAC,QAAU,MAAK,IAAI;QAC5C,UAAU,GAAE,GAAa,GAAb,IAAI,EAAC,KAAO,gBAAZ,GAAa,cAAb,GAAa;aAAK,GAAK;aAAE,IAAM;aAAE,GAAK;aAAE,IAAM;;QAC1D,wBAAwB,EAAE,IAAI,EAAC,6BAA+B,MAAK,IAAI;QACvE,SAAS,GAAE,IAAkB,GAAlB,IAAI,EAAC,UAAY,gBAAjB,IAAkB,cAAlB,IAAkB;QAC7B,GAAG,GAAE,IAAa,GAAb,IAAI,EAAC,KAAO,gBAAZ,IAAa,cAAb,IAAa,GAAI,KAAK;QAC3B,QAAQ,GAAE,IAAkB,GAAlB,IAAI,EAAC,UAAY,gBAAjB,IAAkB,cAAlB,IAAkB,GAAI,IAAI;QACpC,UAAU,EAAE,IAAI,EAAC,aAAe,MAAK,IAAI;QACzC,MAAM,GAAG,OAAO,CAAC,IAAI,EAAC,WAAa;QACnC,iBAAiB,GAAG,OAAO,CAAC,IAAI,EAAC,kBAAoB;QACrD,6BAA6B,EAC3B,IAAI,EAAC,kCAAoC,MAAK,IAAI;QACpD,KAAK,GAAG,OAAO,CAAC,IAAI,EAAC,UAAY;QACjC,aAAa,EAAE,IAAI,EAAC,gBAAkB,MAAK,oBAAoB;QAC/D,uBAAuB,EAAE,IAAI,EAAC,4BAA8B,KACxD,OAAO,CAAC,IAAI,EAAC,4BAA8B,MAC3C,KAAK;;;AAGX,KAAK,CAAC,QAAQ,UAAsB,IAAI,GAAK,CAAC;QAkHJ,IAAiB;IAjHzD,KAAK,CAAC,SAAS;QACb,EAAQ,AAAR,MAAQ;SACR,MAAQ,GAAE,OAAO;SACjB,UAAY,GAAE,MAAM;SACpB,KAAO;YAAG,MAAM;;SAChB,QAAU,GAAE,OAAO;QAEnB,EAAU,AAAV,QAAU;SACV,EAAI,IAAE,MAAQ;SACd,EAAI,IAAE,UAAY;SAClB,EAAI,IAAE,KAAO;;IAGf,KAAK,CAAC,eAAe;QACnB,EAAQ,AAAR,MAAQ;SACR,QAAU,GAAE,MAAM;SAClB,KAAO;YAAG,MAAM;;SAChB,6BAA+B,GAAE,MAAM;SACvC,UAAY;YAAG,MAAM;;SACrB,KAAO,GAAE,OAAO;SAChB,UAAY;YAAG,MAAM;;SACrB,aAAe,GAAE,MAAM;SACvB,WAAa,GAAE,OAAO;SACtB,OAAS,GAAE,OAAO;SAClB,cAAgB,GAAE,MAAM;SACxB,kBAAoB,GAAE,OAAO;SAC7B,kCAAoC,GAAE,MAAM;SAC5C,OAAS,GAAE,OAAO;SAClB,UAAY,GAAE,OAAO;SACrB,gBAAkB,GAAE,MAAM;SAC1B,4BAA8B,GAAE,OAAO;SACvC,QAAU,GAAE,MAAM;QAElB,EAAU,AAAV,QAAU;SACV,EAAI,IAAE,QAAU;SAChB,EAAI,IAAE,QAAU;;IAGlB,GAAG,CAAC,IAAI;QACJ,CAAC;QACH,IAAI,OA1EQ,QAAiC;eA0E7B,SAAS;eAAK,eAAe;;YAAM,IAAI;;IACzD,CAAC,QAAQ,KAAK,EAAE,CAAC;QACf,EAAE,EAAE,KAAK,CAAC,IAAI,MAAK,kBAAoB,GAAE,CAAC;uBArEjB,MAAqB,eAsExB,KAAK,CAAC,OAAO,EAAE,CAAC;QACtC,CAAC;QACD,KAAK,CAAC,KAAK;IACb,CAAC;IACD,EAAE,EAAE,IAAI,EAAC,MAAQ,IAAG,CAAC;YA1EM,MAAqB,gBA4E3C,08EAkDG,GACJ,CAAC;IAEL,CAAC;IAED,KAAK,CAAC,OAAO,OAzIe,KAAM,UAyIV,IAAI,CAAC,CAAC,CAAC,CAAC,MAAK,CAAG;IAExC,EAAyC,AAAzC,uCAAyC;IACzC,EAAE,OA9IuB,GAAI,aA8Ib,OAAO,GAAG,CAAC;YAtIA,MAAqB,gBAuIhC,gDAAgD,EAAE,OAAO;IACzE,CAAC;IAED,KAAK,CAAC,UAAU,aAxIK,OAAkB,UACF,WAAyB,yBAuIF,OAAO;QAE5C,IAAa;IAApC,KAAK,CAAC,IAAI,IAAa,IAAa,GAAb,IAAI,EAAC,KAAO,gBAAZ,IAAa,cAAb,IAAa,IAAI,IAAiB,GAAjB,UAAU,CAAC,MAAM,cAAjB,IAAiB,UAAjB,CAAuB,QAAvB,CAAuB,GAAvB,IAAiB,CAAE,IAAI;IAC/D,KAAK,CAAC,QAAQ,IAAI,IAAI,aAAJ,IAAI,cAAJ,IAAI,GA/IY,UAAkB,sBA+IL,MAAM,EAClD,GAAa,EAAE,CAAS,GAAK,CAAC;QAC7B,KAAK,CAAC,OAAO,OArJW,KAAM,OAqJT,OAAO,EAAE,CAAC;QAC/B,EAAE,OAxJmB,GAAI,aAwJT,OAAO,UAAU,GAAG;QACpC,GAAG,CAAC,IAAI,CAAC,OAAO;eACT,GAAG;IACZ,CAAC;IAIH,KAAK,CAAC,gBAAgB,GAAG,OAAO,CAAC,IAAI,EAAC,OAAS;QAC3B,IAAsB;IAA1C,KAAK,CAAC,WAAW,IAAG,IAAsB,GAAtB,IAAI,EAAC,cAAgB,gBAArB,IAAsB,cAAtB,IAAsB,IAAK,CAAC;IAChD,KAAK,CAAC,SAAS,GAAG,IAAI,EAAC,QAAU,MAAK,IAAI;IAC1C,KAAK,CAAC,MAAM,GAAG,OAAO,CAAC,IAAI,EAAC,QAAU;IAEtC,KAAK,CAAC,OAAO,OAlKe,KAAM,OAkKb,OAAO,EAAE,UAAU,CAAC,OAAO;IAChD,KAAK,CAAC,oBAAoB,OAnKE,KAAM,OAmKA,OAAO,GAAE,KAAO,IAAE,OAAS;QA9JlC,aAA4B,eAiKrD,OAAO,EACP,QAAQ,EACR,KAAK,EACL,aAAa,CAAC,IAAI,EAAE,oBAAoB,GACxC,gBAAgB,EAChB,WAAW,EACX,SAAS,EACT,MAAM,EAEL,IAAI,QAAQ,WAAW,GAAK,CAAC;QAC5B,KAAK,CAAC,UAAU,UACP,WAAW,MAAK,MAAQ,IAAG,WAAW,GAAG,WAAW,aAAX,WAAW,UAAX,CAAmB,QAAnB,CAAmB,GAAnB,WAAW,CAAE,MAAM;QAErE,EAAE,SAAS,WAAW,MAAK,MAAQ,MAAI,WAAW,aAAX,WAAW,UAAX,CAAsB,QAAtB,CAAsB,GAAtB,WAAW,CAAE,SAAS,GAAE,CAAC;YAC9D,KAAK,CAAC,SAAS,GAAG,GAAG,CA7KH,QAAsB;gBA8KtC,OAAO;;YAET,SAAS,CAAC,MAAM,KA7KgB,OAAqB;mBA+K9C,WAAW,CAAC,SAAS;gBACxB,SAAS,EAAE,KAAK;;kBAGd,SAAS,CAAC,KAAK;QACvB,CAAC;QAED,EAAE,SACO,WAAW,MAAK,MAAQ,MAC/B,WAAW,aAAX,WAAW,UAAX,CAAoB,QAApB,CAAoB,GAApB,WAAW,CAAE,OAAO,KACpB,UAAU,EACV,CAAC;YACD,KAAK,CAAC,GAAG,CA1LY,aAAsB,cA0LpB,UAAU;QACnC,CAAC;QAED,EAAE,EAAE,UAAU,EAAE,CAAC;gBAlMM,MAAqB,eAmM7B,UAAU,EAAE,CAAC;QAC5B,CAAC,MAAM,EAAE,EAAE,WAAW,KAAK,UAAU,EAAE,CAAC;gBApMjB,MAAqB,eALhC,MAAO,SA0ME,KAAK,EAAC,gCAAgC,IAAG,CAAC;QAC/D,CAAC;IACH,CAAC,EACA,KAAK,EAAE,GAAG,GAAK,CAAC;YAxMQ,MAAqB,eAyM/B,GAAG,CAAC,OAAO;IAC1B,CAAC;AACL,CAAC;QAEQ,QAAQ,GAAR,QAAQ"}