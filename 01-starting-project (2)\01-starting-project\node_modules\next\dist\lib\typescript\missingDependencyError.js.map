{"version": 3, "sources": ["../../../lib/typescript/missingDependencyError.ts"], "sourcesContent": ["import chalk from 'chalk'\n\nimport { getOxfordCommaList } from '../oxford-comma-list'\nimport { MissingDependency } from '../has-necessary-dependencies'\nimport { FatalError } from '../fatal-error'\nimport { isYarn } from '../is-yarn'\n\nexport function missingDepsError(\n  dir: string,\n  missingPackages: MissingDependency[]\n) {\n  const packagesHuman = getOxfordCommaList(missingPackages.map((p) => p.pkg))\n  const packagesCli = missingPackages.map((p) => p.pkg).join(' ')\n\n  const removalMsg =\n    '\\n\\n' +\n    chalk.bold(\n      'If you are not trying to use TypeScript, please remove the ' +\n        chalk.cyan('tsconfig.json') +\n        ' file from your package root (and any TypeScript files in your pages directory).'\n    )\n\n  throw new FatalError(\n    chalk.bold.red(\n      `It looks like you're trying to use TypeScript but do not have the required package(s) installed.`\n    ) +\n      '\\n\\n' +\n      chalk.bold(`Please install ${chalk.bold(packagesHuman)} by running:`) +\n      '\\n\\n' +\n      `\\t${chalk.bold.cyan(\n        (isYarn(dir) ? 'yarn add --dev' : 'npm install --save-dev') +\n          ' ' +\n          packagesCli\n      )}` +\n      removalMsg +\n      '\\n'\n  )\n}\n"], "names": [], "mappings": ";;;;QAOgB,gBAAgB,GAAhB,gBAAgB;AAPd,GAAO,CAAP,MAAO;AAEU,GAAsB,CAAtB,gBAAsB;AAE9B,GAAgB,CAAhB,WAAgB;AACpB,GAAY,CAAZ,OAAY;;;;;;SAEnB,gBAAgB,CAC9B,GAAW,EACX,eAAoC,EACpC,CAAC;IACD,KAAK,CAAC,aAAa,OATc,gBAAsB,qBASd,eAAe,CAAC,GAAG,EAAE,CAAC,GAAK,CAAC,CAAC,GAAG;;IACzE,KAAK,CAAC,WAAW,GAAG,eAAe,CAAC,GAAG,EAAE,CAAC,GAAK,CAAC,CAAC,GAAG;MAAE,IAAI,EAAC,CAAG;IAE9D,KAAK,CAAC,UAAU,IACd,IAAM,IAfQ,MAAO,SAgBf,IAAI,EACR,2DAA6D,IAjBjD,MAAO,SAkBX,IAAI,EAAC,aAAe,MAC1B,gFAAkF;IAGxF,KAAK,CAAC,GAAG,CAlBgB,WAAgB,YAJzB,MAAO,SAuBf,IAAI,CAAC,GAAG,EACX,gGAAgG,MAEjG,IAAM,IA1BM,MAAO,SA2Bb,IAAI,EAAE,eAAe,EA3Bf,MAAO,SA2BgB,IAAI,CAAC,aAAa,EAAE,YAAY,MACnE,IAAM,KACL,EAAE,EA7BS,MAAO,SA6BR,IAAI,CAAC,IAAI,MAxBH,OAAY,SAyBnB,GAAG,KAAI,cAAgB,KAAG,sBAAwB,MACxD,CAAG,IACH,WAAW,MAEf,UAAU,IACV,EAAI;AAEV,CAAC"}