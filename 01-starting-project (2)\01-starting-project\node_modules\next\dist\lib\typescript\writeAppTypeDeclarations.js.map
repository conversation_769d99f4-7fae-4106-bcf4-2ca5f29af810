{"version": 3, "sources": ["../../../lib/typescript/writeAppTypeDeclarations.ts"], "sourcesContent": ["import { promises as fs } from 'fs'\nimport os from 'os'\nimport path from 'path'\nimport { fileExists } from '../file-exists'\n\nexport async function writeAppTypeDeclarations(\n  baseDir: string,\n  imageImportsEnabled: boolean\n): Promise<void> {\n  // Reference `next` types\n  const appTypeDeclarations = path.join(baseDir, 'next-env.d.ts')\n\n  const content =\n    '/// <reference types=\"next\" />' +\n    os.EOL +\n    '/// <reference types=\"next/types/global\" />' +\n    os.EOL +\n    (imageImportsEnabled\n      ? '/// <reference types=\"next/image-types/global\" />' + os.EOL\n      : '') +\n    os.EOL +\n    '// NOTE: This file should not be edited' +\n    os.EOL +\n    '// see https://nextjs.org/docs/basic-features/typescript for more information.' +\n    os.EOL\n\n  // Avoids a write for read-only filesystems\n  if (\n    (await fileExists(appTypeDeclarations)) &&\n    (await fs.readFile(appTypeDeclarations, 'utf8')) === content\n  ) {\n    return\n  }\n\n  await fs.writeFile(appTypeDeclarations, content)\n}\n"], "names": [], "mappings": ";;;;QAKsB,wBAAwB,GAAxB,wBAAwB;AALf,GAAI,CAAJ,GAAI;AACpB,GAAI,CAAJ,GAAI;AACF,GAAM,CAAN,KAAM;AACI,GAAgB,CAAhB,WAAgB;;;;;;eAErB,wBAAwB,CAC5C,OAAe,EACf,mBAA4B,EACb,CAAC;IAChB,EAAyB,AAAzB,uBAAyB;IACzB,KAAK,CAAC,mBAAmB,GARV,KAAM,SAQY,IAAI,CAAC,OAAO,GAAE,aAAe;IAE9D,KAAK,CAAC,OAAO,IACX,8BAAgC,IAZrB,GAAI,SAaZ,GAAG,IACN,2CAA6C,IAdlC,GAAI,SAeZ,GAAG,IACL,mBAAmB,IAChB,iDAAmD,IAjB5C,GAAI,SAiB8C,GAAG,SAjBrD,GAAI,SAmBZ,GAAG,IACN,uCAAyC,IApB9B,GAAI,SAqBZ,GAAG,IACN,8EAAgF,IAtBrE,GAAI,SAuBZ,GAAG;IAER,EAA2C,AAA3C,yCAA2C;IAC3C,EAAE,YAxBuB,WAAgB,aAyBrB,mBAAmB,WA5BV,GAAI,UA6BrB,QAAQ,CAAC,mBAAmB,GAAE,IAAM,OAAO,OAAO,EAC5D,CAAC;;IAEH,CAAC;UAhC4B,GAAI,UAkCxB,SAAS,CAAC,mBAAmB,EAAE,OAAO;AACjD,CAAC"}