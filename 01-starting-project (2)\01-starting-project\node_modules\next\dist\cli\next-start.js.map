{"version": 3, "sources": ["../../cli/next-start.ts"], "sourcesContent": ["#!/usr/bin/env node\n\nimport { resolve } from 'path'\nimport arg from 'next/dist/compiled/arg/index.js'\nimport startServer from '../server/lib/start-server'\nimport { printAndExit } from '../server/lib/utils'\nimport { cliCommand } from '../bin/next'\nimport * as Log from '../build/output/log'\n\nconst nextStart: cliCommand = (argv) => {\n  const validArgs: arg.Spec = {\n    // Types\n    '--help': Boolean,\n    '--port': Number,\n    '--hostname': String,\n\n    // Aliases\n    '-h': '--help',\n    '-p': '--port',\n    '-H': '--hostname',\n  }\n  let args: arg.Result<arg.Spec>\n  try {\n    args = arg(validArgs, { argv })\n  } catch (error) {\n    if (error.code === 'ARG_UNKNOWN_OPTION') {\n      return printAndExit(error.message, 1)\n    }\n    throw error\n  }\n  if (args['--help']) {\n    console.log(`\n      Description\n        Starts the application in production mode.\n        The application should be compiled with \\`next build\\` first.\n\n      Usage\n        $ next start <dir> -p <port>\n\n      <dir> represents the directory of the Next.js application.\n      If no directory is provided, the current directory will be used.\n\n      Options\n        --port, -p      A port number on which to start the application\n        --hostname, -H  Hostname on which to start the application (default: 0.0.0.0)\n        --help, -h      Displays this message\n    `)\n    process.exit(0)\n  }\n\n  const dir = resolve(args._[0] || '.')\n  const port =\n    args['--port'] || (process.env.PORT && parseInt(process.env.PORT)) || 3000\n  const host = args['--hostname'] || '0.0.0.0'\n  const appUrl = `http://${host === '0.0.0.0' ? 'localhost' : host}:${port}`\n  startServer({ dir }, port, host)\n    .then(async (app) => {\n      Log.ready(`started server on ${host}:${port}, url: ${appUrl}`)\n      await app.prepare()\n    })\n    .catch((err) => {\n      console.error(err)\n      process.exit(1)\n    })\n}\n\nexport { nextStart }\n"], "names": [], "mappings": ";;;;;;AAEwB,GAAM,CAAN,KAAM;AACd,GAAiC,CAAjC,QAAiC;AACzB,GAA4B,CAA5B,YAA4B;AACvB,GAAqB,CAArB,MAAqB;AAEtC,GAAG,CAAH,GAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEf,KAAK,CAAC,SAAS,IAAgB,IAAI,GAAK,CAAC;IACvC,KAAK,CAAC,SAAS;QACb,EAAQ,AAAR,MAAQ;SACR,MAAQ,GAAE,OAAO;SACjB,MAAQ,GAAE,MAAM;SAChB,UAAY,GAAE,MAAM;QAEpB,EAAU,AAAV,QAAU;SACV,EAAI,IAAE,MAAQ;SACd,EAAI,IAAE,MAAQ;SACd,EAAI,IAAE,UAAY;;IAEpB,GAAG,CAAC,IAAI;QACJ,CAAC;QACH,IAAI,OApBQ,QAAiC,UAoBlC,SAAS;YAAI,IAAI;;IAC9B,CAAC,QAAQ,KAAK,EAAE,CAAC;QACf,EAAE,EAAE,KAAK,CAAC,IAAI,MAAK,kBAAoB,GAAE,CAAC;uBApBjB,MAAqB,eAqBxB,KAAK,CAAC,OAAO,EAAE,CAAC;QACtC,CAAC;QACD,KAAK,CAAC,KAAK;IACb,CAAC;IACD,EAAE,EAAE,IAAI,EAAC,MAAQ,IAAG,CAAC;QACnB,OAAO,CAAC,GAAG,EAAE,siBAeb;QACA,OAAO,CAAC,IAAI,CAAC,CAAC;IAChB,CAAC;IAED,KAAK,CAAC,GAAG,OAhDa,KAAM,UAgDR,IAAI,CAAC,CAAC,CAAC,CAAC,MAAK,CAAG;IACpC,KAAK,CAAC,IAAI,GACR,IAAI,EAAC,MAAQ,MAAM,OAAO,CAAC,GAAG,CAAC,IAAI,IAAI,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,KAAM,IAAI;IAC5E,KAAK,CAAC,IAAI,GAAG,IAAI,EAAC,UAAY,OAAK,OAAS;IAC5C,KAAK,CAAC,MAAM,IAAI,OAAO,EAAE,IAAI,MAAK,OAAS,KAAG,SAAW,IAAG,IAAI,CAAC,CAAC,EAAE,IAAI;QAlDlD,YAA4B;QAmDpC,GAAG;OAAI,IAAI,EAAE,IAAI,EAC5B,IAAI,QAAQ,GAAG,GAAK,CAAC;QAjDd,GAAG,CAkDL,KAAK,EAAE,kBAAkB,EAAE,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,OAAO,EAAE,MAAM;cACrD,GAAG,CAAC,OAAO;IACnB,CAAC,EACA,KAAK,EAAE,GAAG,GAAK,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,GAAG;QACjB,OAAO,CAAC,IAAI,CAAC,CAAC;IAChB,CAAC;AACL,CAAC;QAEQ,SAAS,GAAT,SAAS"}