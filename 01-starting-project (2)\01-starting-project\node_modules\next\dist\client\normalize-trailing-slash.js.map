{"version": 3, "sources": ["../../client/normalize-trailing-slash.ts"], "sourcesContent": ["/**\n * Removes the trailing slash of a path if there is one. Preserves the root path `/`.\n */\nexport function removePathTrailingSlash(path: string): string {\n  return path.endsWith('/') && path !== '/' ? path.slice(0, -1) : path\n}\n\n/**\n * Normalizes the trailing slash of a path according to the `trailingSlash` option\n * in `next.config.js`.\n */\nexport const normalizePathTrailingSlash = process.env.__NEXT_TRAILING_SLASH\n  ? (path: string): string => {\n      if (/\\.[^/]+\\/?$/.test(path)) {\n        return removePathTrailingSlash(path)\n      } else if (path.endsWith('/')) {\n        return path\n      } else {\n        return path + '/'\n      }\n    }\n  : removePathTrailingSlash\n"], "names": [], "mappings": ";;;;QAGgB,uBAAuB,GAAvB,uBAAuB;;SAAvB,uBAAuB,CAAC,IAAY,EAAU,CAAC;WACtD,IAAI,CAAC,QAAQ,EAAC,CAAG,MAAK,IAAI,MAAK,CAAG,IAAG,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,IAAI,IAAI;AACtE,CAAC;AAMM,KAAK,CAAC,0BAA0B,GAAG,OAAO,CAAC,GAAG,CAAC,qBAAqB,IACtE,IAAY,GAAa,CAAC;IACzB,EAAE,gBAAgB,IAAI,CAAC,IAAI,GAAG,CAAC;eACtB,uBAAuB,CAAC,IAAI;IACrC,CAAC,MAAM,EAAE,EAAE,IAAI,CAAC,QAAQ,EAAC,CAAG,IAAG,CAAC;eACvB,IAAI;IACb,CAAC,MAAM,CAAC;eACC,IAAI,IAAG,CAAG;IACnB,CAAC;AACH,CAAC,GACD,uBAAuB;QAVd,0BAA0B,GAA1B,0BAA0B"}