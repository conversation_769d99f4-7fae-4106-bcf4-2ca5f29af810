{"version": 3, "sources": ["../../../build/output/log.ts"], "sourcesContent": ["import chalk from 'chalk'\n\nexport const prefixes = {\n  wait: chalk.cyan('wait') + '  -',\n  error: chalk.red('error') + ' -',\n  warn: chalk.yellow('warn') + '  -',\n  ready: chalk.green('ready') + ' -',\n  info: chalk.cyan('info') + '  -',\n  event: chalk.magenta('event') + ' -',\n}\n\nexport function wait(...message: string[]) {\n  console.log(prefixes.wait, ...message)\n}\n\nexport function error(...message: string[]) {\n  console.error(prefixes.error, ...message)\n}\n\nexport function warn(...message: string[]) {\n  console.warn(prefixes.warn, ...message)\n}\n\nexport function ready(...message: string[]) {\n  console.log(prefixes.ready, ...message)\n}\n\nexport function info(...message: string[]) {\n  console.log(prefixes.info, ...message)\n}\n\nexport function event(...message: string[]) {\n  console.log(prefixes.event, ...message)\n}\n"], "names": [], "mappings": ";;;;QAWgB,IAAI,GAAJ,IAAI;QAIJ,KAAK,GAAL,KAAK;QAIL,IAAI,GAAJ,IAAI;QAIJ,KAAK,GAAL,KAAK;QAIL,IAAI,GAAJ,IAAI;QAIJ,KAAK,GAAL,KAAK;;AA/BH,GAAO,CAAP,MAAO;;;;;;AAElB,KAAK,CAAC,QAAQ;IACnB,IAAI,EAHY,MAAO,SAGX,IAAI,EAAC,IAAM,MAAI,GAAK;IAChC,KAAK,EAJW,MAAO,SAIV,GAAG,EAAC,KAAO,MAAI,EAAI;IAChC,IAAI,EALY,MAAO,SAKX,MAAM,EAAC,IAAM,MAAI,GAAK;IAClC,KAAK,EANW,MAAO,SAMV,KAAK,EAAC,KAAO,MAAI,EAAI;IAClC,IAAI,EAPY,MAAO,SAOX,IAAI,EAAC,IAAM,MAAI,GAAK;IAChC,KAAK,EARW,MAAO,SAQV,OAAO,EAAC,KAAO,MAAI,EAAI;;QANzB,QAAQ,GAAR,QAAQ;SASL,IAAI,IAAI,OAAO,EAAY,CAAC;IAC1C,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,KAAK,OAAO;AACvC,CAAC;SAEe,KAAK,IAAI,OAAO,EAAY,CAAC;IAC3C,OAAO,CAAC,KAAK,CAAC,QAAQ,CAAC,KAAK,KAAK,OAAO;AAC1C,CAAC;SAEe,IAAI,IAAI,OAAO,EAAY,CAAC;IAC1C,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,KAAK,OAAO;AACxC,CAAC;SAEe,KAAK,IAAI,OAAO,EAAY,CAAC;IAC3C,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,KAAK,KAAK,OAAO;AACxC,CAAC;SAEe,IAAI,IAAI,OAAO,EAAY,CAAC;IAC1C,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,KAAK,OAAO;AACvC,CAAC;SAEe,KAAK,IAAI,OAAO,EAAY,CAAC;IAC3C,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,KAAK,KAAK,OAAO;AACxC,CAAC"}