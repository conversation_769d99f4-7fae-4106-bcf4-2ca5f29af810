{"version": 3, "sources": ["../../client/link.tsx"], "sourcesContent": ["import React from 'react'\nimport { UrlObject } from 'url'\nimport {\n  addB<PERSON><PERSON><PERSON>,\n  addLocale,\n  getDomainLocale,\n  isLocalURL,\n  NextRouter,\n  PrefetchOptions,\n  resolveHref,\n} from '../shared/lib/router/router'\nimport { useRouter } from './router'\nimport { useIntersection } from './use-intersection'\n\ntype Url = string | UrlObject\ntype RequiredKeys<T> = {\n  [K in keyof T]-?: {} extends Pick<T, K> ? never : K\n}[keyof T]\ntype OptionalKeys<T> = {\n  [K in keyof T]-?: {} extends Pick<T, K> ? K : never\n}[keyof T]\n\nexport type LinkProps = {\n  href: Url\n  as?: Url\n  replace?: boolean\n  scroll?: boolean\n  shallow?: boolean\n  passHref?: boolean\n  prefetch?: boolean\n  locale?: string | false\n}\ntype LinkPropsRequired = RequiredKeys<LinkProps>\ntype LinkPropsOptional = OptionalKeys<LinkProps>\n\nconst prefetched: { [cacheKey: string]: boolean } = {}\n\nfunction prefetch(\n  router: NextRouter,\n  href: string,\n  as: string,\n  options?: PrefetchOptions\n): void {\n  if (typeof window === 'undefined' || !router) return\n  if (!isLocalURL(href)) return\n  // Prefetch the JSON page if asked (only in the client)\n  // We need to handle a prefetch error here since we may be\n  // loading with priority which can reject but we don't\n  // want to force navigation since this is only a prefetch\n  router.prefetch(href, as, options).catch((err) => {\n    if (process.env.NODE_ENV !== 'production') {\n      // rethrow to show invalid URL errors\n      throw err\n    }\n  })\n  const curLocale =\n    options && typeof options.locale !== 'undefined'\n      ? options.locale\n      : router && router.locale\n\n  // Join on an invalid URI character\n  prefetched[href + '%' + as + (curLocale ? '%' + curLocale : '')] = true\n}\n\nfunction isModifiedEvent(event: React.MouseEvent): boolean {\n  const { target } = event.currentTarget as HTMLAnchorElement\n  return (\n    (target && target !== '_self') ||\n    event.metaKey ||\n    event.ctrlKey ||\n    event.shiftKey ||\n    event.altKey || // triggers resource download\n    (event.nativeEvent && event.nativeEvent.which === 2)\n  )\n}\n\nfunction linkClicked(\n  e: React.MouseEvent,\n  router: NextRouter,\n  href: string,\n  as: string,\n  replace?: boolean,\n  shallow?: boolean,\n  scroll?: boolean,\n  locale?: string | false\n): void {\n  const { nodeName } = e.currentTarget\n\n  if (nodeName === 'A' && (isModifiedEvent(e) || !isLocalURL(href))) {\n    // ignore click for browser’s default behavior\n    return\n  }\n\n  e.preventDefault()\n\n  //  avoid scroll for urls with anchor refs\n  if (scroll == null && as.indexOf('#') >= 0) {\n    scroll = false\n  }\n\n  // replace state instead of push if prop is present\n  router[replace ? 'replace' : 'push'](href, as, {\n    shallow,\n    locale,\n    scroll,\n  })\n}\n\nfunction Link(props: React.PropsWithChildren<LinkProps>) {\n  if (process.env.NODE_ENV !== 'production') {\n    function createPropError(args: {\n      key: string\n      expected: string\n      actual: string\n    }) {\n      return new Error(\n        `Failed prop type: The prop \\`${args.key}\\` expects a ${args.expected} in \\`<Link>\\`, but got \\`${args.actual}\\` instead.` +\n          (typeof window !== 'undefined'\n            ? \"\\nOpen your browser's console to view the Component stack trace.\"\n            : '')\n      )\n    }\n\n    // TypeScript trick for type-guarding:\n    const requiredPropsGuard: Record<LinkPropsRequired, true> = {\n      href: true,\n    } as const\n    const requiredProps: LinkPropsRequired[] = Object.keys(\n      requiredPropsGuard\n    ) as LinkPropsRequired[]\n    requiredProps.forEach((key: LinkPropsRequired) => {\n      if (key === 'href') {\n        if (\n          props[key] == null ||\n          (typeof props[key] !== 'string' && typeof props[key] !== 'object')\n        ) {\n          throw createPropError({\n            key,\n            expected: '`string` or `object`',\n            actual: props[key] === null ? 'null' : typeof props[key],\n          })\n        }\n      } else {\n        // TypeScript trick for type-guarding:\n        // eslint-disable-next-line @typescript-eslint/no-unused-vars\n        const _: never = key\n      }\n    })\n\n    // TypeScript trick for type-guarding:\n    const optionalPropsGuard: Record<LinkPropsOptional, true> = {\n      as: true,\n      replace: true,\n      scroll: true,\n      shallow: true,\n      passHref: true,\n      prefetch: true,\n      locale: true,\n    } as const\n    const optionalProps: LinkPropsOptional[] = Object.keys(\n      optionalPropsGuard\n    ) as LinkPropsOptional[]\n    optionalProps.forEach((key: LinkPropsOptional) => {\n      const valType = typeof props[key]\n\n      if (key === 'as') {\n        if (props[key] && valType !== 'string' && valType !== 'object') {\n          throw createPropError({\n            key,\n            expected: '`string` or `object`',\n            actual: valType,\n          })\n        }\n      } else if (key === 'locale') {\n        if (props[key] && valType !== 'string') {\n          throw createPropError({\n            key,\n            expected: '`string`',\n            actual: valType,\n          })\n        }\n      } else if (\n        key === 'replace' ||\n        key === 'scroll' ||\n        key === 'shallow' ||\n        key === 'passHref' ||\n        key === 'prefetch'\n      ) {\n        if (props[key] != null && valType !== 'boolean') {\n          throw createPropError({\n            key,\n            expected: '`boolean`',\n            actual: valType,\n          })\n        }\n      } else {\n        // TypeScript trick for type-guarding:\n        // eslint-disable-next-line @typescript-eslint/no-unused-vars\n        const _: never = key\n      }\n    })\n\n    // This hook is in a conditional but that is ok because `process.env.NODE_ENV` never changes\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n    const hasWarned = React.useRef(false)\n    if (props.prefetch && !hasWarned.current) {\n      hasWarned.current = true\n      console.warn(\n        'Next.js auto-prefetches automatically based on viewport. The prefetch attribute is no longer needed. More: https://nextjs.org/docs/messages/prefetch-true-deprecated'\n      )\n    }\n  }\n  const p = props.prefetch !== false\n  const router = useRouter()\n\n  const { href, as } = React.useMemo(() => {\n    const [resolvedHref, resolvedAs] = resolveHref(router, props.href, true)\n    return {\n      href: resolvedHref,\n      as: props.as ? resolveHref(router, props.as) : resolvedAs || resolvedHref,\n    }\n  }, [router, props.href, props.as])\n\n  let { children, replace, shallow, scroll, locale } = props\n\n  // Deprecated. Warning shown by propType check. If the children provided is a string (<Link>example</Link>) we wrap it in an <a> tag\n  if (typeof children === 'string') {\n    children = <a>{children}</a>\n  }\n\n  // This will return the first child, if multiple are provided it will throw an error\n  let child: any\n  if (process.env.NODE_ENV === 'development') {\n    try {\n      child = React.Children.only(children)\n    } catch (err) {\n      throw new Error(\n        `Multiple children were passed to <Link> with \\`href\\` of \\`${props.href}\\` but only one child is supported https://nextjs.org/docs/messages/link-multiple-children` +\n          (typeof window !== 'undefined'\n            ? \" \\nOpen your browser's console to view the Component stack trace.\"\n            : '')\n      )\n    }\n  } else {\n    child = React.Children.only(children)\n  }\n  const childRef: any = child && typeof child === 'object' && child.ref\n\n  const [setIntersectionRef, isVisible] = useIntersection({\n    rootMargin: '200px',\n  })\n  const setRef = React.useCallback(\n    (el: Element) => {\n      setIntersectionRef(el)\n      if (childRef) {\n        if (typeof childRef === 'function') childRef(el)\n        else if (typeof childRef === 'object') {\n          childRef.current = el\n        }\n      }\n    },\n    [childRef, setIntersectionRef]\n  )\n  React.useEffect(() => {\n    const shouldPrefetch = isVisible && p && isLocalURL(href)\n    const curLocale =\n      typeof locale !== 'undefined' ? locale : router && router.locale\n    const isPrefetched =\n      prefetched[href + '%' + as + (curLocale ? '%' + curLocale : '')]\n    if (shouldPrefetch && !isPrefetched) {\n      prefetch(router, href, as, {\n        locale: curLocale,\n      })\n    }\n  }, [as, href, isVisible, locale, p, router])\n\n  const childProps: {\n    onMouseEnter?: React.MouseEventHandler\n    onClick: React.MouseEventHandler\n    href?: string\n    ref?: any\n  } = {\n    ref: setRef,\n    onClick: (e: React.MouseEvent) => {\n      if (child.props && typeof child.props.onClick === 'function') {\n        child.props.onClick(e)\n      }\n      if (!e.defaultPrevented) {\n        linkClicked(e, router, href, as, replace, shallow, scroll, locale)\n      }\n    },\n  }\n\n  childProps.onMouseEnter = (e: React.MouseEvent) => {\n    if (!isLocalURL(href)) return\n    if (child.props && typeof child.props.onMouseEnter === 'function') {\n      child.props.onMouseEnter(e)\n    }\n    prefetch(router, href, as, { priority: true })\n  }\n\n  // If child is an <a> tag and doesn't have a href attribute, or if the 'passHref' property is\n  // defined, we specify the current 'href', so that repetition is not needed by the user\n  if (props.passHref || (child.type === 'a' && !('href' in child.props))) {\n    const curLocale =\n      typeof locale !== 'undefined' ? locale : router && router.locale\n\n    // we only render domain locales if we are currently on a domain locale\n    // so that locale links are still visitable in development/preview envs\n    const localeDomain =\n      router &&\n      router.isLocaleDomain &&\n      getDomainLocale(\n        as,\n        curLocale,\n        router && router.locales,\n        router && router.domainLocales\n      )\n\n    childProps.href =\n      localeDomain ||\n      addBasePath(addLocale(as, curLocale, router && router.defaultLocale))\n  }\n\n  return React.cloneElement(child, childProps)\n}\n\nexport default Link\n"], "names": [], "mappings": ";;;;;AAAkB,GAAO,CAAP,MAAO;AAUlB,GAA6B,CAA7B,OAA6B;AACV,GAAU,CAAV,QAAU;AACJ,GAAoB,CAApB,gBAAoB;;;;;;AAuBpD,KAAK,CAAC,UAAU;;SAEP,QAAQ,CACf,MAAkB,EAClB,IAAY,EACZ,EAAU,EACV,OAAyB,EACnB,CAAC;IACP,EAAE,SAAS,MAAM,MAAK,SAAW,MAAK,MAAM;IAC5C,EAAE,OAlCG,OAA6B,aAkClB,IAAI;IACpB,EAAuD,AAAvD,qDAAuD;IACvD,EAA0D,AAA1D,wDAA0D;IAC1D,EAAsD,AAAtD,oDAAsD;IACtD,EAAyD,AAAzD,uDAAyD;IACzD,MAAM,CAAC,QAAQ,CAAC,IAAI,EAAE,EAAE,EAAE,OAAO,EAAE,KAAK,EAAE,GAAG,GAAK,CAAC;QACjD,EAAE,EAAE,OAAO,CAAC,GAAG,CAAC,QAAQ,MAAK,UAAY,GAAE,CAAC;YAC1C,EAAqC,AAArC,mCAAqC;YACrC,KAAK,CAAC,GAAG;QACX,CAAC;IACH,CAAC;IACD,KAAK,CAAC,SAAS,GACb,OAAO,WAAW,OAAO,CAAC,MAAM,MAAK,SAAW,IAC5C,OAAO,CAAC,MAAM,GACd,MAAM,IAAI,MAAM,CAAC,MAAM;IAE7B,EAAmC,AAAnC,iCAAmC;IACnC,UAAU,CAAC,IAAI,IAAG,CAAG,IAAG,EAAE,IAAI,SAAS,IAAG,CAAG,IAAG,SAAS,UAAU,IAAI;AACzE,CAAC;SAEQ,eAAe,CAAC,KAAuB,EAAW,CAAC;IAC1D,KAAK,GAAG,MAAM,MAAK,KAAK,CAAC,aAAa;WAEnC,MAAM,IAAI,MAAM,MAAK,KAAO,KAC7B,KAAK,CAAC,OAAO,IACb,KAAK,CAAC,OAAO,IACb,KAAK,CAAC,QAAQ,IACd,KAAK,CAAC,MAAM,IACX,KAAK,CAAC,WAAW,IAAI,KAAK,CAAC,WAAW,CAAC,KAAK,KAAK,CAAC;AAEvD,CAAC;SAEQ,WAAW,CAClB,CAAmB,EACnB,MAAkB,EAClB,IAAY,EACZ,EAAU,EACV,OAAiB,EACjB,OAAiB,EACjB,MAAgB,EAChB,MAAuB,EACjB,CAAC;IACP,KAAK,GAAG,QAAQ,MAAK,CAAC,CAAC,aAAa;IAEpC,EAAE,EAAE,QAAQ,MAAK,CAAG,MAAK,eAAe,CAAC,CAAC,UA9ErC,OAA6B,aA8EyB,IAAI,IAAI,CAAC;QAClE,EAA8C,AAA9C,8CAA8C;;IAEhD,CAAC;IAED,CAAC,CAAC,cAAc;IAEhB,EAA0C,AAA1C,wCAA0C;IAC1C,EAAE,EAAE,MAAM,IAAI,IAAI,IAAI,EAAE,CAAC,OAAO,EAAC,CAAG,MAAK,CAAC,EAAE,CAAC;QAC3C,MAAM,GAAG,KAAK;IAChB,CAAC;IAED,EAAmD,AAAnD,iDAAmD;IACnD,MAAM,CAAC,OAAO,IAAG,OAAS,KAAG,IAAM,GAAE,IAAI,EAAE,EAAE;QAC3C,OAAO;QACP,MAAM;QACN,MAAM;;AAEV,CAAC;SAEQ,IAAI,CAAC,KAAyC,EAAE,CAAC;IACxD,EAAE,EAAE,OAAO,CAAC,GAAG,CAAC,QAAQ,MAAK,UAAY,GAAE,CAAC;iBACjC,eAAe,CAAC,IAIxB,EAAE,CAAC;mBACK,GAAG,CAAC,KAAK,EACb,6BAA6B,EAAE,IAAI,CAAC,GAAG,CAAC,aAAa,EAAE,IAAI,CAAC,QAAQ,CAAC,0BAA0B,EAAE,IAAI,CAAC,MAAM,CAAC,WAAW,YAC/G,MAAM,MAAK,SAAW,KAC1B,gEAAkE;QAG5E,CAAC;QAED,EAAsC,AAAtC,oCAAsC;QACtC,KAAK,CAAC,kBAAkB;YACtB,IAAI,EAAE,IAAI;;QAEZ,KAAK,CAAC,aAAa,GAAwB,MAAM,CAAC,IAAI,CACpD,kBAAkB;QAEpB,aAAa,CAAC,OAAO,EAAE,GAAsB,GAAK,CAAC;YACjD,EAAE,EAAE,GAAG,MAAK,IAAM,GAAE,CAAC;gBACnB,EAAE,EACA,KAAK,CAAC,GAAG,KAAK,IAAI,WACV,KAAK,CAAC,GAAG,OAAM,MAAQ,YAAW,KAAK,CAAC,GAAG,OAAM,MAAQ,GACjE,CAAC;oBACD,KAAK,CAAC,eAAe;wBACnB,GAAG;wBACH,QAAQ,GAAE,oBAAsB;wBAChC,MAAM,EAAE,KAAK,CAAC,GAAG,MAAM,IAAI,IAAG,IAAM,WAAU,KAAK,CAAC,GAAG;;gBAE3D,CAAC;YACH,CAAC,MAAM,CAAC;gBACN,EAAsC,AAAtC,oCAAsC;gBACtC,EAA6D,AAA7D,2DAA6D;gBAC7D,KAAK,CAAC,CAAC,GAAU,GAAG;YACtB,CAAC;QACH,CAAC;QAED,EAAsC,AAAtC,oCAAsC;QACtC,KAAK,CAAC,kBAAkB;YACtB,EAAE,EAAE,IAAI;YACR,OAAO,EAAE,IAAI;YACb,MAAM,EAAE,IAAI;YACZ,OAAO,EAAE,IAAI;YACb,QAAQ,EAAE,IAAI;YACd,QAAQ,EAAE,IAAI;YACd,MAAM,EAAE,IAAI;;QAEd,KAAK,CAAC,aAAa,GAAwB,MAAM,CAAC,IAAI,CACpD,kBAAkB;QAEpB,aAAa,CAAC,OAAO,EAAE,GAAsB,GAAK,CAAC;YACjD,KAAK,CAAC,OAAO,UAAU,KAAK,CAAC,GAAG;YAEhC,EAAE,EAAE,GAAG,MAAK,EAAI,GAAE,CAAC;gBACjB,EAAE,EAAE,KAAK,CAAC,GAAG,KAAK,OAAO,MAAK,MAAQ,KAAI,OAAO,MAAK,MAAQ,GAAE,CAAC;oBAC/D,KAAK,CAAC,eAAe;wBACnB,GAAG;wBACH,QAAQ,GAAE,oBAAsB;wBAChC,MAAM,EAAE,OAAO;;gBAEnB,CAAC;YACH,CAAC,MAAM,EAAE,EAAE,GAAG,MAAK,MAAQ,GAAE,CAAC;gBAC5B,EAAE,EAAE,KAAK,CAAC,GAAG,KAAK,OAAO,MAAK,MAAQ,GAAE,CAAC;oBACvC,KAAK,CAAC,eAAe;wBACnB,GAAG;wBACH,QAAQ,GAAE,QAAU;wBACpB,MAAM,EAAE,OAAO;;gBAEnB,CAAC;YACH,CAAC,MAAM,EAAE,EACP,GAAG,MAAK,OAAS,KACjB,GAAG,MAAK,MAAQ,KAChB,GAAG,MAAK,OAAS,KACjB,GAAG,MAAK,QAAU,KAClB,GAAG,MAAK,QAAU,GAClB,CAAC;gBACD,EAAE,EAAE,KAAK,CAAC,GAAG,KAAK,IAAI,IAAI,OAAO,MAAK,OAAS,GAAE,CAAC;oBAChD,KAAK,CAAC,eAAe;wBACnB,GAAG;wBACH,QAAQ,GAAE,SAAW;wBACrB,MAAM,EAAE,OAAO;;gBAEnB,CAAC;YACH,CAAC,MAAM,CAAC;gBACN,EAAsC,AAAtC,oCAAsC;gBACtC,EAA6D,AAA7D,2DAA6D;gBAC7D,KAAK,CAAC,CAAC,GAAU,GAAG;YACtB,CAAC;QACH,CAAC;QAED,EAA4F,AAA5F,0FAA4F;QAC5F,EAAsD,AAAtD,oDAAsD;QACtD,KAAK,CAAC,SAAS,GA5MD,MAAO,SA4MG,MAAM,CAAC,KAAK;QACpC,EAAE,EAAE,KAAK,CAAC,QAAQ,KAAK,SAAS,CAAC,OAAO,EAAE,CAAC;YACzC,SAAS,CAAC,OAAO,GAAG,IAAI;YACxB,OAAO,CAAC,IAAI,EACV,oKAAsK;QAE1K,CAAC;IACH,CAAC;IACD,KAAK,CAAC,CAAC,GAAG,KAAK,CAAC,QAAQ,KAAK,KAAK;IAClC,KAAK,CAAC,MAAM,OA1MY,QAAU;IA4MlC,KAAK,GAAG,IAAI,GAAE,EAAE,MAvNA,MAAO,SAuNI,OAAO,KAAO,CAAC;QACxC,KAAK,EAAE,YAAY,EAAE,UAAU,QA9M5B,OAA6B,cA8Me,MAAM,EAAE,KAAK,CAAC,IAAI,EAAE,IAAI;;YAErE,IAAI,EAAE,YAAY;YAClB,EAAE,EAAE,KAAK,CAAC,EAAE,OAjNX,OAA6B,cAiNH,MAAM,EAAE,KAAK,CAAC,EAAE,IAAI,UAAU,IAAI,YAAY;;IAE7E,CAAC;QAAG,MAAM;QAAE,KAAK,CAAC,IAAI;QAAE,KAAK,CAAC,EAAE;;IAEhC,GAAG,GAAG,QAAQ,GAAE,OAAO,GAAE,OAAO,GAAE,MAAM,GAAE,MAAM,MAAK,KAAK;IAE1D,EAAoI,AAApI,kIAAoI;IACpI,EAAE,SAAS,QAAQ,MAAK,MAAQ,GAAE,CAAC;QACjC,QAAQ,iBAnOM,MAAO,wBAmOT,CAAC,SAAE,QAAQ;IACzB,CAAC;IAED,EAAoF,AAApF,kFAAoF;IACpF,GAAG,CAAC,KAAK;IACT,EAAE,EAAE,OAAO,CAAC,GAAG,CAAC,QAAQ,MAAK,WAAa,GAAE,CAAC;YACvC,CAAC;YACH,KAAK,GA1OO,MAAO,SA0OL,QAAQ,CAAC,IAAI,CAAC,QAAQ;QACtC,CAAC,QAAQ,GAAG,EAAE,CAAC;YACb,KAAK,CAAC,GAAG,CAAC,KAAK,EACZ,2DAA2D,EAAE,KAAK,CAAC,IAAI,CAAC,0FAA0F,YACzJ,MAAM,MAAK,SAAW,KAC1B,iEAAmE;QAG7E,CAAC;IACH,CAAC,MAAM,CAAC;QACN,KAAK,GApPS,MAAO,SAoPP,QAAQ,CAAC,IAAI,CAAC,QAAQ;IACtC,CAAC;IACD,KAAK,CAAC,QAAQ,GAAQ,KAAK,WAAW,KAAK,MAAK,MAAQ,KAAI,KAAK,CAAC,GAAG;IAErE,KAAK,EAAE,kBAAkB,EAAE,SAAS,QA5ON,gBAAoB;QA6OhD,UAAU,GAAE,KAAO;;IAErB,KAAK,CAAC,MAAM,GA3PI,MAAO,SA2PF,WAAW,EAC7B,EAAW,GAAK,CAAC;QAChB,kBAAkB,CAAC,EAAE;QACrB,EAAE,EAAE,QAAQ,EAAE,CAAC;YACb,EAAE,SAAS,QAAQ,MAAK,QAAU,GAAE,QAAQ,CAAC,EAAE;iBAC1C,EAAE,SAAS,QAAQ,MAAK,MAAQ,GAAE,CAAC;gBACtC,QAAQ,CAAC,OAAO,GAAG,EAAE;YACvB,CAAC;QACH,CAAC;IACH,CAAC;QACA,QAAQ;QAAE,kBAAkB;;IArQf,MAAO,SAuQjB,SAAS,KAAO,CAAC;QACrB,KAAK,CAAC,cAAc,GAAG,SAAS,IAAI,CAAC,QA9PlC,OAA6B,aA8PoB,IAAI;QACxD,KAAK,CAAC,SAAS,UACN,MAAM,MAAK,SAAW,IAAG,MAAM,GAAG,MAAM,IAAI,MAAM,CAAC,MAAM;QAClE,KAAK,CAAC,YAAY,GAChB,UAAU,CAAC,IAAI,IAAG,CAAG,IAAG,EAAE,IAAI,SAAS,IAAG,CAAG,IAAG,SAAS;QAC3D,EAAE,EAAE,cAAc,KAAK,YAAY,EAAE,CAAC;YACpC,QAAQ,CAAC,MAAM,EAAE,IAAI,EAAE,EAAE;gBACvB,MAAM,EAAE,SAAS;;QAErB,CAAC;IACH,CAAC;QAAG,EAAE;QAAE,IAAI;QAAE,SAAS;QAAE,MAAM;QAAE,CAAC;QAAE,MAAM;;IAE1C,KAAK,CAAC,UAAU;QAMd,GAAG,EAAE,MAAM;QACX,OAAO,GAAG,CAAmB,GAAK,CAAC;YACjC,EAAE,EAAE,KAAK,CAAC,KAAK,WAAW,KAAK,CAAC,KAAK,CAAC,OAAO,MAAK,QAAU,GAAE,CAAC;gBAC7D,KAAK,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;YACvB,CAAC;YACD,EAAE,GAAG,CAAC,CAAC,gBAAgB,EAAE,CAAC;gBACxB,WAAW,CAAC,CAAC,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,EAAE,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE,MAAM;YACnE,CAAC;QACH,CAAC;;IAGH,UAAU,CAAC,YAAY,IAAI,CAAmB,GAAK,CAAC;QAClD,EAAE,OA5RC,OAA6B,aA4RhB,IAAI;QACpB,EAAE,EAAE,KAAK,CAAC,KAAK,WAAW,KAAK,CAAC,KAAK,CAAC,YAAY,MAAK,QAAU,GAAE,CAAC;YAClE,KAAK,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC;QAC5B,CAAC;QACD,QAAQ,CAAC,MAAM,EAAE,IAAI,EAAE,EAAE;YAAI,QAAQ,EAAE,IAAI;;IAC7C,CAAC;IAED,EAA6F,AAA7F,2FAA6F;IAC7F,EAAuF,AAAvF,qFAAuF;IACvF,EAAE,EAAE,KAAK,CAAC,QAAQ,IAAK,KAAK,CAAC,IAAI,MAAK,CAAG,QAAM,IAAM,KAAI,KAAK,CAAC,KAAK,GAAI,CAAC;QACvE,KAAK,CAAC,SAAS,UACN,MAAM,MAAK,SAAW,IAAG,MAAM,GAAG,MAAM,IAAI,MAAM,CAAC,MAAM;QAElE,EAAuE,AAAvE,qEAAuE;QACvE,EAAuE,AAAvE,qEAAuE;QACvE,KAAK,CAAC,YAAY,GAChB,MAAM,IACN,MAAM,CAAC,cAAc,QA7SpB,OAA6B,kBA+S5B,EAAE,EACF,SAAS,EACT,MAAM,IAAI,MAAM,CAAC,OAAO,EACxB,MAAM,IAAI,MAAM,CAAC,aAAa;QAGlC,UAAU,CAAC,IAAI,GACb,YAAY,QAtTX,OAA6B,kBAA7B,OAA6B,YAuTR,EAAE,EAAE,SAAS,EAAE,MAAM,IAAI,MAAM,CAAC,aAAa;IACvE,CAAC;yBAlUe,MAAO,SAoUV,YAAY,CAAC,KAAK,EAAE,UAAU;AAC7C,CAAC;eAEc,IAAI"}