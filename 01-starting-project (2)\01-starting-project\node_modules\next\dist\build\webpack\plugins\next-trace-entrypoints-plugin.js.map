{"version": 3, "sources": ["../../../../build/webpack/plugins/next-trace-entrypoints-plugin.ts"], "sourcesContent": ["import nodePath from 'path'\nimport { nodeFileTrace } from 'next/dist/compiled/@vercel/nft'\nimport {\n  webpack,\n  isWebpack5,\n  sources,\n} from 'next/dist/compiled/webpack/webpack'\nimport { TRACE_OUTPUT_VERSION } from '../../../shared/lib/constants'\n\nconst PLUGIN_NAME = 'TraceEntryPointsPlugin'\nconst TRACE_IGNORES = [\n  '**/*/node_modules/react/**/*.development.js',\n  '**/*/node_modules/react-dom/**/*.development.js',\n]\n\nfunction getModuleFromDependency(\n  compilation: any,\n  dep: any\n): webpack.Module & { resource?: string } {\n  if (isWebpack5) {\n    return compilation.moduleGraph.getModule(dep)\n  }\n\n  return dep.module\n}\n\nexport class TraceEntryPointsPlugin implements webpack.Plugin {\n  private appDir: string\n  private entryTraces: Map<string, string[]>\n  private excludeFiles: string[]\n\n  constructor({\n    appDir,\n    excludeFiles,\n  }: {\n    appDir: string\n    excludeFiles?: string[]\n  }) {\n    this.appDir = appDir\n    this.entryTraces = new Map()\n    this.excludeFiles = excludeFiles || []\n  }\n\n  // Here we output all traced assets and webpack chunks to a\n  // ${page}.js.nft.json file\n  createTraceAssets(compilation: any, assets: any) {\n    const outputPath = compilation.outputOptions.path\n\n    for (const entrypoint of compilation.entrypoints.values()) {\n      const entryFiles = new Set<string>()\n\n      for (const chunk of entrypoint\n        .getEntrypointChunk()\n        .getAllReferencedChunks()) {\n        for (const file of chunk.files) {\n          entryFiles.add(nodePath.join(outputPath, file))\n        }\n        for (const file of chunk.auxiliaryFiles) {\n          entryFiles.add(nodePath.join(outputPath, file))\n        }\n      }\n      // don't include the entry itself in the trace\n      entryFiles.delete(\n        nodePath.join(\n          outputPath,\n          `${isWebpack5 ? '../' : ''}${entrypoint.name}.js`\n        )\n      )\n      const traceOutputName = `${isWebpack5 ? '../' : ''}${\n        entrypoint.name\n      }.js.nft.json`\n      const traceOutputPath = nodePath.dirname(\n        nodePath.join(outputPath, traceOutputName)\n      )\n\n      assets[traceOutputName] = new sources.RawSource(\n        JSON.stringify({\n          version: TRACE_OUTPUT_VERSION,\n          files: [...entryFiles, ...this.entryTraces.get(entrypoint.name)!].map(\n            (file) => {\n              return nodePath\n                .relative(traceOutputPath, file)\n                .replace(/\\\\/g, '/')\n            }\n          ),\n        })\n      )\n    }\n  }\n\n  apply(compiler: webpack.Compiler) {\n    if (isWebpack5) {\n      compiler.hooks.compilation.tap(PLUGIN_NAME, (compilation) => {\n        // @ts-ignore TODO: Remove ignore when webpack 5 is stable\n        compilation.hooks.processAssets.tap(\n          {\n            name: PLUGIN_NAME,\n            // @ts-ignore TODO: Remove ignore when webpack 5 is stable\n            stage: webpack.Compilation.PROCESS_ASSETS_STAGE_SUMMARIZE,\n          },\n          (assets: any) => {\n            this.createTraceAssets(compilation, assets)\n          }\n        )\n      })\n    } else {\n      compiler.hooks.emit.tap(PLUGIN_NAME, (compilation: any) => {\n        this.createTraceAssets(compilation, compilation.assets)\n      })\n    }\n\n    compiler.hooks.compilation.tap(PLUGIN_NAME, (compilation) => {\n      compilation.hooks.finishModules.tapAsync(\n        PLUGIN_NAME,\n        async (_stats: any, callback: any) => {\n          // we create entry -> module maps so that we can\n          // look them up faster instead of having to iterate\n          // over the compilation modules list\n          const entryNameMap = new Map<string, string>()\n          const entryModMap = new Map<string, any>()\n\n          try {\n            const depModMap = new Map<string, any>()\n\n            compilation.entries.forEach((entry) => {\n              const name = entry.name || entry.options?.name\n\n              if (name?.startsWith('pages/') && entry.dependencies[0]) {\n                const entryMod = getModuleFromDependency(\n                  compilation,\n                  entry.dependencies[0]\n                )\n\n                if (entryMod.resource) {\n                  entryNameMap.set(entryMod.resource, name)\n                  entryModMap.set(entryMod.resource, entryMod)\n                }\n              }\n            })\n\n            // TODO: investigate allowing non-sync fs calls in node-file-trace\n            // for better performance\n            const readFile = (path: string) => {\n              const mod = depModMap.get(path) || entryModMap.get(path)\n\n              // map the transpiled source when available to avoid\n              // parse errors in node-file-trace\n              const source = mod?.originalSource?.()\n\n              if (source) {\n                return source.buffer()\n              }\n\n              try {\n                return compilation.inputFileSystem.readFileSync(path)\n              } catch (e) {\n                if (e.code === 'ENOENT' || e.code === 'EISDIR') {\n                  return null\n                }\n                throw e\n              }\n            }\n            const readlink = (path: string) => {\n              try {\n                return compilation.inputFileSystem.readlinkSync(path)\n              } catch (e) {\n                if (\n                  e.code !== 'EINVAL' &&\n                  e.code !== 'ENOENT' &&\n                  e.code !== 'UNKNOWN'\n                ) {\n                  throw e\n                }\n                return null\n              }\n            }\n            const stat = (path: string) => {\n              try {\n                return compilation.inputFileSystem.statSync(path)\n              } catch (e) {\n                if (e.code === 'ENOENT') {\n                  return null\n                }\n                throw e\n              }\n            }\n\n            const nftCache = {}\n            const entryPaths = Array.from(entryModMap.keys())\n\n            for (const entry of entryPaths) {\n              depModMap.clear()\n              const entryMod = entryModMap.get(entry)\n              // TODO: investigate caching, will require ensuring no traced\n              // files in the cache have changed, we could potentially hash\n              // all traced files and only leverage the cache if the hashes\n              // match\n              // const cachedTraces = entryMod.buildInfo?.cachedNextEntryTrace\n\n              // Use cached trace if available and trace version matches\n              // if (\n              //   isWebpack5 &&\n              //   cachedTraces &&\n              //   cachedTraces.version === TRACE_OUTPUT_VERSION\n              // ) {\n              //   this.entryTraces.set(\n              //     entryNameMap.get(entry)!,\n              //     cachedTraces.tracedDeps\n              //   )\n              //   continue\n              // }\n              const collectDependencies = (mod: any) => {\n                if (!mod || !mod.dependencies) return\n\n                for (const dep of mod.dependencies) {\n                  const depMod = getModuleFromDependency(compilation, dep)\n\n                  if (depMod?.resource && !depModMap.get(depMod.resource)) {\n                    depModMap.set(depMod.resource, depMod)\n                    collectDependencies(depMod)\n                  }\n                }\n              }\n              collectDependencies(entryMod)\n\n              const toTrace: string[] = [entry, ...depModMap.keys()]\n\n              const root = nodePath.parse(process.cwd()).root\n              const result = await nodeFileTrace(toTrace, {\n                base: root,\n                cache: nftCache,\n                processCwd: this.appDir,\n                readFile,\n                readlink,\n                stat,\n                ignore: [...TRACE_IGNORES, ...this.excludeFiles],\n              })\n\n              const tracedDeps: string[] = []\n\n              for (const file of result.fileList) {\n                if (result.reasons[file].type === 'initial') {\n                  continue\n                }\n                tracedDeps.push(nodePath.join(root, file))\n              }\n\n              // entryMod.buildInfo.cachedNextEntryTrace = {\n              //   version: TRACE_OUTPUT_VERSION,\n              //   tracedDeps,\n              // }\n              this.entryTraces.set(entryNameMap.get(entry)!, tracedDeps)\n            }\n\n            callback()\n          } catch (err) {\n            callback(err)\n          }\n        }\n      )\n    })\n  }\n}\n"], "names": [], "mappings": ";;;;AAAqB,GAAM,CAAN,KAAM;AACG,GAAgC,CAAhC,IAAgC;AAKvD,GAAoC,CAApC,QAAoC;AACN,GAA+B,CAA/B,UAA+B;;;;;;AAEpE,KAAK,CAAC,WAAW,IAAG,sBAAwB;AAC5C,KAAK,CAAC,aAAa;KACjB,2CAA6C;KAC7C,+CAAiD;;SAG1C,uBAAuB,CAC9B,WAAgB,EAChB,GAAQ,EACgC,CAAC;IACzC,EAAE,EAbG,QAAoC,aAazB,CAAC;eACR,WAAW,CAAC,WAAW,CAAC,SAAS,CAAC,GAAG;IAC9C,CAAC;WAEM,GAAG,CAAC,MAAM;AACnB,CAAC;MAEY,sBAAsB;kBAM/B,MAAM,GACN,YAAY,IAIX,CAAC;aACG,MAAM,GAAG,MAAM;aACf,WAAW,GAAG,GAAG,CAAC,GAAG;aACrB,YAAY,GAAG,YAAY;IAClC,CAAC;IAED,EAA2D,AAA3D,yDAA2D;IAC3D,EAA2B,AAA3B,yBAA2B;IAC3B,iBAAiB,CAAC,WAAgB,EAAE,MAAW,EAAE,CAAC;QAChD,KAAK,CAAC,UAAU,GAAG,WAAW,CAAC,aAAa,CAAC,IAAI;aAE5C,KAAK,CAAC,UAAU,IAAI,WAAW,CAAC,WAAW,CAAC,MAAM,GAAI,CAAC;YAC1D,KAAK,CAAC,UAAU,GAAG,GAAG,CAAC,GAAG;iBAErB,KAAK,CAAC,KAAK,IAAI,UAAU,CAC3B,kBAAkB,GAClB,sBAAsB,GAAI,CAAC;qBACvB,KAAK,CAAC,IAAI,IAAI,KAAK,CAAC,KAAK,CAAE,CAAC;oBAC/B,UAAU,CAAC,GAAG,CAvDH,KAAM,SAuDO,IAAI,CAAC,UAAU,EAAE,IAAI;gBAC/C,CAAC;qBACI,KAAK,CAAC,KAAI,IAAI,KAAK,CAAC,cAAc,CAAE,CAAC;oBACxC,UAAU,CAAC,GAAG,CA1DH,KAAM,SA0DO,IAAI,CAAC,UAAU,EAAE,KAAI;gBAC/C,CAAC;YACH,CAAC;YACD,EAA8C,AAA9C,4CAA8C;YAC9C,UAAU,CAAC,MAAM,CA9DF,KAAM,SA+DV,IAAI,CACX,UAAU,KA1Db,QAAoC,eA2DjB,GAAK,SAAQ,UAAU,CAAC,IAAI,CAAC,GAAG;YAGpD,KAAK,CAAC,eAAe,MA9DpB,QAAoC,eA8DG,GAAK,SAC3C,UAAU,CAAC,IAAI,CAChB,YAAY;YACb,KAAK,CAAC,eAAe,GAvEN,KAAM,SAuEY,OAAO,CAvEzB,KAAM,SAwEV,IAAI,CAAC,UAAU,EAAE,eAAe;YAG3C,MAAM,CAAC,eAAe,IAAI,GAAG,CArE5B,QAAoC,SAqEC,SAAS,CAC7C,IAAI,CAAC,SAAS;gBACZ,OAAO,EAtEoB,UAA+B;gBAuE1D,KAAK;uBAAM,UAAU;4BAAU,WAAW,CAAC,GAAG,CAAC,UAAU,CAAC,IAAI;kBAAI,GAAG,EAClE,IAAI,GAAK,CAAC;2BA/EF,KAAM,SAiFV,QAAQ,CAAC,eAAe,EAAE,IAAI,EAC9B,OAAO,SAAQ,CAAG;gBACvB,CAAC;;QAIT,CAAC;IACH,CAAC;IAED,KAAK,CAAC,QAA0B,EAAE,CAAC;QACjC,EAAE,EArFC,QAAoC,aAqFvB,CAAC;YACf,QAAQ,CAAC,KAAK,CAAC,WAAW,CAAC,GAAG,CAAC,WAAW,GAAG,WAAW,GAAK,CAAC;gBAC5D,EAA0D,AAA1D,wDAA0D;gBAC1D,WAAW,CAAC,KAAK,CAAC,aAAa,CAAC,GAAG;oBAE/B,IAAI,EAAE,WAAW;oBACjB,EAA0D,AAA1D,wDAA0D;oBAC1D,KAAK,EA5FV,QAAoC,SA4FhB,WAAW,CAAC,8BAA8B;oBAE1D,MAAW,GAAK,CAAC;yBACX,iBAAiB,CAAC,WAAW,EAAE,MAAM;gBAC5C,CAAC;YAEL,CAAC;QACH,CAAC,MAAM,CAAC;YACN,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,WAAW,GAAG,WAAgB,GAAK,CAAC;qBACrD,iBAAiB,CAAC,WAAW,EAAE,WAAW,CAAC,MAAM;YACxD,CAAC;QACH,CAAC;QAED,QAAQ,CAAC,KAAK,CAAC,WAAW,CAAC,GAAG,CAAC,WAAW,GAAG,WAAW,GAAK,CAAC;YAC5D,WAAW,CAAC,KAAK,CAAC,aAAa,CAAC,QAAQ,CACtC,WAAW,SACJ,MAAW,EAAE,QAAa,GAAK,CAAC;gBACrC,EAAgD,AAAhD,8CAAgD;gBAChD,EAAmD,AAAnD,iDAAmD;gBACnD,EAAoC,AAApC,kCAAoC;gBACpC,KAAK,CAAC,YAAY,GAAG,GAAG,CAAC,GAAG;gBAC5B,KAAK,CAAC,WAAW,GAAG,GAAG,CAAC,GAAG;oBAEvB,CAAC;oBACH,KAAK,CAAC,SAAS,GAAG,GAAG,CAAC,GAAG;oBAEzB,WAAW,CAAC,OAAO,CAAC,OAAO,EAAE,KAAK,GAAK,CAAC;4BACX,GAAa;wBAAxC,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI,MAAI,GAAa,GAAb,KAAK,CAAC,OAAO,cAAb,GAAa,UAAb,CAAmB,QAAnB,CAAmB,GAAnB,GAAa,CAAE,IAAI;wBAE9C,EAAE,GAAE,IAAI,aAAJ,IAAI,UAAJ,CAAgB,QAAhB,CAAgB,GAAhB,IAAI,CAAE,UAAU,EAAC,MAAQ,OAAK,KAAK,CAAC,YAAY,CAAC,CAAC,GAAG,CAAC;4BACxD,KAAK,CAAC,QAAQ,GAAG,uBAAuB,CACtC,WAAW,EACX,KAAK,CAAC,YAAY,CAAC,CAAC;4BAGtB,EAAE,EAAE,QAAQ,CAAC,QAAQ,EAAE,CAAC;gCACtB,YAAY,CAAC,GAAG,CAAC,QAAQ,CAAC,QAAQ,EAAE,IAAI;gCACxC,WAAW,CAAC,GAAG,CAAC,QAAQ,CAAC,QAAQ,EAAE,QAAQ;4BAC7C,CAAC;wBACH,CAAC;oBACH,CAAC;oBAED,EAAkE,AAAlE,gEAAkE;oBAClE,EAAyB,AAAzB,uBAAyB;oBACzB,KAAK,CAAC,QAAQ,IAAI,IAAY,GAAK,CAAC;4BAKnB,GAAmB;wBAJlC,KAAK,CAAC,GAAG,GAAG,SAAS,CAAC,GAAG,CAAC,IAAI,KAAK,WAAW,CAAC,GAAG,CAAC,IAAI;wBAEvD,EAAoD,AAApD,kDAAoD;wBACpD,EAAkC,AAAlC,gCAAkC;wBAClC,KAAK,CAAC,MAAM,GAAG,GAAG,aAAH,GAAG,UAAH,CAAmB,QAAnB,CAAmB,IAAnB,GAAmB,GAAnB,GAAG,CAAE,cAAc,cAAnB,GAAmB,UAAnB,CAAuB,QAAvB,CAAuB,GAAvB,GAAmB,CAAnB,IAAuB,CAAvB,GAAG;wBAElB,EAAE,EAAE,MAAM,EAAE,CAAC;mCACJ,MAAM,CAAC,MAAM;wBACtB,CAAC;4BAEG,CAAC;mCACI,WAAW,CAAC,eAAe,CAAC,YAAY,CAAC,IAAI;wBACtD,CAAC,QAAQ,CAAC,EAAE,CAAC;4BACX,EAAE,EAAE,CAAC,CAAC,IAAI,MAAK,MAAQ,KAAI,CAAC,CAAC,IAAI,MAAK,MAAQ,GAAE,CAAC;uCACxC,IAAI;4BACb,CAAC;4BACD,KAAK,CAAC,CAAC;wBACT,CAAC;oBACH,CAAC;oBACD,KAAK,CAAC,QAAQ,IAAI,IAAY,GAAK,CAAC;4BAC9B,CAAC;mCACI,WAAW,CAAC,eAAe,CAAC,YAAY,CAAC,IAAI;wBACtD,CAAC,QAAQ,CAAC,EAAE,CAAC;4BACX,EAAE,EACA,CAAC,CAAC,IAAI,MAAK,MAAQ,KACnB,CAAC,CAAC,IAAI,MAAK,MAAQ,KACnB,CAAC,CAAC,IAAI,MAAK,OAAS,GACpB,CAAC;gCACD,KAAK,CAAC,CAAC;4BACT,CAAC;mCACM,IAAI;wBACb,CAAC;oBACH,CAAC;oBACD,KAAK,CAAC,IAAI,IAAI,IAAY,GAAK,CAAC;4BAC1B,CAAC;mCACI,WAAW,CAAC,eAAe,CAAC,QAAQ,CAAC,IAAI;wBAClD,CAAC,QAAQ,CAAC,EAAE,CAAC;4BACX,EAAE,EAAE,CAAC,CAAC,IAAI,MAAK,MAAQ,GAAE,CAAC;uCACjB,IAAI;4BACb,CAAC;4BACD,KAAK,CAAC,CAAC;wBACT,CAAC;oBACH,CAAC;oBAED,KAAK,CAAC,QAAQ;;oBACd,KAAK,CAAC,UAAU,GAAG,KAAK,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI;yBAEzC,KAAK,CAAC,KAAK,IAAI,UAAU,CAAE,CAAC;wBAC/B,SAAS,CAAC,KAAK;wBACf,KAAK,CAAC,QAAQ,GAAG,WAAW,CAAC,GAAG,CAAC,KAAK;wBACtC,EAA6D,AAA7D,2DAA6D;wBAC7D,EAA6D,AAA7D,2DAA6D;wBAC7D,EAA6D,AAA7D,2DAA6D;wBAC7D,EAAQ,AAAR,MAAQ;wBACR,EAAgE,AAAhE,8DAAgE;wBAEhE,EAA0D,AAA1D,wDAA0D;wBAC1D,EAAO,AAAP,KAAO;wBACP,EAAkB,AAAlB,gBAAkB;wBAClB,EAAoB,AAApB,kBAAoB;wBACpB,EAAkD,AAAlD,gDAAkD;wBAClD,EAAM,AAAN,IAAM;wBACN,EAA0B,AAA1B,wBAA0B;wBAC1B,EAAgC,AAAhC,8BAAgC;wBAChC,EAA8B,AAA9B,4BAA8B;wBAC9B,EAAM,AAAN,IAAM;wBACN,EAAa,AAAb,WAAa;wBACb,EAAI,AAAJ,EAAI;wBACJ,KAAK,CAAC,mBAAmB,IAAI,GAAQ,GAAK,CAAC;4BACzC,EAAE,GAAG,GAAG,KAAK,GAAG,CAAC,YAAY;iCAExB,KAAK,CAAC,GAAG,IAAI,GAAG,CAAC,YAAY,CAAE,CAAC;gCACnC,KAAK,CAAC,MAAM,GAAG,uBAAuB,CAAC,WAAW,EAAE,GAAG;gCAEvD,EAAE,GAAE,MAAM,aAAN,MAAM,UAAN,CAAgB,QAAhB,CAAgB,GAAhB,MAAM,CAAE,QAAQ,MAAK,SAAS,CAAC,GAAG,CAAC,MAAM,CAAC,QAAQ,GAAG,CAAC;oCACxD,SAAS,CAAC,GAAG,CAAC,MAAM,CAAC,QAAQ,EAAE,MAAM;oCACrC,mBAAmB,CAAC,MAAM;gCAC5B,CAAC;4BACH,CAAC;wBACH,CAAC;wBACD,mBAAmB,CAAC,QAAQ;wBAE5B,KAAK,CAAC,OAAO;4BAAc,KAAK;+BAAK,SAAS,CAAC,IAAI;;wBAEnD,KAAK,CAAC,IAAI,GAnOH,KAAM,SAmOS,KAAK,CAAC,OAAO,CAAC,GAAG,IAAI,IAAI;wBAC/C,KAAK,CAAC,MAAM,aAnOI,IAAgC,gBAmOb,OAAO;4BACxC,IAAI,EAAE,IAAI;4BACV,KAAK,EAAE,QAAQ;4BACf,UAAU,OAAO,MAAM;4BACvB,QAAQ;4BACR,QAAQ;4BACR,IAAI;4BACJ,MAAM;mCAAM,aAAa;wCAAU,YAAY;;;wBAGjD,KAAK,CAAC,UAAU;6BAEX,KAAK,CAAC,IAAI,IAAI,MAAM,CAAC,QAAQ,CAAE,CAAC;4BACnC,EAAE,EAAE,MAAM,CAAC,OAAO,CAAC,IAAI,EAAE,IAAI,MAAK,OAAS,GAAE,CAAC;;4BAE9C,CAAC;4BACD,UAAU,CAAC,IAAI,CApPV,KAAM,SAoPc,IAAI,CAAC,IAAI,EAAE,IAAI;wBAC1C,CAAC;wBAED,EAA8C,AAA9C,4CAA8C;wBAC9C,EAAmC,AAAnC,iCAAmC;wBACnC,EAAgB,AAAhB,cAAgB;wBAChB,EAAI,AAAJ,EAAI;6BACC,WAAW,CAAC,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,KAAK,GAAI,UAAU;oBAC3D,CAAC;oBAED,QAAQ;gBACV,CAAC,QAAQ,GAAG,EAAE,CAAC;oBACb,QAAQ,CAAC,GAAG;gBACd,CAAC;YACH,CAAC;QAEL,CAAC;IACH,CAAC;;QA3OU,sBAAsB,GAAtB,sBAAsB"}