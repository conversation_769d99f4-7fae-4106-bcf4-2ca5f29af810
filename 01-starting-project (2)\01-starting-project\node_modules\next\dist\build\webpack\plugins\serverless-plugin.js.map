{"version": 3, "sources": ["../../../../build/webpack/plugins/serverless-plugin.ts"], "sourcesContent": ["import { webpack } from 'next/dist/compiled/webpack/webpack'\nimport { isWebpack5, GraphHelpers } from 'next/dist/compiled/webpack/webpack'\n\n/**\n * Makes sure there are no dynamic chunks when the target is serverless\n * The dynamic chunks are integrated back into their parent chunk\n * This is to make sure there is a single render bundle instead of that bundle importing dynamic chunks\n */\n\nexport class ServerlessPlugin {\n  apply(compiler: webpack.Compiler) {\n    compiler.hooks.compilation.tap('ServerlessPlugin', (compilation) => {\n      const hook = isWebpack5\n        ? compilation.hooks.optimizeChunks\n        : compilation.hooks.optimizeChunksBasic\n\n      hook.tap('ServerlessPlugin', (chunks) => {\n        for (const chunk of chunks) {\n          // If chunk is not an entry point skip them\n          if (!chunk.hasEntryModule()) {\n            continue\n          }\n\n          // Async chunks are usages of import() for example\n          const dynamicChunks = chunk.getAllAsyncChunks()\n          for (const dynamicChunk of dynamicChunks) {\n            if (isWebpack5) {\n              // @ts-ignore TODO: Remove ignore when webpack 5 is stable\n              for (const module of compilation.chunkGraph.getChunkModulesIterable(\n                chunk\n              )) {\n                // Add module back into the entry chunk\n                chunk.addModule(module)\n              }\n              continue\n            }\n\n            for (const module of dynamicChunk.modulesIterable) {\n              // Webpack 4 has separate GraphHelpers\n              GraphHelpers.connectChunkAndModule(chunk, module)\n            }\n          }\n        }\n      })\n    })\n  }\n}\n"], "names": [], "mappings": ";;;;AACyC,GAAoC,CAApC,QAAoC;MAQhE,gBAAgB;IAC3B,KAAK,CAAC,QAA0B,EAAE,CAAC;QACjC,QAAQ,CAAC,KAAK,CAAC,WAAW,CAAC,GAAG,EAAC,gBAAkB,IAAG,WAAW,GAAK,CAAC;YACnE,KAAK,CAAC,IAAI,GAXyB,QAAoC,cAYnE,WAAW,CAAC,KAAK,CAAC,cAAc,GAChC,WAAW,CAAC,KAAK,CAAC,mBAAmB;YAEzC,IAAI,CAAC,GAAG,EAAC,gBAAkB,IAAG,MAAM,GAAK,CAAC;qBACnC,KAAK,CAAC,KAAK,IAAI,MAAM,CAAE,CAAC;oBAC3B,EAA2C,AAA3C,yCAA2C;oBAC3C,EAAE,GAAG,KAAK,CAAC,cAAc,IAAI,CAAC;;oBAE9B,CAAC;oBAED,EAAkD,AAAlD,gDAAkD;oBAClD,KAAK,CAAC,aAAa,GAAG,KAAK,CAAC,iBAAiB;yBACxC,KAAK,CAAC,YAAY,IAAI,aAAa,CAAE,CAAC;wBACzC,EAAE,EAzB2B,QAAoC,aAyBjD,CAAC;4BACf,EAA0D,AAA1D,wDAA0D;iCACrD,KAAK,CAAC,MAAM,IAAI,WAAW,CAAC,UAAU,CAAC,uBAAuB,CACjE,KAAK,EACJ,CAAC;gCACF,EAAuC,AAAvC,qCAAuC;gCACvC,KAAK,CAAC,SAAS,CAAC,MAAM;4BACxB,CAAC;;wBAEH,CAAC;6BAEI,KAAK,CAAC,MAAM,IAAI,YAAY,CAAC,eAAe,CAAE,CAAC;4BAClD,EAAsC,AAAtC,oCAAsC;4BArCX,QAAoC,cAsClD,qBAAqB,CAAC,KAAK,EAAE,MAAM;wBAClD,CAAC;oBACH,CAAC;gBACH,CAAC;YACH,CAAC;QACH,CAAC;IACH,CAAC;;QApCU,gBAAgB,GAAhB,gBAAgB"}