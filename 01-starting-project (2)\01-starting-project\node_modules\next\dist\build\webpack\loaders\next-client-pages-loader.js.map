{"version": 3, "sources": ["../../../../build/webpack/loaders/next-client-pages-loader.ts"], "sourcesContent": ["import loaderUtils from 'next/dist/compiled/loader-utils'\n\nexport type ClientPagesLoaderOptions = {\n  absolutePagePath: string\n  page: string\n}\n\n// this parameter: https://www.typescriptlang.org/docs/handbook/functions.html#this-parameters\nfunction nextClientPagesLoader(this: any) {\n  const pagesLoaderSpan = this.currentTraceSpan.traceChild(\n    'next-client-pages-loader'\n  )\n\n  return pagesLoaderSpan.traceFn(() => {\n    const { absolutePagePath, page } = loaderUtils.getOptions(\n      this\n    ) as ClientPagesLoaderOptions\n\n    pagesLoaderSpan.setAttribute('absolutePagePath', absolutePagePath)\n\n    const stringifiedAbsolutePagePath = JSON.stringify(absolutePagePath)\n    const stringifiedPage = JSON.stringify(page)\n\n    return `\n    (window.__NEXT_P = window.__NEXT_P || []).push([\n      ${stringifiedPage},\n      function () {\n        return require(${stringifiedAbsolutePagePath});\n      }\n    ]);\n  `\n  })\n}\n\nexport default nextClientPagesLoader\n"], "names": [], "mappings": ";;;;;AAAwB,GAAiC,CAAjC,YAAiC;;;;;;AAOzD,EAA8F,AAA9F,4FAA8F;SACrF,qBAAqB,GAAY,CAAC;IACzC,KAAK,CAAC,eAAe,QAAQ,gBAAgB,CAAC,UAAU,EACtD,wBAA0B;WAGrB,eAAe,CAAC,OAAO,KAAO,CAAC;QACpC,KAAK,GAAG,gBAAgB,GAAE,IAAI,MAdV,YAAiC,SAcN,UAAU;QAIzD,eAAe,CAAC,YAAY,EAAC,gBAAkB,GAAE,gBAAgB;QAEjE,KAAK,CAAC,2BAA2B,GAAG,IAAI,CAAC,SAAS,CAAC,gBAAgB;QACnE,KAAK,CAAC,eAAe,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI;gBAEnC,4DAEN,EAAE,eAAe,CAAC,6CAED,EAAE,2BAA2B,CAAC,qBAGnD;IACA,CAAC;AACH,CAAC;eAEc,qBAAqB"}