{"version": 3, "sources": ["../../../../telemetry/trace/report/to-telemetry.ts"], "sourcesContent": ["import { traceGlobals } from '../shared'\n\nconst TRACE_EVENT_ACCESSLIST = new Map(\n  Object.entries({\n    'webpack-invalidated': 'WEBPACK_INVALIDATED',\n  })\n)\n\nconst reportToTelemetry = (spanName: string, duration: number) => {\n  const eventName = TRACE_EVENT_ACCESSLIST.get(spanName)\n  if (!eventName) {\n    return\n  }\n  const telemetry = traceGlobals.get('telemetry')\n  if (!telemetry) {\n    return\n  }\n\n  telemetry.record({\n    eventName,\n    payload: {\n      durationInMicroseconds: duration,\n    },\n  })\n}\n\nexport default {\n  flushAll: () => {},\n  report: reportToTelemetry,\n}\n"], "names": [], "mappings": ";;;;;AAA6B,GAAW,CAAX,OAAW;AAExC,KAAK,CAAC,sBAAsB,GAAG,GAAG,CAAC,GAAG,CACpC,MAAM,CAAC,OAAO;KACZ,mBAAqB,IAAE,mBAAqB;;AAIhD,KAAK,CAAC,iBAAiB,IAAI,QAAgB,EAAE,QAAgB,GAAK,CAAC;IACjE,KAAK,CAAC,SAAS,GAAG,sBAAsB,CAAC,GAAG,CAAC,QAAQ;IACrD,EAAE,GAAG,SAAS,EAAE,CAAC;;IAEjB,CAAC;IACD,KAAK,CAAC,SAAS,GAbY,OAAW,cAaP,GAAG,EAAC,SAAW;IAC9C,EAAE,GAAG,SAAS,EAAE,CAAC;;IAEjB,CAAC;IAED,SAAS,CAAC,MAAM;QACd,SAAS;QACT,OAAO;YACL,sBAAsB,EAAE,QAAQ;;;AAGtC,CAAC;;IAGC,QAAQ,MAAQ,CAAC;IAAA,CAAC;IAClB,MAAM,EAAE,iBAAiB"}