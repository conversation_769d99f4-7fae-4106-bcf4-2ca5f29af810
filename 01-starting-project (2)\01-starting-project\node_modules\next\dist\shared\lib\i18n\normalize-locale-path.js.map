{"version": 3, "sources": ["../../../../shared/lib/i18n/normalize-locale-path.ts"], "sourcesContent": ["export interface PathLocale {\n  detectedLocale?: string\n  pathname: string\n}\n\n/**\n * For a pathname that may include a locale from a list of locales, it\n * removes the locale from the pathname returning it alongside with the\n * detected locale.\n *\n * @param pathname A pathname that may include a locale.\n * @param locales A list of locales.\n * @returns The detected locale and pathname without locale\n */\nexport function normalizeLocalePath(\n  pathname: string,\n  locales?: string[]\n): PathLocale {\n  let detectedLocale: string | undefined\n  // first item will be empty string from splitting at first char\n  const pathnameParts = pathname.split('/')\n\n  ;(locales || []).some((locale) => {\n    if (pathnameParts[1].toLowerCase() === locale.toLowerCase()) {\n      detectedLocale = locale\n      pathnameParts.splice(1, 1)\n      pathname = pathnameParts.join('/') || '/'\n      return true\n    }\n    return false\n  })\n\n  return {\n    pathname,\n    detectedLocale,\n  }\n}\n"], "names": [], "mappings": ";;;;QAcgB,mBAAmB,GAAnB,mBAAmB;SAAnB,mBAAmB,CACjC,QAAgB,EAChB,OAAkB,EACN,CAAC;IACb,GAAG,CAAC,cAAc;IAClB,EAA+D,AAA/D,6DAA+D;IAC/D,KAAK,CAAC,aAAa,GAAG,QAAQ,CAAC,KAAK,EAAC,CAAG;KAEtC,OAAO,QAAQ,IAAI,EAAE,MAAM,GAAK,CAAC;QACjC,EAAE,EAAE,aAAa,CAAC,CAAC,EAAE,WAAW,OAAO,MAAM,CAAC,WAAW,IAAI,CAAC;YAC5D,cAAc,GAAG,MAAM;YACvB,aAAa,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC;YACzB,QAAQ,GAAG,aAAa,CAAC,IAAI,EAAC,CAAG,OAAK,CAAG;mBAClC,IAAI;QACb,CAAC;eACM,KAAK;IACd,CAAC;;QAGC,QAAQ;QACR,cAAc;;AAElB,CAAC"}