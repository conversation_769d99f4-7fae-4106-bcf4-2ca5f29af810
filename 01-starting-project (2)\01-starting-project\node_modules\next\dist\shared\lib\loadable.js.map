{"version": 3, "sources": ["../../../shared/lib/loadable.js"], "sourcesContent": ["/**\n@copyright (c) 2017-present <PERSON> <<EMAIL>>\n MIT License\n Permission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n The above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE\n*/\n// https://github.com/jamiebuilds/react-loadable/blob/v5.5.0/src/index.js\n// Modified to be compatible with webpack 4 / Next.js\n\nimport React from 'react'\nimport { useSubscription } from 'use-subscription'\nimport { LoadableContext } from './loadable-context'\n\nconst ALL_INITIALIZERS = []\nconst READY_INITIALIZERS = []\nlet initialized = false\n\nfunction load(loader) {\n  let promise = loader()\n\n  let state = {\n    loading: true,\n    loaded: null,\n    error: null,\n  }\n\n  state.promise = promise\n    .then((loaded) => {\n      state.loading = false\n      state.loaded = loaded\n      return loaded\n    })\n    .catch((err) => {\n      state.loading = false\n      state.error = err\n      throw err\n    })\n\n  return state\n}\n\nfunction resolve(obj) {\n  return obj && obj.__esModule ? obj.default : obj\n}\n\nfunction createLoadableComponent(loadFn, options) {\n  let opts = Object.assign(\n    {\n      loader: null,\n      loading: null,\n      delay: 200,\n      timeout: null,\n      webpack: null,\n      modules: null,\n      suspense: false,\n    },\n    options\n  )\n\n  if (opts.suspense) {\n    opts.lazy = React.lazy(opts.loader)\n  }\n\n  let subscription = null\n  function init() {\n    if (!subscription) {\n      const sub = new LoadableSubscription(loadFn, opts)\n      subscription = {\n        getCurrentValue: sub.getCurrentValue.bind(sub),\n        subscribe: sub.subscribe.bind(sub),\n        retry: sub.retry.bind(sub),\n        promise: sub.promise.bind(sub),\n      }\n    }\n    return subscription.promise()\n  }\n\n  // Server only\n  if (typeof window === 'undefined' && !opts.suspense) {\n    ALL_INITIALIZERS.push(init)\n  }\n\n  // Client only\n  if (\n    !initialized &&\n    typeof window !== 'undefined' &&\n    typeof opts.webpack === 'function' &&\n    typeof require.resolveWeak === 'function' &&\n    !opts.suspense\n  ) {\n    const moduleIds = opts.webpack()\n    READY_INITIALIZERS.push((ids) => {\n      for (const moduleId of moduleIds) {\n        if (ids.indexOf(moduleId) !== -1) {\n          return init()\n        }\n      }\n    })\n  }\n\n  function LoadableImpl(props, ref) {\n    init()\n\n    const context = React.useContext(LoadableContext)\n    const state = useSubscription(subscription)\n    React.useImperativeHandle(\n      ref,\n      () => ({\n        retry: subscription.retry,\n      }),\n      []\n    )\n\n    if (context && Array.isArray(opts.modules)) {\n      opts.modules.forEach((moduleName) => {\n        context(moduleName)\n      })\n    }\n\n    return React.useMemo(() => {\n      if (state.loading || state.error) {\n        return React.createElement(opts.loading, {\n          isLoading: state.loading,\n          pastDelay: state.pastDelay,\n          timedOut: state.timedOut,\n          error: state.error,\n          retry: subscription.retry,\n        })\n      } else if (state.loaded) {\n        return React.createElement(resolve(state.loaded), props)\n      } else {\n        return null\n      }\n    }, [props, state])\n  }\n\n  function LazyImpl(props, ref) {\n    return React.createElement(opts.lazy, { ...props, ref })\n  }\n\n  const LoadableComponent = opts.suspense ? LazyImpl : LoadableImpl\n  LoadableComponent.preload = () => !opts.suspense && init()\n  LoadableComponent.displayName = 'LoadableComponent'\n\n  return React.forwardRef(LoadableComponent)\n}\n\nclass LoadableSubscription {\n  constructor(loadFn, opts) {\n    this._loadFn = loadFn\n    this._opts = opts\n    this._callbacks = new Set()\n    this._delay = null\n    this._timeout = null\n\n    this.retry()\n  }\n\n  promise() {\n    return this._res.promise\n  }\n\n  retry() {\n    this._clearTimeouts()\n    this._res = this._loadFn(this._opts.loader)\n\n    this._state = {\n      pastDelay: false,\n      timedOut: false,\n    }\n\n    const { _res: res, _opts: opts } = this\n\n    if (res.loading) {\n      if (typeof opts.delay === 'number') {\n        if (opts.delay === 0) {\n          this._state.pastDelay = true\n        } else {\n          this._delay = setTimeout(() => {\n            this._update({\n              pastDelay: true,\n            })\n          }, opts.delay)\n        }\n      }\n\n      if (typeof opts.timeout === 'number') {\n        this._timeout = setTimeout(() => {\n          this._update({ timedOut: true })\n        }, opts.timeout)\n      }\n    }\n\n    this._res.promise\n      .then(() => {\n        this._update({})\n        this._clearTimeouts()\n      })\n      .catch((_err) => {\n        this._update({})\n        this._clearTimeouts()\n      })\n    this._update({})\n  }\n\n  _update(partial) {\n    this._state = {\n      ...this._state,\n      error: this._res.error,\n      loaded: this._res.loaded,\n      loading: this._res.loading,\n      ...partial,\n    }\n    this._callbacks.forEach((callback) => callback())\n  }\n\n  _clearTimeouts() {\n    clearTimeout(this._delay)\n    clearTimeout(this._timeout)\n  }\n\n  getCurrentValue() {\n    return this._state\n  }\n\n  subscribe(callback) {\n    this._callbacks.add(callback)\n    return () => {\n      this._callbacks.delete(callback)\n    }\n  }\n}\n\nfunction Loadable(opts) {\n  return createLoadableComponent(load, opts)\n}\n\nfunction flushInitializers(initializers, ids) {\n  let promises = []\n\n  while (initializers.length) {\n    let init = initializers.pop()\n    promises.push(init(ids))\n  }\n\n  return Promise.all(promises).then(() => {\n    if (initializers.length) {\n      return flushInitializers(initializers, ids)\n    }\n  })\n}\n\nLoadable.preloadAll = () => {\n  return new Promise((resolveInitializers, reject) => {\n    flushInitializers(ALL_INITIALIZERS).then(resolveInitializers, reject)\n  })\n}\n\nLoadable.preloadReady = (ids = []) => {\n  return new Promise((resolvePreload) => {\n    const res = () => {\n      initialized = true\n      return resolvePreload()\n    }\n    // We always will resolve, errors should be handled within loading UIs.\n    flushInitializers(READY_INITIALIZERS, ids).then(res, res)\n  })\n}\n\nif (typeof window !== 'undefined') {\n  window.__NEXT_PRELOADREADY = Loadable.preloadReady\n}\n\nexport default Loadable\n"], "names": [], "mappings": ";;;;;AAuBkB,GAAO,CAAP,MAAO;AACO,GAAkB,CAAlB,gBAAkB;AAClB,GAAoB,CAApB,gBAAoB;;;;;;AAEpD,KAAK,CAAC,gBAAgB;AACtB,KAAK,CAAC,kBAAkB;AACxB,GAAG,CAAC,WAAW,GAAG,KAAK;SAEd,IAAI,CAAC,MAAM,EAAE,CAAC;IACrB,GAAG,CAAC,OAAO,GAAG,MAAM;IAEpB,GAAG,CAAC,KAAK;QACP,OAAO,EAAE,IAAI;QACb,MAAM,EAAE,IAAI;QACZ,KAAK,EAAE,IAAI;;IAGb,KAAK,CAAC,OAAO,GAAG,OAAO,CACpB,IAAI,EAAE,MAAM,GAAK,CAAC;QACjB,KAAK,CAAC,OAAO,GAAG,KAAK;QACrB,KAAK,CAAC,MAAM,GAAG,MAAM;eACd,MAAM;IACf,CAAC,EACA,KAAK,EAAE,GAAG,GAAK,CAAC;QACf,KAAK,CAAC,OAAO,GAAG,KAAK;QACrB,KAAK,CAAC,KAAK,GAAG,GAAG;QACjB,KAAK,CAAC,GAAG;IACX,CAAC;WAEI,KAAK;AACd,CAAC;SAEQ,OAAO,CAAC,GAAG,EAAE,CAAC;WACd,GAAG,IAAI,GAAG,CAAC,UAAU,GAAG,GAAG,CAAC,OAAO,GAAG,GAAG;AAClD,CAAC;SAEQ,uBAAuB,CAAC,MAAM,EAAE,OAAO,EAAE,CAAC;IACjD,GAAG,CAAC,IAAI,GAAG,MAAM,CAAC,MAAM;QAEpB,MAAM,EAAE,IAAI;QACZ,OAAO,EAAE,IAAI;QACb,KAAK,EAAE,GAAG;QACV,OAAO,EAAE,IAAI;QACb,OAAO,EAAE,IAAI;QACb,OAAO,EAAE,IAAI;QACb,QAAQ,EAAE,KAAK;OAEjB,OAAO;IAGT,EAAE,EAAE,IAAI,CAAC,QAAQ,EAAE,CAAC;QAClB,IAAI,CAAC,IAAI,GAnDK,MAAO,SAmDH,IAAI,CAAC,IAAI,CAAC,MAAM;IACpC,CAAC;IAED,GAAG,CAAC,YAAY,GAAG,IAAI;aACd,IAAI,GAAG,CAAC;QACf,EAAE,GAAG,YAAY,EAAE,CAAC;YAClB,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,oBAAoB,CAAC,MAAM,EAAE,IAAI;YACjD,YAAY;gBACV,eAAe,EAAE,GAAG,CAAC,eAAe,CAAC,IAAI,CAAC,GAAG;gBAC7C,SAAS,EAAE,GAAG,CAAC,SAAS,CAAC,IAAI,CAAC,GAAG;gBACjC,KAAK,EAAE,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG;gBACzB,OAAO,EAAE,GAAG,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG;;QAEjC,CAAC;eACM,YAAY,CAAC,OAAO;IAC7B,CAAC;IAED,EAAc,AAAd,YAAc;IACd,EAAE,SAAS,MAAM,MAAK,SAAW,MAAK,IAAI,CAAC,QAAQ,EAAE,CAAC;QACpD,gBAAgB,CAAC,IAAI,CAAC,IAAI;IAC5B,CAAC;IAED,EAAc,AAAd,YAAc;IACd,EAAE,GACC,WAAW,WACL,MAAM,MAAK,SAAW,YACtB,IAAI,CAAC,OAAO,MAAK,QAAU,YAC3B,OAAO,CAAC,WAAW,MAAK,QAAU,MACxC,IAAI,CAAC,QAAQ,EACd,CAAC;QACD,KAAK,CAAC,SAAS,GAAG,IAAI,CAAC,OAAO;QAC9B,kBAAkB,CAAC,IAAI,EAAE,GAAG,GAAK,CAAC;iBAC3B,KAAK,CAAC,QAAQ,IAAI,SAAS,CAAE,CAAC;gBACjC,EAAE,EAAE,GAAG,CAAC,OAAO,CAAC,QAAQ,OAAO,CAAC,EAAE,CAAC;2BAC1B,IAAI;gBACb,CAAC;YACH,CAAC;QACH,CAAC;IACH,CAAC;aAEQ,YAAY,CAAC,KAAK,EAAE,GAAG,EAAE,CAAC;QACjC,IAAI;QAEJ,KAAK,CAAC,OAAO,GA9FC,MAAO,SA8FC,UAAU,CA5FJ,gBAAoB;QA6FhD,KAAK,CAAC,KAAK,OA9FiB,gBAAkB,kBA8FhB,YAAY;QA/F5B,MAAO,SAgGf,mBAAmB,CACvB,GAAG;gBAED,KAAK,EAAE,YAAY,CAAC,KAAK;;;QAK7B,EAAE,EAAE,OAAO,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,GAAG,CAAC;YAC3C,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,UAAU,GAAK,CAAC;gBACpC,OAAO,CAAC,UAAU;YACpB,CAAC;QACH,CAAC;eA5Ga,MAAO,SA8GR,OAAO,KAAO,CAAC;YAC1B,EAAE,EAAE,KAAK,CAAC,OAAO,IAAI,KAAK,CAAC,KAAK,EAAE,CAAC;uBA/GvB,MAAO,SAgHJ,aAAa,CAAC,IAAI,CAAC,OAAO;oBACrC,SAAS,EAAE,KAAK,CAAC,OAAO;oBACxB,SAAS,EAAE,KAAK,CAAC,SAAS;oBAC1B,QAAQ,EAAE,KAAK,CAAC,QAAQ;oBACxB,KAAK,EAAE,KAAK,CAAC,KAAK;oBAClB,KAAK,EAAE,YAAY,CAAC,KAAK;;YAE7B,CAAC,MAAM,EAAE,EAAE,KAAK,CAAC,MAAM,EAAE,CAAC;uBAvHd,MAAO,SAwHJ,aAAa,CAAC,OAAO,CAAC,KAAK,CAAC,MAAM,GAAG,KAAK;YACzD,CAAC,MAAM,CAAC;uBACC,IAAI;YACb,CAAC;QACH,CAAC;YAAG,KAAK;YAAE,KAAK;;IAClB,CAAC;aAEQ,QAAQ,CAAC,KAAK,EAAE,GAAG,EAAE,CAAC;eA/Hf,MAAO,SAgIR,aAAa,CAAC,IAAI,CAAC,IAAI;eAAO,KAAK;YAAE,GAAG;;IACvD,CAAC;IAED,KAAK,CAAC,iBAAiB,GAAG,IAAI,CAAC,QAAQ,GAAG,QAAQ,GAAG,YAAY;IACjE,iBAAiB,CAAC,OAAO,QAAU,IAAI,CAAC,QAAQ,IAAI,IAAI;;IACxD,iBAAiB,CAAC,WAAW,IAAG,iBAAmB;WArInC,MAAO,SAuIV,UAAU,CAAC,iBAAiB;AAC3C,CAAC;MAEK,oBAAoB;gBACZ,MAAM,EAAE,IAAI,CAAE,CAAC;aACpB,OAAO,GAAG,MAAM;aAChB,KAAK,GAAG,IAAI;aACZ,UAAU,GAAG,GAAG,CAAC,GAAG;aACpB,MAAM,GAAG,IAAI;aACb,QAAQ,GAAG,IAAI;aAEf,KAAK;IACZ,CAAC;IAED,OAAO,GAAG,CAAC;oBACG,IAAI,CAAC,OAAO;IAC1B,CAAC;IAED,KAAK,GAAG,CAAC;aACF,cAAc;aACd,IAAI,QAAQ,OAAO,MAAM,KAAK,CAAC,MAAM;aAErC,MAAM;YACT,SAAS,EAAE,KAAK;YAChB,QAAQ,EAAE,KAAK;;QAGjB,KAAK,GAAG,IAAI,EAAE,GAAG,GAAE,KAAK,EAAE,KAAI;QAE9B,EAAE,EAAE,GAAG,CAAC,OAAO,EAAE,CAAC;YAChB,EAAE,SAAS,KAAI,CAAC,KAAK,MAAK,MAAQ,GAAE,CAAC;gBACnC,EAAE,EAAE,KAAI,CAAC,KAAK,KAAK,CAAC,EAAE,CAAC;yBAChB,MAAM,CAAC,SAAS,GAAG,IAAI;gBAC9B,CAAC,MAAM,CAAC;yBACD,MAAM,GAAG,UAAU,KAAO,CAAC;6BACzB,OAAO;4BACV,SAAS,EAAE,IAAI;;oBAEnB,CAAC,EAAE,KAAI,CAAC,KAAK;gBACf,CAAC;YACH,CAAC;YAED,EAAE,SAAS,KAAI,CAAC,OAAO,MAAK,MAAQ,GAAE,CAAC;qBAChC,QAAQ,GAAG,UAAU,KAAO,CAAC;yBAC3B,OAAO;wBAAG,QAAQ,EAAE,IAAI;;gBAC/B,CAAC,EAAE,KAAI,CAAC,OAAO;YACjB,CAAC;QACH,CAAC;aAEI,IAAI,CAAC,OAAO,CACd,IAAI,KAAO,CAAC;iBACN,OAAO;;iBACP,cAAc;QACrB,CAAC,EACA,KAAK,EAAE,IAAI,GAAK,CAAC;iBACX,OAAO;;iBACP,cAAc;QACrB,CAAC;aACE,OAAO;;IACd,CAAC;IAED,OAAO,CAAC,OAAO,EAAE,CAAC;aACX,MAAM;oBACD,MAAM;YACd,KAAK,OAAO,IAAI,CAAC,KAAK;YACtB,MAAM,OAAO,IAAI,CAAC,MAAM;YACxB,OAAO,OAAO,IAAI,CAAC,OAAO;eACvB,OAAO;;aAEP,UAAU,CAAC,OAAO,EAAE,QAAQ,GAAK,QAAQ;;IAChD,CAAC;IAED,cAAc,GAAG,CAAC;QAChB,YAAY,MAAM,MAAM;QACxB,YAAY,MAAM,QAAQ;IAC5B,CAAC;IAED,eAAe,GAAG,CAAC;oBACL,MAAM;IACpB,CAAC;IAED,SAAS,CAAC,QAAQ,EAAE,CAAC;aACd,UAAU,CAAC,GAAG,CAAC,QAAQ;mBACf,CAAC;iBACP,UAAU,CAAC,MAAM,CAAC,QAAQ;QACjC,CAAC;IACH,CAAC;;SAGM,QAAQ,CAAC,KAAI,EAAE,CAAC;WAChB,uBAAuB,CAAC,IAAI,EAAE,KAAI;AAC3C,CAAC;SAEQ,iBAAiB,CAAC,YAAY,EAAE,GAAG,EAAE,CAAC;IAC7C,GAAG,CAAC,QAAQ;UAEL,YAAY,CAAC,MAAM,CAAE,CAAC;QAC3B,GAAG,CAAC,IAAI,GAAG,YAAY,CAAC,GAAG;QAC3B,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG;IACxB,CAAC;WAEM,OAAO,CAAC,GAAG,CAAC,QAAQ,EAAE,IAAI,KAAO,CAAC;QACvC,EAAE,EAAE,YAAY,CAAC,MAAM,EAAE,CAAC;mBACjB,iBAAiB,CAAC,YAAY,EAAE,GAAG;QAC5C,CAAC;IACH,CAAC;AACH,CAAC;AAED,QAAQ,CAAC,UAAU,OAAS,CAAC;WACpB,GAAG,CAAC,OAAO,EAAE,mBAAmB,EAAE,MAAM,GAAK,CAAC;QACnD,iBAAiB,CAAC,gBAAgB,EAAE,IAAI,CAAC,mBAAmB,EAAE,MAAM;IACtE,CAAC;AACH,CAAC;AAED,QAAQ,CAAC,YAAY,IAAI,GAAG,QAAU,CAAC;WAC9B,GAAG,CAAC,OAAO,EAAE,cAAc,GAAK,CAAC;QACtC,KAAK,CAAC,GAAG,OAAS,CAAC;YACjB,WAAW,GAAG,IAAI;mBACX,cAAc;QACvB,CAAC;QACD,EAAuE,AAAvE,qEAAuE;QACvE,iBAAiB,CAAC,kBAAkB,EAAE,GAAG,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG;IAC1D,CAAC;AACH,CAAC;AAED,EAAE,SAAS,MAAM,MAAK,SAAW,GAAE,CAAC;IAClC,MAAM,CAAC,mBAAmB,GAAG,QAAQ,CAAC,YAAY;AACpD,CAAC;eAEc,QAAQ"}