{"version": 3, "sources": ["../../server/normalize-page-path.ts"], "sourcesContent": ["import { posix } from 'path'\n\nexport { normalizePathSep, denormalizePagePath } from './denormalize-page-path'\n\nexport function normalizePagePath(page: string): string {\n  // If the page is `/` we need to append `/index`, otherwise the returned directory root will be bundles instead of pages\n  if (page === '/') {\n    page = '/index'\n  } else if (/^\\/index(\\/|$)/.test(page)) {\n    page = `/index${page}`\n  }\n  // Resolve on anything that doesn't start with `/`\n  if (!page.startsWith('/')) {\n    page = `/${page}`\n  }\n  // Throw when using ../ etc in the pathname\n  const resolvedPage = posix.normalize(page)\n  if (page !== resolvedPage) {\n    throw new Error(\n      `Requested and resolved page mismatch: ${page} ${resolvedPage}`\n    )\n  }\n  return page\n}\n"], "names": [], "mappings": ";;;;gCAES,gBAAgB;;;oCAAhB,gBAAgB;;;gCAAE,mBAAmB;;;oCAAnB,mBAAmB;;;QAE9B,iBAAiB,GAAjB,iBAAiB;AAJX,GAAM,CAAN,KAAM;;SAIZ,iBAAiB,CAAC,IAAY,EAAU,CAAC;IACvD,EAAwH,AAAxH,sHAAwH;IACxH,EAAE,EAAE,IAAI,MAAK,CAAG,GAAE,CAAC;QACjB,IAAI,IAAG,MAAQ;IACjB,CAAC,MAAM,EAAE,mBAAmB,IAAI,CAAC,IAAI,GAAG,CAAC;QACvC,IAAI,IAAI,MAAM,EAAE,IAAI;IACtB,CAAC;IACD,EAAkD,AAAlD,gDAAkD;IAClD,EAAE,GAAG,IAAI,CAAC,UAAU,EAAC,CAAG,IAAG,CAAC;QAC1B,IAAI,IAAI,CAAC,EAAE,IAAI;IACjB,CAAC;IACD,EAA2C,AAA3C,yCAA2C;IAC3C,KAAK,CAAC,YAAY,GAhBE,KAAM,OAgBC,SAAS,CAAC,IAAI;IACzC,EAAE,EAAE,IAAI,KAAK,YAAY,EAAE,CAAC;QAC1B,KAAK,CAAC,GAAG,CAAC,KAAK,EACZ,sCAAsC,EAAE,IAAI,CAAC,CAAC,EAAE,YAAY;IAEjE,CAAC;WACM,IAAI;AACb,CAAC"}