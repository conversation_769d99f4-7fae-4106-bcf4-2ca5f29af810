{"version": 3, "sources": ["../../../../../shared/lib/router/utils/route-matcher.ts"], "sourcesContent": ["import { DecodeError } from '../../utils'\nimport { getRouteRegex } from './route-regex'\n\nexport function getRouteMatcher(routeRegex: ReturnType<typeof getRouteRegex>) {\n  const { re, groups } = routeRegex\n  return (pathname: string | null | undefined) => {\n    const routeMatch = re.exec(pathname!)\n    if (!routeMatch) {\n      return false\n    }\n\n    const decode = (param: string) => {\n      try {\n        return decodeURIComponent(param)\n      } catch (_) {\n        throw new DecodeError('failed to decode param')\n      }\n    }\n    const params: { [paramName: string]: string | string[] } = {}\n\n    Object.keys(groups).forEach((slugName: string) => {\n      const g = groups[slugName]\n      const m = routeMatch[g.pos]\n      if (m !== undefined) {\n        params[slugName] = ~m.indexOf('/')\n          ? m.split('/').map((entry) => decode(entry))\n          : g.repeat\n          ? [decode(m)]\n          : decode(m)\n      }\n    })\n    return params\n  }\n}\n"], "names": [], "mappings": ";;;;QAGgB,eAAe,GAAf,eAAe;AAHH,GAAa,CAAb,MAAa;SAGzB,eAAe,CAAC,UAA4C,EAAE,CAAC;IAC7E,KAAK,GAAG,EAAE,GAAE,MAAM,MAAK,UAAU;YACzB,QAAmC,GAAK,CAAC;QAC/C,KAAK,CAAC,UAAU,GAAG,EAAE,CAAC,IAAI,CAAC,QAAQ;QACnC,EAAE,GAAG,UAAU,EAAE,CAAC;mBACT,KAAK;QACd,CAAC;QAED,KAAK,CAAC,MAAM,IAAI,KAAa,GAAK,CAAC;gBAC7B,CAAC;uBACI,kBAAkB,CAAC,KAAK;YACjC,CAAC,QAAQ,CAAC,EAAE,CAAC;gBACX,KAAK,CAAC,GAAG,CAfW,MAAa,cAeX,sBAAwB;YAChD,CAAC;QACH,CAAC;QACD,KAAK,CAAC,MAAM;;QAEZ,MAAM,CAAC,IAAI,CAAC,MAAM,EAAE,OAAO,EAAE,QAAgB,GAAK,CAAC;YACjD,KAAK,CAAC,CAAC,GAAG,MAAM,CAAC,QAAQ;YACzB,KAAK,CAAC,CAAC,GAAG,UAAU,CAAC,CAAC,CAAC,GAAG;YAC1B,EAAE,EAAE,CAAC,KAAK,SAAS,EAAE,CAAC;gBACpB,MAAM,CAAC,QAAQ,KAAK,CAAC,CAAC,OAAO,EAAC,CAAG,KAC7B,CAAC,CAAC,KAAK,EAAC,CAAG,GAAE,GAAG,EAAE,KAAK,GAAK,MAAM,CAAC,KAAK;oBACxC,CAAC,CAAC,MAAM;oBACP,MAAM,CAAC,CAAC;oBACT,MAAM,CAAC,CAAC;YACd,CAAC;QACH,CAAC;eACM,MAAM;IACf,CAAC;AACH,CAAC"}