{"version": 3, "sources": ["../../../shared/lib/to-base-64.ts"], "sourcesContent": ["/**\n * Isomorphic base64 that works on the server and client\n */\nexport function toBase64(str: string) {\n  if (typeof window === 'undefined') {\n    return Buffer.from(str).toString('base64')\n  } else {\n    return window.btoa(str)\n  }\n}\n"], "names": [], "mappings": ";;;;QAGgB,QAAQ,GAAR,QAAQ;SAAR,QAAQ,CAAC,GAAW,EAAE,CAAC;IACrC,EAAE,SAAS,MAAM,MAAK,SAAW,GAAE,CAAC;eAC3B,MAAM,CAAC,IAAI,CAAC,GAAG,EAAE,QAAQ,EAAC,MAAQ;IAC3C,CAAC,MAAM,CAAC;eACC,MAAM,CAAC,IAAI,CAAC,GAAG;IACxB,CAAC;AACH,CAAC"}