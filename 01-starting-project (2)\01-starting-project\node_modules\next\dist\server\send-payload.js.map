{"version": 3, "sources": ["../../server/send-payload.ts"], "sourcesContent": ["import { IncomingMessage, ServerResponse } from 'http'\nimport { isResSent } from '../shared/lib/utils'\nimport generateETag from 'etag'\nimport fresh from 'next/dist/compiled/fresh'\nimport { RenderResult } from './utils'\n\nexport type PayloadOptions =\n  | { private: true }\n  | { private: boolean; stateful: true }\n  | { private: boolean; stateful: false; revalidate: number | false }\n\nexport function setRevalidateHeaders(\n  res: ServerResponse,\n  options: PayloadOptions\n) {\n  if (options.private || options.stateful) {\n    if (options.private || !res.hasHeader('Cache-Control')) {\n      res.setHeader(\n        'Cache-Control',\n        `private, no-cache, no-store, max-age=0, must-revalidate`\n      )\n    }\n  } else if (typeof options.revalidate === 'number') {\n    if (options.revalidate < 1) {\n      throw new Error(\n        `invariant: invalid Cache-Control duration provided: ${options.revalidate} < 1`\n      )\n    }\n\n    res.setHeader(\n      'Cache-Control',\n      `s-maxage=${options.revalidate}, stale-while-revalidate`\n    )\n  } else if (options.revalidate === false) {\n    res.setHeader('Cache-Control', `s-maxage=31536000, stale-while-revalidate`)\n  }\n}\n\nexport function sendPayload(\n  req: IncomingMessage,\n  res: ServerResponse,\n  payload: any,\n  type: 'html' | 'json',\n  {\n    generateEtags,\n    poweredByHeader,\n  }: { generateEtags: boolean; poweredByHeader: boolean },\n  options?: PayloadOptions\n): void {\n  sendRenderResult({\n    req,\n    res,\n    resultOrPayload: payload,\n    type,\n    generateEtags,\n    poweredByHeader,\n    options,\n  })\n}\n\nexport async function sendRenderResult({\n  req,\n  res,\n  resultOrPayload,\n  type,\n  generateEtags,\n  poweredByHeader,\n  options,\n}: {\n  req: IncomingMessage\n  res: ServerResponse\n  resultOrPayload: RenderResult | string\n  type: 'html' | 'json'\n  generateEtags: boolean\n  poweredByHeader: boolean\n  options?: PayloadOptions\n}): Promise<void> {\n  if (isResSent(res)) {\n    return\n  }\n\n  if (poweredByHeader && type === 'html') {\n    res.setHeader('X-Powered-By', 'Next.js')\n  }\n\n  const isPayload = typeof resultOrPayload === 'string'\n\n  if (isPayload) {\n    const etag = generateEtags\n      ? generateETag(resultOrPayload as string)\n      : undefined\n    if (sendEtagResponse(req, res, etag)) {\n      return\n    }\n  }\n\n  if (!res.getHeader('Content-Type')) {\n    res.setHeader(\n      'Content-Type',\n      type === 'json' ? 'application/json' : 'text/html; charset=utf-8'\n    )\n  }\n\n  if (isPayload) {\n    res.setHeader(\n      'Content-Length',\n      Buffer.byteLength(resultOrPayload as string)\n    )\n  }\n\n  if (options != null) {\n    setRevalidateHeaders(res, options)\n  }\n\n  if (req.method === 'HEAD') {\n    res.end(null)\n  } else if (isPayload) {\n    res.end(resultOrPayload as string)\n  } else {\n    const maybeFlush =\n      typeof (res as any).flush === 'function'\n        ? () => (res as any).flush()\n        : () => {}\n    await (resultOrPayload as RenderResult).forEach((chunk) => {\n      res.write(chunk)\n      maybeFlush()\n    })\n    res.end()\n  }\n}\n\nexport function sendEtagResponse(\n  req: IncomingMessage,\n  res: ServerResponse,\n  etag: string | undefined\n): boolean {\n  if (etag) {\n    /**\n     * The server generating a 304 response MUST generate any of the\n     * following header fields that would have been sent in a 200 (OK)\n     * response to the same request: Cache-Control, Content-Location, Date,\n     * ETag, Expires, and Vary. https://tools.ietf.org/html/rfc7232#section-4.1\n     */\n    res.setHeader('ETag', etag)\n  }\n\n  if (fresh(req.headers, { etag })) {\n    res.statusCode = 304\n    res.end()\n    return true\n  }\n\n  return false\n}\n"], "names": [], "mappings": ";;;;QAWgB,oBAAoB,GAApB,oBAAoB;QA2BpB,WAAW,GAAX,WAAW;QAsBL,gBAAgB,GAAhB,gBAAgB;QAuEtB,gBAAgB,GAAhB,gBAAgB;AAlIN,GAAqB,CAArB,MAAqB;AACtB,GAAM,CAAN,KAAM;AACb,GAA0B,CAA1B,MAA0B;;;;;;SAQ5B,oBAAoB,CAClC,GAAmB,EACnB,OAAuB,EACvB,CAAC;IACD,EAAE,EAAE,OAAO,CAAC,OAAO,IAAI,OAAO,CAAC,QAAQ,EAAE,CAAC;QACxC,EAAE,EAAE,OAAO,CAAC,OAAO,KAAK,GAAG,CAAC,SAAS,EAAC,aAAe,IAAG,CAAC;YACvD,GAAG,CAAC,SAAS,EACX,aAAe,IACd,uDAAuD;QAE5D,CAAC;IACH,CAAC,MAAM,EAAE,SAAS,OAAO,CAAC,UAAU,MAAK,MAAQ,GAAE,CAAC;QAClD,EAAE,EAAE,OAAO,CAAC,UAAU,GAAG,CAAC,EAAE,CAAC;YAC3B,KAAK,CAAC,GAAG,CAAC,KAAK,EACZ,oDAAoD,EAAE,OAAO,CAAC,UAAU,CAAC,IAAI;QAElF,CAAC;QAED,GAAG,CAAC,SAAS,EACX,aAAe,IACd,SAAS,EAAE,OAAO,CAAC,UAAU,CAAC,wBAAwB;IAE3D,CAAC,MAAM,EAAE,EAAE,OAAO,CAAC,UAAU,KAAK,KAAK,EAAE,CAAC;QACxC,GAAG,CAAC,SAAS,EAAC,aAAe,IAAG,yCAAyC;IAC3E,CAAC;AACH,CAAC;SAEe,WAAW,CACzB,GAAoB,EACpB,GAAmB,EACnB,OAAY,EACZ,IAAqB,IAEnB,aAAa,GACb,eAAe,KAEjB,OAAwB,EAClB,CAAC;IACP,gBAAgB;QACd,GAAG;QACH,GAAG;QACH,eAAe,EAAE,OAAO;QACxB,IAAI;QACJ,aAAa;QACb,eAAe;QACf,OAAO;;AAEX,CAAC;eAEqB,gBAAgB,GACpC,GAAG,GACH,GAAG,GACH,eAAe,GACf,IAAI,GACJ,aAAa,GACb,eAAe,GACf,OAAO,KASS,CAAC;IACjB,EAAE,MA5EsB,MAAqB,YA4E/B,GAAG,GAAG,CAAC;;IAErB,CAAC;IAED,EAAE,EAAE,eAAe,IAAI,IAAI,MAAK,IAAM,GAAE,CAAC;QACvC,GAAG,CAAC,SAAS,EAAC,YAAc,IAAE,OAAS;IACzC,CAAC;IAED,KAAK,CAAC,SAAS,UAAU,eAAe,MAAK,MAAQ;IAErD,EAAE,EAAE,SAAS,EAAE,CAAC;QACd,KAAK,CAAC,IAAI,GAAG,aAAa,OAtFL,KAAM,UAuFV,eAAe,IAC5B,SAAS;QACb,EAAE,EAAE,gBAAgB,CAAC,GAAG,EAAE,GAAG,EAAE,IAAI,GAAG,CAAC;;QAEvC,CAAC;IACH,CAAC;IAED,EAAE,GAAG,GAAG,CAAC,SAAS,EAAC,YAAc,IAAG,CAAC;QACnC,GAAG,CAAC,SAAS,EACX,YAAc,GACd,IAAI,MAAK,IAAM,KAAG,gBAAkB,KAAG,wBAA0B;IAErE,CAAC;IAED,EAAE,EAAE,SAAS,EAAE,CAAC;QACd,GAAG,CAAC,SAAS,EACX,cAAgB,GAChB,MAAM,CAAC,UAAU,CAAC,eAAe;IAErC,CAAC;IAED,EAAE,EAAE,OAAO,IAAI,IAAI,EAAE,CAAC;QACpB,oBAAoB,CAAC,GAAG,EAAE,OAAO;IACnC,CAAC;IAED,EAAE,EAAE,GAAG,CAAC,MAAM,MAAK,IAAM,GAAE,CAAC;QAC1B,GAAG,CAAC,GAAG,CAAC,IAAI;IACd,CAAC,MAAM,EAAE,EAAE,SAAS,EAAE,CAAC;QACrB,GAAG,CAAC,GAAG,CAAC,eAAe;IACzB,CAAC,MAAM,CAAC;QACN,KAAK,CAAC,UAAU,UACN,GAAG,CAAS,KAAK,MAAK,QAAU,QAC7B,GAAG,CAAS,KAAK;eAClB,CAAC;QAAA,CAAC;cACP,eAAe,CAAkB,OAAO,EAAE,KAAK,GAAK,CAAC;YAC1D,GAAG,CAAC,KAAK,CAAC,KAAK;YACf,UAAU;QACZ,CAAC;QACD,GAAG,CAAC,GAAG;IACT,CAAC;AACH,CAAC;SAEe,gBAAgB,CAC9B,GAAoB,EACpB,GAAmB,EACnB,IAAwB,EACf,CAAC;IACV,EAAE,EAAE,IAAI,EAAE,CAAC;QACT,EAKG,AALH;;;;;KAKG,AALH,EAKG,CACH,GAAG,CAAC,SAAS,EAAC,IAAM,GAAE,IAAI;IAC5B,CAAC;IAED,EAAE,MA/Ic,MAA0B,UA+IhC,GAAG,CAAC,OAAO;QAAI,IAAI;QAAK,CAAC;QACjC,GAAG,CAAC,UAAU,GAAG,GAAG;QACpB,GAAG,CAAC,GAAG;eACA,IAAI;IACb,CAAC;WAEM,KAAK;AACd,CAAC"}