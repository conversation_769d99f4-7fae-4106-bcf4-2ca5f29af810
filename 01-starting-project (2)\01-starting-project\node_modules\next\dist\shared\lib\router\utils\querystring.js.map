{"version": 3, "sources": ["../../../../../shared/lib/router/utils/querystring.ts"], "sourcesContent": ["import { ParsedUrlQuery } from 'querystring'\n\nexport function searchParamsToUrlQuery(\n  searchParams: URLSearchParams\n): ParsedUrlQuery {\n  const query: ParsedUrlQuery = {}\n  searchParams.forEach((value, key) => {\n    if (typeof query[key] === 'undefined') {\n      query[key] = value\n    } else if (Array.isArray(query[key])) {\n      ;(query[key] as string[]).push(value)\n    } else {\n      query[key] = [query[key] as string, value]\n    }\n  })\n  return query\n}\n\nfunction stringifyUrlQueryParam(param: string): string {\n  if (\n    typeof param === 'string' ||\n    (typeof param === 'number' && !isNaN(param)) ||\n    typeof param === 'boolean'\n  ) {\n    return String(param)\n  } else {\n    return ''\n  }\n}\n\nexport function urlQueryToSearchParams(\n  urlQuery: ParsedUrlQuery\n): URLSearchParams {\n  const result = new URLSearchParams()\n  Object.entries(urlQuery).forEach(([key, value]) => {\n    if (Array.isArray(value)) {\n      value.forEach((item) => result.append(key, stringifyUrlQueryParam(item)))\n    } else {\n      result.set(key, stringifyUrlQueryParam(value))\n    }\n  })\n  return result\n}\n\nexport function assign(\n  target: URLSearchParams,\n  ...searchParamsList: URLSearchParams[]\n): URLSearchParams {\n  searchParamsList.forEach((searchParams) => {\n    Array.from(searchParams.keys()).forEach((key) => target.delete(key))\n    searchParams.forEach((value, key) => target.append(key, value))\n  })\n  return target\n}\n"], "names": [], "mappings": ";;;;QAEgB,sBAAsB,GAAtB,sBAAsB;QA4BtB,sBAAsB,GAAtB,sBAAsB;QActB,MAAM,GAAN,MAAM;SA1CN,sBAAsB,CACpC,YAA6B,EACb,CAAC;IACjB,KAAK,CAAC,KAAK;;IACX,YAAY,CAAC,OAAO,EAAE,KAAK,EAAE,GAAG,GAAK,CAAC;QACpC,EAAE,SAAS,KAAK,CAAC,GAAG,OAAM,SAAW,GAAE,CAAC;YACtC,KAAK,CAAC,GAAG,IAAI,KAAK;QACpB,CAAC,MAAM,EAAE,EAAE,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC;YACnC,KAAK,CAAC,GAAG,EAAe,IAAI,CAAC,KAAK;QACtC,CAAC,MAAM,CAAC;YACN,KAAK,CAAC,GAAG;gBAAK,KAAK,CAAC,GAAG;gBAAa,KAAK;;QAC3C,CAAC;IACH,CAAC;WACM,KAAK;AACd,CAAC;SAEQ,sBAAsB,CAAC,KAAa,EAAU,CAAC;IACtD,EAAE,SACO,KAAK,MAAK,MAAQ,YACjB,KAAK,MAAK,MAAQ,MAAK,KAAK,CAAC,KAAK,YACnC,KAAK,MAAK,OAAS,GAC1B,CAAC;eACM,MAAM,CAAC,KAAK;IACrB,CAAC,MAAM,CAAC;;IAER,CAAC;AACH,CAAC;SAEe,sBAAsB,CACpC,QAAwB,EACP,CAAC;IAClB,KAAK,CAAC,MAAM,GAAG,GAAG,CAAC,eAAe;IAClC,MAAM,CAAC,OAAO,CAAC,QAAQ,EAAE,OAAO,GAAG,GAAG,EAAE,KAAK,IAAM,CAAC;QAClD,EAAE,EAAE,KAAK,CAAC,OAAO,CAAC,KAAK,GAAG,CAAC;YACzB,KAAK,CAAC,OAAO,EAAE,IAAI,GAAK,MAAM,CAAC,MAAM,CAAC,GAAG,EAAE,sBAAsB,CAAC,IAAI;;QACxE,CAAC,MAAM,CAAC;YACN,MAAM,CAAC,GAAG,CAAC,GAAG,EAAE,sBAAsB,CAAC,KAAK;QAC9C,CAAC;IACH,CAAC;WACM,MAAM;AACf,CAAC;SAEe,MAAM,CACpB,MAAuB,KACpB,gBAAgB,EACF,CAAC;IAClB,gBAAgB,CAAC,OAAO,EAAE,YAAY,GAAK,CAAC;QAC1C,KAAK,CAAC,IAAI,CAAC,YAAY,CAAC,IAAI,IAAI,OAAO,EAAE,GAAG,GAAK,MAAM,CAAC,MAAM,CAAC,GAAG;;QAClE,YAAY,CAAC,OAAO,EAAE,KAAK,EAAE,GAAG,GAAK,MAAM,CAAC,MAAM,CAAC,GAAG,EAAE,KAAK;;IAC/D,CAAC;WACM,MAAM;AACf,CAAC"}