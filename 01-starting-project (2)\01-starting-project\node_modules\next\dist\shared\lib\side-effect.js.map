{"version": 3, "sources": ["../../../shared/lib/side-effect.tsx"], "sourcesContent": ["import React, { Component } from 'react'\n\nconst isServer = typeof window === 'undefined'\n\ntype State = JSX.Element[] | undefined\n\ntype SideEffectProps = {\n  reduceComponentsToState: <T>(\n    components: Array<React.ReactElement<any>>,\n    props: T\n  ) => State\n  handleStateChange?: (state: State) => void\n  headManager: any\n  inAmpMode?: boolean\n}\n\nexport default class extends Component<SideEffectProps> {\n  private _hasHeadManager: boolean\n\n  emitChange = (): void => {\n    if (this._hasHeadManager) {\n      this.props.headManager.updateHead(\n        this.props.reduceComponentsToState(\n          [...this.props.headManager.mountedInstances],\n          this.props\n        )\n      )\n    }\n  }\n\n  constructor(props: any) {\n    super(props)\n    this._hasHeadManager =\n      this.props.headManager && this.props.headManager.mountedInstances\n\n    if (isServer && this._hasHeadManager) {\n      this.props.headManager.mountedInstances.add(this)\n      this.emitChange()\n    }\n  }\n  componentDidMount() {\n    if (this._hasHeadManager) {\n      this.props.headManager.mountedInstances.add(this)\n    }\n    this.emitChange()\n  }\n  componentDidUpdate() {\n    this.emitChange()\n  }\n  componentWillUnmount() {\n    if (this._hasHeadManager) {\n      this.props.headManager.mountedInstances.delete(this)\n    }\n    this.emitChange()\n  }\n\n  render() {\n    return null\n  }\n}\n"], "names": [], "mappings": ";;;;;AAAiC,GAAO,CAAP,MAAO;AAExC,KAAK,CAAC,QAAQ,UAAU,MAAM,MAAK,SAAW;qBAFb,MAAO;gBA8B1B,KAAU,CAAE,CAAC;QACvB,KAAK,CAAC,KAAK;aAZb,UAAU,OAAe,CAAC;YACxB,EAAE,OAAO,eAAe,EAAE,CAAC;qBACpB,KAAK,CAAC,WAAW,CAAC,UAAU,MAC1B,KAAK,CAAC,uBAAuB;4BACvB,KAAK,CAAC,WAAW,CAAC,gBAAgB;wBACtC,KAAK;YAGhB,CAAC;QACH,CAAC;aAIM,eAAe,QACb,KAAK,CAAC,WAAW,SAAS,KAAK,CAAC,WAAW,CAAC,gBAAgB;QAEnE,EAAE,EAAE,QAAQ,SAAS,eAAe,EAAE,CAAC;iBAChC,KAAK,CAAC,WAAW,CAAC,gBAAgB,CAAC,GAAG;iBACtC,UAAU;QACjB,CAAC;IACH,CAAC;IACD,iBAAiB,GAAG,CAAC;QACnB,EAAE,OAAO,eAAe,EAAE,CAAC;iBACpB,KAAK,CAAC,WAAW,CAAC,gBAAgB,CAAC,GAAG;QAC7C,CAAC;aACI,UAAU;IACjB,CAAC;IACD,kBAAkB,GAAG,CAAC;aACf,UAAU;IACjB,CAAC;IACD,oBAAoB,GAAG,CAAC;QACtB,EAAE,OAAO,eAAe,EAAE,CAAC;iBACpB,KAAK,CAAC,WAAW,CAAC,gBAAgB,CAAC,MAAM;QAChD,CAAC;aACI,UAAU;IACjB,CAAC;IAED,MAAM,GAAG,CAAC;eACD,IAAI;IACb,CAAC"}