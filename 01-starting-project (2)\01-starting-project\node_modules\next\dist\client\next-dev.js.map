{"version": 3, "sources": ["../../client/next-dev.js"], "sourcesContent": ["/* globals __REPLACE_NOOP_IMPORT__ */\nimport { initNext, version, router, emitter, render, renderError } from './'\nimport EventSourcePolyfill from './dev/event-source-polyfill'\nimport initOnDemandEntries from './dev/on-demand-entries-client'\nimport initWebpackHMR from './dev/webpack-hot-middleware-client'\nimport initializeBuildWatcher from './dev/dev-build-watcher'\nimport { displayContent } from './dev/fouc'\nimport { addMessageListener } from './dev/error-overlay/eventsource'\nimport {\n  assign,\n  urlQueryToSearchParams,\n} from '../shared/lib/router/utils/querystring'\n\n// Temporary workaround for the issue described here:\n// https://github.com/vercel/next.js/issues/3775#issuecomment-407438123\n// The runtimeChunk doesn't have dynamic import handling code when there hasn't been a dynamic import\n// The runtimeChunk can't hot reload itself currently to correct it when adding pages using on-demand-entries\n// eslint-disable-next-line no-unused-expressions\n__REPLACE_NOOP_IMPORT__\n\n// Support EventSource on Internet Explorer 11\nif (!window.EventSource) {\n  window.EventSource = EventSourcePolyfill\n}\n\nconst {\n  __NEXT_DATA__: { assetPrefix },\n} = window\n\nconst prefix = assetPrefix || ''\nconst webpackHMR = initWebpackHMR()\n\nwindow.next = {\n  version,\n  // router is initialized later so it has to be live-binded\n  get router() {\n    return router\n  },\n  emitter,\n  render,\n  renderError,\n}\ninitNext({ webpackHMR })\n  .then(({ renderCtx }) => {\n    initOnDemandEntries({ assetPrefix: prefix })\n\n    let buildIndicatorHandler = () => {}\n\n    function devPagesManifestListener(event) {\n      if (event.data.indexOf('devPagesManifest') !== -1) {\n        fetch(`${prefix}/_next/static/development/_devPagesManifest.json`)\n          .then((res) => res.json())\n          .then((manifest) => {\n            window.__DEV_PAGES_MANIFEST = manifest\n          })\n          .catch((err) => {\n            console.log(`Failed to fetch devPagesManifest`, err)\n          })\n      } else if (event.data.indexOf('serverOnlyChanges') !== -1) {\n        const { pages } = JSON.parse(event.data)\n\n        // Make sure to reload when the dev-overlay is showing for an\n        // API route\n        if (pages.includes(router.query.__NEXT_PAGE)) {\n          return window.location.reload()\n        }\n\n        if (!router.clc && pages.includes(router.pathname)) {\n          console.log('Refreshing page data due to server-side change')\n\n          buildIndicatorHandler('building')\n\n          const clearIndicator = () => buildIndicatorHandler('built')\n\n          router\n            .replace(\n              router.pathname +\n                '?' +\n                String(\n                  assign(\n                    urlQueryToSearchParams(router.query),\n                    new URLSearchParams(location.search)\n                  )\n                ),\n              router.asPath\n            )\n            .finally(clearIndicator)\n        }\n      }\n    }\n    devPagesManifestListener.unfiltered = true\n    addMessageListener(devPagesManifestListener)\n\n    if (process.env.__NEXT_BUILD_INDICATOR) {\n      initializeBuildWatcher((handler) => {\n        buildIndicatorHandler = handler\n      })\n    }\n\n    // delay rendering until after styles have been applied in development\n    displayContent(() => {\n      render(renderCtx)\n    })\n  })\n  .catch((err) => {\n    console.error('Error was not caught', err)\n  })\n"], "names": [], "mappings": ";AACwE,GAAI,CAAJ,CAAI;AAC5C,GAA6B,CAA7B,oBAA6B;AAC7B,GAAgC,CAAhC,sBAAgC;AACrC,GAAqC,CAArC,2BAAqC;AAC7B,GAAyB,CAAzB,gBAAyB;AAC7B,GAAY,CAAZ,KAAY;AACR,GAAiC,CAAjC,YAAiC;AAI7D,GAAwC,CAAxC,YAAwC;;;;;;AAE/C,EAAqD,AAArD,mDAAqD;AACrD,EAAuE,AAAvE,qEAAuE;AACvE,EAAqG,AAArG,mGAAqG;AACrG,EAA6G,AAA7G,2GAA6G;AAC7G,EAAiD,AAAjD,+CAAiD;AACjD,uBAAuB;AAEvB,EAA8C,AAA9C,4CAA8C;AAC9C,EAAE,GAAG,MAAM,CAAC,WAAW,EAAE,CAAC;IACxB,MAAM,CAAC,WAAW,GApBY,oBAA6B;AAqB7D,CAAC;AAED,KAAK,GACH,aAAa,IAAI,WAAW,WAC1B,MAAM;AAEV,KAAK,CAAC,MAAM,GAAG,WAAW;AAC1B,KAAK,CAAC,UAAU,OA1BW,2BAAqC;AA4BhE,MAAM,CAAC,IAAI;IACT,OAAO,EAhC+D,CAAI;IAiC1E,EAA0D,AAA1D,wDAA0D;QACtD,MAAM,IAAG,CAAC;eAlCwD,CAAI;IAoC1E,CAAC;IACD,OAAO,EArC+D,CAAI;IAsC1E,MAAM,EAtCgE,CAAI;IAuC1E,WAAW,EAvC2D,CAAI;;IAAJ,CAAI;IAyCjE,UAAU;GAClB,IAAI,IAAI,SAAS,MAAO,CAAC;QAxCI,sBAAgC;QAyCtC,WAAW,EAAE,MAAM;;IAEzC,GAAG,CAAC,qBAAqB,OAAS,CAAC;IAAA,CAAC;aAE3B,wBAAwB,CAAC,KAAK,EAAE,CAAC;QACxC,EAAE,EAAE,KAAK,CAAC,IAAI,CAAC,OAAO,EAAC,gBAAkB,QAAO,CAAC,EAAE,CAAC;YAClD,KAAK,IAAI,MAAM,CAAC,gDAAgD,GAC7D,IAAI,EAAE,GAAG,GAAK,GAAG,CAAC,IAAI;cACtB,IAAI,EAAE,QAAQ,GAAK,CAAC;gBACnB,MAAM,CAAC,oBAAoB,GAAG,QAAQ;YACxC,CAAC,EACA,KAAK,EAAE,GAAG,GAAK,CAAC;gBACf,OAAO,CAAC,GAAG,EAAE,gCAAgC,GAAG,GAAG;YACrD,CAAC;QACL,CAAC,MAAM,EAAE,EAAE,KAAK,CAAC,IAAI,CAAC,OAAO,EAAC,iBAAmB,QAAO,CAAC,EAAE,CAAC;YAC1D,KAAK,GAAG,KAAK,MAAK,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI;YAEvC,EAA6D,AAA7D,2DAA6D;YAC7D,EAAY,AAAZ,UAAY;YACZ,EAAE,EAAE,KAAK,CAAC,QAAQ,CA9D8C,CAAI,QA8D1C,KAAK,CAAC,WAAW,GAAG,CAAC;uBACtC,MAAM,CAAC,QAAQ,CAAC,MAAM;YAC/B,CAAC;YAED,EAAE,GAlE8D,CAAI,QAkExD,GAAG,IAAI,KAAK,CAAC,QAAQ,CAlE+B,CAAI,QAkE3B,QAAQ,GAAG,CAAC;gBACnD,OAAO,CAAC,GAAG,EAAC,8CAAgD;gBAE5D,qBAAqB,EAAC,QAAU;gBAEhC,KAAK,CAAC,cAAc,OAAS,qBAAqB,EAAC,KAAO;;gBAvEI,CAAI,QA0E/D,OAAO,CA1EoD,CAAI,QA2EvD,QAAQ,IACb,CAAG,IACH,MAAM,KAnEf,YAAwC,aAAxC,YAAwC,yBAVyB,CAAI,QA+E1B,KAAK,GACnC,GAAG,CAAC,eAAe,CAAC,QAAQ,CAAC,MAAM,KAhFiB,CAAI,QAmFvD,MAAM,EAEd,OAAO,CAAC,cAAc;YAC3B,CAAC;QACH,CAAC;IACH,CAAC;IACD,wBAAwB,CAAC,UAAU,GAAG,IAAI;QAnFX,YAAiC,qBAoF7C,wBAAwB;IAE3C,EAAE,EAAE,OAAO,CAAC,GAAG,CAAC,sBAAsB,EAAE,CAAC;YAxFV,gBAAyB,WAyF9B,OAAO,GAAK,CAAC;YACnC,qBAAqB,GAAG,OAAO;QACjC,CAAC;IACH,CAAC;IAED,EAAsE,AAAtE,oEAAsE;QA7F3C,KAAY,qBA8FlB,CAAC;YAnG8C,CAAI,SAoG/D,SAAS;IAClB,CAAC;AACH,CAAC,EACA,KAAK,EAAE,GAAG,GAAK,CAAC;IACf,OAAO,CAAC,KAAK,EAAC,oBAAsB,GAAE,GAAG;AAC3C,CAAC"}