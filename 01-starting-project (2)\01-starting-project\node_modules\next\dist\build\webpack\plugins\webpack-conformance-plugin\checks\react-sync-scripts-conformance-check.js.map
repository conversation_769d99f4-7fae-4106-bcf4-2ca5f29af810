{"version": 3, "sources": ["../../../../../../build/webpack/plugins/webpack-conformance-plugin/checks/react-sync-scripts-conformance-check.ts"], "sourcesContent": ["import {\n  IWebpackConformanceTest,\n  IGetAstNodeResult,\n  IParsedModuleDetails,\n  IConformanceTestResult,\n  IConformanceTestStatus,\n} from '../TestInterface'\nimport {\n  CONFORMANCE_ERROR_PREFIX,\n  CONFORMANCE_WARNING_PREFIX,\n} from '../constants'\n// eslint-disable-next-line import/no-extraneous-dependencies\nimport { namedTypes } from 'ast-types/'\n// eslint-disable-next-line import/no-extraneous-dependencies\nimport { NodePath } from 'ast-types/lib/node-path'\nimport { getLocalFileName } from '../utils/file-utils'\nimport { isNodeCreatingScriptElement } from '../utils/ast-utils'\nexport const ErrorMessage: string = `${CONFORMANCE_ERROR_PREFIX}: A sync script was found in a react module.`\nexport const WarningMessage: string = `${CONFORMANCE_WARNING_PREFIX}: A sync script was found in a react module.`\nexport const ErrorDescription = ``\nconst EARLY_EXIT_SUCCESS_RESULT: IConformanceTestResult = {\n  result: IConformanceTestStatus.SUCCESS,\n}\n\nexport interface ReactSyncScriptsConformanceCheckOptions {\n  AllowedSources?: String[]\n}\nexport class ReactSyncScriptsConformanceCheck\n  implements IWebpackConformanceTest\n{\n  private allowedSources: String[] = []\n  constructor({\n    AllowedSources,\n  }: ReactSyncScriptsConformanceCheckOptions = {}) {\n    if (AllowedSources) {\n      this.allowedSources = AllowedSources\n    }\n  }\n\n  public getAstNode(): IGetAstNodeResult[] {\n    return [\n      {\n        visitor: 'visitCallExpression',\n        inspectNode: (path: NodePath, { request }: IParsedModuleDetails) => {\n          const { node }: { node: namedTypes.CallExpression } = path\n          if (!node.arguments || node.arguments.length < 2) {\n            return EARLY_EXIT_SUCCESS_RESULT\n          }\n          if (isNodeCreatingScriptElement(node)) {\n            const propsNode = node.arguments[1] as namedTypes.ObjectExpression\n            if (!propsNode.properties) {\n              return EARLY_EXIT_SUCCESS_RESULT\n            }\n            const props: {\n              [key: string]: string\n            } = propsNode.properties.reduce((originalProps, prop: any) => {\n              // @ts-ignore\n              originalProps[prop.key.name] = prop.value.value\n              return originalProps\n            }, {})\n            if (\n              'defer' in props ||\n              'async' in props ||\n              !('src' in props) ||\n              this.allowedSources.includes(props.src)\n            ) {\n              return EARLY_EXIT_SUCCESS_RESULT\n            }\n\n            // Todo: Add an absolute error case for modern js when class is a subclass of next/head.\n            return {\n              result: IConformanceTestStatus.FAILED,\n              warnings: [\n                {\n                  message: `${WarningMessage} ${getLocalFileName(\n                    request\n                  )}. This can potentially delay FCP/FP metrics.`,\n                },\n              ],\n            }\n          }\n          return EARLY_EXIT_SUCCESS_RESULT\n        },\n      },\n    ]\n  }\n}\n"], "names": [], "mappings": ";;;;;AAMO,GAAkB,CAAlB,cAAkB;AAIlB,GAAc,CAAd,UAAc;AAKY,GAAqB,CAArB,UAAqB;AACV,GAAoB,CAApB,SAAoB;AACzD,KAAK,CAAC,YAAY,MAPlB,UAAc,0BAO2C,4CAA4C;QAA/F,YAAY,GAAZ,YAAY;AAClB,KAAK,CAAC,cAAc,MARpB,UAAc,4BAQ+C,4CAA4C;QAAnG,cAAc,GAAd,cAAc;AACpB,KAAK,CAAC,gBAAgB;QAAhB,gBAAgB,GAAhB,gBAAgB;AAC7B,KAAK,CAAC,yBAAyB;IAC7B,MAAM,EAfD,cAAkB,wBAeQ,OAAO;;MAM3B,gCAAgC;kBAKzC,cAAc;MACiC,CAAC;aAH1C,cAAc;QAIpB,EAAE,EAAE,cAAc,EAAE,CAAC;iBACd,cAAc,GAAG,cAAc;QACtC,CAAC;IACH,CAAC;IAEM,UAAU,GAAwB,CAAC;;;gBAGpC,OAAO,GAAE,mBAAqB;gBAC9B,WAAW,GAAG,IAAc,IAAI,OAAO,MAA6B,CAAC;oBACnE,KAAK,GAAG,IAAI,MAA0C,IAAI;oBAC1D,EAAE,GAAG,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;+BAC1C,yBAAyB;oBAClC,CAAC;oBACD,EAAE,MAhCgC,SAAoB,8BAgCtB,IAAI,GAAG,CAAC;wBACtC,KAAK,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC;wBAClC,EAAE,GAAG,SAAS,CAAC,UAAU,EAAE,CAAC;mCACnB,yBAAyB;wBAClC,CAAC;wBACD,KAAK,CAAC,KAAK,GAEP,SAAS,CAAC,UAAU,CAAC,MAAM,EAAE,aAAa,EAAE,IAAS,GAAK,CAAC;4BAC7D,EAAa,AAAb,WAAa;4BACb,aAAa,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,IAAI,IAAI,CAAC,KAAK,CAAC,KAAK;mCACxC,aAAa;wBACtB,CAAC;;wBACD,EAAE,GACA,KAAO,KAAI,KAAK,KAChB,KAAO,KAAI,KAAK,OACd,GAAK,KAAI,KAAK,UACX,cAAc,CAAC,QAAQ,CAAC,KAAK,CAAC,GAAG,GACtC,CAAC;mCACM,yBAAyB;wBAClC,CAAC;wBAED,EAAwF,AAAxF,sFAAwF;;4BAEtF,MAAM,EAjEb,cAAkB,wBAiEoB,MAAM;4BACrC,QAAQ;;oCAEJ,OAAO,KAAK,cAAc,CAAC,CAAC,MA3Db,UAAqB,mBA4DlC,OAAO,EACP,4CAA4C;;;;oBAItD,CAAC;2BACM,yBAAyB;gBAClC,CAAC;;;IAGP,CAAC;;QA1DU,gCAAgC,GAAhC,gCAAgC"}