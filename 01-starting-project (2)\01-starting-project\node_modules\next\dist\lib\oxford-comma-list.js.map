{"version": 3, "sources": ["../../lib/oxford-comma-list.ts"], "sourcesContent": ["export function getOxfordCommaList(items: string[]): string {\n  return items\n    .map(\n      (v, index, { length }) =>\n        (index > 0\n          ? index === length - 1\n            ? length > 2\n              ? ', and '\n              : ' and '\n            : ', '\n          : '') + v\n    )\n    .join('')\n}\n"], "names": [], "mappings": ";;;;QAAgB,kBAAkB,GAAlB,kBAAkB;SAAlB,kBAAkB,CAAC,KAAe,EAAU,CAAC;WACpD,KAAK,CACT,GAAG,EACD,CAAC,EAAE,KAAK,IAAI,MAAM,OAChB,KAAK,GAAG,CAAC,GACN,KAAK,KAAK,MAAM,GAAG,CAAC,GAClB,MAAM,GAAG,CAAC,IACR,MAAQ,KACR,KAAO,KACT,EAAI,UACA,CAAC;MAEd,IAAI;AACT,CAAC"}