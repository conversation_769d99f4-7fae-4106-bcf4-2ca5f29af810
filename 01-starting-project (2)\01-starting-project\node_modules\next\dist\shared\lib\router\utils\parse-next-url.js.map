{"version": 3, "sources": ["../../../../../shared/lib/router/utils/parse-next-url.ts"], "sourcesContent": ["import { getCookieParser } from '../../../../server/api-utils'\nimport { getLocaleMetadata } from '../../i18n/get-locale-metadata'\nimport { parseUrl } from './parse-url'\nimport type { NextConfig, DomainLocale } from '../../../../server/config-shared'\nimport type { ParsedUrl } from './parse-url'\nimport type { PathLocale } from '../../i18n/normalize-locale-path'\n\ninterface Params {\n  headers?: { [key: string]: string | string[] | undefined }\n  nextConfig: NextConfig\n  url?: string\n}\n\nexport function parseNextUrl({ headers, nextConfig, url = '/' }: Params) {\n  const urlParsed: ParsedNextUrl = parseUrl(url)\n  const { basePath } = nextConfig\n\n  if (basePath && urlParsed.pathname.startsWith(basePath)) {\n    urlParsed.pathname = urlParsed.pathname.replace(basePath, '') || '/'\n    urlParsed.basePath = basePath\n  }\n\n  if (nextConfig.i18n) {\n    urlParsed.locale = getLocaleMetadata({\n      cookies: getCookieParser(headers || {}),\n      headers: headers,\n      nextConfig: {\n        basePath: nextConfig.basePath,\n        i18n: nextConfig.i18n,\n        trailingSlash: nextConfig.trailingSlash,\n      },\n      url: urlParsed,\n    })\n\n    if (urlParsed.locale?.path.detectedLocale) {\n      urlParsed.pathname = urlParsed.locale.path.pathname\n    }\n  }\n\n  return urlParsed\n}\n\nexport interface ParsedNextUrl extends ParsedUrl {\n  basePath?: string\n  locale?: {\n    defaultLocale: string\n    domain?: DomainLocale\n    locale: string\n    path: PathLocale\n    redirect?: string\n    trailingSlash?: boolean\n  }\n}\n"], "names": [], "mappings": ";;;;QAagB,YAAY,GAAZ,YAAY;AAbI,GAA8B,CAA9B,SAA8B;AAC5B,GAAgC,CAAhC,kBAAgC;AACzC,GAAa,CAAb,SAAa;SAWtB,YAAY,GAAG,OAAO,GAAE,UAAU,GAAE,GAAG,GAAG,CAAG,MAAY,CAAC;IACxE,KAAK,CAAC,SAAS,OAZQ,SAAa,WAYM,GAAG;IAC7C,KAAK,GAAG,QAAQ,MAAK,UAAU;IAE/B,EAAE,EAAE,QAAQ,IAAI,SAAS,CAAC,QAAQ,CAAC,UAAU,CAAC,QAAQ,GAAG,CAAC;QACxD,SAAS,CAAC,QAAQ,GAAG,SAAS,CAAC,QAAQ,CAAC,OAAO,CAAC,QAAQ,UAAS,CAAG;QACpE,SAAS,CAAC,QAAQ,GAAG,QAAQ;IAC/B,CAAC;IAED,EAAE,EAAE,UAAU,CAAC,IAAI,EAAE,CAAC;YAYhB,GAAgB;QAXpB,SAAS,CAAC,MAAM,OAtBc,kBAAgC;YAuB5D,OAAO,MAxBmB,SAA8B,kBAwB/B,OAAO;;YAChC,OAAO,EAAE,OAAO;YAChB,UAAU;gBACR,QAAQ,EAAE,UAAU,CAAC,QAAQ;gBAC7B,IAAI,EAAE,UAAU,CAAC,IAAI;gBACrB,aAAa,EAAE,UAAU,CAAC,aAAa;;YAEzC,GAAG,EAAE,SAAS;;QAGhB,EAAE,GAAE,GAAgB,GAAhB,SAAS,CAAC,MAAM,cAAhB,GAAgB,UAAhB,CAAsB,QAAtB,CAAsB,GAAtB,GAAgB,CAAE,IAAI,CAAC,cAAc,EAAE,CAAC;YAC1C,SAAS,CAAC,QAAQ,GAAG,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ;QACrD,CAAC;IACH,CAAC;WAEM,SAAS;AAClB,CAAC"}