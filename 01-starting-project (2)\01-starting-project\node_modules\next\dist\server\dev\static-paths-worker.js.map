{"version": 3, "sources": ["../../../server/dev/static-paths-worker.ts"], "sourcesContent": ["import { buildStaticPaths } from '../../build/utils'\nimport { setHttpAgentOptions } from '../config'\nimport { NextConfigComplete } from '../config-shared'\nimport { loadComponents } from '../load-components'\nimport '../node-polyfill-fetch'\n\ntype RuntimeConfig = any\n\nlet workerWasUsed = false\n\n// we call getStaticPaths in a separate process to ensure\n// side-effects aren't relied on in dev that will break\n// during a production build\nexport async function loadStaticPaths(\n  distDir: string,\n  pathname: string,\n  serverless: boolean,\n  config: RuntimeConfig,\n  httpAgentOptions: NextConfigComplete['httpAgentOptions'],\n  locales?: string[],\n  defaultLocale?: string\n) {\n  // we only want to use each worker once to prevent any invalid\n  // caches\n  if (workerWasUsed) {\n    process.exit(1)\n  }\n\n  // update work memory runtime-config\n  require('../../shared/lib/runtime-config').setConfig(config)\n  setHttpAgentOptions(httpAgentOptions)\n\n  const components = await loadComponents(distDir, pathname, serverless)\n\n  if (!components.getStaticPaths) {\n    // we shouldn't get to this point since the worker should\n    // only be called for SSG pages with getStaticPaths\n    throw new Error(\n      `Invariant: failed to load page with getStaticPaths for ${pathname}`\n    )\n  }\n\n  workerWasUsed = true\n  return buildStaticPaths(\n    pathname,\n    components.getStaticPaths,\n    locales,\n    defaultLocale\n  )\n}\n"], "names": [], "mappings": ";;;;QAasB,eAAe,GAAf,eAAe;AAbJ,GAAmB,CAAnB,MAAmB;AAChB,GAAW,CAAX,OAAW;AAEhB,GAAoB,CAApB,eAAoB;;AAKnD,GAAG,CAAC,aAAa,GAAG,KAAK;eAKH,eAAe,CACnC,OAAe,EACf,QAAgB,EAChB,UAAmB,EACnB,MAAqB,EACrB,gBAAwD,EACxD,OAAkB,EAClB,aAAsB,EACtB,CAAC;IACD,EAA8D,AAA9D,4DAA8D;IAC9D,EAAS,AAAT,OAAS;IACT,EAAE,EAAE,aAAa,EAAE,CAAC;QAClB,OAAO,CAAC,IAAI,CAAC,CAAC;IAChB,CAAC;IAED,EAAoC,AAApC,kCAAoC;IACpC,OAAO,EAAC,+BAAiC,GAAE,SAAS,CAAC,MAAM;QA5BzB,OAAW,sBA6BzB,gBAAgB;IAEpC,KAAK,CAAC,UAAU,aA7Ba,eAAoB,iBA6BT,OAAO,EAAE,QAAQ,EAAE,UAAU;IAErE,EAAE,GAAG,UAAU,CAAC,cAAc,EAAE,CAAC;QAC/B,EAAyD,AAAzD,uDAAyD;QACzD,EAAmD,AAAnD,iDAAmD;QACnD,KAAK,CAAC,GAAG,CAAC,KAAK,EACZ,uDAAuD,EAAE,QAAQ;IAEtE,CAAC;IAED,aAAa,GAAG,IAAI;eA1CW,MAAmB,mBA4ChD,QAAQ,EACR,UAAU,CAAC,cAAc,EACzB,OAAO,EACP,aAAa;AAEjB,CAAC"}