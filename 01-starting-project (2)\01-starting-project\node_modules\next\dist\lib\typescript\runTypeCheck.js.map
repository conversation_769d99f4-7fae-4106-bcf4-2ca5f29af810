{"version": 3, "sources": ["../../../lib/typescript/runTypeCheck.ts"], "sourcesContent": ["import path from 'path'\nimport {\n  DiagnosticCategory,\n  getFormattedDiagnostic,\n} from './diagnosticFormatter'\nimport { getTypeScriptConfiguration } from './getTypeScriptConfiguration'\nimport { getRequiredConfiguration } from './writeConfigurationDefaults'\n\nimport { CompileError } from '../compile-error'\n\nexport interface TypeCheckResult {\n  hasWarnings: boolean\n  warnings?: string[]\n  inputFilesCount: number\n  totalFilesCount: number\n  incremental: boolean\n}\n\nexport async function runTypeCheck(\n  ts: typeof import('typescript'),\n  baseDir: string,\n  tsConfigPath: string,\n  cacheDir?: string\n): Promise<TypeCheckResult> {\n  const effectiveConfiguration = await getTypeScriptConfiguration(\n    ts,\n    tsConfigPath\n  )\n\n  if (effectiveConfiguration.fileNames.length < 1) {\n    return {\n      hasWarnings: false,\n      inputFilesCount: 0,\n      totalFilesCount: 0,\n      incremental: false,\n    }\n  }\n  const requiredConfig = getRequiredConfiguration(ts)\n\n  const options = {\n    ...effectiveConfiguration.options,\n    ...requiredConfig,\n    noEmit: true,\n  }\n\n  let program:\n    | import('typescript').Program\n    | import('typescript').BuilderProgram\n  let incremental = false\n  if (options.incremental && cacheDir) {\n    incremental = true\n    program = ts.createIncrementalProgram({\n      rootNames: effectiveConfiguration.fileNames,\n      options: {\n        ...options,\n        incremental: true,\n        tsBuildInfoFile: path.join(cacheDir, '.tsbuildinfo'),\n      },\n    })\n  } else {\n    program = ts.createProgram(effectiveConfiguration.fileNames, options)\n  }\n  const result = program.emit()\n\n  // Intended to match:\n  // - pages/test.js\n  // - pages/apples.test.js\n  // - pages/__tests__/a.js\n  //\n  // But not:\n  // - pages/contest.js\n  // - pages/other.js\n  // - pages/test/a.js\n  //\n  const regexIgnoredFile =\n    /[\\\\/]__(?:tests|mocks)__[\\\\/]|(?<=[\\\\/.])(?:spec|test)\\.[^\\\\/]+$/\n  const allDiagnostics = ts\n    .getPreEmitDiagnostics(program as import('typescript').Program)\n    .concat(result.diagnostics)\n    .filter((d) => !(d.file && regexIgnoredFile.test(d.file.fileName)))\n\n  const firstError =\n    allDiagnostics.find(\n      (d) => d.category === DiagnosticCategory.Error && Boolean(d.file)\n    ) ?? allDiagnostics.find((d) => d.category === DiagnosticCategory.Error)\n\n  if (firstError) {\n    throw new CompileError(\n      await getFormattedDiagnostic(ts, baseDir, firstError)\n    )\n  }\n\n  const warnings = await Promise.all(\n    allDiagnostics\n      .filter((d) => d.category === DiagnosticCategory.Warning)\n      .map((d) => getFormattedDiagnostic(ts, baseDir, d))\n  )\n  return {\n    hasWarnings: true,\n    warnings,\n    inputFilesCount: effectiveConfiguration.fileNames.length,\n    totalFilesCount: program.getSourceFiles().length,\n    incremental,\n  }\n}\n"], "names": [], "mappings": ";;;;QAkBsB,YAAY,GAAZ,YAAY;AAlBjB,GAAM,CAAN,KAAM;AAIhB,GAAuB,CAAvB,oBAAuB;AACa,GAA8B,CAA9B,2BAA8B;AAChC,GAA8B,CAA9B,2BAA8B;AAE1C,GAAkB,CAAlB,aAAkB;;;;;;eAUzB,YAAY,CAChC,EAA+B,EAC/B,OAAe,EACf,YAAoB,EACpB,QAAiB,EACS,CAAC;IAC3B,KAAK,CAAC,sBAAsB,aAnBa,2BAA8B,6BAoBrE,EAAE,EACF,YAAY;IAGd,EAAE,EAAE,sBAAsB,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;;YAE9C,WAAW,EAAE,KAAK;YAClB,eAAe,EAAE,CAAC;YAClB,eAAe,EAAE,CAAC;YAClB,WAAW,EAAE,KAAK;;IAEtB,CAAC;IACD,KAAK,CAAC,cAAc,OA/BmB,2BAA8B,2BA+BrB,EAAE;IAElD,KAAK,CAAC,OAAO;WACR,sBAAsB,CAAC,OAAO;WAC9B,cAAc;QACjB,MAAM,EAAE,IAAI;;IAGd,GAAG,CAAC,OAAO;IAGX,GAAG,CAAC,WAAW,GAAG,KAAK;IACvB,EAAE,EAAE,OAAO,CAAC,WAAW,IAAI,QAAQ,EAAE,CAAC;QACpC,WAAW,GAAG,IAAI;QAClB,OAAO,GAAG,EAAE,CAAC,wBAAwB;YACnC,SAAS,EAAE,sBAAsB,CAAC,SAAS;YAC3C,OAAO;mBACF,OAAO;gBACV,WAAW,EAAE,IAAI;gBACjB,eAAe,EAxDN,KAAM,SAwDO,IAAI,CAAC,QAAQ,GAAE,YAAc;;;IAGzD,CAAC,MAAM,CAAC;QACN,OAAO,GAAG,EAAE,CAAC,aAAa,CAAC,sBAAsB,CAAC,SAAS,EAAE,OAAO;IACtE,CAAC;IACD,KAAK,CAAC,MAAM,GAAG,OAAO,CAAC,IAAI;IAE3B,EAAqB,AAArB,mBAAqB;IACrB,EAAkB,AAAlB,gBAAkB;IAClB,EAAyB,AAAzB,uBAAyB;IACzB,EAAyB,AAAzB,uBAAyB;IACzB,EAAE;IACF,EAAW,AAAX,SAAW;IACX,EAAqB,AAArB,mBAAqB;IACrB,EAAmB,AAAnB,iBAAmB;IACnB,EAAoB,AAApB,kBAAoB;IACpB,EAAE;IACF,KAAK,CAAC,gBAAgB;IAEtB,KAAK,CAAC,cAAc,GAAG,EAAE,CACtB,qBAAqB,CAAC,OAAO,EAC7B,MAAM,CAAC,MAAM,CAAC,WAAW,EACzB,MAAM,EAAE,CAAC,KAAO,CAAC,CAAC,IAAI,IAAI,gBAAgB,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,QAAQ;;QAGhE,GAEC;IAHH,KAAK,CAAC,UAAU,IACd,GAEC,GAFD,cAAc,CAAC,IAAI,EAChB,CAAC,GAAK,CAAC,CAAC,QAAQ,KA/EhB,oBAAuB,oBA+EiB,KAAK,IAAI,OAAO,CAAC,CAAC,CAAC,IAAI;mBADlE,GAEC,cAFD,GAEC,GAAI,cAAc,CAAC,IAAI,EAAE,CAAC,GAAK,CAAC,CAAC,QAAQ,KAhFvC,oBAAuB,oBAgFwC,KAAK;;IAEzE,EAAE,EAAE,UAAU,EAAE,CAAC;QACf,KAAK,CAAC,GAAG,CA/EgB,aAAkB,wBAJxC,oBAAuB,yBAoFK,EAAE,EAAE,OAAO,EAAE,UAAU;IAExD,CAAC;IAED,KAAK,CAAC,QAAQ,SAAS,OAAO,CAAC,GAAG,CAChC,cAAc,CACX,MAAM,EAAE,CAAC,GAAK,CAAC,CAAC,QAAQ,KA1FxB,oBAAuB,oBA0FyB,OAAO;MACvD,GAAG,EAAE,CAAC,OA3FN,oBAAuB,yBA2FW,EAAE,EAAE,OAAO,EAAE,CAAC;;;QAGnD,WAAW,EAAE,IAAI;QACjB,QAAQ;QACR,eAAe,EAAE,sBAAsB,CAAC,SAAS,CAAC,MAAM;QACxD,eAAe,EAAE,OAAO,CAAC,cAAc,GAAG,MAAM;QAChD,WAAW;;AAEf,CAAC"}