{"version": 3, "sources": ["../../build/write-build-id.ts"], "sourcesContent": ["import { promises } from 'fs'\nimport { join } from 'path'\nimport { BUILD_ID_FILE } from '../shared/lib/constants'\n\nexport async function writeBuildId(\n  distDir: string,\n  buildId: string\n): Promise<void> {\n  const buildIdPath = join(distDir, BUILD_ID_FILE)\n  await promises.writeFile(buildIdPath, buildId, 'utf8')\n}\n"], "names": [], "mappings": ";;;;QAIsB,YAAY,GAAZ,YAAY;AAJT,GAAI,CAAJ,GAAI;AACR,GAAM,CAAN,KAAM;AACG,GAAyB,CAAzB,UAAyB;eAEjC,YAAY,CAChC,OAAe,EACf,OAAe,EACA,CAAC;IAChB,KAAK,CAAC,WAAW,OAPE,KAAM,OAOA,OAAO,EANJ,UAAyB;UAF9B,GAAI,UASZ,SAAS,CAAC,WAAW,EAAE,OAAO,GAAE,IAAM;AACvD,CAAC"}