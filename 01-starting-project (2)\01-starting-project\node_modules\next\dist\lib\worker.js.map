{"version": 3, "sources": ["../../lib/worker.ts"], "sourcesContent": ["import { Worker as Jest<PERSON>or<PERSON> } from 'jest-worker'\n\ntype FarmOptions = ConstructorParameters<typeof JestWorker>[1]\n\nconst RESTARTED = Symbol('restarted')\n\nexport class Worker {\n  private _worker: JestWorker | undefined\n\n  constructor(\n    workerPath: string,\n    options: FarmOptions & {\n      timeout?: number\n      onRestart?: (method: string, args: any[], attempts: number) => void\n      exposedMethods: ReadonlyArray<string>\n    }\n  ) {\n    let { timeout, onRestart, ...farmOptions } = options\n\n    let restartPromise: Promise<typeof RESTARTED>\n    let resolveRestartPromise: (arg: typeof RESTARTED) => void\n    let activeTasks = 0\n\n    this._worker = undefined\n\n    const createWorker = () => {\n      this._worker = new JestWorker(workerPath, farmOptions) as JestWorker\n      restartPromise = new Promise(\n        (resolve) => (resolveRestartPromise = resolve)\n      )\n\n      this._worker.getStdout().pipe(process.stdout)\n      this._worker.getStderr().pipe(process.stderr)\n    }\n    createWorker()\n\n    const onHanging = () => {\n      const worker = this._worker\n      if (!worker) return\n      const resolve = resolveRestartPromise\n      createWorker()\n      worker.end().then(() => {\n        resolve(RESTARTED)\n      })\n    }\n\n    let hangingTimer: number | false = false\n\n    const onActivity = () => {\n      if (hangingTimer) clearTimeout(hangingTimer)\n      hangingTimer = activeTasks > 0 && setTimeout(onHanging, timeout)\n    }\n\n    for (const method of farmOptions.exposedMethods) {\n      if (method.startsWith('_')) continue\n      ;(this as any)[method] = timeout\n        ? // eslint-disable-next-line no-loop-func\n          async (...args: any[]) => {\n            activeTasks++\n            try {\n              let attempts = 0\n              for (;;) {\n                onActivity()\n                const result = await Promise.race([\n                  (this._worker as any)[method](...args),\n                  restartPromise,\n                ])\n                if (result !== RESTARTED) return result\n                if (onRestart) onRestart(method, args, ++attempts)\n              }\n            } finally {\n              activeTasks--\n              onActivity()\n            }\n          }\n        : (this._worker as any)[method].bind(this._worker)\n    }\n  }\n\n  end(): ReturnType<JestWorker['end']> {\n    const worker = this._worker\n    if (!worker) {\n      throw new Error('Farm is ended, no more calls can be done to it')\n    }\n    this._worker = undefined\n    return worker.end()\n  }\n}\n"], "names": [], "mappings": ";;;;AAAqC,GAAa,CAAb,WAAa;AAIlD,KAAK,CAAC,SAAS,GAAG,MAAM,EAAC,SAAW;MAEvB,OAAM;gBAIf,UAAkB,EAClB,OAIC,CACD,CAAC;QACD,GAAG,GAAG,OAAO,GAAE,SAAS,MAAK,WAAW,KAAK,OAAO;QAEpD,GAAG,CAAC,cAAc;QAClB,GAAG,CAAC,qBAAqB;QACzB,GAAG,CAAC,WAAW,GAAG,CAAC;aAEd,OAAO,GAAG,SAAS;QAExB,KAAK,CAAC,YAAY,OAAS,CAAC;iBACrB,OAAO,GAAG,GAAG,CA1Ba,WAAa,QA0Bd,UAAU,EAAE,WAAW;YACrD,cAAc,GAAG,GAAG,CAAC,OAAO,EACzB,OAAO,GAAM,qBAAqB,GAAG,OAAO;;iBAG1C,OAAO,CAAC,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM;iBACvC,OAAO,CAAC,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM;QAC9C,CAAC;QACD,YAAY;QAEZ,KAAK,CAAC,SAAS,OAAS,CAAC;YACvB,KAAK,CAAC,MAAM,QAAQ,OAAO;YAC3B,EAAE,GAAG,MAAM;YACX,KAAK,CAAC,OAAO,GAAG,qBAAqB;YACrC,YAAY;YACZ,MAAM,CAAC,GAAG,GAAG,IAAI,KAAO,CAAC;gBACvB,OAAO,CAAC,SAAS;YACnB,CAAC;QACH,CAAC;QAED,GAAG,CAAC,YAAY,GAAmB,KAAK;QAExC,KAAK,CAAC,UAAU,OAAS,CAAC;YACxB,EAAE,EAAE,YAAY,EAAE,YAAY,CAAC,YAAY;YAC3C,YAAY,GAAG,WAAW,GAAG,CAAC,IAAI,UAAU,CAAC,SAAS,EAAE,OAAO;QACjE,CAAC;aAEI,KAAK,CAAC,MAAM,IAAI,WAAW,CAAC,cAAc,CAAE,CAAC;YAChD,EAAE,EAAE,MAAM,CAAC,UAAU,EAAC,CAAG;iBACV,MAAM,IAAI,OAAO,aAElB,IAAI,GAAY,CAAC;gBACzB,WAAW;oBACP,CAAC;oBACH,GAAG,CAAC,QAAQ,GAAG,CAAC;2BACP,CAAC;wBACR,UAAU;wBACV,KAAK,CAAC,MAAM,SAAS,OAAO,CAAC,IAAI;iCACzB,OAAO,CAAS,MAAM,KAAK,IAAI;4BACrC,cAAc;;wBAEhB,EAAE,EAAE,MAAM,KAAK,SAAS,SAAS,MAAM;wBACvC,EAAE,EAAE,SAAS,EAAE,SAAS,CAAC,MAAM,EAAE,IAAI,IAAI,QAAQ;oBACnD,CAAC;gBACH,CAAC,QAAS,CAAC;oBACT,WAAW;oBACX,UAAU;gBACZ,CAAC;YACH,CAAC,QACK,OAAO,CAAS,MAAM,EAAE,IAAI,MAAM,OAAO;QACrD,CAAC;IACH,CAAC;IAED,GAAG,GAAkC,CAAC;QACpC,KAAK,CAAC,MAAM,QAAQ,OAAO;QAC3B,EAAE,GAAG,MAAM,EAAE,CAAC;YACZ,KAAK,CAAC,GAAG,CAAC,KAAK,EAAC,8CAAgD;QAClE,CAAC;aACI,OAAO,GAAG,SAAS;eACjB,MAAM,CAAC,GAAG;IACnB,CAAC;;QAhFU,MAAM,GAAN,OAAM"}