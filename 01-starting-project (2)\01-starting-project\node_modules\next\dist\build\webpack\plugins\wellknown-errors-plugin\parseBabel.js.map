{"version": 3, "sources": ["../../../../../build/webpack/plugins/wellknown-errors-plugin/parseBabel.ts"], "sourcesContent": ["import Chalk from 'chalk'\nimport { SimpleWebpackError } from './simpleWebpackError'\n\nconst chalk = new Chalk.constructor({ enabled: true })\n\nexport function getBabelError(\n  fileName: string,\n  err: Error & {\n    code?: 'BABEL_PARSE_ERROR'\n    loc?: { line: number; column: number }\n  }\n): SimpleWebpackError | false {\n  if (err.code !== 'BABEL_PARSE_ERROR') {\n    return false\n  }\n\n  // https://github.com/babel/babel/blob/34693d6024da3f026534dd8d569f97ac0109602e/packages/babel-core/src/parser/index.js\n  if (err.loc) {\n    const lineNumber = Math.max(1, err.loc.line)\n    const column = Math.max(1, err.loc.column)\n\n    let message = err.message\n      // Remove file information, which instead is provided by webpack.\n      .replace(/^.+?: /, '')\n      // Remove column information from message\n      .replace(\n        new RegExp(`[^\\\\S\\\\r\\\\n]*\\\\(${lineNumber}:${column}\\\\)[^\\\\S\\\\r\\\\n]*`),\n        ''\n      )\n\n    return new SimpleWebpackError(\n      `${chalk.cyan(fileName)}:${chalk.yellow(\n        lineNumber.toString()\n      )}:${chalk.yellow(column.toString())}`,\n      chalk.red.bold('Syntax error').concat(`: ${message}`)\n    )\n  }\n\n  return false\n}\n"], "names": [], "mappings": ";;;;QAKgB,aAAa,GAAb,aAAa;AALX,GAAO,CAAP,MAAO;AACU,GAAsB,CAAtB,mBAAsB;;;;;;AAEzD,KAAK,CAAC,KAAK,GAAG,GAAG,CAHC,MAAO,SAGD,WAAW;IAAG,OAAO,EAAE,IAAI;;SAEnC,aAAa,CAC3B,QAAgB,EAChB,GAGC,EAC2B,CAAC;IAC7B,EAAE,EAAE,GAAG,CAAC,IAAI,MAAK,iBAAmB,GAAE,CAAC;eAC9B,KAAK;IACd,CAAC;IAED,EAAuH,AAAvH,qHAAuH;IACvH,EAAE,EAAE,GAAG,CAAC,GAAG,EAAE,CAAC;QACZ,KAAK,CAAC,UAAU,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,GAAG,CAAC,IAAI;QAC3C,KAAK,CAAC,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,GAAG,CAAC,MAAM;QAEzC,GAAG,CAAC,OAAO,GAAG,GAAG,CAAC,OAAO,AACvB,EAAiE,AAAjE,+DAAiE;SAChE,OAAO,cACR,EAAyC,AAAzC,uCAAyC;SACxC,OAAO,CACN,GAAG,CAAC,MAAM,EAAE,gBAAgB,EAAE,UAAU,CAAC,CAAC,EAAE,MAAM,CAAC,gBAAgB;eAIhE,GAAG,CA7BqB,mBAAsB,uBA8BhD,KAAK,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC,EAAE,KAAK,CAAC,MAAM,CACrC,UAAU,CAAC,QAAQ,IACnB,CAAC,EAAE,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC,QAAQ,OACjC,KAAK,CAAC,GAAG,CAAC,IAAI,EAAC,YAAc,GAAE,MAAM,EAAE,EAAE,EAAE,OAAO;IAEtD,CAAC;WAEM,KAAK;AACd,CAAC"}