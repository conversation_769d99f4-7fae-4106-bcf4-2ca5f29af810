{"version": 3, "sources": ["../../../../../build/webpack/loaders/next-serverless-loader/utils.ts"], "sourcesContent": ["import { IncomingMessage, ServerResponse } from 'http'\nimport { format as formatUrl, UrlWithParsedQuery, parse as parseUrl } from 'url'\nimport { parse as parseQs, ParsedUrlQuery } from 'querystring'\nimport { Rewrite } from '../../../../lib/load-custom-routes'\nimport { normalizeLocalePath } from '../../../../shared/lib/i18n/normalize-locale-path'\nimport pathMatch from '../../../../shared/lib/router/utils/path-match'\nimport { getRouteRegex } from '../../../../shared/lib/router/utils/route-regex'\nimport { getRouteMatcher } from '../../../../shared/lib/router/utils/route-matcher'\nimport prepareDestination, {\n  matchHas,\n} from '../../../../shared/lib/router/utils/prepare-destination'\nimport { __ApiPreviewProps } from '../../../../server/api-utils'\nimport { BuildManifest } from '../../../../server/get-page-files'\nimport {\n  GetServerSideProps,\n  GetStaticPaths,\n  GetStaticProps,\n} from '../../../../types'\nimport accept from '@hapi/accept'\nimport { detectLocaleCookie } from '../../../../shared/lib/i18n/detect-locale-cookie'\nimport { detectDomainLocale } from '../../../../shared/lib/i18n/detect-domain-locale'\nimport { denormalizePagePath } from '../../../../server/denormalize-page-path'\nimport cookie from 'next/dist/compiled/cookie'\nimport { TEMPORARY_REDIRECT_STATUS } from '../../../../shared/lib/constants'\nimport { NextConfig } from '../../../../server/config'\n\nconst getCustomRouteMatcher = pathMatch(true)\n\nexport const vercelHeader = 'x-vercel-id'\n\nexport type ServerlessHandlerCtx = {\n  page: string\n\n  pageModule: any\n  pageComponent?: any\n  pageConfig?: any\n  pageGetStaticProps?: GetStaticProps\n  pageGetStaticPaths?: GetStaticPaths\n  pageGetServerSideProps?: GetServerSideProps\n\n  appModule?: any\n  errorModule?: any\n  documentModule?: any\n  notFoundModule?: any\n\n  runtimeConfig: any\n  buildManifest?: BuildManifest\n  reactLoadableManifest?: any\n  basePath: string\n  rewrites: Rewrite[]\n  pageIsDynamic: boolean\n  generateEtags: boolean\n  distDir: string\n  buildId: string\n  escapedBuildId: string\n  assetPrefix: string\n  poweredByHeader: boolean\n  canonicalBase: string\n  encodedPreviewProps: __ApiPreviewProps\n  i18n?: NextConfig['i18n']\n}\n\nexport function getUtils({\n  page,\n  i18n,\n  basePath,\n  rewrites,\n  pageIsDynamic,\n}: {\n  page: ServerlessHandlerCtx['page']\n  i18n?: ServerlessHandlerCtx['i18n']\n  basePath: ServerlessHandlerCtx['basePath']\n  rewrites: ServerlessHandlerCtx['rewrites']\n  pageIsDynamic: ServerlessHandlerCtx['pageIsDynamic']\n}) {\n  let defaultRouteRegex: ReturnType<typeof getRouteRegex> | undefined\n  let dynamicRouteMatcher: ReturnType<typeof getRouteMatcher> | undefined\n  let defaultRouteMatches: ParsedUrlQuery | undefined\n\n  if (pageIsDynamic) {\n    defaultRouteRegex = getRouteRegex(page)\n    dynamicRouteMatcher = getRouteMatcher(defaultRouteRegex)\n    defaultRouteMatches = dynamicRouteMatcher(page) as ParsedUrlQuery\n  }\n\n  function handleRewrites(req: IncomingMessage, parsedUrl: UrlWithParsedQuery) {\n    for (const rewrite of rewrites) {\n      const matcher = getCustomRouteMatcher(rewrite.source)\n      let params = matcher(parsedUrl.pathname)\n\n      if (rewrite.has && params) {\n        const hasParams = matchHas(req, rewrite.has, parsedUrl.query)\n\n        if (hasParams) {\n          Object.assign(params, hasParams)\n        } else {\n          params = false\n        }\n      }\n\n      if (params) {\n        const { parsedDestination } = prepareDestination(\n          rewrite.destination,\n          params,\n          parsedUrl.query,\n          true\n        )\n\n        Object.assign(parsedUrl.query, parsedDestination.query)\n        delete (parsedDestination as any).query\n\n        Object.assign(parsedUrl, parsedDestination)\n\n        let fsPathname = parsedUrl.pathname\n\n        if (basePath) {\n          fsPathname =\n            fsPathname!.replace(new RegExp(`^${basePath}`), '') || '/'\n        }\n\n        if (i18n) {\n          const destLocalePathResult = normalizeLocalePath(\n            fsPathname!,\n            i18n.locales\n          )\n          fsPathname = destLocalePathResult.pathname\n          parsedUrl.query.nextInternalLocale =\n            destLocalePathResult.detectedLocale || params.nextInternalLocale\n        }\n\n        if (fsPathname === page) {\n          break\n        }\n\n        if (pageIsDynamic && dynamicRouteMatcher) {\n          const dynamicParams = dynamicRouteMatcher(fsPathname)\n          if (dynamicParams) {\n            parsedUrl.query = {\n              ...parsedUrl.query,\n              ...dynamicParams,\n            }\n            break\n          }\n        }\n      }\n    }\n\n    return parsedUrl\n  }\n\n  function handleBasePath(req: IncomingMessage, parsedUrl: UrlWithParsedQuery) {\n    // always strip the basePath if configured since it is required\n    req.url = req.url!.replace(new RegExp(`^${basePath}`), '') || '/'\n    parsedUrl.pathname =\n      parsedUrl.pathname!.replace(new RegExp(`^${basePath}`), '') || '/'\n  }\n\n  function getParamsFromRouteMatches(\n    req: IncomingMessage,\n    renderOpts?: any,\n    detectedLocale?: string\n  ) {\n    return getRouteMatcher(\n      (function () {\n        const { groups, routeKeys } = defaultRouteRegex!\n\n        return {\n          re: {\n            // Simulate a RegExp match from the \\`req.url\\` input\n            exec: (str: string) => {\n              const obj = parseQs(str)\n\n              // favor named matches if available\n              const routeKeyNames = Object.keys(routeKeys || {})\n\n              const filterLocaleItem = (val: string | string[]) => {\n                if (i18n) {\n                  // locale items can be included in route-matches\n                  // for fallback SSG pages so ensure they are\n                  // filtered\n                  const isCatchAll = Array.isArray(val)\n                  const _val = isCatchAll ? val[0] : val\n\n                  if (\n                    typeof _val === 'string' &&\n                    i18n.locales.some((item) => {\n                      if (item.toLowerCase() === _val.toLowerCase()) {\n                        detectedLocale = item\n                        renderOpts.locale = detectedLocale\n                        return true\n                      }\n                      return false\n                    })\n                  ) {\n                    // remove the locale item from the match\n                    if (isCatchAll) {\n                      ;(val as string[]).splice(0, 1)\n                    }\n\n                    // the value is only a locale item and\n                    // shouldn't be added\n                    return isCatchAll ? val.length === 0 : true\n                  }\n                }\n                return false\n              }\n\n              if (routeKeyNames.every((name) => obj[name])) {\n                return routeKeyNames.reduce((prev, keyName) => {\n                  const paramName = routeKeys?.[keyName]\n\n                  if (paramName && !filterLocaleItem(obj[keyName])) {\n                    prev[groups[paramName].pos] = obj[keyName]\n                  }\n                  return prev\n                }, {} as any)\n              }\n\n              return Object.keys(obj).reduce((prev, key) => {\n                if (!filterLocaleItem(obj[key])) {\n                  return Object.assign(prev, {\n                    [key]: obj[key],\n                  })\n                }\n                return prev\n              }, {})\n            },\n          },\n          groups,\n        }\n      })() as any\n    )(req.headers['x-now-route-matches'] as string) as ParsedUrlQuery\n  }\n\n  function interpolateDynamicPath(pathname: string, params: ParsedUrlQuery) {\n    if (!defaultRouteRegex) return pathname\n\n    for (const param of Object.keys(defaultRouteRegex.groups)) {\n      const { optional, repeat } = defaultRouteRegex.groups[param]\n      let builtParam = `[${repeat ? '...' : ''}${param}]`\n\n      if (optional) {\n        builtParam = `[${builtParam}]`\n      }\n\n      const paramIdx = pathname!.indexOf(builtParam)\n\n      if (paramIdx > -1) {\n        let paramValue: string\n\n        if (Array.isArray(params[param])) {\n          paramValue = (params[param] as string[])\n            .map((v) => v && encodeURIComponent(v))\n            .join('/')\n        } else {\n          paramValue =\n            params[param] && encodeURIComponent(params[param] as string)\n        }\n\n        pathname =\n          pathname.substr(0, paramIdx) +\n          (paramValue || '') +\n          pathname.substr(paramIdx + builtParam.length)\n      }\n    }\n\n    return pathname\n  }\n\n  function normalizeVercelUrl(req: IncomingMessage, trustQuery: boolean) {\n    // make sure to normalize req.url on Vercel to strip dynamic params\n    // from the query which are added during routing\n    if (pageIsDynamic && trustQuery && defaultRouteRegex) {\n      const _parsedUrl = parseUrl(req.url!, true)\n      delete (_parsedUrl as any).search\n\n      for (const param of Object.keys(defaultRouteRegex.groups)) {\n        delete _parsedUrl.query[param]\n      }\n      req.url = formatUrl(_parsedUrl)\n    }\n  }\n\n  function normalizeDynamicRouteParams(params: ParsedUrlQuery) {\n    let hasValidParams = true\n    if (!defaultRouteRegex) return { params, hasValidParams: false }\n\n    params = Object.keys(defaultRouteRegex.groups).reduce((prev, key) => {\n      let value: string | string[] | undefined = params[key]\n\n      // if the value matches the default value we can't rely\n      // on the parsed params, this is used to signal if we need\n      // to parse x-now-route-matches or not\n      const defaultValue = defaultRouteMatches![key]\n\n      const isDefaultValue = Array.isArray(defaultValue)\n        ? defaultValue.some((defaultVal) => {\n            return Array.isArray(value)\n              ? value.some((val) => val.includes(defaultVal))\n              : value?.includes(defaultVal)\n          })\n        : value?.includes(defaultValue as string)\n\n      if (isDefaultValue || typeof value === 'undefined') {\n        hasValidParams = false\n      }\n\n      // non-provided optional values should be undefined so normalize\n      // them to undefined\n      if (\n        defaultRouteRegex!.groups[key].optional &&\n        (!value ||\n          (Array.isArray(value) &&\n            value.length === 1 &&\n            // fallback optional catch-all SSG pages have\n            // [[...paramName]] for the root path on Vercel\n            (value[0] === 'index' || value[0] === `[[...${key}]]`)))\n      ) {\n        value = undefined\n        delete params[key]\n      }\n\n      // query values from the proxy aren't already split into arrays\n      // so make sure to normalize catch-all values\n      if (\n        value &&\n        typeof value === 'string' &&\n        defaultRouteRegex!.groups[key].repeat\n      ) {\n        value = value.split('/')\n      }\n\n      if (value) {\n        prev[key] = value\n      }\n      return prev\n    }, {} as ParsedUrlQuery)\n\n    return {\n      params,\n      hasValidParams,\n    }\n  }\n\n  function handleLocale(\n    req: IncomingMessage,\n    res: ServerResponse,\n    parsedUrl: UrlWithParsedQuery,\n    routeNoAssetPath: string,\n    shouldNotRedirect: boolean\n  ) {\n    if (!i18n) return\n    const pathname = parsedUrl.pathname || '/'\n\n    let defaultLocale = i18n.defaultLocale\n    let detectedLocale = detectLocaleCookie(req, i18n.locales)\n    let acceptPreferredLocale\n    try {\n      acceptPreferredLocale =\n        i18n.localeDetection !== false\n          ? accept.language(req.headers['accept-language'], i18n.locales)\n          : detectedLocale\n    } catch (_) {\n      acceptPreferredLocale = detectedLocale\n    }\n\n    const { host } = req.headers || {}\n    // remove port from host and remove port if present\n    const hostname = host && host.split(':')[0].toLowerCase()\n\n    const detectedDomain = detectDomainLocale(i18n.domains, hostname)\n    if (detectedDomain) {\n      defaultLocale = detectedDomain.defaultLocale\n      detectedLocale = defaultLocale\n      ;(req as any).__nextIsLocaleDomain = true\n    }\n\n    // if not domain specific locale use accept-language preferred\n    detectedLocale = detectedLocale || acceptPreferredLocale\n\n    let localeDomainRedirect\n    const localePathResult = normalizeLocalePath(pathname, i18n.locales)\n\n    routeNoAssetPath = normalizeLocalePath(\n      routeNoAssetPath,\n      i18n.locales\n    ).pathname\n\n    if (localePathResult.detectedLocale) {\n      detectedLocale = localePathResult.detectedLocale\n      req.url = formatUrl({\n        ...parsedUrl,\n        pathname: localePathResult.pathname,\n      })\n      ;(req as any).__nextStrippedLocale = true\n      parsedUrl.pathname = localePathResult.pathname\n    }\n\n    // If a detected locale is a domain specific locale and we aren't already\n    // on that domain and path prefix redirect to it to prevent duplicate\n    // content from multiple domains\n    if (detectedDomain) {\n      const localeToCheck = localePathResult.detectedLocale\n        ? detectedLocale\n        : acceptPreferredLocale\n\n      const matchedDomain = detectDomainLocale(\n        i18n.domains,\n        undefined,\n        localeToCheck\n      )\n\n      if (matchedDomain && matchedDomain.domain !== detectedDomain.domain) {\n        localeDomainRedirect = `http${matchedDomain.http ? '' : 's'}://${\n          matchedDomain.domain\n        }/${localeToCheck === matchedDomain.defaultLocale ? '' : localeToCheck}`\n      }\n    }\n\n    const denormalizedPagePath = denormalizePagePath(pathname)\n    const detectedDefaultLocale =\n      !detectedLocale ||\n      detectedLocale.toLowerCase() === defaultLocale.toLowerCase()\n    const shouldStripDefaultLocale = false\n    // detectedDefaultLocale &&\n    // denormalizedPagePath.toLowerCase() === \\`/\\${i18n.defaultLocale.toLowerCase()}\\`\n\n    const shouldAddLocalePrefix =\n      !detectedDefaultLocale && denormalizedPagePath === '/'\n\n    detectedLocale = detectedLocale || i18n.defaultLocale\n\n    if (\n      !shouldNotRedirect &&\n      !req.headers[vercelHeader] &&\n      i18n.localeDetection !== false &&\n      (localeDomainRedirect ||\n        shouldAddLocalePrefix ||\n        shouldStripDefaultLocale)\n    ) {\n      // set the NEXT_LOCALE cookie when a user visits the default locale\n      // with the locale prefix so that they aren't redirected back to\n      // their accept-language preferred locale\n      if (shouldStripDefaultLocale && acceptPreferredLocale !== defaultLocale) {\n        const previous = res.getHeader('set-cookie')\n\n        res.setHeader('set-cookie', [\n          ...(typeof previous === 'string'\n            ? [previous]\n            : Array.isArray(previous)\n            ? previous\n            : []),\n          cookie.serialize('NEXT_LOCALE', defaultLocale, {\n            httpOnly: true,\n            path: '/',\n          }),\n        ])\n      }\n\n      res.setHeader(\n        'Location',\n        formatUrl({\n          // make sure to include any query values when redirecting\n          ...parsedUrl,\n          pathname: localeDomainRedirect\n            ? localeDomainRedirect\n            : shouldStripDefaultLocale\n            ? basePath || '/'\n            : `${basePath}/${detectedLocale}`,\n        })\n      )\n      res.statusCode = TEMPORARY_REDIRECT_STATUS\n      res.end()\n      return\n    }\n\n    detectedLocale =\n      localePathResult.detectedLocale ||\n      (detectedDomain && detectedDomain.defaultLocale) ||\n      defaultLocale\n\n    return {\n      defaultLocale,\n      detectedLocale,\n      routeNoAssetPath,\n    }\n  }\n\n  return {\n    handleLocale,\n    handleRewrites,\n    handleBasePath,\n    defaultRouteRegex,\n    normalizeVercelUrl,\n    dynamicRouteMatcher,\n    defaultRouteMatches,\n    interpolateDynamicPath,\n    getParamsFromRouteMatches,\n    normalizeDynamicRouteParams,\n  }\n}\n"], "names": [], "mappings": ";;;;QA8DgB,QAAQ,GAAR,QAAQ;;AA7DmD,GAAK,CAAL,IAAK;AAC/B,GAAa,CAAb,YAAa;AAE1B,GAAmD,CAAnD,oBAAmD;AACjE,GAAgD,CAAhD,UAAgD;AACxC,GAAiD,CAAjD,WAAiD;AAC/C,GAAmD,CAAnD,aAAmD;AAG5E,GAAyD,CAAzD,mBAAyD;AAQ7C,GAAc,CAAd,OAAc;AACE,GAAkD,CAAlD,mBAAkD;AAClD,GAAkD,CAAlD,mBAAkD;AACjD,GAA0C,CAA1C,oBAA0C;AAC3D,GAA2B,CAA3B,OAA2B;AACJ,GAAkC,CAAlC,UAAkC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAG5E,KAAK,CAAC,qBAAqB,OArBL,UAAgD,UAqB9B,IAAI;AAErC,KAAK,CAAC,YAAY,IAAG,WAAa;QAA5B,YAAY,GAAZ,YAAY;SAkCT,QAAQ,GACtB,IAAI,GACJ,IAAI,GACJ,QAAQ,GACR,QAAQ,GACR,aAAa,KAOZ,CAAC;IACF,GAAG,CAAC,iBAAiB;IACrB,GAAG,CAAC,mBAAmB;IACvB,GAAG,CAAC,mBAAmB;IAEvB,EAAE,EAAE,aAAa,EAAE,CAAC;QAClB,iBAAiB,OA1ES,WAAiD,gBA0EzC,IAAI;QACtC,mBAAmB,OA1ES,aAAmD,kBA0EzC,iBAAiB;QACvD,mBAAmB,GAAG,mBAAmB,CAAC,IAAI;IAChD,CAAC;aAEQ,cAAc,CAAC,GAAoB,EAAE,SAA6B,EAAE,CAAC;aACvE,KAAK,CAAC,OAAO,IAAI,QAAQ,CAAE,CAAC;YAC/B,KAAK,CAAC,OAAO,GAAG,qBAAqB,CAAC,OAAO,CAAC,MAAM;YACpD,GAAG,CAAC,MAAM,GAAG,OAAO,CAAC,SAAS,CAAC,QAAQ;YAEvC,EAAE,EAAE,OAAO,CAAC,GAAG,IAAI,MAAM,EAAE,CAAC;gBAC1B,KAAK,CAAC,SAAS,OAjFhB,mBAAyD,WAiF7B,GAAG,EAAE,OAAO,CAAC,GAAG,EAAE,SAAS,CAAC,KAAK;gBAE5D,EAAE,EAAE,SAAS,EAAE,CAAC;oBACd,MAAM,CAAC,MAAM,CAAC,MAAM,EAAE,SAAS;gBACjC,CAAC,MAAM,CAAC;oBACN,MAAM,GAAG,KAAK;gBAChB,CAAC;YACH,CAAC;YAED,EAAE,EAAE,MAAM,EAAE,CAAC;gBACX,KAAK,GAAG,iBAAiB,UA3F1B,mBAAyD,UA4FtD,OAAO,CAAC,WAAW,EACnB,MAAM,EACN,SAAS,CAAC,KAAK,EACf,IAAI;gBAGN,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,KAAK,EAAE,iBAAiB,CAAC,KAAK;uBAC9C,iBAAiB,CAAS,KAAK;gBAEvC,MAAM,CAAC,MAAM,CAAC,SAAS,EAAE,iBAAiB;gBAE1C,GAAG,CAAC,UAAU,GAAG,SAAS,CAAC,QAAQ;gBAEnC,EAAE,EAAE,QAAQ,EAAE,CAAC;oBACb,UAAU,GACR,UAAU,CAAE,OAAO,CAAC,GAAG,CAAC,MAAM,EAAE,CAAC,EAAE,QAAQ,aAAY,CAAG;gBAC9D,CAAC;gBAED,EAAE,EAAE,IAAI,EAAE,CAAC;oBACT,KAAK,CAAC,oBAAoB,OArHA,oBAAmD,sBAsH3E,UAAU,EACV,IAAI,CAAC,OAAO;oBAEd,UAAU,GAAG,oBAAoB,CAAC,QAAQ;oBAC1C,SAAS,CAAC,KAAK,CAAC,kBAAkB,GAChC,oBAAoB,CAAC,cAAc,IAAI,MAAM,CAAC,kBAAkB;gBACpE,CAAC;gBAED,EAAE,EAAE,UAAU,KAAK,IAAI,EAAE,CAAC;;gBAE1B,CAAC;gBAED,EAAE,EAAE,aAAa,IAAI,mBAAmB,EAAE,CAAC;oBACzC,KAAK,CAAC,aAAa,GAAG,mBAAmB,CAAC,UAAU;oBACpD,EAAE,EAAE,aAAa,EAAE,CAAC;wBAClB,SAAS,CAAC,KAAK;+BACV,SAAS,CAAC,KAAK;+BACf,aAAa;;;oBAGpB,CAAC;gBACH,CAAC;YACH,CAAC;QACH,CAAC;eAEM,SAAS;IAClB,CAAC;aAEQ,cAAc,CAAC,GAAoB,EAAE,SAA6B,EAAE,CAAC;QAC5E,EAA+D,AAA/D,6DAA+D;QAC/D,GAAG,CAAC,GAAG,GAAG,GAAG,CAAC,GAAG,CAAE,OAAO,CAAC,GAAG,CAAC,MAAM,EAAE,CAAC,EAAE,QAAQ,aAAY,CAAG;QACjE,SAAS,CAAC,QAAQ,GAChB,SAAS,CAAC,QAAQ,CAAE,OAAO,CAAC,GAAG,CAAC,MAAM,EAAE,CAAC,EAAE,QAAQ,aAAY,CAAG;IACtE,CAAC;aAEQ,yBAAyB,CAChC,GAAoB,EACpB,UAAgB,EAChB,cAAuB,EACvB,CAAC;mBA1J2B,aAAmD,6BA4JhE,CAAC;YACZ,KAAK,GAAG,MAAM,GAAE,SAAS,MAAK,iBAAiB;;gBAG7C,EAAE;oBACA,EAAqD,AAArD,mDAAqD;oBACrD,IAAI,GAAG,GAAW,GAAK,CAAC;wBACtB,KAAK,CAAC,GAAG,OAxK0B,YAAa,QAwK5B,GAAG;wBAEvB,EAAmC,AAAnC,iCAAmC;wBACnC,KAAK,CAAC,aAAa,GAAG,MAAM,CAAC,IAAI,CAAC,SAAS;;wBAE3C,KAAK,CAAC,gBAAgB,IAAI,GAAsB,GAAK,CAAC;4BACpD,EAAE,EAAE,IAAI,EAAE,CAAC;gCACT,EAAgD,AAAhD,8CAAgD;gCAChD,EAA4C,AAA5C,0CAA4C;gCAC5C,EAAW,AAAX,SAAW;gCACX,KAAK,CAAC,UAAU,GAAG,KAAK,CAAC,OAAO,CAAC,GAAG;gCACpC,KAAK,CAAC,IAAI,GAAG,UAAU,GAAG,GAAG,CAAC,CAAC,IAAI,GAAG;gCAEtC,EAAE,SACO,IAAI,MAAK,MAAQ,KACxB,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,IAAI,GAAK,CAAC;oCAC3B,EAAE,EAAE,IAAI,CAAC,WAAW,OAAO,IAAI,CAAC,WAAW,IAAI,CAAC;wCAC9C,cAAc,GAAG,IAAI;wCACrB,UAAU,CAAC,MAAM,GAAG,cAAc;+CAC3B,IAAI;oCACb,CAAC;2CACM,KAAK;gCACd,CAAC,GACD,CAAC;oCACD,EAAwC,AAAxC,sCAAwC;oCACxC,EAAE,EAAE,UAAU,EAAE,CAAC;wCACb,GAAG,CAAc,MAAM,CAAC,CAAC,EAAE,CAAC;oCAChC,CAAC;oCAED,EAAsC,AAAtC,oCAAsC;oCACtC,EAAqB,AAArB,mBAAqB;2CACd,UAAU,GAAG,GAAG,CAAC,MAAM,KAAK,CAAC,GAAG,IAAI;gCAC7C,CAAC;4BACH,CAAC;mCACM,KAAK;wBACd,CAAC;wBAED,EAAE,EAAE,aAAa,CAAC,KAAK,EAAE,IAAI,GAAK,GAAG,CAAC,IAAI;2BAAI,CAAC;mCACtC,aAAa,CAAC,MAAM,EAAE,IAAI,EAAE,OAAO,GAAK,CAAC;gCAC9C,KAAK,CAAC,SAAS,GAAG,SAAS,aAAT,SAAS,UAAT,CAAoB,QAApB,CAAoB,GAApB,SAAS,CAAG,OAAO;gCAErC,EAAE,EAAE,SAAS,KAAK,gBAAgB,CAAC,GAAG,CAAC,OAAO,IAAI,CAAC;oCACjD,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,GAAG,IAAI,GAAG,CAAC,OAAO;gCAC3C,CAAC;uCACM,IAAI;4BACb,CAAC;;wBACH,CAAC;+BAEM,MAAM,CAAC,IAAI,CAAC,GAAG,EAAE,MAAM,EAAE,IAAI,EAAE,GAAG,GAAK,CAAC;4BAC7C,EAAE,GAAG,gBAAgB,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC;uCACzB,MAAM,CAAC,MAAM,CAAC,IAAI;qCACtB,GAAG,GAAG,GAAG,CAAC,GAAG;;4BAElB,CAAC;mCACM,IAAI;wBACb,CAAC;;oBACH,CAAC;;gBAEH,MAAM;;QAEV,CAAC,IACD,GAAG,CAAC,OAAO,EAAC,mBAAqB;IACrC,CAAC;aAEQ,sBAAsB,CAAC,QAAgB,EAAE,MAAsB,EAAE,CAAC;QACzE,EAAE,GAAG,iBAAiB,SAAS,QAAQ;aAElC,KAAK,CAAC,KAAK,IAAI,MAAM,CAAC,IAAI,CAAC,iBAAiB,CAAC,MAAM,EAAG,CAAC;YAC1D,KAAK,GAAG,QAAQ,GAAE,MAAM,MAAK,iBAAiB,CAAC,MAAM,CAAC,KAAK;YAC3D,GAAG,CAAC,UAAU,IAAI,CAAC,EAAE,MAAM,IAAG,GAAK,SAAQ,KAAK,CAAC,CAAC;YAElD,EAAE,EAAE,QAAQ,EAAE,CAAC;gBACb,UAAU,IAAI,CAAC,EAAE,UAAU,CAAC,CAAC;YAC/B,CAAC;YAED,KAAK,CAAC,QAAQ,GAAG,QAAQ,CAAE,OAAO,CAAC,UAAU;YAE7C,EAAE,EAAE,QAAQ,IAAI,CAAC,EAAE,CAAC;gBAClB,GAAG,CAAC,UAAU;gBAEd,EAAE,EAAE,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,IAAI,CAAC;oBACjC,UAAU,GAAI,MAAM,CAAC,KAAK,EACvB,GAAG,EAAE,CAAC,GAAK,CAAC,IAAI,kBAAkB,CAAC,CAAC;sBACpC,IAAI,EAAC,CAAG;gBACb,CAAC,MAAM,CAAC;oBACN,UAAU,GACR,MAAM,CAAC,KAAK,KAAK,kBAAkB,CAAC,MAAM,CAAC,KAAK;gBACpD,CAAC;gBAED,QAAQ,GACN,QAAQ,CAAC,MAAM,CAAC,CAAC,EAAE,QAAQ,KAC1B,UAAU,UACX,QAAQ,CAAC,MAAM,CAAC,QAAQ,GAAG,UAAU,CAAC,MAAM;YAChD,CAAC;QACH,CAAC;eAEM,QAAQ;IACjB,CAAC;aAEQ,kBAAkB,CAAC,GAAoB,EAAE,UAAmB,EAAE,CAAC;QACtE,EAAmE,AAAnE,iEAAmE;QACnE,EAAgD,AAAhD,8CAAgD;QAChD,EAAE,EAAE,aAAa,IAAI,UAAU,IAAI,iBAAiB,EAAE,CAAC;YACrD,KAAK,CAAC,UAAU,OAhRqD,IAAK,QAgR9C,GAAG,CAAC,GAAG,EAAG,IAAI;mBAClC,UAAU,CAAS,MAAM;iBAE5B,KAAK,CAAC,KAAK,IAAI,MAAM,CAAC,IAAI,CAAC,iBAAiB,CAAC,MAAM,EAAG,CAAC;uBACnD,UAAU,CAAC,KAAK,CAAC,KAAK;YAC/B,CAAC;YACD,GAAG,CAAC,GAAG,OAtR8D,IAAK,SAsRtD,UAAU;QAChC,CAAC;IACH,CAAC;aAEQ,2BAA2B,CAAC,MAAsB,EAAE,CAAC;QAC5D,GAAG,CAAC,cAAc,GAAG,IAAI;QACzB,EAAE,GAAG,iBAAiB;YAAW,MAAM;YAAE,cAAc,EAAE,KAAK;;QAE9D,MAAM,GAAG,MAAM,CAAC,IAAI,CAAC,iBAAiB,CAAC,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,GAAG,GAAK,CAAC;YACpE,GAAG,CAAC,KAAK,GAAkC,MAAM,CAAC,GAAG;YAErD,EAAuD,AAAvD,qDAAuD;YACvD,EAA0D,AAA1D,wDAA0D;YAC1D,EAAsC,AAAtC,oCAAsC;YACtC,KAAK,CAAC,YAAY,GAAG,mBAAmB,CAAE,GAAG;YAE7C,KAAK,CAAC,cAAc,GAAG,KAAK,CAAC,OAAO,CAAC,YAAY,IAC7C,YAAY,CAAC,IAAI,EAAE,UAAU,GAAK,CAAC;uBAC1B,KAAK,CAAC,OAAO,CAAC,KAAK,IACtB,KAAK,CAAC,IAAI,EAAE,GAAG,GAAK,GAAG,CAAC,QAAQ,CAAC,UAAU;oBAC3C,KAAK,aAAL,KAAK,UAAL,CAAe,QAAf,CAAe,GAAf,KAAK,CAAE,QAAQ,CAAC,UAAU;YAChC,CAAC,IACD,KAAK,aAAL,KAAK,UAAL,CAAe,QAAf,CAAe,GAAf,KAAK,CAAE,QAAQ,CAAC,YAAY;YAEhC,EAAE,EAAE,cAAc,WAAW,KAAK,MAAK,SAAW,GAAE,CAAC;gBACnD,cAAc,GAAG,KAAK;YACxB,CAAC;YAED,EAAgE,AAAhE,8DAAgE;YAChE,EAAoB,AAApB,kBAAoB;YACpB,EAAE,EACA,iBAAiB,CAAE,MAAM,CAAC,GAAG,EAAE,QAAQ,MACrC,KAAK,IACJ,KAAK,CAAC,OAAO,CAAC,KAAK,KAClB,KAAK,CAAC,MAAM,KAAK,CAAC,IAClB,EAA6C,AAA7C,2CAA6C;YAC7C,EAA+C,AAA/C,6CAA+C;aAC9C,KAAK,CAAC,CAAC,OAAM,KAAO,KAAI,KAAK,CAAC,CAAC,OAAO,KAAK,EAAE,GAAG,CAAC,EAAE,KACxD,CAAC;gBACD,KAAK,GAAG,SAAS;uBACV,MAAM,CAAC,GAAG;YACnB,CAAC;YAED,EAA+D,AAA/D,6DAA+D;YAC/D,EAA6C,AAA7C,2CAA6C;YAC7C,EAAE,EACA,KAAK,WACE,KAAK,MAAK,MAAQ,KACzB,iBAAiB,CAAE,MAAM,CAAC,GAAG,EAAE,MAAM,EACrC,CAAC;gBACD,KAAK,GAAG,KAAK,CAAC,KAAK,EAAC,CAAG;YACzB,CAAC;YAED,EAAE,EAAE,KAAK,EAAE,CAAC;gBACV,IAAI,CAAC,GAAG,IAAI,KAAK;YACnB,CAAC;mBACM,IAAI;QACb,CAAC;;;YAGC,MAAM;YACN,cAAc;;IAElB,CAAC;aAEQ,YAAY,CACnB,GAAoB,EACpB,GAAmB,EACnB,SAA6B,EAC7B,gBAAwB,EACxB,iBAA0B,EAC1B,CAAC;QACD,EAAE,GAAG,IAAI;QACT,KAAK,CAAC,QAAQ,GAAG,SAAS,CAAC,QAAQ,KAAI,CAAG;QAE1C,GAAG,CAAC,aAAa,GAAG,IAAI,CAAC,aAAa;QACtC,GAAG,CAAC,cAAc,OAhVa,mBAAkD,qBAgVzC,GAAG,EAAE,IAAI,CAAC,OAAO;QACzD,GAAG,CAAC,qBAAqB;YACrB,CAAC;YACH,qBAAqB,GACnB,IAAI,CAAC,eAAe,KAAK,KAAK,GArVnB,OAAc,SAsVd,QAAQ,CAAC,GAAG,CAAC,OAAO,EAAC,eAAiB,IAAG,IAAI,CAAC,OAAO,IAC5D,cAAc;QACtB,CAAC,QAAQ,CAAC,EAAE,CAAC;YACX,qBAAqB,GAAG,cAAc;QACxC,CAAC;QAED,KAAK,GAAG,IAAI,MAAK,GAAG,CAAC,OAAO;;QAC5B,EAAmD,AAAnD,iDAAmD;QACnD,KAAK,CAAC,QAAQ,GAAG,IAAI,IAAI,IAAI,CAAC,KAAK,EAAC,CAAG,GAAE,CAAC,EAAE,WAAW;QAEvD,KAAK,CAAC,cAAc,OA9VW,mBAAkD,qBA8VvC,IAAI,CAAC,OAAO,EAAE,QAAQ;QAChE,EAAE,EAAE,cAAc,EAAE,CAAC;YACnB,aAAa,GAAG,cAAc,CAAC,aAAa;YAC5C,cAAc,GAAG,aAAa;YAC5B,GAAG,CAAS,oBAAoB,GAAG,IAAI;QAC3C,CAAC;QAED,EAA8D,AAA9D,4DAA8D;QAC9D,cAAc,GAAG,cAAc,IAAI,qBAAqB;QAExD,GAAG,CAAC,oBAAoB;QACxB,KAAK,CAAC,gBAAgB,OAzXU,oBAAmD,sBAyXtC,QAAQ,EAAE,IAAI,CAAC,OAAO;QAEnE,gBAAgB,OA3XgB,oBAAmD,sBA4XjF,gBAAgB,EAChB,IAAI,CAAC,OAAO,EACZ,QAAQ;QAEV,EAAE,EAAE,gBAAgB,CAAC,cAAc,EAAE,CAAC;YACpC,cAAc,GAAG,gBAAgB,CAAC,cAAc;YAChD,GAAG,CAAC,GAAG,OArY8D,IAAK;mBAsYrE,SAAS;gBACZ,QAAQ,EAAE,gBAAgB,CAAC,QAAQ;;YAEnC,GAAG,CAAS,oBAAoB,GAAG,IAAI;YACzC,SAAS,CAAC,QAAQ,GAAG,gBAAgB,CAAC,QAAQ;QAChD,CAAC;QAED,EAAyE,AAAzE,uEAAyE;QACzE,EAAqE,AAArE,mEAAqE;QACrE,EAAgC,AAAhC,8BAAgC;QAChC,EAAE,EAAE,cAAc,EAAE,CAAC;YACnB,KAAK,CAAC,aAAa,GAAG,gBAAgB,CAAC,cAAc,GACjD,cAAc,GACd,qBAAqB;YAEzB,KAAK,CAAC,aAAa,OAlYU,mBAAkD,qBAmY7E,IAAI,CAAC,OAAO,EACZ,SAAS,EACT,aAAa;YAGf,EAAE,EAAE,aAAa,IAAI,aAAa,CAAC,MAAM,KAAK,cAAc,CAAC,MAAM,EAAE,CAAC;gBACpE,oBAAoB,IAAI,IAAI,EAAE,aAAa,CAAC,IAAI,SAAQ,CAAG,EAAC,GAAG,EAC7D,aAAa,CAAC,MAAM,CACrB,CAAC,EAAE,aAAa,KAAK,aAAa,CAAC,aAAa,QAAQ,aAAa;YACxE,CAAC;QACH,CAAC;QAED,KAAK,CAAC,oBAAoB,OA9YM,oBAA0C,sBA8YzB,QAAQ;QACzD,KAAK,CAAC,qBAAqB,IACxB,cAAc,IACf,cAAc,CAAC,WAAW,OAAO,aAAa,CAAC,WAAW;QAC5D,KAAK,CAAC,wBAAwB,GAAG,KAAK;QACtC,EAA2B,AAA3B,yBAA2B;QAC3B,EAAmF,AAAnF,iFAAmF;QAEnF,KAAK,CAAC,qBAAqB,IACxB,qBAAqB,IAAI,oBAAoB,MAAK,CAAG;QAExD,cAAc,GAAG,cAAc,IAAI,IAAI,CAAC,aAAa;QAErD,EAAE,GACC,iBAAiB,KACjB,GAAG,CAAC,OAAO,CAAC,YAAY,KACzB,IAAI,CAAC,eAAe,KAAK,KAAK,KAC7B,oBAAoB,IACnB,qBAAqB,IACrB,wBAAwB,GAC1B,CAAC;YACD,EAAmE,AAAnE,iEAAmE;YACnE,EAAgE,AAAhE,8DAAgE;YAChE,EAAyC,AAAzC,uCAAyC;YACzC,EAAE,EAAE,wBAAwB,IAAI,qBAAqB,KAAK,aAAa,EAAE,CAAC;gBACxE,KAAK,CAAC,QAAQ,GAAG,GAAG,CAAC,SAAS,EAAC,UAAY;gBAE3C,GAAG,CAAC,SAAS,EAAC,UAAY;8BACb,QAAQ,MAAK,MAAQ;wBAC3B,QAAQ;wBACT,KAAK,CAAC,OAAO,CAAC,QAAQ,IACtB,QAAQ;oBA5aH,OAA2B,SA8a7B,SAAS,EAAC,WAAa,GAAE,aAAa;wBAC3C,QAAQ,EAAE,IAAI;wBACd,IAAI,GAAE,CAAG;;;YAGf,CAAC;YAED,GAAG,CAAC,SAAS,EACX,QAAU,OA3cyD,IAAK;gBA6ctE,EAAyD,AAAzD,uDAAyD;mBACtD,SAAS;gBACZ,QAAQ,EAAE,oBAAoB,GAC1B,oBAAoB,GACpB,wBAAwB,GACxB,QAAQ,KAAI,CAAG,OACZ,QAAQ,CAAC,CAAC,EAAE,cAAc;;YAGrC,GAAG,CAAC,UAAU,GAhcsB,UAAkC;YAictE,GAAG,CAAC,GAAG;;QAET,CAAC;QAED,cAAc,GACZ,gBAAgB,CAAC,cAAc,IAC9B,cAAc,IAAI,cAAc,CAAC,aAAa,IAC/C,aAAa;;YAGb,aAAa;YACb,cAAc;YACd,gBAAgB;;IAEpB,CAAC;;QAGC,YAAY;QACZ,cAAc;QACd,cAAc;QACd,iBAAiB;QACjB,kBAAkB;QAClB,mBAAmB;QACnB,mBAAmB;QACnB,sBAAsB;QACtB,yBAAyB;QACzB,2BAA2B;;AAE/B,CAAC"}