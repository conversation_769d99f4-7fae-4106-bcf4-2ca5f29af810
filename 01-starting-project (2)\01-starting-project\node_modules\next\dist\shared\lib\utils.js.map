{"version": 3, "sources": ["../../../shared/lib/utils.ts"], "sourcesContent": ["import { formatUrl } from './router/utils/format-url'\nimport type { BuildManifest } from '../../server/get-page-files'\nimport type { ComponentType } from 'react'\nimport type { DomainLocale } from '../../server/config'\nimport type { Env } from '@next/env'\nimport type { IncomingMessage, ServerResponse } from 'http'\nimport type { NextRouter } from './router/router'\nimport type { ParsedUrlQuery } from 'querystring'\nimport type { PreviewData } from 'next/types'\nimport type { UrlObject } from 'url'\nimport { createContext } from 'react'\n\nexport type NextComponentType<\n  C extends BaseContext = NextPageContext,\n  IP = {},\n  P = {}\n> = ComponentType<P> & {\n  /**\n   * Used for initial page load data population. Data returned from `getInitialProps` is serialized when server rendered.\n   * Make sure to return plain `Object` without using `Date`, `Map`, `Set`.\n   * @param ctx Context of `page`\n   */\n  getInitialProps?(context: C): IP | Promise<IP>\n}\n\nexport type DocumentType = NextComponentType<\n  DocumentContext,\n  DocumentInitialProps,\n  DocumentProps\n>\n\nexport type AppType = NextComponentType<\n  AppContextType,\n  AppInitialProps,\n  AppPropsType\n>\n\nexport type AppTreeType = ComponentType<\n  AppInitialProps & { [name: string]: any }\n>\n\n/**\n * Web vitals provided to _app.reportWebVitals by Core Web Vitals plugin developed by Google Chrome team.\n * https://nextjs.org/blog/next-9-4#integrated-web-vitals-reporting\n */\nexport type NextWebVitalsMetric = {\n  id: string\n  startTime: number\n  value: number\n} & (\n  | {\n      label: 'web-vital'\n      name: 'FCP' | 'LCP' | 'CLS' | 'FID' | 'TTFB'\n    }\n  | {\n      label: 'custom'\n      name:\n        | 'Next.js-hydration'\n        | 'Next.js-route-change-to-render'\n        | 'Next.js-render'\n    }\n)\n\nexport type Enhancer<C> = (Component: C) => C\n\nexport type ComponentsEnhancer =\n  | {\n      enhanceApp?: Enhancer<AppType>\n      enhanceComponent?: Enhancer<NextComponentType>\n    }\n  | Enhancer<NextComponentType>\n\nexport type RenderPageResult = {\n  html: string\n  head?: Array<JSX.Element | null>\n}\n\nexport type RenderPage = (\n  options?: ComponentsEnhancer\n) => RenderPageResult | Promise<RenderPageResult>\n\nexport type BaseContext = {\n  res?: ServerResponse\n  [k: string]: any\n}\n\nexport type NEXT_DATA = {\n  props: Record<string, any>\n  page: string\n  query: ParsedUrlQuery\n  buildId: string\n  assetPrefix?: string\n  runtimeConfig?: { [key: string]: any }\n  nextExport?: boolean\n  autoExport?: boolean\n  isFallback?: boolean\n  dynamicIds?: (string | number)[]\n  err?: Error & { statusCode?: number }\n  gsp?: boolean\n  gssp?: boolean\n  customServer?: boolean\n  gip?: boolean\n  appGip?: boolean\n  locale?: string\n  locales?: string[]\n  defaultLocale?: string\n  domainLocales?: DomainLocale[]\n  scriptLoader?: any[]\n  isPreview?: boolean\n}\n\n/**\n * `Next` context\n */\nexport interface NextPageContext {\n  /**\n   * Error object if encountered during rendering\n   */\n  err?: (Error & { statusCode?: number }) | null\n  /**\n   * `HTTP` request object.\n   */\n  req?: IncomingMessage\n  /**\n   * `HTTP` response object.\n   */\n  res?: ServerResponse\n  /**\n   * Path section of `URL`.\n   */\n  pathname: string\n  /**\n   * Query string section of `URL` parsed as an object.\n   */\n  query: ParsedUrlQuery\n  /**\n   * `String` of the actual path including query.\n   */\n  asPath?: string\n  /**\n   * The currently active locale\n   */\n  locale?: string\n  /**\n   * All configured locales\n   */\n  locales?: string[]\n  /**\n   * The configured default locale\n   */\n  defaultLocale?: string\n  /**\n   * `Component` the tree of the App to use if needing to render separately\n   */\n  AppTree: AppTreeType\n}\n\nexport type AppContextType<R extends NextRouter = NextRouter> = {\n  Component: NextComponentType<NextPageContext>\n  AppTree: AppTreeType\n  ctx: NextPageContext\n  router: R\n}\n\nexport type AppInitialProps = {\n  pageProps: any\n}\n\nexport type AppPropsType<\n  R extends NextRouter = NextRouter,\n  P = {}\n> = AppInitialProps & {\n  Component: NextComponentType<NextPageContext, any, P>\n  router: R\n  __N_SSG?: boolean\n  __N_SSP?: boolean\n}\n\nexport type DocumentContext = NextPageContext & {\n  renderPage: RenderPage\n}\n\nexport type DocumentInitialProps = RenderPageResult & {\n  styles?: React.ReactElement[] | React.ReactFragment\n}\n\nexport type DocumentProps = DocumentInitialProps & HtmlProps\n\nexport type HtmlProps = {\n  __NEXT_DATA__: NEXT_DATA\n  dangerousAsPath: string\n  docComponentsRendered: {\n    Html?: boolean\n    Main?: boolean\n    Head?: boolean\n    NextScript?: boolean\n  }\n  buildManifest: BuildManifest\n  ampPath: string\n  inAmpMode: boolean\n  hybridAmp: boolean\n  isDevelopment: boolean\n  dynamicImports: string[]\n  assetPrefix?: string\n  canonicalBase: string\n  headTags: any[]\n  unstable_runtimeJS?: false\n  unstable_JsPreload?: false\n  devOnlyCacheBusterQueryString: string\n  scriptLoader: { afterInteractive?: string[]; beforeInteractive?: any[] }\n  locale?: string\n  disableOptimizedLoading?: boolean\n  styles?: React.ReactElement[] | React.ReactFragment\n  head?: Array<JSX.Element | null>\n}\n\n/**\n * Next `API` route request\n */\nexport interface NextApiRequest extends IncomingMessage {\n  /**\n   * Object of `query` values from url\n   */\n  query: {\n    [key: string]: string | string[]\n  }\n  /**\n   * Object of `cookies` from header\n   */\n  cookies: {\n    [key: string]: string\n  }\n\n  body: any\n\n  env: Env\n\n  preview?: boolean\n  /**\n   * Preview data set on the request, if any\n   * */\n  previewData?: PreviewData\n}\n\n/**\n * Send body of response\n */\ntype Send<T> = (body: T) => void\n\n/**\n * Next `API` route response\n */\nexport type NextApiResponse<T = any> = ServerResponse & {\n  /**\n   * Send data `any` data in response\n   */\n  send: Send<T>\n  /**\n   * Send data `json` data in response\n   */\n  json: Send<T>\n  status: (statusCode: number) => NextApiResponse<T>\n  redirect(url: string): NextApiResponse<T>\n  redirect(status: number, url: string): NextApiResponse<T>\n\n  /**\n   * Set preview data for Next.js' prerender mode\n   */\n  setPreviewData: (\n    data: object | string,\n    options?: {\n      /**\n       * Specifies the number (in seconds) for the preview session to last for.\n       * The given number will be converted to an integer by rounding down.\n       * By default, no maximum age is set and the preview session finishes\n       * when the client shuts down (browser is closed).\n       */\n      maxAge?: number\n    }\n  ) => NextApiResponse<T>\n  clearPreviewData: () => NextApiResponse<T>\n}\n\n/**\n * Next `API` route handler\n */\nexport type NextApiHandler<T = any> = (\n  req: NextApiRequest,\n  res: NextApiResponse<T>\n) => void | Promise<void>\n\n/**\n * Utils\n */\nexport function execOnce<T extends (...args: any[]) => ReturnType<T>>(\n  fn: T\n): T {\n  let used = false\n  let result: ReturnType<T>\n\n  return ((...args: any[]) => {\n    if (!used) {\n      used = true\n      result = fn(...args)\n    }\n    return result\n  }) as T\n}\n\nexport function getLocationOrigin() {\n  const { protocol, hostname, port } = window.location\n  return `${protocol}//${hostname}${port ? ':' + port : ''}`\n}\n\nexport function getURL() {\n  const { href } = window.location\n  const origin = getLocationOrigin()\n  return href.substring(origin.length)\n}\n\nexport function getDisplayName<P>(Component: ComponentType<P>) {\n  return typeof Component === 'string'\n    ? Component\n    : Component.displayName || Component.name || 'Unknown'\n}\n\nexport function isResSent(res: ServerResponse) {\n  return res.finished || res.headersSent\n}\n\nexport function normalizeRepeatedSlashes(url: string) {\n  const urlParts = url.split('?')\n  const urlNoQuery = urlParts[0]\n\n  return (\n    urlNoQuery\n      // first we replace any non-encoded backslashes with forward\n      // then normalize repeated forward slashes\n      .replace(/\\\\/g, '/')\n      .replace(/\\/\\/+/g, '/') +\n    (urlParts[1] ? `?${urlParts.slice(1).join('?')}` : '')\n  )\n}\n\nexport async function loadGetInitialProps<\n  C extends BaseContext,\n  IP = {},\n  P = {}\n>(App: NextComponentType<C, IP, P>, ctx: C): Promise<IP> {\n  if (process.env.NODE_ENV !== 'production') {\n    if (App.prototype?.getInitialProps) {\n      const message = `\"${getDisplayName(\n        App\n      )}.getInitialProps()\" is defined as an instance method - visit https://nextjs.org/docs/messages/get-initial-props-as-an-instance-method for more information.`\n      throw new Error(message)\n    }\n  }\n  // when called from _app `ctx` is nested in `ctx`\n  const res = ctx.res || (ctx.ctx && ctx.ctx.res)\n\n  if (!App.getInitialProps) {\n    if (ctx.ctx && ctx.Component) {\n      // @ts-ignore pageProps default\n      return {\n        pageProps: await loadGetInitialProps(ctx.Component, ctx.ctx),\n      }\n    }\n    return {} as IP\n  }\n\n  const props = await App.getInitialProps(ctx)\n\n  if (res && isResSent(res)) {\n    return props\n  }\n\n  if (!props) {\n    const message = `\"${getDisplayName(\n      App\n    )}.getInitialProps()\" should resolve to an object. But found \"${props}\" instead.`\n    throw new Error(message)\n  }\n\n  if (process.env.NODE_ENV !== 'production') {\n    if (Object.keys(props).length === 0 && !ctx.ctx) {\n      console.warn(\n        `${getDisplayName(\n          App\n        )} returned an empty object from \\`getInitialProps\\`. This de-optimizes and prevents automatic static optimization. https://nextjs.org/docs/messages/empty-object-getInitialProps`\n      )\n    }\n  }\n\n  return props\n}\n\nexport const urlObjectKeys = [\n  'auth',\n  'hash',\n  'host',\n  'hostname',\n  'href',\n  'path',\n  'pathname',\n  'port',\n  'protocol',\n  'query',\n  'search',\n  'slashes',\n]\n\nexport function formatWithValidation(url: UrlObject): string {\n  if (process.env.NODE_ENV === 'development') {\n    if (url !== null && typeof url === 'object') {\n      Object.keys(url).forEach((key) => {\n        if (urlObjectKeys.indexOf(key) === -1) {\n          console.warn(\n            `Unknown key passed via urlObject into url.format: ${key}`\n          )\n        }\n      })\n    }\n  }\n\n  return formatUrl(url)\n}\n\nexport const SP = typeof performance !== 'undefined'\nexport const ST =\n  SP &&\n  typeof performance.mark === 'function' &&\n  typeof performance.measure === 'function'\n\nexport class DecodeError extends Error {}\n\nexport const HtmlContext = createContext<HtmlProps>(null as any)\nif (process.env.NODE_ENV !== 'production') {\n  HtmlContext.displayName = 'HtmlContext'\n}\n"], "names": [], "mappings": ";;;;QAsSgB,QAAQ,GAAR,QAAQ;QAeR,iBAAiB,GAAjB,iBAAiB;QAKjB,MAAM,GAAN,MAAM;QAMN,cAAc,GAAd,cAAc;QAMd,SAAS,GAAT,SAAS;QAIT,wBAAwB,GAAxB,wBAAwB;QAclB,mBAAmB,GAAnB,mBAAmB;QAmEzB,oBAAoB,GAApB,oBAAoB;;AA3ZV,GAA2B,CAA3B,UAA2B;AAUvB,GAAO,CAAP,MAAO;SA4RrB,QAAQ,CACtB,EAAK,EACF,CAAC;IACJ,GAAG,CAAC,IAAI,GAAG,KAAK;IAChB,GAAG,CAAC,MAAM;eAEE,IAAI,GAAY,CAAC;QAC3B,EAAE,GAAG,IAAI,EAAE,CAAC;YACV,IAAI,GAAG,IAAI;YACX,MAAM,GAAG,EAAE,IAAI,IAAI;QACrB,CAAC;eACM,MAAM;IACf,CAAC;AACH,CAAC;SAEe,iBAAiB,GAAG,CAAC;IACnC,KAAK,GAAG,QAAQ,GAAE,QAAQ,GAAE,IAAI,MAAK,MAAM,CAAC,QAAQ;cAC1C,QAAQ,CAAC,EAAE,EAAE,QAAQ,GAAG,IAAI,IAAG,CAAG,IAAG,IAAI;AACrD,CAAC;SAEe,MAAM,GAAG,CAAC;IACxB,KAAK,GAAG,IAAI,MAAK,MAAM,CAAC,QAAQ;IAChC,KAAK,CAAC,MAAM,GAAG,iBAAiB;WACzB,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,MAAM;AACrC,CAAC;SAEe,cAAc,CAAI,SAA2B,EAAE,CAAC;kBAChD,SAAS,MAAK,MAAQ,IAChC,SAAS,GACT,SAAS,CAAC,WAAW,IAAI,SAAS,CAAC,IAAI,KAAI,OAAS;AAC1D,CAAC;SAEe,SAAS,CAAC,GAAmB,EAAE,CAAC;WACvC,GAAG,CAAC,QAAQ,IAAI,GAAG,CAAC,WAAW;AACxC,CAAC;SAEe,wBAAwB,CAAC,GAAW,EAAE,CAAC;IACrD,KAAK,CAAC,QAAQ,GAAG,GAAG,CAAC,KAAK,EAAC,CAAG;IAC9B,KAAK,CAAC,UAAU,GAAG,QAAQ,CAAC,CAAC;WAG3B,UAAU,AACR,EAA4D,AAA5D,0DAA4D;IAC5D,EAA0C,AAA1C,wCAA0C;KACzC,OAAO,SAAQ,CAAG,GAClB,OAAO,YAAW,CAAG,MACvB,QAAQ,CAAC,CAAC,KAAK,CAAC,EAAE,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE,IAAI,EAAC,CAAG;AAEjD,CAAC;eAEqB,mBAAmB,CAIvC,GAAgC,EAAE,GAAM,EAAe,CAAC;IACxD,EAAE,EAAE,OAAO,CAAC,GAAG,CAAC,QAAQ,MAAK,UAAY,GAAE,CAAC;YACtC,GAAa;QAAjB,EAAE,GAAE,GAAa,GAAb,GAAG,CAAC,SAAS,cAAb,GAAa,UAAb,CAA8B,QAA9B,CAA8B,GAA9B,GAAa,CAAE,eAAe,EAAE,CAAC;YACnC,KAAK,CAAC,OAAO,IAAI,CAAC,EAAE,cAAc,CAChC,GAAG,EACH,2JAA2J;YAC7J,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,OAAO;QACzB,CAAC;IACH,CAAC;IACD,EAAiD,AAAjD,+CAAiD;IACjD,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,GAAG,IAAK,GAAG,CAAC,GAAG,IAAI,GAAG,CAAC,GAAG,CAAC,GAAG;IAE9C,EAAE,GAAG,GAAG,CAAC,eAAe,EAAE,CAAC;QACzB,EAAE,EAAE,GAAG,CAAC,GAAG,IAAI,GAAG,CAAC,SAAS,EAAE,CAAC;YAC7B,EAA+B,AAA/B,6BAA+B;;gBAE7B,SAAS,QAAQ,mBAAmB,CAAC,GAAG,CAAC,SAAS,EAAE,GAAG,CAAC,GAAG;;QAE/D,CAAC;;;IAEH,CAAC;IAED,KAAK,CAAC,KAAK,SAAS,GAAG,CAAC,eAAe,CAAC,GAAG;IAE3C,EAAE,EAAE,GAAG,IAAI,SAAS,CAAC,GAAG,GAAG,CAAC;eACnB,KAAK;IACd,CAAC;IAED,EAAE,GAAG,KAAK,EAAE,CAAC;QACX,KAAK,CAAC,OAAO,IAAI,CAAC,EAAE,cAAc,CAChC,GAAG,EACH,4DAA4D,EAAE,KAAK,CAAC,UAAU;QAChF,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,OAAO;IACzB,CAAC;IAED,EAAE,EAAE,OAAO,CAAC,GAAG,CAAC,QAAQ,MAAK,UAAY,GAAE,CAAC;QAC1C,EAAE,EAAE,MAAM,CAAC,IAAI,CAAC,KAAK,EAAE,MAAM,KAAK,CAAC,KAAK,GAAG,CAAC,GAAG,EAAE,CAAC;YAChD,OAAO,CAAC,IAAI,IACP,cAAc,CACf,GAAG,EACH,+KAA+K;QAErL,CAAC;IACH,CAAC;WAEM,KAAK;AACd,CAAC;AAEM,KAAK,CAAC,aAAa;KACxB,IAAM;KACN,IAAM;KACN,IAAM;KACN,QAAU;KACV,IAAM;KACN,IAAM;KACN,QAAU;KACV,IAAM;KACN,QAAU;KACV,KAAO;KACP,MAAQ;KACR,OAAS;;QAZE,aAAa,GAAb,aAAa;SAeV,oBAAoB,CAAC,GAAc,EAAU,CAAC;IAC5D,EAAE,EAAE,OAAO,CAAC,GAAG,CAAC,QAAQ,MAAK,WAAa,GAAE,CAAC;QAC3C,EAAE,EAAE,GAAG,KAAK,IAAI,WAAW,GAAG,MAAK,MAAQ,GAAE,CAAC;YAC5C,MAAM,CAAC,IAAI,CAAC,GAAG,EAAE,OAAO,EAAE,GAAG,GAAK,CAAC;gBACjC,EAAE,EAAE,aAAa,CAAC,OAAO,CAAC,GAAG,OAAO,CAAC,EAAE,CAAC;oBACtC,OAAO,CAAC,IAAI,EACT,kDAAkD,EAAE,GAAG;gBAE5D,CAAC;YACH,CAAC;QACH,CAAC;IACH,CAAC;eAtauB,UAA2B,YAwalC,GAAG;AACtB,CAAC;AAEM,KAAK,CAAC,EAAE,UAAU,WAAW,MAAK,SAAW;QAAvC,EAAE,GAAF,EAAE;AACR,KAAK,CAAC,EAAE,GACb,EAAE,WACK,WAAW,CAAC,IAAI,MAAK,QAAU,YAC/B,WAAW,CAAC,OAAO,MAAK,QAAU;QAH9B,EAAE,GAAF,EAAE;MAKF,WAAW,SAAS,KAAK;;QAAzB,WAAW,GAAX,WAAW;AAEjB,KAAK,CAAC,WAAW,OAzaM,MAAO,gBAyae,IAAI;QAA3C,WAAW,GAAX,WAAW;AACxB,EAAE,EAAE,OAAO,CAAC,GAAG,CAAC,QAAQ,MAAK,UAAY,GAAE,CAAC;IAC1C,WAAW,CAAC,WAAW,IAAG,WAAa;AACzC,CAAC"}