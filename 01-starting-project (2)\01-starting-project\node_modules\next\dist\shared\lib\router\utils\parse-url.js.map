{"version": 3, "sources": ["../../../../../shared/lib/router/utils/parse-url.ts"], "sourcesContent": ["import type { ParsedUrlQuery } from 'querystring'\nimport { searchParamsToUrlQuery } from './querystring'\nimport { parseRelativeUrl } from './parse-relative-url'\n\nexport interface ParsedUrl {\n  hash: string\n  hostname?: string | null\n  href: string\n  pathname: string\n  port?: string | null\n  protocol?: string | null\n  query: ParsedUrlQuery\n  search: string\n}\n\nexport function parseUrl(url: string): ParsedUrl {\n  if (url.startsWith('/')) {\n    return parseRelativeUrl(url)\n  }\n\n  const parsedURL = new URL(url)\n  return {\n    hash: parsedURL.hash,\n    hostname: parsedURL.hostname,\n    href: parsedURL.href,\n    pathname: parsedURL.pathname,\n    port: parsedURL.port,\n    protocol: parsedURL.protocol,\n    query: searchParamsToUrlQuery(parsedURL.searchParams),\n    search: parsedURL.search,\n  }\n}\n"], "names": [], "mappings": ";;;;QAegB,QAAQ,GAAR,QAAQ;AAde,GAAe,CAAf,YAAe;AACrB,GAAsB,CAAtB,iBAAsB;SAavC,QAAQ,CAAC,GAAW,EAAa,CAAC;IAChD,EAAE,EAAE,GAAG,CAAC,UAAU,EAAC,CAAG,IAAG,CAAC;mBAdK,iBAAsB,mBAe3B,GAAG;IAC7B,CAAC;IAED,KAAK,CAAC,SAAS,GAAG,GAAG,CAAC,GAAG,CAAC,GAAG;;QAE3B,IAAI,EAAE,SAAS,CAAC,IAAI;QACpB,QAAQ,EAAE,SAAS,CAAC,QAAQ;QAC5B,IAAI,EAAE,SAAS,CAAC,IAAI;QACpB,QAAQ,EAAE,SAAS,CAAC,QAAQ;QAC5B,IAAI,EAAE,SAAS,CAAC,IAAI;QACpB,QAAQ,EAAE,SAAS,CAAC,QAAQ;QAC5B,KAAK,MA3B8B,YAAe,yBA2BpB,SAAS,CAAC,YAAY;QACpD,MAAM,EAAE,SAAS,CAAC,MAAM;;AAE5B,CAAC"}