{"version": 3, "sources": ["../../../../server/lib/squoosh/main.ts"], "sourcesContent": ["import { Worker } from 'jest-worker'\nimport * as path from 'path'\nimport { execOnce } from '../../../shared/lib/utils'\nimport { cpus } from 'os'\n\ntype RotateOperation = {\n  type: 'rotate'\n  numRotations: number\n}\ntype ResizeOperation = {\n  type: 'resize'\n} & ({ width: number; height?: never } | { height: number; width?: never })\nexport type Operation = RotateOperation | ResizeOperation\nexport type Encoding = 'jpeg' | 'png' | 'webp'\n\nconst getWorker = execOnce(\n  () =>\n    new Worker(path.resolve(__dirname, 'impl'), {\n      enableWorkerThreads: true,\n      // There will be at most 6 workers needed since each worker will take\n      // at least 1 operation type.\n      numWorkers: Math.max(1, Math.min(cpus().length - 1, 6)),\n      computeWorkerKey: (method) => method,\n    })\n)\n\nexport async function processBuffer(\n  buffer: Buffer,\n  operations: Operation[],\n  encoding: Encoding,\n  quality: number\n): Promise<Buffer> {\n  const worker: typeof import('./impl') = getWorker() as any\n\n  let imageData = await worker.decodeBuffer(buffer)\n  for (const operation of operations) {\n    if (operation.type === 'rotate') {\n      imageData = await worker.rotate(imageData, operation.numRotations)\n    } else if (operation.type === 'resize') {\n      if (\n        operation.width &&\n        imageData.width &&\n        imageData.width > operation.width\n      ) {\n        imageData = await worker.resize({\n          image: imageData,\n          width: operation.width,\n        })\n      } else if (\n        operation.height &&\n        imageData.height &&\n        imageData.height > operation.height\n      ) {\n        imageData = await worker.resize({\n          image: imageData,\n          height: operation.height,\n        })\n      }\n    }\n  }\n\n  switch (encoding) {\n    case 'jpeg':\n      return Buffer.from(await worker.encodeJpeg(imageData, { quality }))\n    case 'webp':\n      return Buffer.from(await worker.encodeWebp(imageData, { quality }))\n    case 'png':\n      return Buffer.from(await worker.encodePng(imageData))\n    default:\n      throw Error(`Unsupported encoding format`)\n  }\n}\n"], "names": [], "mappings": ";;;;QA0BsB,aAAa,GAAb,aAAa;AA1BZ,GAAa,CAAb,WAAa;AACxB,GAAI,CAAJ,IAAI;AACS,GAA2B,CAA3B,MAA2B;AAC/B,GAAI,CAAJ,GAAI;;;;;;;;;;;;;;;;;;;;;;;;AAYzB,KAAK,CAAC,SAAS,OAbU,MAA2B,eAehD,GAAG,CAjBgB,WAAa,QACxB,IAAI,CAgBI,OAAO,CAAC,SAAS,GAAE,IAAM;QACvC,mBAAmB,EAAE,IAAI;QACzB,EAAqE,AAArE,mEAAqE;QACrE,EAA6B,AAA7B,2BAA6B;QAC7B,UAAU,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,KAlBjB,GAAI,SAkBqB,MAAM,GAAG,CAAC,EAAE,CAAC;QACrD,gBAAgB,GAAG,MAAM,GAAK,MAAM;;;eAIpB,aAAa,CACjC,MAAc,EACd,UAAuB,EACvB,QAAkB,EAClB,OAAe,EACE,CAAC;IAClB,KAAK,CAAC,MAAM,GAA4B,SAAS;IAEjD,GAAG,CAAC,SAAS,SAAS,MAAM,CAAC,YAAY,CAAC,MAAM;SAC3C,KAAK,CAAC,SAAS,IAAI,UAAU,CAAE,CAAC;QACnC,EAAE,EAAE,SAAS,CAAC,IAAI,MAAK,MAAQ,GAAE,CAAC;YAChC,SAAS,SAAS,MAAM,CAAC,MAAM,CAAC,SAAS,EAAE,SAAS,CAAC,YAAY;QACnE,CAAC,MAAM,EAAE,EAAE,SAAS,CAAC,IAAI,MAAK,MAAQ,GAAE,CAAC;YACvC,EAAE,EACA,SAAS,CAAC,KAAK,IACf,SAAS,CAAC,KAAK,IACf,SAAS,CAAC,KAAK,GAAG,SAAS,CAAC,KAAK,EACjC,CAAC;gBACD,SAAS,SAAS,MAAM,CAAC,MAAM;oBAC7B,KAAK,EAAE,SAAS;oBAChB,KAAK,EAAE,SAAS,CAAC,KAAK;;YAE1B,CAAC,MAAM,EAAE,EACP,SAAS,CAAC,MAAM,IAChB,SAAS,CAAC,MAAM,IAChB,SAAS,CAAC,MAAM,GAAG,SAAS,CAAC,MAAM,EACnC,CAAC;gBACD,SAAS,SAAS,MAAM,CAAC,MAAM;oBAC7B,KAAK,EAAE,SAAS;oBAChB,MAAM,EAAE,SAAS,CAAC,MAAM;;YAE5B,CAAC;QACH,CAAC;IACH,CAAC;WAEO,QAAQ;cACT,IAAM;mBACF,MAAM,CAAC,IAAI,OAAO,MAAM,CAAC,UAAU,CAAC,SAAS;gBAAI,OAAO;;cAC5D,IAAM;mBACF,MAAM,CAAC,IAAI,OAAO,MAAM,CAAC,UAAU,CAAC,SAAS;gBAAI,OAAO;;cAC5D,GAAK;mBACD,MAAM,CAAC,IAAI,OAAO,MAAM,CAAC,SAAS,CAAC,SAAS;;YAEnD,KAAK,CAAC,KAAK,EAAE,2BAA2B;;AAE9C,CAAC"}