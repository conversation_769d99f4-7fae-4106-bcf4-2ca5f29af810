{"version": 3, "sources": ["../../../../build/webpack/plugins/react-loadable-plugin.ts"], "sourcesContent": ["/**\nCOPYRIGHT (c) 2017-present <PERSON> <<EMAIL>>\n MIT License\n Permission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n The above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWAR\n*/\n// Implementation of this PR: https://github.com/jamiebuilds/react-loadable/pull/132\n// Modified to strip out unneeded results for Next's specific use case\n\nimport {\n  webpack,\n  isWebpack5,\n  sources,\n} from 'next/dist/compiled/webpack/webpack'\n\nimport path from 'path'\n\nfunction getModuleId(compilation: any, module: any): string | number {\n  if (isWebpack5) {\n    return compilation.chunkGraph.getModuleId(module)\n  }\n\n  return module.id\n}\n\nfunction getModuleFromDependency(\n  compilation: any,\n  dep: any\n): webpack.Module & { resource?: string } {\n  if (isWebpack5) {\n    return compilation.moduleGraph.getModule(dep)\n  }\n\n  return dep.module\n}\n\nfunction getOriginModuleFromDependency(\n  compilation: any,\n  dep: any\n): webpack.Module & { resource?: string } {\n  if (isWebpack5) {\n    return compilation.moduleGraph.getParentModule(dep)\n  }\n\n  return dep.originModule\n}\n\nfunction getChunkGroupFromBlock(\n  compilation: any,\n  block: any\n): webpack.compilation.ChunkGroup {\n  if (isWebpack5) {\n    return compilation.chunkGraph.getBlockChunkGroup(block)\n  }\n\n  return block.chunkGroup\n}\n\nfunction buildManifest(\n  _compiler: webpack.Compiler,\n  compilation: webpack.compilation.Compilation,\n  pagesDir: string\n) {\n  let manifest: { [k: string]: { id: string | number; files: string[] } } = {}\n\n  // This is allowed:\n  // import(\"./module\"); <- ImportDependency\n\n  // We don't support that:\n  // import(/* webpackMode: \"eager\" */ \"./module\") <- ImportEagerDependency\n  // import(`./module/${param}`) <- ImportContextDependency\n\n  // Find all dependencies blocks which contains a `import()` dependency\n  const handleBlock = (block: any) => {\n    block.blocks.forEach(handleBlock)\n    const chunkGroup = getChunkGroupFromBlock(compilation, block)\n    for (const dependency of block.dependencies) {\n      if (dependency.type.startsWith('import()')) {\n        // get the referenced module\n        const module = getModuleFromDependency(compilation, dependency)\n        if (!module) return\n\n        // get the module containing the import()\n        const originModule = getOriginModuleFromDependency(\n          compilation,\n          dependency\n        )\n        const originRequest: string | undefined = originModule?.resource\n        if (!originRequest) return\n\n        // We construct a \"unique\" key from origin module and request\n        // It's not perfect unique, but that will be fine for us.\n        // We also need to construct the same in the babel plugin.\n        const key = `${path.relative(pagesDir, originRequest)} -> ${\n          dependency.request\n        }`\n\n        // Capture all files that need to be loaded.\n        const files = new Set<string>()\n\n        if (manifest[key]) {\n          // In the \"rare\" case where multiple chunk groups\n          // are created for the same `import()` or multiple\n          // import()s reference the same module, we merge\n          // the files to make sure to not miss files\n          // This may cause overfetching in edge cases.\n          for (const file of manifest[key].files) {\n            files.add(file)\n          }\n        }\n\n        // There might not be a chunk group when all modules\n        // are already loaded. In this case we only need need\n        // the module id and no files\n        if (chunkGroup) {\n          for (const chunk of (chunkGroup as any)\n            .chunks as webpack.compilation.Chunk[]) {\n            chunk.files.forEach((file: string) => {\n              if (\n                (file.endsWith('.js') || file.endsWith('.css')) &&\n                file.match(/^static\\/(chunks|css)\\//)\n              ) {\n                files.add(file)\n              }\n            })\n          }\n        }\n\n        // usually we have to add the parent chunk groups too\n        // but we assume that all parents are also imported by\n        // next/dynamic so they are loaded by the same technique\n\n        // add the id and files to the manifest\n        const id = getModuleId(compilation, module)\n        manifest[key] = { id, files: Array.from(files) }\n      }\n    }\n  }\n  for (const module of compilation.modules) {\n    module.blocks.forEach(handleBlock)\n  }\n\n  manifest = Object.keys(manifest)\n    .sort()\n    // eslint-disable-next-line no-sequences\n    .reduce((a, c) => ((a[c] = manifest[c]), a), {} as any)\n\n  return manifest\n}\n\nexport class ReactLoadablePlugin {\n  private filename: string\n  private pagesDir: string\n\n  constructor(opts: { filename: string; pagesDir: string }) {\n    this.filename = opts.filename\n    this.pagesDir = opts.pagesDir\n  }\n\n  createAssets(compiler: any, compilation: any, assets: any) {\n    const manifest = buildManifest(compiler, compilation, this.pagesDir)\n    // @ts-ignore: TODO: remove when webpack 5 is stable\n    assets[this.filename] = new sources.RawSource(\n      JSON.stringify(manifest, null, 2)\n    )\n    return assets\n  }\n\n  apply(compiler: webpack.Compiler) {\n    if (isWebpack5) {\n      compiler.hooks.make.tap('ReactLoadableManifest', (compilation) => {\n        // @ts-ignore TODO: Remove ignore when webpack 5 is stable\n        compilation.hooks.processAssets.tap(\n          {\n            name: 'ReactLoadableManifest',\n            // @ts-ignore TODO: Remove ignore when webpack 5 is stable\n            stage: webpack.Compilation.PROCESS_ASSETS_STAGE_ADDITIONS,\n          },\n          (assets: any) => {\n            this.createAssets(compiler, compilation, assets)\n          }\n        )\n      })\n      return\n    }\n\n    compiler.hooks.emit.tap('ReactLoadableManifest', (compilation: any) => {\n      this.createAssets(compiler, compilation, compilation.assets)\n    })\n  }\n}\n"], "names": [], "mappings": ";;;;AA2BO,GAAoC,CAApC,QAAoC;AAE1B,GAAM,CAAN,KAAM;;;;;;SAEd,WAAW,CAAC,WAAgB,EAAE,MAAW,EAAmB,CAAC;IACpE,EAAE,EALG,QAAoC,aAKzB,CAAC;eACR,WAAW,CAAC,UAAU,CAAC,WAAW,CAAC,MAAM;IAClD,CAAC;WAEM,MAAM,CAAC,EAAE;AAClB,CAAC;SAEQ,uBAAuB,CAC9B,WAAgB,EAChB,GAAQ,EACgC,CAAC;IACzC,EAAE,EAhBG,QAAoC,aAgBzB,CAAC;eACR,WAAW,CAAC,WAAW,CAAC,SAAS,CAAC,GAAG;IAC9C,CAAC;WAEM,GAAG,CAAC,MAAM;AACnB,CAAC;SAEQ,6BAA6B,CACpC,WAAgB,EAChB,GAAQ,EACgC,CAAC;IACzC,EAAE,EA3BG,QAAoC,aA2BzB,CAAC;eACR,WAAW,CAAC,WAAW,CAAC,eAAe,CAAC,GAAG;IACpD,CAAC;WAEM,GAAG,CAAC,YAAY;AACzB,CAAC;SAEQ,sBAAsB,CAC7B,WAAgB,EAChB,KAAU,EACsB,CAAC;IACjC,EAAE,EAtCG,QAAoC,aAsCzB,CAAC;eACR,WAAW,CAAC,UAAU,CAAC,kBAAkB,CAAC,KAAK;IACxD,CAAC;WAEM,KAAK,CAAC,UAAU;AACzB,CAAC;SAEQ,aAAa,CACpB,SAA2B,EAC3B,WAA4C,EAC5C,QAAgB,EAChB,CAAC;IACD,GAAG,CAAC,QAAQ;;IAEZ,EAAmB,AAAnB,iBAAmB;IACnB,EAA0C,AAA1C,wCAA0C;IAE1C,EAAyB,AAAzB,uBAAyB;IACzB,EAAyE,AAAzE,uEAAyE;IACzE,EAAyD,AAAzD,uDAAyD;IAEzD,EAAsE,AAAtE,oEAAsE;IACtE,KAAK,CAAC,WAAW,IAAI,KAAU,GAAK,CAAC;QACnC,KAAK,CAAC,MAAM,CAAC,OAAO,CAAC,WAAW;QAChC,KAAK,CAAC,UAAU,GAAG,sBAAsB,CAAC,WAAW,EAAE,KAAK;aACvD,KAAK,CAAC,UAAU,IAAI,KAAK,CAAC,YAAY,CAAE,CAAC;YAC5C,EAAE,EAAE,UAAU,CAAC,IAAI,CAAC,UAAU,EAAC,QAAU,IAAG,CAAC;gBAC3C,EAA4B,AAA5B,0BAA4B;gBAC5B,KAAK,CAAC,MAAM,GAAG,uBAAuB,CAAC,WAAW,EAAE,UAAU;gBAC9D,EAAE,GAAG,MAAM;gBAEX,EAAyC,AAAzC,uCAAyC;gBACzC,KAAK,CAAC,YAAY,GAAG,6BAA6B,CAChD,WAAW,EACX,UAAU;gBAEZ,KAAK,CAAC,aAAa,GAAuB,YAAY,aAAZ,YAAY,UAAZ,CAAsB,QAAtB,CAAsB,GAAtB,YAAY,CAAE,QAAQ;gBAChE,EAAE,GAAG,aAAa;gBAElB,EAA6D,AAA7D,2DAA6D;gBAC7D,EAAyD,AAAzD,uDAAyD;gBACzD,EAA0D,AAA1D,wDAA0D;gBAC1D,KAAK,CAAC,GAAG,MA9EA,KAAM,SA8EK,QAAQ,CAAC,QAAQ,EAAE,aAAa,EAAE,IAAI,EACxD,UAAU,CAAC,OAAO;gBAGpB,EAA4C,AAA5C,0CAA4C;gBAC5C,KAAK,CAAC,KAAK,GAAG,GAAG,CAAC,GAAG;gBAErB,EAAE,EAAE,QAAQ,CAAC,GAAG,GAAG,CAAC;oBAClB,EAAiD,AAAjD,+CAAiD;oBACjD,EAAkD,AAAlD,gDAAkD;oBAClD,EAAgD,AAAhD,8CAAgD;oBAChD,EAA2C,AAA3C,yCAA2C;oBAC3C,EAA6C,AAA7C,2CAA6C;yBACxC,KAAK,CAAC,IAAI,IAAI,QAAQ,CAAC,GAAG,EAAE,KAAK,CAAE,CAAC;wBACvC,KAAK,CAAC,GAAG,CAAC,IAAI;oBAChB,CAAC;gBACH,CAAC;gBAED,EAAoD,AAApD,kDAAoD;gBACpD,EAAqD,AAArD,mDAAqD;gBACrD,EAA6B,AAA7B,2BAA6B;gBAC7B,EAAE,EAAE,UAAU,EAAE,CAAC;yBACV,KAAK,CAAC,KAAK,IAAK,UAAU,CAC5B,MAAM,CAAiC,CAAC;wBACzC,KAAK,CAAC,KAAK,CAAC,OAAO,EAAE,IAAY,GAAK,CAAC;4BACrC,EAAE,GACC,IAAI,CAAC,QAAQ,EAAC,GAAK,MAAK,IAAI,CAAC,QAAQ,EAAC,IAAM,OAC7C,IAAI,CAAC,KAAK,6BACV,CAAC;gCACD,KAAK,CAAC,GAAG,CAAC,IAAI;4BAChB,CAAC;wBACH,CAAC;oBACH,CAAC;gBACH,CAAC;gBAED,EAAqD,AAArD,mDAAqD;gBACrD,EAAsD,AAAtD,oDAAsD;gBACtD,EAAwD,AAAxD,sDAAwD;gBAExD,EAAuC,AAAvC,qCAAuC;gBACvC,KAAK,CAAC,EAAE,GAAG,WAAW,CAAC,WAAW,EAAE,MAAM;gBAC1C,QAAQ,CAAC,GAAG;oBAAM,EAAE;oBAAE,KAAK,EAAE,KAAK,CAAC,IAAI,CAAC,KAAK;;YAC/C,CAAC;QACH,CAAC;IACH,CAAC;SACI,KAAK,CAAC,MAAM,IAAI,WAAW,CAAC,OAAO,CAAE,CAAC;QACzC,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,WAAW;IACnC,CAAC;IAED,QAAQ,GAAG,MAAM,CAAC,IAAI,CAAC,QAAQ,EAC5B,IAAI,EACL,EAAwC,AAAxC,sCAAwC;KACvC,MAAM,EAAE,CAAC,EAAE,CAAC,IAAO,CAAC,CAAC,CAAC,IAAI,QAAQ,CAAC,CAAC,GAAI,CAAC;;;WAErC,QAAQ;AACjB,CAAC;MAEY,mBAAmB;gBAIlB,IAA4C,CAAE,CAAC;aACpD,QAAQ,GAAG,IAAI,CAAC,QAAQ;aACxB,QAAQ,GAAG,IAAI,CAAC,QAAQ;IAC/B,CAAC;IAED,YAAY,CAAC,QAAa,EAAE,WAAgB,EAAE,MAAW,EAAE,CAAC;QAC1D,KAAK,CAAC,QAAQ,GAAG,aAAa,CAAC,QAAQ,EAAE,WAAW,OAAO,QAAQ;QACnE,EAAoD,AAApD,kDAAoD;QACpD,MAAM,MAAM,QAAQ,IAAI,GAAG,CArJxB,QAAoC,SAqJH,SAAS,CAC3C,IAAI,CAAC,SAAS,CAAC,QAAQ,EAAE,IAAI,EAAE,CAAC;eAE3B,MAAM;IACf,CAAC;IAED,KAAK,CAAC,QAA0B,EAAE,CAAC;QACjC,EAAE,EA5JC,QAAoC,aA4JvB,CAAC;YACf,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,EAAC,qBAAuB,IAAG,WAAW,GAAK,CAAC;gBACjE,EAA0D,AAA1D,wDAA0D;gBAC1D,WAAW,CAAC,KAAK,CAAC,aAAa,CAAC,GAAG;oBAE/B,IAAI,GAAE,qBAAuB;oBAC7B,EAA0D,AAA1D,wDAA0D;oBAC1D,KAAK,EAnKV,QAAoC,SAmKhB,WAAW,CAAC,8BAA8B;oBAE1D,MAAW,GAAK,CAAC;yBACX,YAAY,CAAC,QAAQ,EAAE,WAAW,EAAE,MAAM;gBACjD,CAAC;YAEL,CAAC;;QAEH,CAAC;QAED,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,EAAC,qBAAuB,IAAG,WAAgB,GAAK,CAAC;iBACjE,YAAY,CAAC,QAAQ,EAAE,WAAW,EAAE,WAAW,CAAC,MAAM;QAC7D,CAAC;IACH,CAAC;;QAvCU,mBAAmB,GAAnB,mBAAmB"}