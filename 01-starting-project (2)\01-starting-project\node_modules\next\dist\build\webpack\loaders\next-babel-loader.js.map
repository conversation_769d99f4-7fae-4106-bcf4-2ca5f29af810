{"version": 3, "sources": ["../../../../build/webpack/loaders/next-babel-loader.js"], "sourcesContent": ["import { join } from 'path'\nimport * as Log from '../../output/log'\nimport babelLoader from './babel-loader/src/index'\n\n// increment 'p' to invalidate cache\n// eslint-disable-next-line no-useless-concat\nconst cacheKey = 'babel-cache-' + 'p' + '-'\nconst nextBabelPreset = require('../../babel/preset')\n\nconst customBabelLoader = babelLoader((babel) => {\n  const presetItem = babel.createConfigItem(nextBabelPreset, {\n    type: 'preset',\n  })\n  const applyCommonJs = babel.createConfigItem(\n    require('../../babel/plugins/commonjs'),\n    { type: 'plugin' }\n  )\n  const commonJsItem = babel.createConfigItem(\n    require('next/dist/compiled/babel/plugin-transform-modules-commonjs'),\n    { type: 'plugin' }\n  )\n\n  const configs = new Set()\n\n  return {\n    customOptions(opts) {\n      const custom = {\n        isServer: opts.isServer,\n        pagesDir: opts.pagesDir,\n        development: opts.development,\n        hasReactRefresh: opts.hasReactRefresh,\n        hasJsxRuntime: opts.hasJsxRuntime,\n      }\n      const filename = join(opts.cwd, 'noop.js')\n      const loader = Object.assign(\n        opts.cache\n          ? {\n              cacheDirectory: join(opts.distDir, 'cache', 'next-babel-loader'),\n              cacheIdentifier:\n                cacheKey +\n                (opts.isServer ? '-server' : '') +\n                '-new-polyfills' +\n                (opts.development ? '-development' : '-production') +\n                (opts.hasReactRefresh ? '-react-refresh' : '') +\n                (opts.hasJsxRuntime ? '-jsx-runtime' : '') +\n                JSON.stringify(\n                  babel.loadPartialConfig({\n                    filename,\n                    cwd: opts.cwd,\n                    sourceFileName: filename,\n                  }).options\n                ),\n            }\n          : {\n              cacheDirectory: false,\n            },\n        opts\n      )\n\n      delete loader.isServer\n      delete loader.cache\n      delete loader.distDir\n      delete loader.pagesDir\n      delete loader.development\n      delete loader.hasReactRefresh\n      delete loader.hasJsxRuntime\n      return { loader, custom }\n    },\n    config(\n      cfg,\n      {\n        source,\n        customOptions: {\n          isServer,\n          pagesDir,\n          development,\n          hasReactRefresh,\n          hasJsxRuntime,\n        },\n      }\n    ) {\n      const filename = this.resourcePath\n      const options = Object.assign({}, cfg.options)\n      const isPageFile = filename.startsWith(pagesDir)\n\n      if (cfg.hasFilesystemConfig()) {\n        for (const file of [cfg.babelrc, cfg.config]) {\n          // We only log for client compilation otherwise there will be double output\n          if (file && !isServer && !configs.has(file)) {\n            configs.add(file)\n            Log.info(`Using external babel configuration from ${file}`)\n          }\n        }\n      } else {\n        // Add our default preset if the no \"babelrc\" found.\n        options.presets = [...options.presets, presetItem]\n      }\n\n      options.caller.isServer = isServer\n      options.caller.isDev = development\n      options.caller.hasJsxRuntime = hasJsxRuntime\n      options.caller.pagesDir = pagesDir\n\n      const emitWarning = this.emitWarning.bind(this)\n      Object.defineProperty(options.caller, 'onWarning', {\n        enumerable: false,\n        writable: false,\n        value: (options.caller.onWarning = function (reason) {\n          if (!(reason instanceof Error)) {\n            reason = new Error(reason)\n          }\n          emitWarning(reason)\n        }),\n      })\n\n      options.plugins = options.plugins || []\n\n      if (hasReactRefresh) {\n        const reactRefreshPlugin = babel.createConfigItem(\n          [require('react-refresh/babel'), { skipEnvCheck: true }],\n          { type: 'plugin' }\n        )\n        options.plugins.unshift(reactRefreshPlugin)\n        if (!isServer) {\n          const noAnonymousDefaultExportPlugin = babel.createConfigItem(\n            [require('../../babel/plugins/no-anonymous-default-export'), {}],\n            { type: 'plugin' }\n          )\n          options.plugins.unshift(noAnonymousDefaultExportPlugin)\n        }\n      }\n\n      if (!isServer && isPageFile) {\n        const pageConfigPlugin = babel.createConfigItem(\n          [require('../../babel/plugins/next-page-config')],\n          { type: 'plugin' }\n        )\n        options.plugins.push(pageConfigPlugin)\n\n        const diallowExportAll = babel.createConfigItem(\n          [\n            require('../../babel/plugins/next-page-disallow-re-export-all-exports'),\n          ],\n          { type: 'plugin' }\n        )\n        options.plugins.push(diallowExportAll)\n      }\n\n      // If the file has `module.exports` we have to transpile commonjs because Babel adds `import` statements\n      // That break webpack, since webpack doesn't support combining commonjs and esmodules\n      if (source.indexOf('module.exports') !== -1) {\n        options.plugins.push(applyCommonJs)\n      }\n\n      options.plugins.push([\n        require.resolve('next/dist/compiled/babel/plugin-transform-define'),\n        {\n          'process.env.NODE_ENV': development ? 'development' : 'production',\n          'typeof window': isServer ? 'undefined' : 'object',\n          'process.browser': isServer ? false : true,\n        },\n        'next-js-transform-define-instance',\n      ])\n\n      if (isPageFile) {\n        if (!isServer) {\n          options.plugins.push([\n            require.resolve('../../babel/plugins/next-ssg-transform'),\n            {},\n          ])\n        }\n      }\n\n      // As shared/lib has stateful modules we have to transpile commonjs\n      options.overrides = [\n        ...(options.overrides || []),\n        {\n          test: [\n            /next[\\\\/]dist[\\\\/]shared[\\\\/]lib/,\n            /next[\\\\/]dist[\\\\/]client/,\n            /next[\\\\/]dist[\\\\/]pages/,\n          ],\n          plugins: [commonJsItem],\n        },\n      ]\n\n      return options\n    },\n  }\n})\n\nexport default customBabelLoader\n"], "names": [], "mappings": ";;;;;AAAqB,GAAM,CAAN,KAAM;AACf,GAAG,CAAH,GAAG;AACS,GAA0B,CAA1B,MAA0B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAElD,EAAoC,AAApC,kCAAoC;AACpC,EAA6C,AAA7C,2CAA6C;AAC7C,KAAK,CAAC,QAAQ,IAAG,YAAc,KAAG,CAAG,KAAG,CAAG;AAC3C,KAAK,CAAC,eAAe,GAAG,OAAO,EAAC,kBAAoB;AAEpD,KAAK,CAAC,iBAAiB,OAPC,MAA0B,WAOX,KAAK,GAAK,CAAC;IAChD,KAAK,CAAC,UAAU,GAAG,KAAK,CAAC,gBAAgB,CAAC,eAAe;QACvD,IAAI,GAAE,MAAQ;;IAEhB,KAAK,CAAC,aAAa,GAAG,KAAK,CAAC,gBAAgB,CAC1C,OAAO,EAAC,4BAA8B;QACpC,IAAI,GAAE,MAAQ;;IAElB,KAAK,CAAC,YAAY,GAAG,KAAK,CAAC,gBAAgB,CACzC,OAAO,EAAC,0DAA4D;QAClE,IAAI,GAAE,MAAQ;;IAGlB,KAAK,CAAC,OAAO,GAAG,GAAG,CAAC,GAAG;;QAGrB,aAAa,EAAC,IAAI,EAAE,CAAC;YACnB,KAAK,CAAC,MAAM;gBACV,QAAQ,EAAE,IAAI,CAAC,QAAQ;gBACvB,QAAQ,EAAE,IAAI,CAAC,QAAQ;gBACvB,WAAW,EAAE,IAAI,CAAC,WAAW;gBAC7B,eAAe,EAAE,IAAI,CAAC,eAAe;gBACrC,aAAa,EAAE,IAAI,CAAC,aAAa;;YAEnC,KAAK,CAAC,QAAQ,OAjCC,KAAM,OAiCC,IAAI,CAAC,GAAG,GAAE,OAAS;YACzC,KAAK,CAAC,MAAM,GAAG,MAAM,CAAC,MAAM,CAC1B,IAAI,CAAC,KAAK;gBAEJ,cAAc,MArCP,KAAM,OAqCQ,IAAI,CAAC,OAAO,GAAE,KAAO,IAAE,iBAAmB;gBAC/D,eAAe,EACb,QAAQ,IACP,IAAI,CAAC,QAAQ,IAAG,OAAS,WAC1B,cAAgB,KACf,IAAI,CAAC,WAAW,IAAG,YAAc,KAAG,WAAa,MACjD,IAAI,CAAC,eAAe,IAAG,cAAgB,WACvC,IAAI,CAAC,aAAa,IAAG,YAAc,UACpC,IAAI,CAAC,SAAS,CACZ,KAAK,CAAC,iBAAiB;oBACrB,QAAQ;oBACR,GAAG,EAAE,IAAI,CAAC,GAAG;oBACb,cAAc,EAAE,QAAQ;mBACvB,OAAO;;gBAId,cAAc,EAAE,KAAK;eAE3B,IAAI;mBAGC,MAAM,CAAC,QAAQ;mBACf,MAAM,CAAC,KAAK;mBACZ,MAAM,CAAC,OAAO;mBACd,MAAM,CAAC,QAAQ;mBACf,MAAM,CAAC,WAAW;mBAClB,MAAM,CAAC,eAAe;mBACtB,MAAM,CAAC,aAAa;;gBAClB,MAAM;gBAAE,MAAM;;QACzB,CAAC;QACD,MAAM,EACJ,GAAG,IAED,MAAM,GACN,aAAa,IACX,QAAQ,GACR,QAAQ,GACR,WAAW,GACX,eAAe,GACf,aAAa,YAGjB,CAAC;YACD,KAAK,CAAC,QAAQ,QAAQ,YAAY;YAClC,KAAK,CAAC,OAAO,GAAG,MAAM,CAAC,MAAM;eAAK,GAAG,CAAC,OAAO;YAC7C,KAAK,CAAC,UAAU,GAAG,QAAQ,CAAC,UAAU,CAAC,QAAQ;YAE/C,EAAE,EAAE,GAAG,CAAC,mBAAmB,IAAI,CAAC;qBACzB,KAAK,CAAC,IAAI;oBAAK,GAAG,CAAC,OAAO;oBAAE,GAAG,CAAC,MAAM;kBAAG,CAAC;oBAC7C,EAA2E,AAA3E,yEAA2E;oBAC3E,EAAE,EAAE,IAAI,KAAK,QAAQ,KAAK,OAAO,CAAC,GAAG,CAAC,IAAI,GAAG,CAAC;wBAC5C,OAAO,CAAC,GAAG,CAAC,IAAI;wBAxFhB,GAAG,CAyFC,IAAI,EAAE,wCAAwC,EAAE,IAAI;oBAC1D,CAAC;gBACH,CAAC;YACH,CAAC,MAAM,CAAC;gBACN,EAAoD,AAApD,kDAAoD;gBACpD,OAAO,CAAC,OAAO;uBAAO,OAAO,CAAC,OAAO;oBAAE,UAAU;;YACnD,CAAC;YAED,OAAO,CAAC,MAAM,CAAC,QAAQ,GAAG,QAAQ;YAClC,OAAO,CAAC,MAAM,CAAC,KAAK,GAAG,WAAW;YAClC,OAAO,CAAC,MAAM,CAAC,aAAa,GAAG,aAAa;YAC5C,OAAO,CAAC,MAAM,CAAC,QAAQ,GAAG,QAAQ;YAElC,KAAK,CAAC,WAAW,QAAQ,WAAW,CAAC,IAAI;YACzC,MAAM,CAAC,cAAc,CAAC,OAAO,CAAC,MAAM,GAAE,SAAW;gBAC/C,UAAU,EAAE,KAAK;gBACjB,QAAQ,EAAE,KAAK;gBACf,KAAK,EAAG,OAAO,CAAC,MAAM,CAAC,SAAS,YAAa,MAAM,EAAE,CAAC;oBACpD,EAAE,IAAI,MAAM,YAAY,KAAK,GAAG,CAAC;wBAC/B,MAAM,GAAG,GAAG,CAAC,KAAK,CAAC,MAAM;oBAC3B,CAAC;oBACD,WAAW,CAAC,MAAM;gBACpB,CAAC;;YAGH,OAAO,CAAC,OAAO,GAAG,OAAO,CAAC,OAAO;YAEjC,EAAE,EAAE,eAAe,EAAE,CAAC;gBACpB,KAAK,CAAC,kBAAkB,GAAG,KAAK,CAAC,gBAAgB;oBAC9C,OAAO,EAAC,mBAAqB;;wBAAK,YAAY,EAAE,IAAI;;;oBACnD,IAAI,GAAE,MAAQ;;gBAElB,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,kBAAkB;gBAC1C,EAAE,GAAG,QAAQ,EAAE,CAAC;oBACd,KAAK,CAAC,8BAA8B,GAAG,KAAK,CAAC,gBAAgB;wBAC1D,OAAO,EAAC,+CAAiD;;;;wBACxD,IAAI,GAAE,MAAQ;;oBAElB,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,8BAA8B;gBACxD,CAAC;YACH,CAAC;YAED,EAAE,GAAG,QAAQ,IAAI,UAAU,EAAE,CAAC;gBAC5B,KAAK,CAAC,gBAAgB,GAAG,KAAK,CAAC,gBAAgB;oBAC5C,OAAO,EAAC,oCAAsC;;oBAC7C,IAAI,GAAE,MAAQ;;gBAElB,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,gBAAgB;gBAErC,KAAK,CAAC,gBAAgB,GAAG,KAAK,CAAC,gBAAgB;oBAE3C,OAAO,EAAC,4DAA8D;;oBAEtE,IAAI,GAAE,MAAQ;;gBAElB,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,gBAAgB;YACvC,CAAC;YAED,EAAwG,AAAxG,sGAAwG;YACxG,EAAqF,AAArF,mFAAqF;YACrF,EAAE,EAAE,MAAM,CAAC,OAAO,EAAC,cAAgB,QAAO,CAAC,EAAE,CAAC;gBAC5C,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,aAAa;YACpC,CAAC;YAED,OAAO,CAAC,OAAO,CAAC,IAAI;gBAClB,OAAO,CAAC,OAAO,EAAC,gDAAkD;;qBAEhE,oBAAsB,GAAE,WAAW,IAAG,WAAa,KAAG,UAAY;qBAClE,aAAe,GAAE,QAAQ,IAAG,SAAW,KAAG,MAAQ;qBAClD,eAAiB,GAAE,QAAQ,GAAG,KAAK,GAAG,IAAI;;iBAE5C,iCAAmC;;YAGrC,EAAE,EAAE,UAAU,EAAE,CAAC;gBACf,EAAE,GAAG,QAAQ,EAAE,CAAC;oBACd,OAAO,CAAC,OAAO,CAAC,IAAI;wBAClB,OAAO,CAAC,OAAO,EAAC,sCAAwC;;;;gBAG5D,CAAC;YACH,CAAC;YAED,EAAmE,AAAnE,iEAAmE;YACnE,OAAO,CAAC,SAAS;mBACX,OAAO,CAAC,SAAS;;oBAEnB,IAAI;;;;;oBAKJ,OAAO;wBAAG,YAAY;;;;mBAInB,OAAO;QAChB,CAAC;;AAEL,CAAC;eAEc,iBAAiB"}