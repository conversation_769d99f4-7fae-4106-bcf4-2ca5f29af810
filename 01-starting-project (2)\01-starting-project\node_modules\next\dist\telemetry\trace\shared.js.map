{"version": 3, "sources": ["../../../telemetry/trace/shared.ts"], "sourcesContent": ["// eslint typescript has a bug with TS enums\n/* eslint-disable no-shadow */\nexport enum TARGET {\n  CONSOLE = 'CONSOLE',\n  ZIPKIN = 'ZIPKIN',\n  JAEGER = 'JAEGER',\n  TELEMETRY = 'TELEMETRY',\n}\n\nexport type SpanId = string\n\nexport const traceGlobals: Map<any, any> = new Map()\nexport const setGlobal = (key: any, val: any) => {\n  traceGlobals.set(key, val)\n}\n\nexport const debugLog = !!process.env.TRACE_DEBUG\n  ? console.info\n  : function noop() {}\n"], "names": [], "mappings": ";;;;;;;UAEY,OAAM;IAAN,OAAM,EAChB,OAAO,MAAP,OAAO;IADG,OAAM,EAEhB,MAAM,MAAN,MAAM;IAFI,OAAM,EAGhB,MAAM,MAAN,MAAM;IAHI,OAAM,EAIhB,SAAS,MAAT,SAAS;GAJC,MAAM,sBAAN,MAAM;;AASX,KAAK,CAAC,YAAY,GAAkB,GAAG,CAAC,GAAG;QAArC,YAAY,GAAZ,YAAY;AAClB,KAAK,CAAC,SAAS,IAAI,GAAQ,EAAE,GAAQ,GAAK,CAAC;IAChD,YAAY,CAAC,GAAG,CAAC,GAAG,EAAE,GAAG;AAC3B,CAAC;QAFY,SAAS,GAAT,SAAS;AAIf,KAAK,CAAC,QAAQ,KAAK,OAAO,CAAC,GAAG,CAAC,WAAW,GAC7C,OAAO,CAAC,IAAI,YACH,IAAI,GAAG,CAAC;AAAA,CAAC;QAFT,QAAQ,GAAR,QAAQ"}