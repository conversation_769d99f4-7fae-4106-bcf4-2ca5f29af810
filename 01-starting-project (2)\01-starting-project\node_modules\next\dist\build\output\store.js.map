{"version": 3, "sources": ["../../../build/output/store.ts"], "sourcesContent": ["import createStore from 'next/dist/compiled/unistore'\nimport stripAnsi from 'next/dist/compiled/strip-ansi'\n\nimport * as Log from './log'\n\nexport type OutputState =\n  | { bootstrap: true; appUrl: string | null; bindAddr: string | null }\n  | ({ bootstrap: false; appUrl: string | null; bindAddr: string | null } & (\n      | { loading: true }\n      | {\n          loading: false\n          typeChecking: boolean\n          errors: string[] | null\n          warnings: string[] | null\n        }\n    ))\n\nexport const store = createStore<OutputState>({\n  appUrl: null,\n  bindAddr: null,\n  bootstrap: true,\n})\n\nlet lastStore: OutputState = { appUrl: null, bindAddr: null, bootstrap: true }\nfunction hasStoreChanged(nextStore: OutputState) {\n  if (\n    (\n      [\n        ...new Set([...Object.keys(lastStore), ...Object.keys(nextStore)]),\n      ] as Array<keyof OutputState>\n    ).every((key) => Object.is(lastStore[key], nextStore[key]))\n  ) {\n    return false\n  }\n\n  lastStore = nextStore\n  return true\n}\n\nstore.subscribe((state) => {\n  if (!hasStoreChanged(state)) {\n    return\n  }\n\n  if (state.bootstrap) {\n    if (state.appUrl) {\n      Log.ready(`started server on ${state.bindAddr}, url: ${state.appUrl}`)\n    }\n    return\n  }\n\n  if (state.loading) {\n    Log.wait('compiling...')\n    return\n  }\n\n  if (state.errors) {\n    Log.error(state.errors[0])\n\n    const cleanError = stripAnsi(state.errors[0])\n    if (cleanError.indexOf('SyntaxError') > -1) {\n      const matches = cleanError.match(/\\[.*\\]=/)\n      if (matches) {\n        for (const match of matches) {\n          const prop = (match.split(']').shift() || '').substr(1)\n          console.log(\n            `AMP bind syntax [${prop}]='' is not supported in JSX, use 'data-amp-bind-${prop}' instead. https://nextjs.org/docs/messages/amp-bind-jsx-alt`\n          )\n        }\n        return\n      }\n    }\n\n    return\n  }\n\n  if (state.warnings) {\n    Log.warn(state.warnings.join('\\n\\n'))\n    if (state.appUrl) {\n      Log.info(`ready on ${state.appUrl}`)\n    }\n    return\n  }\n\n  if (state.typeChecking) {\n    Log.info('bundled successfully, waiting for typecheck results...')\n    return\n  }\n\n  Log.event('compiled successfully')\n})\n"], "names": [], "mappings": ";;;;;AAAwB,GAA6B,CAA7B,SAA6B;AAC/B,GAA+B,CAA/B,UAA+B;AAEzC,GAAG,CAAH,GAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAcR,KAAK,CAAC,KAAK,OAjBM,SAA6B;IAkBnD,MAAM,EAAE,IAAI;IACZ,QAAQ,EAAE,IAAI;IACd,SAAS,EAAE,IAAI;;QAHJ,KAAK,GAAL,KAAK;AAMlB,GAAG,CAAC,SAAS;IAAkB,MAAM,EAAE,IAAI;IAAE,QAAQ,EAAE,IAAI;IAAE,SAAS,EAAE,IAAI;;SACnE,eAAe,CAAC,SAAsB,EAAE,CAAC;IAChD,EAAE;WAGO,GAAG,CAAC,GAAG;eAAK,MAAM,CAAC,IAAI,CAAC,SAAS;eAAM,MAAM,CAAC,IAAI,CAAC,SAAS;;MAEjE,KAAK,EAAE,GAAG,GAAK,MAAM,CAAC,EAAE,CAAC,SAAS,CAAC,GAAG,GAAG,SAAS,CAAC,GAAG;OACxD,CAAC;eACM,KAAK;IACd,CAAC;IAED,SAAS,GAAG,SAAS;WACd,IAAI;AACb,CAAC;AAED,KAAK,CAAC,SAAS,EAAE,KAAK,GAAK,CAAC;IAC1B,EAAE,GAAG,eAAe,CAAC,KAAK,GAAG,CAAC;;IAE9B,CAAC;IAED,EAAE,EAAE,KAAK,CAAC,SAAS,EAAE,CAAC;QACpB,EAAE,EAAE,KAAK,CAAC,MAAM,EAAE,CAAC;YA1CX,GAAG,CA2CL,KAAK,EAAE,kBAAkB,EAAE,KAAK,CAAC,QAAQ,CAAC,OAAO,EAAE,KAAK,CAAC,MAAM;QACrE,CAAC;;IAEH,CAAC;IAED,EAAE,EAAE,KAAK,CAAC,OAAO,EAAE,CAAC;QAhDV,GAAG,CAiDP,IAAI,EAAC,YAAc;;IAEzB,CAAC;IAED,EAAE,EAAE,KAAK,CAAC,MAAM,EAAE,CAAC;QArDT,GAAG,CAsDP,KAAK,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;QAExB,KAAK,CAAC,UAAU,OA1DE,UAA+B,UA0DpB,KAAK,CAAC,MAAM,CAAC,CAAC;QAC3C,EAAE,EAAE,UAAU,CAAC,OAAO,EAAC,WAAa,MAAK,CAAC,EAAE,CAAC;YAC3C,KAAK,CAAC,OAAO,GAAG,UAAU,CAAC,KAAK;YAChC,EAAE,EAAE,OAAO,EAAE,CAAC;qBACP,KAAK,CAAC,KAAK,IAAI,OAAO,CAAE,CAAC;oBAC5B,KAAK,CAAC,IAAI,IAAI,KAAK,CAAC,KAAK,EAAC,CAAG,GAAE,KAAK,UAAU,MAAM,CAAC,CAAC;oBACtD,OAAO,CAAC,GAAG,EACR,iBAAiB,EAAE,IAAI,CAAC,iDAAiD,EAAE,IAAI,CAAC,4DAA4D;gBAEjJ,CAAC;;YAEH,CAAC;QACH,CAAC;;IAGH,CAAC;IAED,EAAE,EAAE,KAAK,CAAC,QAAQ,EAAE,CAAC;QAzEX,GAAG,CA0EP,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,IAAI,EAAC,IAAM;QACnC,EAAE,EAAE,KAAK,CAAC,MAAM,EAAE,CAAC;YA3EX,GAAG,CA4EL,IAAI,EAAE,SAAS,EAAE,KAAK,CAAC,MAAM;QACnC,CAAC;;IAEH,CAAC;IAED,EAAE,EAAE,KAAK,CAAC,YAAY,EAAE,CAAC;QAjFf,GAAG,CAkFP,IAAI,EAAC,sDAAwD;;IAEnE,CAAC;IApFS,GAAG,CAsFT,KAAK,EAAC,qBAAuB;AACnC,CAAC"}