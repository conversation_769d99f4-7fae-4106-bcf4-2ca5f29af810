{"version": 3, "sources": ["../../../../../../build/webpack/config/blocks/css/messages.ts"], "sourcesContent": ["import chalk from 'chalk'\n\nexport function getGlobalImportError() {\n  return `Global CSS ${chalk.bold(\n    'cannot'\n  )} be imported from files other than your ${chalk.bold(\n    'Custom <App>'\n  )}. Due to the Global nature of stylesheets, and to avoid conflicts, Please move all first-party global CSS imports to ${chalk.cyan(\n    'pages/_app.js'\n  )}. Or convert the import to Component-Level CSS (CSS Modules).\\nRead more: https://nextjs.org/docs/messages/css-global`\n}\n\nexport function getGlobalModuleImportError() {\n  return `Global CSS ${chalk.bold(\n    'cannot'\n  )} be imported from within ${chalk.bold(\n    'node_modules'\n  )}.\\nRead more: https://nextjs.org/docs/messages/css-npm`\n}\n\nexport function getLocalModuleImportError() {\n  return `CSS Modules ${chalk.bold(\n    'cannot'\n  )} be imported from within ${chalk.bold(\n    'node_modules'\n  )}.\\nRead more: https://nextjs.org/docs/messages/css-modules-npm`\n}\n\nexport function getCustomDocumentError() {\n  return `CSS ${chalk.bold('cannot')} be imported within ${chalk.cyan(\n    'pages/_document.js'\n  )}. Please move global styles to ${chalk.cyan('pages/_app.js')}.`\n}\n"], "names": [], "mappings": ";;;;QAEgB,oBAAoB,GAApB,oBAAoB;QAUpB,0BAA0B,GAA1B,0BAA0B;QAQ1B,yBAAyB,GAAzB,yBAAyB;QAQzB,sBAAsB,GAAtB,sBAAsB;AA5BpB,GAAO,CAAP,MAAO;;;;;;SAET,oBAAoB,GAAG,CAAC;YAC9B,WAAW,EAHH,MAAO,SAGI,IAAI,EAC7B,MAAQ,GACR,wCAAwC,EAL1B,MAAO,SAK2B,IAAI,EACpD,YAAc,GACd,qHAAqH,EAPvG,MAAO,SAOwG,IAAI,EACjI,aAAe,GACf,qHAAqH;AACzH,CAAC;SAEe,0BAA0B,GAAG,CAAC;YACpC,WAAW,EAbH,MAAO,SAaI,IAAI,EAC7B,MAAQ,GACR,yBAAyB,EAfX,MAAO,SAeY,IAAI,EACrC,YAAc,GACd,sDAAsD;AAC1D,CAAC;SAEe,yBAAyB,GAAG,CAAC;YACnC,YAAY,EArBJ,MAAO,SAqBK,IAAI,EAC9B,MAAQ,GACR,yBAAyB,EAvBX,MAAO,SAuBY,IAAI,EACrC,YAAc,GACd,8DAA8D;AAClE,CAAC;SAEe,sBAAsB,GAAG,CAAC;YAChC,IAAI,EA7BI,MAAO,SA6BH,IAAI,EAAC,MAAQ,GAAE,oBAAoB,EA7BvC,MAAO,SA6BwC,IAAI,EACjE,kBAAoB,GACpB,+BAA+B,EA/BjB,MAAO,SA+BkB,IAAI,EAAC,aAAe,GAAE,CAAC;AAClE,CAAC"}