{"version": 3, "sources": ["../../../../build/webpack/plugins/next-drop-client-page-plugin.ts"], "sourcesContent": ["import { webpack } from 'next/dist/compiled/webpack/webpack'\nimport { isWebpack5 } from 'next/dist/compiled/webpack/webpack'\nimport { STRING_LITERAL_DROP_BUNDLE } from '../../../shared/lib/constants'\n\nexport const ampFirstEntryNamesMap: WeakMap<\n  webpack.compilation.Compilation,\n  string[]\n> = new WeakMap()\n\nconst PLUGIN_NAME = 'DropAmpFirstPagesPlugin'\n\n// Prevents outputting client pages when they are not needed\nexport class DropClientPage implements webpack.Plugin {\n  ampPages = new Set()\n\n  apply(compiler: webpack.Compiler) {\n    compiler.hooks.compilation.tap(\n      PLUGIN_NAME,\n      (compilation: any, { normalModuleFactory }: any) => {\n        // Recursively look up the issuer till it ends up at the root\n        function findEntryModule(mod: any): webpack.compilation.Module | null {\n          const queue = new Set([mod])\n          for (const module of queue) {\n            if (isWebpack5) {\n              // @ts-ignore TODO: webpack 5 types\n              const incomingConnections =\n                compilation.moduleGraph.getIncomingConnections(module)\n\n              for (const incomingConnection of incomingConnections) {\n                if (!incomingConnection.originModule) return module\n                queue.add(incomingConnection.originModule)\n              }\n              continue\n            }\n\n            for (const reason of module.reasons) {\n              if (!reason.module) return module\n              queue.add(reason.module)\n            }\n          }\n\n          return null\n        }\n\n        function handler(parser: any) {\n          function markAsAmpFirst() {\n            const entryModule = findEntryModule(parser.state.module)\n\n            if (!entryModule) {\n              return\n            }\n\n            // @ts-ignore buildInfo exists on Module\n            entryModule.buildInfo.NEXT_ampFirst = true\n          }\n\n          if (isWebpack5) {\n            parser.hooks.preDeclarator.tap(PLUGIN_NAME, (declarator: any) => {\n              if (declarator?.id?.name === STRING_LITERAL_DROP_BUNDLE) {\n                markAsAmpFirst()\n              }\n            })\n            return\n          }\n\n          parser.hooks.varDeclaration\n            .for(STRING_LITERAL_DROP_BUNDLE)\n            .tap(PLUGIN_NAME, markAsAmpFirst)\n        }\n\n        normalModuleFactory.hooks.parser\n          .for('javascript/auto')\n          .tap(PLUGIN_NAME, handler)\n\n        normalModuleFactory.hooks.parser\n          .for('javascript/esm')\n          .tap(PLUGIN_NAME, handler)\n\n        normalModuleFactory.hooks.parser\n          .for('javascript/dynamic')\n          .tap(PLUGIN_NAME, handler)\n\n        if (!ampFirstEntryNamesMap.has(compilation)) {\n          ampFirstEntryNamesMap.set(compilation, [])\n        }\n\n        const ampFirstEntryNamesItem = ampFirstEntryNamesMap.get(\n          compilation\n        ) as string[]\n\n        compilation.hooks.seal.tap(PLUGIN_NAME, () => {\n          if (isWebpack5) {\n            for (const [name, entryData] of compilation.entries) {\n              for (const dependency of entryData.dependencies) {\n                // @ts-ignore TODO: webpack 5 types\n                const module = compilation.moduleGraph.getModule(dependency)\n                if (module?.buildInfo?.NEXT_ampFirst) {\n                  ampFirstEntryNamesItem.push(name)\n                  // @ts-ignore @types/webpack has outdated types for webpack 5\n                  compilation.entries.delete(name)\n                }\n              }\n            }\n            return\n          }\n          // Remove preparedEntrypoint that has bundle drop marker\n          // This will ensure webpack does not create chunks/bundles for this particular entrypoint\n          for (\n            let i = compilation._preparedEntrypoints.length - 1;\n            i >= 0;\n            i--\n          ) {\n            const entrypoint = compilation._preparedEntrypoints[i]\n            if (entrypoint?.module?.buildInfo?.NEXT_ampFirst) {\n              ampFirstEntryNamesItem.push(entrypoint.name)\n              compilation._preparedEntrypoints.splice(i, 1)\n            }\n          }\n\n          for (let i = compilation.entries.length - 1; i >= 0; i--) {\n            const entryModule = compilation.entries[i]\n            if (entryModule?.buildInfo?.NEXT_ampFirst) {\n              compilation.entries.splice(i, 1)\n            }\n          }\n        })\n      }\n    )\n  }\n}\n"], "names": [], "mappings": ";;;;;AAC2B,GAAoC,CAApC,QAAoC;AACpB,GAA+B,CAA/B,UAA+B;AAEnE,KAAK,CAAC,qBAAqB,GAG9B,GAAG,CAAC,OAAO;QAHF,qBAAqB,GAArB,qBAAqB;AAKlC,KAAK,CAAC,WAAW,IAAG,uBAAyB;MAGhC,cAAc;IAGzB,KAAK,CAAC,QAA0B,EAAE,CAAC;QACjC,QAAQ,CAAC,KAAK,CAAC,WAAW,CAAC,GAAG,CAC5B,WAAW,GACV,WAAgB,IAAI,mBAAmB,MAAY,CAAC;YACnD,EAA6D,AAA7D,2DAA6D;qBACpD,eAAe,CAAC,GAAQ,EAAqC,CAAC;gBACrE,KAAK,CAAC,KAAK,GAAG,GAAG,CAAC,GAAG;oBAAE,GAAG;;qBACrB,KAAK,CAAC,MAAM,IAAI,KAAK,CAAE,CAAC;oBAC3B,EAAE,EAtBa,QAAoC,aAsBnC,CAAC;wBACf,EAAmC,AAAnC,iCAAmC;wBACnC,KAAK,CAAC,mBAAmB,GACvB,WAAW,CAAC,WAAW,CAAC,sBAAsB,CAAC,MAAM;6BAElD,KAAK,CAAC,kBAAkB,IAAI,mBAAmB,CAAE,CAAC;4BACrD,EAAE,GAAG,kBAAkB,CAAC,YAAY,SAAS,MAAM;4BACnD,KAAK,CAAC,GAAG,CAAC,kBAAkB,CAAC,YAAY;wBAC3C,CAAC;;oBAEH,CAAC;yBAEI,KAAK,CAAC,MAAM,IAAI,MAAM,CAAC,OAAO,CAAE,CAAC;wBACpC,EAAE,GAAG,MAAM,CAAC,MAAM,SAAS,MAAM;wBACjC,KAAK,CAAC,GAAG,CAAC,MAAM,CAAC,MAAM;oBACzB,CAAC;gBACH,CAAC;uBAEM,IAAI;YACb,CAAC;qBAEQ,OAAO,CAAC,MAAW,EAAE,CAAC;yBACpB,cAAc,GAAG,CAAC;oBACzB,KAAK,CAAC,WAAW,GAAG,eAAe,CAAC,MAAM,CAAC,KAAK,CAAC,MAAM;oBAEvD,EAAE,GAAG,WAAW,EAAE,CAAC;;oBAEnB,CAAC;oBAED,EAAwC,AAAxC,sCAAwC;oBACxC,WAAW,CAAC,SAAS,CAAC,aAAa,GAAG,IAAI;gBAC5C,CAAC;gBAED,EAAE,EAvDe,QAAoC,aAuDrC,CAAC;oBACf,MAAM,CAAC,KAAK,CAAC,aAAa,CAAC,GAAG,CAAC,WAAW,GAAG,UAAe,GAAK,CAAC;4BAC5D,GAAc;wBAAlB,EAAE,GAAE,UAAU,aAAV,UAAU,UAAV,CAAc,QAAd,CAAc,IAAd,GAAc,GAAd,UAAU,CAAE,EAAE,cAAd,GAAc,UAAd,CAAc,QAAd,CAAc,GAAd,GAAc,CAAE,IAAI,MAxDK,UAA+B,6BAwDH,CAAC;4BACxD,cAAc;wBAChB,CAAC;oBACH,CAAC;;gBAEH,CAAC;gBAED,MAAM,CAAC,KAAK,CAAC,cAAc,CACxB,GAAG,CAhE2B,UAA+B,6BAiE7D,GAAG,CAAC,WAAW,EAAE,cAAc;YACpC,CAAC;YAED,mBAAmB,CAAC,KAAK,CAAC,MAAM,CAC7B,GAAG,EAAC,eAAiB,GACrB,GAAG,CAAC,WAAW,EAAE,OAAO;YAE3B,mBAAmB,CAAC,KAAK,CAAC,MAAM,CAC7B,GAAG,EAAC,cAAgB,GACpB,GAAG,CAAC,WAAW,EAAE,OAAO;YAE3B,mBAAmB,CAAC,KAAK,CAAC,MAAM,CAC7B,GAAG,EAAC,kBAAoB,GACxB,GAAG,CAAC,WAAW,EAAE,OAAO;YAE3B,EAAE,GAAG,qBAAqB,CAAC,GAAG,CAAC,WAAW,GAAG,CAAC;gBAC5C,qBAAqB,CAAC,GAAG,CAAC,WAAW;YACvC,CAAC;YAED,KAAK,CAAC,sBAAsB,GAAG,qBAAqB,CAAC,GAAG,CACtD,WAAW;YAGb,WAAW,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,WAAW,MAAQ,CAAC;gBAC7C,EAAE,EA1Fe,QAAoC,aA0FrC,CAAC;yBACV,KAAK,EAAE,IAAI,EAAE,SAAS,KAAK,WAAW,CAAC,OAAO,CAAE,CAAC;6BAC/C,KAAK,CAAC,UAAU,IAAI,SAAS,CAAC,YAAY,CAAE,CAAC;gCAG5C,GAAiB;4BAFrB,EAAmC,AAAnC,iCAAmC;4BACnC,KAAK,CAAC,MAAM,GAAG,WAAW,CAAC,WAAW,CAAC,SAAS,CAAC,UAAU;4BAC3D,EAAE,EAAE,MAAM,aAAN,MAAM,UAAN,CAAiB,QAAjB,CAAiB,IAAjB,GAAiB,GAAjB,MAAM,CAAE,SAAS,cAAjB,GAAiB,UAAjB,CAAiB,QAAjB,CAAiB,GAAjB,GAAiB,CAAE,aAAa,EAAE,CAAC;gCACrC,sBAAsB,CAAC,IAAI,CAAC,IAAI;gCAChC,EAA6D,AAA7D,2DAA6D;gCAC7D,WAAW,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI;4BACjC,CAAC;wBACH,CAAC;oBACH,CAAC;;gBAEH,CAAC;gBACD,EAAwD,AAAxD,sDAAwD;gBACxD,EAAyF,AAAzF,uFAAyF;oBAEvF,GAAG,CAAC,CAAC,GAAG,WAAW,CAAC,oBAAoB,CAAC,MAAM,GAAG,CAAC,EACnD,CAAC,IAAI,CAAC,EACN,CAAC,GACD,CAAC;wBAEG,GAAkB;oBADtB,KAAK,CAAC,UAAU,GAAG,WAAW,CAAC,oBAAoB,CAAC,CAAC;oBACrD,EAAE,EAAE,UAAU,aAAV,UAAU,UAAV,CAAkB,QAAlB,CAAkB,IAAlB,GAAkB,GAAlB,UAAU,CAAE,MAAM,cAAlB,GAAkB,UAAlB,CAAkB,QAAlB,CAAkB,WAAlB,GAAkB,CAAE,SAAS,4BAA7B,CAAkB,QAAlB,CAAkB,QAAa,aAAa,EAAE,CAAC;wBACjD,sBAAsB,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI;wBAC3C,WAAW,CAAC,oBAAoB,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC;oBAC9C,CAAC;gBACH,CAAC;oBAEI,GAAG,CAAC,EAAC,GAAG,WAAW,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE,EAAC,IAAI,CAAC,EAAE,EAAC,GAAI,CAAC;wBAErD,IAAsB;oBAD1B,KAAK,CAAC,WAAW,GAAG,WAAW,CAAC,OAAO,CAAC,EAAC;oBACzC,EAAE,EAAE,WAAW,aAAX,WAAW,UAAX,CAAsB,QAAtB,CAAsB,IAAtB,IAAsB,GAAtB,WAAW,CAAE,SAAS,cAAtB,IAAsB,UAAtB,CAAsB,QAAtB,CAAsB,GAAtB,IAAsB,CAAE,aAAa,EAAE,CAAC;wBAC1C,WAAW,CAAC,OAAO,CAAC,MAAM,CAAC,EAAC,EAAE,CAAC;oBACjC,CAAC;gBACH,CAAC;YACH,CAAC;QACH,CAAC;IAEL,CAAC;;aAnHD,QAAQ,GAAG,GAAG,CAAC,GAAG;;;QADP,cAAc,GAAd,cAAc"}