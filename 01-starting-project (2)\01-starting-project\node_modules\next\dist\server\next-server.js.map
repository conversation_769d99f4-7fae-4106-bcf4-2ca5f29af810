{"version": 3, "sources": ["../../server/next-server.ts"], "sourcesContent": ["import compression from 'next/dist/compiled/compression'\nimport fs from 'fs'\nimport chalk from 'chalk'\nimport { IncomingMessage, ServerResponse } from 'http'\nimport Proxy from 'next/dist/compiled/http-proxy'\nimport { join, relative, resolve, sep } from 'path'\nimport {\n  parse as parseQs,\n  stringify as stringifyQs,\n  ParsedUrlQuery,\n} from 'querystring'\nimport { format as formatUrl, parse as parseUrl, UrlWithParsedQuery } from 'url'\nimport Observable from 'next/dist/compiled/zen-observable'\nimport { PrerenderManifest } from '../build'\nimport {\n  getRedirectStatus,\n  Header,\n  Redirect,\n  Rewrite,\n  RouteType,\n  CustomRoutes,\n  modifyRouteRegex,\n} from '../lib/load-custom-routes'\nimport {\n  BUILD_ID_FILE,\n  CLIENT_PUBLIC_FILES_PATH,\n  CLIENT_STATIC_FILES_PATH,\n  CLIENT_STATIC_FILES_RUNTIME,\n  PAGES_MANIFEST,\n  PERMANENT_REDIRECT_STATUS,\n  PRERENDER_MANIFEST,\n  ROUTES_MANIFEST,\n  SERVERLESS_DIRECTORY,\n  SERVER_DIRECTORY,\n  STATIC_STATUS_PAGES,\n  TEMPORARY_REDIRECT_STATUS,\n} from '../shared/lib/constants'\nimport {\n  getRouteMatcher,\n  getRouteRegex,\n  getSortedRoutes,\n  isDynamicRoute,\n} from '../shared/lib/router/utils'\nimport * as envConfig from '../shared/lib/runtime-config'\nimport {\n  DecodeError,\n  isResSent,\n  NextApiRequest,\n  NextApiResponse,\n  normalizeRepeatedSlashes,\n} from '../shared/lib/utils'\nimport {\n  apiResolver,\n  setLazyProp,\n  getCookieParser,\n  tryGetPreviewData,\n  __ApiPreviewProps,\n} from './api-utils'\nimport { DomainLocale, isTargetLikeServerless, NextConfig } from './config'\nimport pathMatch from '../shared/lib/router/utils/path-match'\nimport { recursiveReadDirSync } from './lib/recursive-readdir-sync'\nimport { loadComponents, LoadComponentsReturnType } from './load-components'\nimport { normalizePagePath } from './normalize-page-path'\nimport { RenderOpts, RenderOptsPartial, renderToHTML } from './render'\nimport { getPagePath, requireFontManifest } from './require'\nimport Router, {\n  DynamicRoutes,\n  PageChecker,\n  Params,\n  route,\n  Route,\n} from './router'\nimport prepareDestination, {\n  compileNonPath,\n} from '../shared/lib/router/utils/prepare-destination'\nimport { sendRenderResult, setRevalidateHeaders } from './send-payload'\nimport { serveStatic } from './serve-static'\nimport { IncrementalCache } from './incremental-cache'\nimport { execOnce } from '../shared/lib/utils'\nimport { isBlockedPage, RenderResult, resultsToString } from './utils'\nimport { loadEnvConfig } from '@next/env'\nimport './node-polyfill-fetch'\nimport { PagesManifest } from '../build/webpack/plugins/pages-manifest-plugin'\nimport { removePathTrailingSlash } from '../client/normalize-trailing-slash'\nimport getRouteFromAssetPath from '../shared/lib/router/utils/get-route-from-asset-path'\nimport { FontManifest } from './font-utils'\nimport { denormalizePagePath } from './denormalize-page-path'\nimport { normalizeLocalePath } from '../shared/lib/i18n/normalize-locale-path'\nimport * as Log from '../build/output/log'\nimport { detectDomainLocale } from '../shared/lib/i18n/detect-domain-locale'\nimport escapePathDelimiters from '../shared/lib/router/utils/escape-path-delimiters'\nimport { getUtils } from '../build/webpack/loaders/next-serverless-loader/utils'\nimport { PreviewData } from 'next/types'\nimport ResponseCache, {\n  ResponseCacheEntry,\n  ResponseCacheValue,\n} from './response-cache'\nimport { NextConfigComplete } from './config-shared'\nimport { parseNextUrl } from '../shared/lib/router/utils/parse-next-url'\n\nconst getCustomRouteMatcher = pathMatch(true)\n\ntype Middleware = (\n  req: IncomingMessage,\n  res: ServerResponse,\n  next: (err?: Error) => void\n) => void\n\nexport type FindComponentsResult = {\n  components: LoadComponentsReturnType\n  query: ParsedUrlQuery\n}\n\ntype DynamicRouteItem = {\n  page: string\n  match: ReturnType<typeof getRouteMatcher>\n}\n\nexport type ServerConstructor = {\n  /**\n   * Where the Next project is located - @default '.'\n   */\n  dir?: string\n  /**\n   * Hide error messages containing server information - @default false\n   */\n  quiet?: boolean\n  /**\n   * Object what you would use in next.config.js - @default {}\n   */\n  conf?: NextConfig | null\n  dev?: boolean\n  customServer?: boolean\n}\n\ntype RequestContext = {\n  req: IncomingMessage\n  res: ServerResponse\n  pathname: string\n  query: ParsedUrlQuery\n  renderOpts: RenderOptsPartial\n}\n\nexport default class Server {\n  protected dir: string\n  protected quiet: boolean\n  protected nextConfig: NextConfigComplete\n  protected distDir: string\n  protected pagesDir?: string\n  protected publicDir: string\n  protected hasStaticDir: boolean\n  protected serverBuildDir: string\n  protected pagesManifest?: PagesManifest\n  protected buildId: string\n  protected minimalMode: boolean\n  protected renderOpts: {\n    poweredByHeader: boolean\n    buildId: string\n    generateEtags: boolean\n    runtimeConfig?: { [key: string]: any }\n    assetPrefix?: string\n    canonicalBase: string\n    dev?: boolean\n    previewProps: __ApiPreviewProps\n    customServer?: boolean\n    ampOptimizerConfig?: { [key: string]: any }\n    basePath: string\n    optimizeFonts: boolean\n    images: string\n    fontManifest: FontManifest\n    optimizeImages: boolean\n    disableOptimizedLoading?: boolean\n    optimizeCss: any\n    locale?: string\n    locales?: string[]\n    defaultLocale?: string\n    domainLocales?: DomainLocale[]\n    distDir: string\n    concurrentFeatures?: boolean\n  }\n  private compression?: Middleware\n  private incrementalCache: IncrementalCache\n  private responseCache: ResponseCache\n  protected router: Router\n  protected dynamicRoutes?: DynamicRoutes\n  protected customRoutes: CustomRoutes\n\n  public constructor({\n    dir = '.',\n    quiet = false,\n    conf,\n    dev = false,\n    minimalMode = false,\n    customServer = true,\n  }: ServerConstructor & { conf: NextConfig; minimalMode?: boolean }) {\n    this.dir = resolve(dir)\n    this.quiet = quiet\n    loadEnvConfig(this.dir, dev, Log)\n\n    this.nextConfig = conf as NextConfigComplete\n\n    this.distDir = join(this.dir, this.nextConfig.distDir)\n    this.publicDir = join(this.dir, CLIENT_PUBLIC_FILES_PATH)\n    this.hasStaticDir = !minimalMode && fs.existsSync(join(this.dir, 'static'))\n\n    // Only serverRuntimeConfig needs the default\n    // publicRuntimeConfig gets it's default in client/index.js\n    const {\n      serverRuntimeConfig = {},\n      publicRuntimeConfig,\n      assetPrefix,\n      generateEtags,\n      compress,\n    } = this.nextConfig\n\n    this.buildId = this.readBuildId()\n    this.minimalMode = minimalMode\n\n    this.renderOpts = {\n      poweredByHeader: this.nextConfig.poweredByHeader,\n      canonicalBase: this.nextConfig.amp.canonicalBase || '',\n      buildId: this.buildId,\n      generateEtags,\n      previewProps: this.getPreviewProps(),\n      customServer: customServer === true ? true : undefined,\n      ampOptimizerConfig: this.nextConfig.experimental.amp?.optimizer,\n      basePath: this.nextConfig.basePath,\n      images: JSON.stringify(this.nextConfig.images),\n      optimizeFonts: !!this.nextConfig.optimizeFonts && !dev,\n      fontManifest:\n        this.nextConfig.optimizeFonts && !dev\n          ? requireFontManifest(this.distDir, this._isLikeServerless)\n          : null,\n      optimizeImages: !!this.nextConfig.experimental.optimizeImages,\n      optimizeCss: this.nextConfig.experimental.optimizeCss,\n      disableOptimizedLoading:\n        this.nextConfig.experimental.disableOptimizedLoading,\n      domainLocales: this.nextConfig.i18n?.domains,\n      distDir: this.distDir,\n      concurrentFeatures: this.nextConfig.experimental.concurrentFeatures,\n    }\n\n    // Only the `publicRuntimeConfig` key is exposed to the client side\n    // It'll be rendered as part of __NEXT_DATA__ on the client side\n    if (Object.keys(publicRuntimeConfig).length > 0) {\n      this.renderOpts.runtimeConfig = publicRuntimeConfig\n    }\n\n    if (compress && this.nextConfig.target === 'server') {\n      this.compression = compression() as Middleware\n    }\n\n    // Initialize next/config with the environment configuration\n    envConfig.setConfig({\n      serverRuntimeConfig,\n      publicRuntimeConfig,\n    })\n\n    this.serverBuildDir = join(\n      this.distDir,\n      this._isLikeServerless ? SERVERLESS_DIRECTORY : SERVER_DIRECTORY\n    )\n    const pagesManifestPath = join(this.serverBuildDir, PAGES_MANIFEST)\n\n    if (!dev) {\n      this.pagesManifest = require(pagesManifestPath)\n    }\n\n    this.customRoutes = this.getCustomRoutes()\n    this.router = new Router(this.generateRoutes())\n    this.setAssetPrefix(assetPrefix)\n\n    this.incrementalCache = new IncrementalCache({\n      dev,\n      distDir: this.distDir,\n      pagesDir: join(\n        this.distDir,\n        this._isLikeServerless ? SERVERLESS_DIRECTORY : SERVER_DIRECTORY,\n        'pages'\n      ),\n      locales: this.nextConfig.i18n?.locales,\n      max: this.nextConfig.experimental.isrMemoryCacheSize,\n      flushToDisk: !minimalMode && this.nextConfig.experimental.isrFlushToDisk,\n    })\n    this.responseCache = new ResponseCache(this.incrementalCache)\n\n    /**\n     * This sets environment variable to be used at the time of SSR by head.tsx.\n     * Using this from process.env allows targeting both serverless and SSR by calling\n     * `process.env.__NEXT_OPTIMIZE_IMAGES`.\n     * TODO(atcastle@): Remove this when experimental.optimizeImages are being cleaned up.\n     */\n    if (this.renderOpts.optimizeFonts) {\n      process.env.__NEXT_OPTIMIZE_FONTS = JSON.stringify(true)\n    }\n    if (this.renderOpts.optimizeImages) {\n      process.env.__NEXT_OPTIMIZE_IMAGES = JSON.stringify(true)\n    }\n    if (this.renderOpts.optimizeCss) {\n      process.env.__NEXT_OPTIMIZE_CSS = JSON.stringify(true)\n    }\n  }\n\n  public logError(err: Error): void {\n    if (this.quiet) return\n    console.error(err)\n  }\n\n  private async handleRequest(\n    req: IncomingMessage,\n    res: ServerResponse,\n    parsedUrl?: UrlWithParsedQuery\n  ): Promise<void> {\n    try {\n      const urlParts = (req.url || '').split('?')\n      const urlNoQuery = urlParts[0]\n\n      if (urlNoQuery?.match(/(\\\\|\\/\\/)/)) {\n        const cleanUrl = normalizeRepeatedSlashes(req.url!)\n        res.setHeader('Location', cleanUrl)\n        res.setHeader('Refresh', `0;url=${cleanUrl}`)\n        res.statusCode = 308\n        res.end(cleanUrl)\n        return\n      }\n\n      setLazyProp({ req: req as any }, 'cookies', getCookieParser(req.headers))\n\n      // Parse url if parsedUrl not provided\n      if (!parsedUrl || typeof parsedUrl !== 'object') {\n        const url: any = req.url\n        parsedUrl = parseUrl(url, true)\n      }\n      const { basePath, i18n } = this.nextConfig\n\n      // Parse the querystring ourselves if the user doesn't handle querystring parsing\n      if (typeof parsedUrl.query === 'string') {\n        parsedUrl.query = parseQs(parsedUrl.query)\n      }\n      ;(req as any).__NEXT_INIT_QUERY = Object.assign({}, parsedUrl.query)\n\n      const url = parseNextUrl({\n        headers: req.headers,\n        nextConfig: this.nextConfig,\n        url: req.url?.replace(/^\\/+/, '/'),\n      })\n\n      if (url.basePath) {\n        ;(req as any)._nextHadBasePath = true\n        req.url = req.url!.replace(basePath, '') || '/'\n      }\n\n      if (\n        this.minimalMode &&\n        req.headers['x-matched-path'] &&\n        typeof req.headers['x-matched-path'] === 'string'\n      ) {\n        const reqUrlIsDataUrl = req.url?.includes('/_next/data')\n        const matchedPathIsDataUrl =\n          req.headers['x-matched-path']?.includes('/_next/data')\n        const isDataUrl = reqUrlIsDataUrl || matchedPathIsDataUrl\n\n        let parsedPath = parseUrl(\n          isDataUrl ? req.url! : (req.headers['x-matched-path'] as string),\n          true\n        )\n        const { pathname, query } = parsedPath\n        let matchedPathname = pathname as string\n\n        let matchedPathnameNoExt = isDataUrl\n          ? matchedPathname.replace(/\\.json$/, '')\n          : matchedPathname\n\n        if (i18n) {\n          const localePathResult = normalizeLocalePath(\n            matchedPathname || '/',\n            i18n.locales\n          )\n\n          if (localePathResult.detectedLocale) {\n            parsedUrl.query.__nextLocale = localePathResult.detectedLocale\n          }\n        }\n\n        if (isDataUrl) {\n          matchedPathname = denormalizePagePath(matchedPathname)\n          matchedPathnameNoExt = denormalizePagePath(matchedPathnameNoExt)\n        }\n\n        const pageIsDynamic = isDynamicRoute(matchedPathnameNoExt)\n        const combinedRewrites: Rewrite[] = []\n\n        combinedRewrites.push(...this.customRoutes.rewrites.beforeFiles)\n        combinedRewrites.push(...this.customRoutes.rewrites.afterFiles)\n        combinedRewrites.push(...this.customRoutes.rewrites.fallback)\n\n        const utils = getUtils({\n          pageIsDynamic,\n          page: matchedPathnameNoExt,\n          i18n: this.nextConfig.i18n,\n          basePath: this.nextConfig.basePath,\n          rewrites: combinedRewrites,\n        })\n\n        utils.handleRewrites(req, parsedUrl)\n\n        // interpolate dynamic params and normalize URL if needed\n        if (pageIsDynamic) {\n          let params: ParsedUrlQuery | false = {}\n\n          Object.assign(parsedUrl.query, query)\n          const paramsResult = utils.normalizeDynamicRouteParams(\n            parsedUrl.query\n          )\n\n          if (paramsResult.hasValidParams) {\n            params = paramsResult.params\n          } else if (req.headers['x-now-route-matches']) {\n            const opts: Record<string, string> = {}\n            params = utils.getParamsFromRouteMatches(\n              req,\n              opts,\n              (parsedUrl.query.__nextLocale as string | undefined) || ''\n            )\n\n            if (opts.locale) {\n              parsedUrl.query.__nextLocale = opts.locale\n            }\n          } else {\n            params = utils.dynamicRouteMatcher!(matchedPathnameNoExt)\n          }\n\n          if (params) {\n            params = utils.normalizeDynamicRouteParams(params).params\n\n            matchedPathname = utils.interpolateDynamicPath(\n              matchedPathname,\n              params\n            )\n            req.url = utils.interpolateDynamicPath(req.url!, params)\n          }\n\n          if (reqUrlIsDataUrl && matchedPathIsDataUrl) {\n            req.url = formatUrl({\n              ...parsedPath,\n              pathname: matchedPathname,\n            })\n          }\n\n          Object.assign(parsedUrl.query, params)\n          utils.normalizeVercelUrl(req, true)\n        }\n\n        parsedUrl.pathname = `${basePath || ''}${\n          matchedPathname === '/' && basePath ? '' : matchedPathname\n        }`\n      }\n\n      ;(req as any).__nextHadTrailingSlash = url.locale?.trailingSlash\n      if (url.locale?.domain) {\n        ;(req as any).__nextIsLocaleDomain = true\n      }\n\n      if (url.locale?.path.detectedLocale) {\n        req.url = formatUrl(url)\n        ;(req as any).__nextStrippedLocale = true\n        if (url.pathname === '/api' || url.pathname.startsWith('/api/')) {\n          return this.render404(req, res, parsedUrl)\n        }\n      }\n\n      if (!this.minimalMode || !parsedUrl.query.__nextLocale) {\n        if (url?.locale?.locale) {\n          parsedUrl.query.__nextLocale = url.locale.locale\n        }\n      }\n\n      if (url?.locale?.defaultLocale) {\n        parsedUrl.query.__nextDefaultLocale = url.locale.defaultLocale\n      }\n\n      if (url.locale?.redirect) {\n        res.setHeader('Location', url.locale.redirect)\n        res.statusCode = TEMPORARY_REDIRECT_STATUS\n        res.end()\n        return\n      }\n\n      res.statusCode = 200\n      return await this.run(req, res, parsedUrl)\n    } catch (err) {\n      if (\n        (err && typeof err === 'object' && err.code === 'ERR_INVALID_URL') ||\n        err instanceof DecodeError\n      ) {\n        res.statusCode = 400\n        return this.renderError(null, req, res, '/_error', {})\n      }\n\n      if (this.minimalMode || this.renderOpts.dev) {\n        throw err\n      }\n      this.logError(err)\n      res.statusCode = 500\n      res.end('Internal Server Error')\n    }\n  }\n\n  public getRequestHandler() {\n    return this.handleRequest.bind(this)\n  }\n\n  public setAssetPrefix(prefix?: string): void {\n    this.renderOpts.assetPrefix = prefix ? prefix.replace(/\\/$/, '') : ''\n  }\n\n  // Backwards compatibility\n  public async prepare(): Promise<void> {}\n\n  // Backwards compatibility\n  protected async close(): Promise<void> {}\n\n  protected setImmutableAssetCacheControl(res: ServerResponse): void {\n    res.setHeader('Cache-Control', 'public, max-age=31536000, immutable')\n  }\n\n  protected getCustomRoutes(): CustomRoutes {\n    const customRoutes = require(join(this.distDir, ROUTES_MANIFEST))\n    let rewrites: CustomRoutes['rewrites']\n\n    // rewrites can be stored as an array when an array is\n    // returned in next.config.js so massage them into\n    // the expected object format\n    if (Array.isArray(customRoutes.rewrites)) {\n      rewrites = {\n        beforeFiles: [],\n        afterFiles: customRoutes.rewrites,\n        fallback: [],\n      }\n    } else {\n      rewrites = customRoutes.rewrites\n    }\n    return Object.assign(customRoutes, { rewrites })\n  }\n\n  private _cachedPreviewManifest: PrerenderManifest | undefined\n  protected getPrerenderManifest(): PrerenderManifest {\n    if (this._cachedPreviewManifest) {\n      return this._cachedPreviewManifest\n    }\n    const manifest = require(join(this.distDir, PRERENDER_MANIFEST))\n    return (this._cachedPreviewManifest = manifest)\n  }\n\n  protected getPreviewProps(): __ApiPreviewProps {\n    return this.getPrerenderManifest().preview\n  }\n\n  protected generateRoutes(): {\n    basePath: string\n    headers: Route[]\n    rewrites: {\n      beforeFiles: Route[]\n      afterFiles: Route[]\n      fallback: Route[]\n    }\n    fsRoutes: Route[]\n    redirects: Route[]\n    catchAllRoute: Route\n    pageChecker: PageChecker\n    useFileSystemPublicRoutes: boolean\n    dynamicRoutes: DynamicRoutes | undefined\n    locales: string[]\n  } {\n    const server: Server = this\n    const publicRoutes = fs.existsSync(this.publicDir)\n      ? this.generatePublicRoutes()\n      : []\n\n    const staticFilesRoute = this.hasStaticDir\n      ? [\n          {\n            // It's very important to keep this route's param optional.\n            // (but it should support as many params as needed, separated by '/')\n            // Otherwise this will lead to a pretty simple DOS attack.\n            // See more: https://github.com/vercel/next.js/issues/2617\n            match: route('/static/:path*'),\n            name: 'static catchall',\n            fn: async (req, res, params, parsedUrl) => {\n              const p = join(this.dir, 'static', ...params.path)\n              await this.serveStatic(req, res, p, parsedUrl)\n              return {\n                finished: true,\n              }\n            },\n          } as Route,\n        ]\n      : []\n\n    const fsRoutes: Route[] = [\n      {\n        match: route('/_next/static/:path*'),\n        type: 'route',\n        name: '_next/static catchall',\n        fn: async (req, res, params, parsedUrl) => {\n          // make sure to 404 for /_next/static itself\n          if (!params.path) {\n            await this.render404(req, res, parsedUrl)\n            return {\n              finished: true,\n            }\n          }\n\n          if (\n            params.path[0] === CLIENT_STATIC_FILES_RUNTIME ||\n            params.path[0] === 'chunks' ||\n            params.path[0] === 'css' ||\n            params.path[0] === 'image' ||\n            params.path[0] === 'media' ||\n            params.path[0] === this.buildId ||\n            params.path[0] === 'pages' ||\n            params.path[1] === 'pages'\n          ) {\n            this.setImmutableAssetCacheControl(res)\n          }\n          const p = join(\n            this.distDir,\n            CLIENT_STATIC_FILES_PATH,\n            ...(params.path || [])\n          )\n          await this.serveStatic(req, res, p, parsedUrl)\n          return {\n            finished: true,\n          }\n        },\n      },\n      {\n        match: route('/_next/data/:path*'),\n        type: 'route',\n        name: '_next/data catchall',\n        fn: async (req, res, params, _parsedUrl) => {\n          // Make sure to 404 for /_next/data/ itself and\n          // we also want to 404 if the buildId isn't correct\n          if (!params.path || params.path[0] !== this.buildId) {\n            await this.render404(req, res, _parsedUrl)\n            return {\n              finished: true,\n            }\n          }\n          // remove buildId from URL\n          params.path.shift()\n\n          // show 404 if it doesn't end with .json\n          if (!params.path[params.path.length - 1].endsWith('.json')) {\n            await this.render404(req, res, _parsedUrl)\n            return {\n              finished: true,\n            }\n          }\n\n          // re-create page's pathname\n          let pathname = `/${params.path.join('/')}`\n          pathname = getRouteFromAssetPath(pathname, '.json')\n\n          const { i18n } = this.nextConfig\n\n          if (i18n) {\n            const { host } = req?.headers || {}\n            // remove port from host and remove port if present\n            const hostname = host?.split(':')[0].toLowerCase()\n            const localePathResult = normalizeLocalePath(pathname, i18n.locales)\n            const { defaultLocale } =\n              detectDomainLocale(i18n.domains, hostname) || {}\n\n            let detectedLocale = ''\n\n            if (localePathResult.detectedLocale) {\n              pathname = localePathResult.pathname\n              detectedLocale = localePathResult.detectedLocale\n            }\n\n            _parsedUrl.query.__nextLocale = detectedLocale!\n            _parsedUrl.query.__nextDefaultLocale =\n              defaultLocale || i18n.defaultLocale\n\n            if (!detectedLocale) {\n              _parsedUrl.query.__nextLocale =\n                _parsedUrl.query.__nextDefaultLocale\n              await this.render404(req, res, _parsedUrl)\n              return { finished: true }\n            }\n          }\n\n          const parsedUrl = parseUrl(pathname, true)\n\n          await this.render(\n            req,\n            res,\n            pathname,\n            { ..._parsedUrl.query, _nextDataReq: '1' },\n            parsedUrl\n          )\n          return {\n            finished: true,\n          }\n        },\n      },\n      {\n        match: route('/_next/image'),\n        type: 'route',\n        name: '_next/image catchall',\n        fn: (req, res, _params, parsedUrl) => {\n          if (this.minimalMode) {\n            res.statusCode = 400\n            res.end('Bad Request')\n            return {\n              finished: true,\n            }\n          }\n          const { imageOptimizer } =\n            require('./image-optimizer') as typeof import('./image-optimizer')\n\n          return imageOptimizer(\n            server,\n            req,\n            res,\n            parsedUrl,\n            server.nextConfig,\n            server.distDir,\n            this.renderOpts.dev\n          )\n        },\n      },\n      {\n        match: route('/_next/:path*'),\n        type: 'route',\n        name: '_next catchall',\n        // This path is needed because `render()` does a check for `/_next` and the calls the routing again\n        fn: async (req, res, _params, parsedUrl) => {\n          await this.render404(req, res, parsedUrl)\n          return {\n            finished: true,\n          }\n        },\n      },\n      ...publicRoutes,\n      ...staticFilesRoute,\n    ]\n\n    const restrictedRedirectPaths = ['/_next'].map((p) =>\n      this.nextConfig.basePath ? `${this.nextConfig.basePath}${p}` : p\n    )\n\n    const getCustomRoute = (\n      r: Rewrite | Redirect | Header,\n      type: RouteType\n    ) => {\n      const match = getCustomRouteMatcher(\n        r.source,\n        !(r as any).internal\n          ? (regex: string) =>\n              modifyRouteRegex(\n                regex,\n                type === 'redirect' ? restrictedRedirectPaths : undefined\n              )\n          : undefined\n      )\n\n      return {\n        ...r,\n        type,\n        match,\n        name: type,\n        fn: async (_req, _res, _params, _parsedUrl) => ({ finished: false }),\n      } as Route & Rewrite & Header\n    }\n\n    // Headers come very first\n    const headers = this.minimalMode\n      ? []\n      : this.customRoutes.headers.map((r) => {\n          const headerRoute = getCustomRoute(r, 'header')\n          return {\n            match: headerRoute.match,\n            has: headerRoute.has,\n            type: headerRoute.type,\n            name: `${headerRoute.type} ${headerRoute.source} header route`,\n            fn: async (_req, res, params, _parsedUrl) => {\n              const hasParams = Object.keys(params).length > 0\n\n              for (const header of (headerRoute as Header).headers) {\n                let { key, value } = header\n                if (hasParams) {\n                  key = compileNonPath(key, params)\n                  value = compileNonPath(value, params)\n                }\n                res.setHeader(key, value)\n              }\n              return { finished: false }\n            },\n          } as Route\n        })\n\n    // since initial query values are decoded by querystring.parse\n    // we need to re-encode them here but still allow passing through\n    // values from rewrites/redirects\n    const stringifyQuery = (req: IncomingMessage, query: ParsedUrlQuery) => {\n      const initialQueryValues = Object.values((req as any).__NEXT_INIT_QUERY)\n\n      return stringifyQs(query, undefined, undefined, {\n        encodeURIComponent(value) {\n          if (initialQueryValues.some((val) => val === value)) {\n            return encodeURIComponent(value)\n          }\n          return value\n        },\n      })\n    }\n\n    const redirects = this.minimalMode\n      ? []\n      : this.customRoutes.redirects.map((redirect) => {\n          const redirectRoute = getCustomRoute(redirect, 'redirect')\n          return {\n            internal: redirectRoute.internal,\n            type: redirectRoute.type,\n            match: redirectRoute.match,\n            has: redirectRoute.has,\n            statusCode: redirectRoute.statusCode,\n            name: `Redirect route ${redirectRoute.source}`,\n            fn: async (req, res, params, parsedUrl) => {\n              const { parsedDestination } = prepareDestination(\n                redirectRoute.destination,\n                params,\n                parsedUrl.query,\n                false\n              )\n\n              const { query } = parsedDestination\n              delete (parsedDestination as any).query\n\n              parsedDestination.search = stringifyQuery(req, query)\n\n              let updatedDestination = formatUrl(parsedDestination)\n\n              if (updatedDestination.startsWith('/')) {\n                updatedDestination =\n                  normalizeRepeatedSlashes(updatedDestination)\n              }\n\n              res.setHeader('Location', updatedDestination)\n              res.statusCode = getRedirectStatus(redirectRoute as Redirect)\n\n              // Since IE11 doesn't support the 308 header add backwards\n              // compatibility using refresh header\n              if (res.statusCode === 308) {\n                res.setHeader('Refresh', `0;url=${updatedDestination}`)\n              }\n\n              res.end(updatedDestination)\n              return {\n                finished: true,\n              }\n            },\n          } as Route\n        })\n\n    const buildRewrite = (rewrite: Rewrite, check = true) => {\n      const rewriteRoute = getCustomRoute(rewrite, 'rewrite')\n      return {\n        ...rewriteRoute,\n        check,\n        type: rewriteRoute.type,\n        name: `Rewrite route ${rewriteRoute.source}`,\n        match: rewriteRoute.match,\n        fn: async (req, res, params, parsedUrl) => {\n          const { newUrl, parsedDestination } = prepareDestination(\n            rewriteRoute.destination,\n            params,\n            parsedUrl.query,\n            true\n          )\n\n          // external rewrite, proxy it\n          if (parsedDestination.protocol) {\n            const { query } = parsedDestination\n            delete (parsedDestination as any).query\n            parsedDestination.search = stringifyQuery(req, query)\n\n            const target = formatUrl(parsedDestination)\n            const proxy = new Proxy({\n              target,\n              changeOrigin: true,\n              ignorePath: true,\n              xfwd: true,\n              proxyTimeout: 30_000, // limit proxying to 30 seconds\n            })\n\n            await new Promise((proxyResolve, proxyReject) => {\n              let finished = false\n\n              proxy.on('proxyReq', (proxyReq) => {\n                proxyReq.on('close', () => {\n                  if (!finished) {\n                    finished = true\n                    proxyResolve(true)\n                  }\n                })\n              })\n              proxy.on('error', (err) => {\n                if (!finished) {\n                  finished = true\n                  proxyReject(err)\n                }\n              })\n              proxy.web(req, res)\n            })\n\n            return {\n              finished: true,\n            }\n          }\n          ;(req as any)._nextRewroteUrl = newUrl\n          ;(req as any)._nextDidRewrite =\n            (req as any)._nextRewroteUrl !== req.url\n\n          return {\n            finished: false,\n            pathname: newUrl,\n            query: parsedDestination.query,\n          }\n        },\n      } as Route\n    }\n\n    let beforeFiles: Route[] = []\n    let afterFiles: Route[] = []\n    let fallback: Route[] = []\n\n    if (!this.minimalMode) {\n      if (Array.isArray(this.customRoutes.rewrites)) {\n        afterFiles = this.customRoutes.rewrites.map((r) => buildRewrite(r))\n      } else {\n        beforeFiles = this.customRoutes.rewrites.beforeFiles.map((r) =>\n          buildRewrite(r, false)\n        )\n        afterFiles = this.customRoutes.rewrites.afterFiles.map((r) =>\n          buildRewrite(r)\n        )\n        fallback = this.customRoutes.rewrites.fallback.map((r) =>\n          buildRewrite(r)\n        )\n      }\n    }\n\n    const catchAllRoute: Route = {\n      match: route('/:path*'),\n      type: 'route',\n      name: 'Catchall render',\n      fn: async (req, res, _params, parsedUrl) => {\n        let { pathname, query } = parsedUrl\n        if (!pathname) {\n          throw new Error('pathname is undefined')\n        }\n\n        // next.js core assumes page path without trailing slash\n        pathname = removePathTrailingSlash(pathname)\n\n        if (this.nextConfig.i18n) {\n          const localePathResult = normalizeLocalePath(\n            pathname,\n            this.nextConfig.i18n?.locales\n          )\n\n          if (localePathResult.detectedLocale) {\n            pathname = localePathResult.pathname\n            parsedUrl.query.__nextLocale = localePathResult.detectedLocale\n          }\n        }\n        const bubbleNoFallback = !!query._nextBubbleNoFallback\n\n        if (pathname === '/api' || pathname.startsWith('/api/')) {\n          delete query._nextBubbleNoFallback\n\n          const handled = await this.handleApiRequest(\n            req as NextApiRequest,\n            res as NextApiResponse,\n            pathname,\n            query\n          )\n          if (handled) {\n            return { finished: true }\n          }\n        }\n\n        try {\n          await this.render(req, res, pathname, query, parsedUrl)\n\n          return {\n            finished: true,\n          }\n        } catch (err) {\n          if (err instanceof NoFallbackError && bubbleNoFallback) {\n            return {\n              finished: false,\n            }\n          }\n          throw err\n        }\n      },\n    }\n\n    const { useFileSystemPublicRoutes } = this.nextConfig\n\n    if (useFileSystemPublicRoutes) {\n      this.dynamicRoutes = this.getDynamicRoutes()\n    }\n\n    return {\n      headers,\n      fsRoutes,\n      rewrites: {\n        beforeFiles,\n        afterFiles,\n        fallback,\n      },\n      redirects,\n      catchAllRoute,\n      useFileSystemPublicRoutes,\n      dynamicRoutes: this.dynamicRoutes,\n      basePath: this.nextConfig.basePath,\n      pageChecker: this.hasPage.bind(this),\n      locales: this.nextConfig.i18n?.locales || [],\n    }\n  }\n\n  private async getPagePath(\n    pathname: string,\n    locales?: string[]\n  ): Promise<string> {\n    return getPagePath(\n      pathname,\n      this.distDir,\n      this._isLikeServerless,\n      this.renderOpts.dev,\n      locales\n    )\n  }\n\n  protected async hasPage(pathname: string): Promise<boolean> {\n    let found = false\n    try {\n      found = !!(await this.getPagePath(\n        pathname,\n        this.nextConfig.i18n?.locales\n      ))\n    } catch (_) {}\n\n    return found\n  }\n\n  protected async _beforeCatchAllRender(\n    _req: IncomingMessage,\n    _res: ServerResponse,\n    _params: Params,\n    _parsedUrl: UrlWithParsedQuery\n  ): Promise<boolean> {\n    return false\n  }\n\n  // Used to build API page in development\n  protected async ensureApiPage(_pathname: string): Promise<void> {}\n\n  /**\n   * Resolves `API` request, in development builds on demand\n   * @param req http request\n   * @param res http response\n   * @param pathname path of request\n   */\n  private async handleApiRequest(\n    req: IncomingMessage,\n    res: ServerResponse,\n    pathname: string,\n    query: ParsedUrlQuery\n  ): Promise<boolean> {\n    let page = pathname\n    let params: Params | boolean = false\n    let pageFound = await this.hasPage(page)\n\n    if (!pageFound && this.dynamicRoutes) {\n      for (const dynamicRoute of this.dynamicRoutes) {\n        params = dynamicRoute.match(pathname)\n        if (dynamicRoute.page.startsWith('/api') && params) {\n          page = dynamicRoute.page\n          pageFound = true\n          break\n        }\n      }\n    }\n\n    if (!pageFound) {\n      return false\n    }\n    // Make sure the page is built before getting the path\n    // or else it won't be in the manifest yet\n    await this.ensureApiPage(page)\n\n    let builtPagePath\n    try {\n      builtPagePath = await this.getPagePath(page)\n    } catch (err) {\n      if (err.code === 'ENOENT') {\n        return false\n      }\n      throw err\n    }\n\n    const pageModule = await require(builtPagePath)\n    query = { ...query, ...params }\n\n    delete query.__nextLocale\n    delete query.__nextDefaultLocale\n\n    if (!this.renderOpts.dev && this._isLikeServerless) {\n      if (typeof pageModule.default === 'function') {\n        prepareServerlessUrl(req, query)\n        await pageModule.default(req, res)\n        return true\n      }\n    }\n\n    await apiResolver(\n      req,\n      res,\n      query,\n      pageModule,\n      this.renderOpts.previewProps,\n      this.minimalMode,\n      this.renderOpts.dev,\n      page\n    )\n    return true\n  }\n\n  protected generatePublicRoutes(): Route[] {\n    const publicFiles = new Set(\n      recursiveReadDirSync(this.publicDir).map((p) =>\n        encodeURI(p.replace(/\\\\/g, '/'))\n      )\n    )\n\n    return [\n      {\n        match: route('/:path*'),\n        name: 'public folder catchall',\n        fn: async (req, res, params, parsedUrl) => {\n          const pathParts: string[] = params.path || []\n          const { basePath } = this.nextConfig\n\n          // if basePath is defined require it be present\n          if (basePath) {\n            const basePathParts = basePath.split('/')\n            // remove first empty value\n            basePathParts.shift()\n\n            if (\n              !basePathParts.every((part: string, idx: number) => {\n                return part === pathParts[idx]\n              })\n            ) {\n              return { finished: false }\n            }\n\n            pathParts.splice(0, basePathParts.length)\n          }\n\n          let path = `/${pathParts.join('/')}`\n\n          if (!publicFiles.has(path)) {\n            // In `next-dev-server.ts`, we ensure encoded paths match\n            // decoded paths on the filesystem. So we need do the\n            // opposite here: make sure decoded paths match encoded.\n            path = encodeURI(path)\n          }\n\n          if (publicFiles.has(path)) {\n            await this.serveStatic(\n              req,\n              res,\n              join(this.publicDir, ...pathParts),\n              parsedUrl\n            )\n            return {\n              finished: true,\n            }\n          }\n          return {\n            finished: false,\n          }\n        },\n      } as Route,\n    ]\n  }\n\n  protected getDynamicRoutes(): Array<DynamicRouteItem> {\n    const addedPages = new Set<string>()\n\n    return getSortedRoutes(\n      Object.keys(this.pagesManifest!).map(\n        (page) =>\n          normalizeLocalePath(page, this.nextConfig.i18n?.locales).pathname\n      )\n    )\n      .map((page) => {\n        if (addedPages.has(page) || !isDynamicRoute(page)) return null\n        addedPages.add(page)\n        return {\n          page,\n          match: getRouteMatcher(getRouteRegex(page)),\n        }\n      })\n      .filter((item): item is DynamicRouteItem => Boolean(item))\n  }\n\n  private handleCompression(req: IncomingMessage, res: ServerResponse): void {\n    if (this.compression) {\n      this.compression(req, res, () => {})\n    }\n  }\n\n  protected async run(\n    req: IncomingMessage,\n    res: ServerResponse,\n    parsedUrl: UrlWithParsedQuery\n  ): Promise<void> {\n    this.handleCompression(req, res)\n\n    try {\n      const matched = await this.router.execute(req, res, parsedUrl)\n      if (matched) {\n        return\n      }\n    } catch (err) {\n      if (err instanceof DecodeError) {\n        res.statusCode = 400\n        return this.renderError(null, req, res, '/_error', {})\n      }\n      throw err\n    }\n\n    await this.render404(req, res, parsedUrl)\n  }\n\n  private async pipe(\n    fn: (ctx: RequestContext) => Promise<ResponsePayload | null>,\n    partialContext: {\n      req: IncomingMessage\n      res: ServerResponse\n      pathname: string\n      query: ParsedUrlQuery\n    }\n  ): Promise<void> {\n    // TODO: Determine when dynamic HTML is allowed\n    const requireStaticHTML = true\n    const ctx = {\n      ...partialContext,\n      renderOpts: {\n        ...this.renderOpts,\n        requireStaticHTML,\n      },\n    } as const\n    const payload = await fn(ctx)\n    if (payload === null) {\n      return\n    }\n    const { req, res } = ctx\n    const { body, type, revalidateOptions } = payload\n    if (!isResSent(res)) {\n      const { generateEtags, poweredByHeader, dev } = this.renderOpts\n      if (dev) {\n        // In dev, we should not cache pages for any reason.\n        res.setHeader('Cache-Control', 'no-store, must-revalidate')\n      }\n      return sendRenderResult({\n        req,\n        res,\n        resultOrPayload: requireStaticHTML\n          ? await resultsToString([body])\n          : body,\n        type,\n        generateEtags,\n        poweredByHeader,\n        options: revalidateOptions,\n      })\n    }\n  }\n\n  private async getStaticHTML(\n    fn: (ctx: RequestContext) => Promise<ResponsePayload | null>,\n    partialContext: {\n      req: IncomingMessage\n      res: ServerResponse\n      pathname: string\n      query: ParsedUrlQuery\n    }\n  ): Promise<string | null> {\n    const payload = await fn({\n      ...partialContext,\n      renderOpts: {\n        ...this.renderOpts,\n        requireStaticHTML: true,\n      },\n    })\n    if (payload === null) {\n      return null\n    }\n    return resultsToString([payload.body])\n  }\n\n  public async render(\n    req: IncomingMessage,\n    res: ServerResponse,\n    pathname: string,\n    query: ParsedUrlQuery = {},\n    parsedUrl?: UrlWithParsedQuery\n  ): Promise<void> {\n    if (!pathname.startsWith('/')) {\n      console.warn(\n        `Cannot render page with path \"${pathname}\", did you mean \"/${pathname}\"?. See more info here: https://nextjs.org/docs/messages/render-no-starting-slash`\n      )\n    }\n\n    if (\n      this.renderOpts.customServer &&\n      pathname === '/index' &&\n      !(await this.hasPage('/index'))\n    ) {\n      // maintain backwards compatibility for custom server\n      // (see custom-server integration tests)\n      pathname = '/'\n    }\n\n    const url: any = req.url\n\n    // we allow custom servers to call render for all URLs\n    // so check if we need to serve a static _next file or not.\n    // we don't modify the URL for _next/data request but still\n    // call render so we special case this to prevent an infinite loop\n    if (\n      !this.minimalMode &&\n      !query._nextDataReq &&\n      (url.match(/^\\/_next\\//) ||\n        (this.hasStaticDir && url.match(/^\\/static\\//)))\n    ) {\n      return this.handleRequest(req, res, parsedUrl)\n    }\n\n    // Custom server users can run `app.render()` which needs compression.\n    if (this.renderOpts.customServer) {\n      this.handleCompression(req, res)\n    }\n\n    if (isBlockedPage(pathname)) {\n      return this.render404(req, res, parsedUrl)\n    }\n\n    return this.pipe((ctx) => this.renderToResponse(ctx), {\n      req,\n      res,\n      pathname,\n      query,\n    })\n  }\n\n  protected async findPageComponents(\n    pathname: string,\n    query: ParsedUrlQuery = {},\n    params: Params | null = null\n  ): Promise<FindComponentsResult | null> {\n    let paths = [\n      // try serving a static AMP version first\n      query.amp ? normalizePagePath(pathname) + '.amp' : null,\n      pathname,\n    ].filter(Boolean)\n\n    if (query.__nextLocale) {\n      paths = [\n        ...paths.map(\n          (path) => `/${query.__nextLocale}${path === '/' ? '' : path}`\n        ),\n        ...paths,\n      ]\n    }\n\n    for (const pagePath of paths) {\n      try {\n        const components = await loadComponents(\n          this.distDir,\n          pagePath!,\n          !this.renderOpts.dev && this._isLikeServerless\n        )\n\n        if (\n          query.__nextLocale &&\n          typeof components.Component === 'string' &&\n          !pagePath?.startsWith(`/${query.__nextLocale}`)\n        ) {\n          // if loading an static HTML file the locale is required\n          // to be present since all HTML files are output under their locale\n          continue\n        }\n\n        return {\n          components,\n          query: {\n            ...(components.getStaticProps\n              ? {\n                  amp: query.amp,\n                  _nextDataReq: query._nextDataReq,\n                  __nextLocale: query.__nextLocale,\n                  __nextDefaultLocale: query.__nextDefaultLocale,\n                }\n              : query),\n            ...(params || {}),\n          },\n        }\n      } catch (err) {\n        if (err.code !== 'ENOENT') throw err\n      }\n    }\n    return null\n  }\n\n  protected async getStaticPaths(pathname: string): Promise<{\n    staticPaths: string[] | undefined\n    fallbackMode: 'static' | 'blocking' | false\n  }> {\n    // `staticPaths` is intentionally set to `undefined` as it should've\n    // been caught when checking disk data.\n    const staticPaths = undefined\n\n    // Read whether or not fallback should exist from the manifest.\n    const fallbackField =\n      this.getPrerenderManifest().dynamicRoutes[pathname].fallback\n\n    return {\n      staticPaths,\n      fallbackMode:\n        typeof fallbackField === 'string'\n          ? 'static'\n          : fallbackField === null\n          ? 'blocking'\n          : false,\n    }\n  }\n\n  private async renderToResponseWithComponents(\n    { req, res, pathname, renderOpts: opts }: RequestContext,\n    { components, query }: FindComponentsResult\n  ): Promise<ResponsePayload | null> {\n    const is404Page = pathname === '/404'\n    const is500Page = pathname === '/500'\n\n    const isLikeServerless =\n      typeof components.Component === 'object' &&\n      typeof (components.Component as any).renderReqToHTML === 'function'\n    const isSSG = !!components.getStaticProps\n    const hasServerProps = !!components.getServerSideProps\n    const hasStaticPaths = !!components.getStaticPaths\n    const hasGetInitialProps = !!(components.Component as any).getInitialProps\n\n    // Toggle whether or not this is a Data request\n    const isDataReq = !!query._nextDataReq && (isSSG || hasServerProps)\n    delete query._nextDataReq\n\n    // we need to ensure the status code if /404 is visited directly\n    if (is404Page && !isDataReq) {\n      res.statusCode = 404\n    }\n\n    // ensure correct status is set when visiting a status page\n    // directly e.g. /500\n    if (STATIC_STATUS_PAGES.includes(pathname)) {\n      res.statusCode = parseInt(pathname.substr(1), 10)\n    }\n\n    // handle static page\n    if (typeof components.Component === 'string') {\n      return {\n        type: 'html',\n        // TODO: Static pages should be written as chunks\n        body: Observable.of(components.Component),\n      }\n    }\n\n    if (!query.amp) {\n      delete query.amp\n    }\n\n    const locale = query.__nextLocale as string\n    const defaultLocale = isSSG\n      ? this.nextConfig.i18n?.defaultLocale\n      : (query.__nextDefaultLocale as string)\n\n    const { i18n } = this.nextConfig\n    const locales = i18n?.locales\n\n    let previewData: PreviewData\n    let isPreviewMode = false\n\n    if (hasServerProps || isSSG) {\n      previewData = tryGetPreviewData(req, res, this.renderOpts.previewProps)\n      isPreviewMode = previewData !== false\n    }\n\n    // Compute the iSSG cache key. We use the rewroteUrl since\n    // pages with fallback: false are allowed to be rewritten to\n    // and we need to look up the path by the rewritten path\n    let urlPathname = parseUrl(req.url || '').pathname || '/'\n\n    let resolvedUrlPathname = (req as any)._nextRewroteUrl\n      ? (req as any)._nextRewroteUrl\n      : urlPathname\n\n    urlPathname = removePathTrailingSlash(urlPathname)\n    resolvedUrlPathname = normalizeLocalePath(\n      removePathTrailingSlash(resolvedUrlPathname),\n      this.nextConfig.i18n?.locales\n    ).pathname\n\n    const stripNextDataPath = (path: string) => {\n      if (path.includes(this.buildId)) {\n        const splitPath = path.substring(\n          path.indexOf(this.buildId) + this.buildId.length\n        )\n\n        path = denormalizePagePath(splitPath.replace(/\\.json$/, ''))\n      }\n\n      if (this.nextConfig.i18n) {\n        return normalizeLocalePath(path, locales).pathname\n      }\n      return path\n    }\n\n    const handleRedirect = (pageData: any) => {\n      const redirect = {\n        destination: pageData.pageProps.__N_REDIRECT,\n        statusCode: pageData.pageProps.__N_REDIRECT_STATUS,\n        basePath: pageData.pageProps.__N_REDIRECT_BASE_PATH,\n      }\n      const statusCode = getRedirectStatus(redirect)\n      const { basePath } = this.nextConfig\n\n      if (\n        basePath &&\n        redirect.basePath !== false &&\n        redirect.destination.startsWith('/')\n      ) {\n        redirect.destination = `${basePath}${redirect.destination}`\n      }\n\n      if (redirect.destination.startsWith('/')) {\n        redirect.destination = normalizeRepeatedSlashes(redirect.destination)\n      }\n\n      if (statusCode === PERMANENT_REDIRECT_STATUS) {\n        res.setHeader('Refresh', `0;url=${redirect.destination}`)\n      }\n\n      res.statusCode = statusCode\n      res.setHeader('Location', redirect.destination)\n      res.end()\n    }\n\n    // remove /_next/data prefix from urlPathname so it matches\n    // for direct page visit and /_next/data visit\n    if (isDataReq) {\n      resolvedUrlPathname = stripNextDataPath(resolvedUrlPathname)\n      urlPathname = stripNextDataPath(urlPathname)\n    }\n\n    let ssgCacheKey =\n      isPreviewMode || !isSSG || this.minimalMode\n        ? null // Preview mode bypasses the cache\n        : `${locale ? `/${locale}` : ''}${\n            (pathname === '/' || resolvedUrlPathname === '/') && locale\n              ? ''\n              : resolvedUrlPathname\n          }${query.amp ? '.amp' : ''}`\n\n    if ((is404Page || is500Page) && isSSG) {\n      ssgCacheKey = `${locale ? `/${locale}` : ''}${pathname}${\n        query.amp ? '.amp' : ''\n      }`\n    }\n\n    if (ssgCacheKey) {\n      // we only encode path delimiters for path segments from\n      // getStaticPaths so we need to attempt decoding the URL\n      // to match against and only escape the path delimiters\n      // this allows non-ascii values to be handled e.g. Japanese characters\n\n      // TODO: investigate adding this handling for non-SSG pages so\n      // non-ascii names work there also\n      ssgCacheKey = ssgCacheKey\n        .split('/')\n        .map((seg) => {\n          try {\n            seg = escapePathDelimiters(decodeURIComponent(seg), true)\n          } catch (_) {\n            // An improperly encoded URL was provided\n            throw new DecodeError('failed to decode param')\n          }\n          return seg\n        })\n        .join('/')\n    }\n\n    const doRender: () => Promise<ResponseCacheEntry | null> = async () => {\n      let pageData: any\n      let body: RenderResult | null\n      let sprRevalidate: number | false\n      let isNotFound: boolean | undefined\n      let isRedirect: boolean | undefined\n\n      // handle serverless\n      if (isLikeServerless) {\n        const renderResult = await (\n          components.Component as any\n        ).renderReqToHTML(req, res, 'passthrough', {\n          locale,\n          locales,\n          defaultLocale,\n          optimizeCss: this.renderOpts.optimizeCss,\n          distDir: this.distDir,\n          fontManifest: this.renderOpts.fontManifest,\n          domainLocales: this.renderOpts.domainLocales,\n        })\n\n        body = renderResult.html\n        pageData = renderResult.renderOpts.pageData\n        sprRevalidate = renderResult.renderOpts.revalidate\n        isNotFound = renderResult.renderOpts.isNotFound\n        isRedirect = renderResult.renderOpts.isRedirect\n      } else {\n        const origQuery = parseUrl(req.url || '', true).query\n        const hadTrailingSlash =\n          urlPathname !== '/' && this.nextConfig.trailingSlash\n\n        const resolvedUrl = formatUrl({\n          pathname: `${resolvedUrlPathname}${hadTrailingSlash ? '/' : ''}`,\n          // make sure to only add query values from original URL\n          query: origQuery,\n        })\n\n        const renderOpts: RenderOpts = {\n          ...components,\n          ...opts,\n          isDataReq,\n          resolvedUrl,\n          locale,\n          locales,\n          defaultLocale,\n          // For getServerSideProps and getInitialProps we need to ensure we use the original URL\n          // and not the resolved URL to prevent a hydration mismatch on\n          // asPath\n          resolvedAsPath:\n            hasServerProps || hasGetInitialProps\n              ? formatUrl({\n                  // we use the original URL pathname less the _next/data prefix if\n                  // present\n                  pathname: `${urlPathname}${hadTrailingSlash ? '/' : ''}`,\n                  query: origQuery,\n                })\n              : resolvedUrl,\n        }\n\n        const renderResult = await renderToHTML(\n          req,\n          res,\n          pathname,\n          query,\n          renderOpts\n        )\n\n        body = renderResult\n        // TODO: change this to a different passing mechanism\n        pageData = (renderOpts as any).pageData\n        sprRevalidate = (renderOpts as any).revalidate\n        isNotFound = (renderOpts as any).isNotFound\n        isRedirect = (renderOpts as any).isRedirect\n      }\n\n      let value: ResponseCacheValue | null\n      if (isNotFound) {\n        value = null\n      } else if (isRedirect) {\n        value = { kind: 'REDIRECT', props: pageData }\n      } else {\n        if (!body) {\n          return null\n        }\n        value = { kind: 'PAGE', html: body, pageData }\n      }\n      return { revalidate: sprRevalidate, value }\n    }\n\n    const cacheEntry = await this.responseCache.get(\n      ssgCacheKey,\n      async (hasResolved) => {\n        const isProduction = !this.renderOpts.dev\n        const isDynamicPathname = isDynamicRoute(pathname)\n        const didRespond = hasResolved || isResSent(res)\n\n        const { staticPaths, fallbackMode } = hasStaticPaths\n          ? await this.getStaticPaths(pathname)\n          : { staticPaths: undefined, fallbackMode: false }\n\n        // When we did not respond from cache, we need to choose to block on\n        // rendering or return a skeleton.\n        //\n        // * Data requests always block.\n        //\n        // * Blocking mode fallback always blocks.\n        //\n        // * Preview mode toggles all pages to be resolved in a blocking manner.\n        //\n        // * Non-dynamic pages should block (though this is an impossible\n        //   case in production).\n        //\n        // * Dynamic pages should return their skeleton if not defined in\n        //   getStaticPaths, then finish the data request on the client-side.\n        //\n        if (\n          this.minimalMode !== true &&\n          fallbackMode !== 'blocking' &&\n          ssgCacheKey &&\n          !didRespond &&\n          !isPreviewMode &&\n          isDynamicPathname &&\n          // Development should trigger fallback when the path is not in\n          // `getStaticPaths`\n          (isProduction ||\n            !staticPaths ||\n            !staticPaths.includes(\n              // we use ssgCacheKey here as it is normalized to match the\n              // encoding from getStaticPaths along with including the locale\n              query.amp ? ssgCacheKey.replace(/\\.amp$/, '') : ssgCacheKey\n            ))\n        ) {\n          if (\n            // In development, fall through to render to handle missing\n            // getStaticPaths.\n            (isProduction || staticPaths) &&\n            // When fallback isn't present, abort this render so we 404\n            fallbackMode !== 'static'\n          ) {\n            throw new NoFallbackError()\n          }\n\n          if (!isDataReq) {\n            // Production already emitted the fallback as static HTML.\n            if (isProduction) {\n              const html = await this.incrementalCache.getFallback(\n                locale ? `/${locale}${pathname}` : pathname\n              )\n              return {\n                value: {\n                  kind: 'PAGE',\n                  html: Observable.of(html),\n                  pageData: {},\n                },\n              }\n            }\n            // We need to generate the fallback on-demand for development.\n            else {\n              query.__nextFallback = 'true'\n              if (isLikeServerless) {\n                prepareServerlessUrl(req, query)\n              }\n              const result = await doRender()\n              if (!result) {\n                return null\n              }\n              // Prevent caching this result\n              delete result.revalidate\n              return result\n            }\n          }\n        }\n\n        const result = await doRender()\n        if (!result) {\n          return null\n        }\n        return {\n          ...result,\n          revalidate:\n            result.revalidate !== undefined\n              ? result.revalidate\n              : /* default to minimum revalidate (this should be an invariant) */ 1,\n        }\n      }\n    )\n\n    if (!cacheEntry) {\n      if (ssgCacheKey) {\n        // A cache entry might not be generated if a response is written\n        // in `getInitialProps` or `getServerSideProps`, but those shouldn't\n        // have a cache key. If we do have a cache key but we don't end up\n        // with a cache entry, then either Next.js or the application has a\n        // bug that needs fixing.\n        throw new Error('invariant: cache entry required but not generated')\n      }\n      return null\n    }\n\n    const { revalidate, value: cachedData } = cacheEntry\n    const revalidateOptions: any =\n      typeof revalidate !== 'undefined' &&\n      (!this.renderOpts.dev || (hasServerProps && !isDataReq))\n        ? {\n            // When the page is 404 cache-control should not be added\n            private: isPreviewMode || is404Page,\n            stateful: !isSSG,\n            revalidate,\n          }\n        : undefined\n\n    if (!cachedData) {\n      if (revalidateOptions) {\n        setRevalidateHeaders(res, revalidateOptions)\n      }\n      if (isDataReq) {\n        res.statusCode = 404\n        res.end('{\"notFound\":true}')\n        return null\n      } else {\n        await this.render404(req, res, {\n          pathname,\n          query,\n        } as UrlWithParsedQuery)\n        return null\n      }\n    } else if (cachedData.kind === 'REDIRECT') {\n      if (isDataReq) {\n        return {\n          type: 'json',\n          body: Observable.of(JSON.stringify(cachedData.props)),\n          revalidateOptions,\n        }\n      } else {\n        await handleRedirect(cachedData.props)\n        return null\n      }\n    } else {\n      return {\n        type: isDataReq ? 'json' : 'html',\n        body: isDataReq\n          ? Observable.of(JSON.stringify(cachedData.pageData))\n          : cachedData.html,\n        revalidateOptions,\n      }\n    }\n  }\n\n  private async renderToResponse(\n    ctx: RequestContext\n  ): Promise<ResponsePayload | null> {\n    const { res, query, pathname } = ctx\n    let page = pathname\n    const bubbleNoFallback = !!query._nextBubbleNoFallback\n    delete query._nextBubbleNoFallback\n\n    try {\n      const result = await this.findPageComponents(pathname, query)\n      if (result) {\n        try {\n          return await this.renderToResponseWithComponents(ctx, result)\n        } catch (err) {\n          const isNoFallbackError = err instanceof NoFallbackError\n\n          if (!isNoFallbackError || (isNoFallbackError && bubbleNoFallback)) {\n            throw err\n          }\n        }\n      }\n\n      if (this.dynamicRoutes) {\n        for (const dynamicRoute of this.dynamicRoutes) {\n          const params = dynamicRoute.match(pathname)\n          if (!params) {\n            continue\n          }\n\n          const dynamicRouteResult = await this.findPageComponents(\n            dynamicRoute.page,\n            query,\n            params\n          )\n          if (dynamicRouteResult) {\n            try {\n              page = dynamicRoute.page\n              return await this.renderToResponseWithComponents(\n                {\n                  ...ctx,\n                  pathname: dynamicRoute.page,\n                  renderOpts: {\n                    ...ctx.renderOpts,\n                    params,\n                  },\n                },\n                dynamicRouteResult\n              )\n            } catch (err) {\n              const isNoFallbackError = err instanceof NoFallbackError\n\n              if (\n                !isNoFallbackError ||\n                (isNoFallbackError && bubbleNoFallback)\n              ) {\n                throw err\n              }\n            }\n          }\n        }\n      }\n    } catch (err) {\n      if (err instanceof NoFallbackError && bubbleNoFallback) {\n        throw err\n      }\n      if (err instanceof DecodeError) {\n        res.statusCode = 400\n        return await this.renderErrorToResponse(ctx, err)\n      }\n\n      res.statusCode = 500\n      const isWrappedError = err instanceof WrappedBuildError\n      const response = await this.renderErrorToResponse(\n        ctx,\n        isWrappedError ? err.innerError : err\n      )\n\n      if (!isWrappedError) {\n        if (this.minimalMode || this.renderOpts.dev) {\n          if (err) {\n            err.page = page\n          }\n          throw err\n        }\n        this.logError(err)\n      }\n      return response\n    }\n    res.statusCode = 404\n    return this.renderErrorToResponse(ctx, null)\n  }\n\n  public async renderToHTML(\n    req: IncomingMessage,\n    res: ServerResponse,\n    pathname: string,\n    query: ParsedUrlQuery = {}\n  ): Promise<string | null> {\n    return this.getStaticHTML((ctx) => this.renderToResponse(ctx), {\n      req,\n      res,\n      pathname,\n      query,\n    })\n  }\n\n  public async renderError(\n    err: Error | null,\n    req: IncomingMessage,\n    res: ServerResponse,\n    pathname: string,\n    query: ParsedUrlQuery = {},\n    setHeaders = true\n  ): Promise<void> {\n    if (setHeaders) {\n      res.setHeader(\n        'Cache-Control',\n        'no-cache, no-store, max-age=0, must-revalidate'\n      )\n    }\n\n    return this.pipe(\n      async (ctx) => {\n        const response = await this.renderErrorToResponse(ctx, err)\n        if (this.minimalMode && res.statusCode === 500) {\n          throw err\n        }\n        return response\n      },\n      { req, res, pathname, query }\n    )\n  }\n\n  private customErrorNo404Warn = execOnce(() => {\n    console.warn(\n      chalk.bold.yellow(`Warning: `) +\n        chalk.yellow(\n          `You have added a custom /_error page without a custom /404 page. This prevents the 404 page from being auto statically optimized.\\nSee here for info: https://nextjs.org/docs/messages/custom-error-no-custom-404`\n        )\n    )\n  })\n\n  private async renderErrorToResponse(\n    ctx: RequestContext,\n    _err: Error | null\n  ): Promise<ResponsePayload | null> {\n    const { res, query } = ctx\n    let err = _err\n    if (this.renderOpts.dev && !err && res.statusCode === 500) {\n      err = new Error(\n        'An undefined error was thrown sometime during render... ' +\n          'See https://nextjs.org/docs/messages/threw-undefined'\n      )\n    }\n    try {\n      let result: null | FindComponentsResult = null\n\n      const is404 = res.statusCode === 404\n      let using404Page = false\n\n      // use static 404 page if available and is 404 response\n      if (is404) {\n        result = await this.findPageComponents('/404', query)\n        using404Page = result !== null\n      }\n      let statusPage = `/${res.statusCode}`\n\n      if (!result && STATIC_STATUS_PAGES.includes(statusPage)) {\n        result = await this.findPageComponents(statusPage, query)\n      }\n\n      if (!result) {\n        result = await this.findPageComponents('/_error', query)\n        statusPage = '/_error'\n      }\n\n      if (\n        process.env.NODE_ENV !== 'production' &&\n        !using404Page &&\n        (await this.hasPage('/_error')) &&\n        !(await this.hasPage('/404'))\n      ) {\n        this.customErrorNo404Warn()\n      }\n\n      try {\n        return await this.renderToResponseWithComponents(\n          {\n            ...ctx,\n            pathname: statusPage,\n            renderOpts: {\n              ...ctx.renderOpts,\n              err,\n            },\n          },\n          result!\n        )\n      } catch (maybeFallbackError) {\n        if (maybeFallbackError instanceof NoFallbackError) {\n          throw new Error('invariant: failed to render error page')\n        }\n        throw maybeFallbackError\n      }\n    } catch (renderToHtmlError) {\n      const isWrappedError = renderToHtmlError instanceof WrappedBuildError\n      if (!isWrappedError) {\n        this.logError(renderToHtmlError)\n      }\n      res.statusCode = 500\n      const fallbackComponents = await this.getFallbackErrorComponents()\n\n      if (fallbackComponents) {\n        return this.renderToResponseWithComponents(\n          {\n            ...ctx,\n            pathname: '/_error',\n            renderOpts: {\n              ...ctx.renderOpts,\n              // We render `renderToHtmlError` here because `err` is\n              // already captured in the stacktrace.\n              err: isWrappedError\n                ? renderToHtmlError.innerError\n                : renderToHtmlError,\n            },\n          },\n          {\n            query,\n            components: fallbackComponents,\n          }\n        )\n      }\n      return {\n        type: 'html',\n        body: Observable.of('Internal Server Error'),\n      }\n    }\n  }\n\n  public async renderErrorToHTML(\n    err: Error | null,\n    req: IncomingMessage,\n    res: ServerResponse,\n    pathname: string,\n    query: ParsedUrlQuery = {}\n  ): Promise<string | null> {\n    return this.getStaticHTML((ctx) => this.renderErrorToResponse(ctx, err), {\n      req,\n      res,\n      pathname,\n      query,\n    })\n  }\n\n  protected async getFallbackErrorComponents(): Promise<LoadComponentsReturnType | null> {\n    // The development server will provide an implementation for this\n    return null\n  }\n\n  public async render404(\n    req: IncomingMessage,\n    res: ServerResponse,\n    parsedUrl?: UrlWithParsedQuery,\n    setHeaders = true\n  ): Promise<void> {\n    const url: any = req.url\n    const { pathname, query } = parsedUrl ? parsedUrl : parseUrl(url, true)\n    const { i18n } = this.nextConfig\n\n    if (i18n) {\n      query.__nextLocale = query.__nextLocale || i18n.defaultLocale\n      query.__nextDefaultLocale =\n        query.__nextDefaultLocale || i18n.defaultLocale\n    }\n\n    res.statusCode = 404\n    return this.renderError(null, req, res, pathname!, query, setHeaders)\n  }\n\n  public async serveStatic(\n    req: IncomingMessage,\n    res: ServerResponse,\n    path: string,\n    parsedUrl?: UrlWithParsedQuery\n  ): Promise<void> {\n    if (!this.isServeableUrl(path)) {\n      return this.render404(req, res, parsedUrl)\n    }\n\n    if (!(req.method === 'GET' || req.method === 'HEAD')) {\n      res.statusCode = 405\n      res.setHeader('Allow', ['GET', 'HEAD'])\n      return this.renderError(null, req, res, path)\n    }\n\n    try {\n      await serveStatic(req, res, path)\n    } catch (err) {\n      if (err.code === 'ENOENT' || err.statusCode === 404) {\n        this.render404(req, res, parsedUrl)\n      } else if (err.statusCode === 412) {\n        res.statusCode = 412\n        return this.renderError(err, req, res, path)\n      } else {\n        throw err\n      }\n    }\n  }\n\n  private _validFilesystemPathSet: Set<string> | null = null\n  private getFilesystemPaths(): Set<string> {\n    if (this._validFilesystemPathSet) {\n      return this._validFilesystemPathSet\n    }\n\n    const pathUserFilesStatic = join(this.dir, 'static')\n    let userFilesStatic: string[] = []\n    if (this.hasStaticDir && fs.existsSync(pathUserFilesStatic)) {\n      userFilesStatic = recursiveReadDirSync(pathUserFilesStatic).map((f) =>\n        join('.', 'static', f)\n      )\n    }\n\n    let userFilesPublic: string[] = []\n    if (this.publicDir && fs.existsSync(this.publicDir)) {\n      userFilesPublic = recursiveReadDirSync(this.publicDir).map((f) =>\n        join('.', 'public', f)\n      )\n    }\n\n    let nextFilesStatic: string[] = []\n    nextFilesStatic = !this.minimalMode\n      ? recursiveReadDirSync(join(this.distDir, 'static')).map((f) =>\n          join('.', relative(this.dir, this.distDir), 'static', f)\n        )\n      : []\n\n    return (this._validFilesystemPathSet = new Set<string>([\n      ...nextFilesStatic,\n      ...userFilesPublic,\n      ...userFilesStatic,\n    ]))\n  }\n\n  protected isServeableUrl(untrustedFileUrl: string): boolean {\n    // This method mimics what the version of `send` we use does:\n    // 1. decodeURIComponent:\n    //    https://github.com/pillarjs/send/blob/0.17.1/index.js#L989\n    //    https://github.com/pillarjs/send/blob/0.17.1/index.js#L518-L522\n    // 2. resolve:\n    //    https://github.com/pillarjs/send/blob/de073ed3237ade9ff71c61673a34474b30e5d45b/index.js#L561\n\n    let decodedUntrustedFilePath: string\n    try {\n      // (1) Decode the URL so we have the proper file name\n      decodedUntrustedFilePath = decodeURIComponent(untrustedFileUrl)\n    } catch {\n      return false\n    }\n\n    // (2) Resolve \"up paths\" to determine real request\n    const untrustedFilePath = resolve(decodedUntrustedFilePath)\n\n    // don't allow null bytes anywhere in the file path\n    if (untrustedFilePath.indexOf('\\0') !== -1) {\n      return false\n    }\n\n    // Check if .next/static, static and public are in the path.\n    // If not the path is not available.\n    if (\n      (untrustedFilePath.startsWith(join(this.distDir, 'static') + sep) ||\n        untrustedFilePath.startsWith(join(this.dir, 'static') + sep) ||\n        untrustedFilePath.startsWith(join(this.dir, 'public') + sep)) === false\n    ) {\n      return false\n    }\n\n    // Check against the real filesystem paths\n    const filesystemUrls = this.getFilesystemPaths()\n    const resolved = relative(this.dir, untrustedFilePath)\n    return filesystemUrls.has(resolved)\n  }\n\n  protected readBuildId(): string {\n    const buildIdFile = join(this.distDir, BUILD_ID_FILE)\n    try {\n      return fs.readFileSync(buildIdFile, 'utf8').trim()\n    } catch (err) {\n      if (!fs.existsSync(buildIdFile)) {\n        throw new Error(\n          `Could not find a production build in the '${this.distDir}' directory. Try building your app with 'next build' before starting the production server. https://nextjs.org/docs/messages/production-start-no-build-id`\n        )\n      }\n\n      throw err\n    }\n  }\n\n  protected get _isLikeServerless(): boolean {\n    return isTargetLikeServerless(this.nextConfig.target)\n  }\n}\n\nfunction prepareServerlessUrl(\n  req: IncomingMessage,\n  query: ParsedUrlQuery\n): void {\n  const curUrl = parseUrl(req.url!, true)\n  req.url = formatUrl({\n    ...curUrl,\n    search: undefined,\n    query: {\n      ...curUrl.query,\n      ...query,\n    },\n  })\n}\n\nclass NoFallbackError extends Error {}\n\n// Internal wrapper around build errors at development\n// time, to prevent us from propagating or logging them\nexport class WrappedBuildError extends Error {\n  innerError: Error\n\n  constructor(innerError: Error) {\n    super()\n    this.innerError = innerError\n  }\n}\n\ntype ResponsePayload = {\n  type: 'html' | 'json'\n  body: RenderResult\n  revalidateOptions?: any\n}\n"], "names": [], "mappings": ";;;;;AAAwB,GAAgC,CAAhC,YAAgC;AACzC,GAAI,CAAJ,GAAI;AACD,GAAO,CAAP,MAAO;AAEP,GAA+B,CAA/B,UAA+B;AACJ,GAAM,CAAN,KAAM;AAK5C,GAAa,CAAb,YAAa;AACuD,GAAK,CAAL,IAAK;AACzD,GAAmC,CAAnC,cAAmC;AAUnD,GAA2B,CAA3B,iBAA2B;AAc3B,GAAyB,CAAzB,UAAyB;AAMzB,GAA4B,CAA5B,MAA4B;AACvB,GAAS,CAAT,SAAS;AAOd,GAAqB,CAArB,OAAqB;AAOrB,GAAa,CAAb,SAAa;AAC6C,GAAU,CAAV,OAAU;AACrD,GAAuC,CAAvC,UAAuC;AACxB,GAA8B,CAA9B,qBAA8B;AACV,GAAmB,CAAnB,eAAmB;AAC1C,GAAuB,CAAvB,kBAAuB;AACG,GAAU,CAAV,OAAU;AACrB,GAAW,CAAX,QAAW;AAOrD,GAAU,CAAV,OAAU;AAGV,GAAgD,CAAhD,mBAAgD;AACA,GAAgB,CAAhB,YAAgB;AAC3C,GAAgB,CAAhB,YAAgB;AACX,GAAqB,CAArB,iBAAqB;AAEO,GAAS,CAAT,OAAS;AACxC,GAAW,CAAX,IAAW;;AAGD,GAAoC,CAApC,uBAAoC;AAC1C,GAAsD,CAAtD,sBAAsD;AAEpD,GAAyB,CAAzB,oBAAyB;AACzB,GAA0C,CAA1C,oBAA0C;AAClE,GAAG,CAAH,GAAG;AACoB,GAAyC,CAAzC,mBAAyC;AAC3C,GAAmD,CAAnD,qBAAmD;AAC3D,GAAuD,CAAvD,OAAuD;AAKzE,GAAkB,CAAlB,cAAkB;AAEI,GAA2C,CAA3C,aAA2C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAExE,KAAK,CAAC,qBAAqB,OAzCL,UAAuC,UAyCrB,IAAI;MA2CvB,MAAM;kBA6CvB,GAAG,GAAG,CAAG,IACT,KAAK,EAAG,KAAK,GACb,IAAI,GACJ,GAAG,EAAG,KAAK,GACX,WAAW,EAAG,KAAK,GACnB,YAAY,EAAG,IAAI,IAC+C,CAAC;YA+B7C,GAAgC,EAYrC,IAAoB,EA2C1B,IAAoB;aAwrDzB,oBAAoB,OA95DvB,OAAqB,eA85DoB,CAAC;YAC7C,OAAO,CAAC,IAAI,CA/8DE,MAAO,SAg9Db,IAAI,CAAC,MAAM,EAAE,SAAS,KAh9DhB,MAAO,SAi9DX,MAAM,EACT,iNAAiN;QAG1N,CAAC;aAwKO,uBAAuB,GAAuB,IAAI;aA57DnD,GAAG,OA9LiC,KAAM,UA8L5B,GAAG;aACjB,KAAK,GAAG,KAAK;YApHQ,IAAW,qBAqHlB,GAAG,EAAE,GAAG,EA7GnB,GAAG;aA+GN,UAAU,GAAG,IAAI;aAEjB,OAAO,OApM6B,KAAM,YAoMtB,GAAG,OAAO,UAAU,CAAC,OAAO;aAChD,SAAS,OArM2B,KAAM,YAqMpB,GAAG,EAtK3B,UAAyB;aAuKvB,YAAY,IAAI,WAAW,IA1MrB,GAAI,SA0MwB,UAAU,KAtMR,KAAM,YAsMa,GAAG,GAAE,MAAQ;QAEzE,EAA6C,AAA7C,2CAA6C;QAC7C,EAA2D,AAA3D,yDAA2D;QAC3D,KAAK,GACH,mBAAmB;YACnB,mBAAmB,GACnB,WAAW,GACX,aAAa,GACb,QAAQ,aACD,UAAU;aAEd,OAAO,QAAQ,WAAW;aAC1B,WAAW,GAAG,WAAW;aAEzB,UAAU;YACb,eAAe,OAAO,UAAU,CAAC,eAAe;YAChD,aAAa,OAAO,UAAU,CAAC,GAAG,CAAC,aAAa;YAChD,OAAO,OAAO,OAAO;YACrB,aAAa;YACb,YAAY,OAAO,eAAe;YAClC,YAAY,EAAE,YAAY,KAAK,IAAI,GAAG,IAAI,GAAG,SAAS;YACtD,kBAAkB,GAAE,GAAgC,QAA3B,UAAU,CAAC,YAAY,CAAC,GAAG,cAAhC,GAAgC,UAAhC,CAA2C,QAA3C,CAA2C,GAA3C,GAAgC,CAAE,SAAS;YAC/D,QAAQ,OAAO,UAAU,CAAC,QAAQ;YAClC,MAAM,EAAE,IAAI,CAAC,SAAS,MAAM,UAAU,CAAC,MAAM;YAC7C,aAAa,SAAS,UAAU,CAAC,aAAa,KAAK,GAAG;YACtD,YAAY,OACL,UAAU,CAAC,aAAa,KAAK,GAAG,OAtKI,QAAW,2BAuKvB,OAAO,OAAO,iBAAiB,IACxD,IAAI;YACV,cAAc,SAAS,UAAU,CAAC,YAAY,CAAC,cAAc;YAC7D,WAAW,OAAO,UAAU,CAAC,YAAY,CAAC,WAAW;YACrD,uBAAuB,OAChB,UAAU,CAAC,YAAY,CAAC,uBAAuB;YACtD,aAAa,GAAE,IAAoB,QAAf,UAAU,CAAC,IAAI,cAApB,IAAoB,UAApB,CAA6B,QAA7B,CAA6B,GAA7B,IAAoB,CAAE,OAAO;YAC5C,OAAO,OAAO,OAAO;YACrB,kBAAkB,OAAO,UAAU,CAAC,YAAY,CAAC,kBAAkB;;QAGrE,EAAmE,AAAnE,iEAAmE;QACnE,EAAgE,AAAhE,8DAAgE;QAChE,EAAE,EAAE,MAAM,CAAC,IAAI,CAAC,mBAAmB,EAAE,MAAM,GAAG,CAAC,EAAE,CAAC;iBAC3C,UAAU,CAAC,aAAa,GAAG,mBAAmB;QACrD,CAAC;QAED,EAAE,EAAE,QAAQ,SAAS,UAAU,CAAC,MAAM,MAAK,MAAQ,GAAE,CAAC;iBAC/C,WAAW,OAzPE,YAAgC;QA0PpD,CAAC;QAED,EAA4D,AAA5D,0DAA4D;QAjNpD,SAAS,CAkNP,SAAS;YACjB,mBAAmB;YACnB,mBAAmB;;aAGhB,cAAc,OA7PsB,KAAM,YA8PxC,OAAO,OACP,iBAAiB,GAhOrB,UAAyB,wBAAzB,UAAyB;QAkO5B,KAAK,CAAC,iBAAiB,OAjQkB,KAAM,YAiQX,cAAc,EAlO/C,UAAyB;QAoO5B,EAAE,GAAG,GAAG,EAAE,CAAC;iBACJ,aAAa,GAAG,OAAO,CAAC,iBAAiB;QAChD,CAAC;aAEI,YAAY,QAAQ,eAAe;aACnC,MAAM,GAAG,GAAG,CAtMd,OAAU,cAsMiB,cAAc;aACvC,cAAc,CAAC,WAAW;aAE1B,gBAAgB,GAAG,GAAG,CAnME,iBAAqB;YAoMhD,GAAG;YACH,OAAO,OAAO,OAAO;YACrB,QAAQ,MA9Q+B,KAAM,YA+QtC,OAAO,OACP,iBAAiB,GAjPvB,UAAyB,wBAAzB,UAAyB,oBAkPxB,KAAO;YAET,OAAO,GAAE,IAAoB,QAAf,UAAU,CAAC,IAAI,cAApB,IAAoB,UAApB,CAA6B,QAA7B,CAA6B,GAA7B,IAAoB,CAAE,OAAO;YACtC,GAAG,OAAO,UAAU,CAAC,YAAY,CAAC,kBAAkB;YACpD,WAAW,GAAG,WAAW,SAAS,UAAU,CAAC,YAAY,CAAC,cAAc;;aAErE,aAAa,GAAG,GAAG,CA5LrB,cAAkB,cA4LuB,gBAAgB;QAE5D,EAKG,AALH;;;;;KAKG,AALH,EAKG,CACH,EAAE,OAAO,UAAU,CAAC,aAAa,EAAE,CAAC;YAClC,OAAO,CAAC,GAAG,CAAC,qBAAqB,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI;QACzD,CAAC;QACD,EAAE,OAAO,UAAU,CAAC,cAAc,EAAE,CAAC;YACnC,OAAO,CAAC,GAAG,CAAC,sBAAsB,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI;QAC1D,CAAC;QACD,EAAE,OAAO,UAAU,CAAC,WAAW,EAAE,CAAC;YAChC,OAAO,CAAC,GAAG,CAAC,mBAAmB,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI;QACvD,CAAC;IACH,CAAC;IAEM,QAAQ,CAAC,GAAU,EAAQ,CAAC;QACjC,EAAE,OAAO,KAAK;QACd,OAAO,CAAC,KAAK,CAAC,GAAG;IACnB,CAAC;UAEa,aAAa,CACzB,GAAoB,EACpB,GAAmB,EACnB,SAA8B,EACf,CAAC;YACZ,CAAC;gBA+BI,IAAO,EAkHyB,IAAU,EAC7C,IAAU,EAIV,IAAU,EAcV,IAAW,EAIX,IAAU;YAvKd,KAAK,CAAC,QAAQ,IAAI,GAAG,CAAC,GAAG,QAAQ,KAAK,EAAC,CAAG;YAC1C,KAAK,CAAC,UAAU,GAAG,QAAQ,CAAC,CAAC;YAE7B,EAAE,EAAE,UAAU,aAAV,UAAU,UAAV,CAAiB,QAAjB,CAAiB,GAAjB,UAAU,CAAE,KAAK,eAAe,CAAC;gBACnC,KAAK,CAAC,QAAQ,OA5Qf,OAAqB,2BA4QsB,GAAG,CAAC,GAAG;gBACjD,GAAG,CAAC,SAAS,EAAC,QAAU,GAAE,QAAQ;gBAClC,GAAG,CAAC,SAAS,EAAC,OAAS,IAAG,MAAM,EAAE,QAAQ;gBAC1C,GAAG,CAAC,UAAU,GAAG,GAAG;gBACpB,GAAG,CAAC,GAAG,CAAC,QAAQ;;YAElB,CAAC;gBA3QA,SAAa;gBA6QA,GAAG,EAAE,GAAG;gBAAW,OAAS,OA7QzC,SAAa,kBA6Q8C,GAAG,CAAC,OAAO;YAEvE,EAAsC,AAAtC,oCAAsC;YACtC,EAAE,GAAG,SAAS,WAAW,SAAS,MAAK,MAAQ,GAAE,CAAC;gBAChD,KAAK,CAAC,GAAG,GAAQ,GAAG,CAAC,GAAG;gBACxB,SAAS,OAhU0D,IAAK,QAgUnD,GAAG,EAAE,IAAI;YAChC,CAAC;YACD,KAAK,GAAG,QAAQ,GAAE,IAAI,WAAU,UAAU;YAE1C,EAAiF,AAAjF,+EAAiF;YACjF,EAAE,SAAS,SAAS,CAAC,KAAK,MAAK,MAAQ,GAAE,CAAC;gBACxC,SAAS,CAAC,KAAK,OAvUhB,YAAa,QAuUc,SAAS,CAAC,KAAK;YAC3C,CAAC;YACC,GAAG,CAAS,iBAAiB,GAAG,MAAM,CAAC,MAAM;eAAK,SAAS,CAAC,KAAK;YAEnE,KAAK,CAAC,GAAG,OAnPc,aAA2C;gBAoPhE,OAAO,EAAE,GAAG,CAAC,OAAO;gBACpB,UAAU,OAAO,UAAU;gBAC3B,GAAG,GAAE,IAAO,GAAP,GAAG,CAAC,GAAG,cAAP,IAAO,UAAP,CAAgB,QAAhB,CAAgB,GAAhB,IAAO,CAAE,OAAO,UAAS,CAAG;;YAGnC,EAAE,EAAE,GAAG,CAAC,QAAQ,EAAE,CAAC;gBACf,GAAG,CAAS,gBAAgB,GAAG,IAAI;gBACrC,GAAG,CAAC,GAAG,GAAG,GAAG,CAAC,GAAG,CAAE,OAAO,CAAC,QAAQ,UAAS,CAAG;YACjD,CAAC;YAED,EAAE,OACK,WAAW,IAChB,GAAG,CAAC,OAAO,EAAC,cAAgB,aACrB,GAAG,CAAC,OAAO,EAAC,cAAgB,QAAM,MAAQ,GACjD,CAAC;oBACuB,IAAO,EAE7B,KAA6B;gBAF/B,KAAK,CAAC,eAAe,IAAG,IAAO,GAAP,GAAG,CAAC,GAAG,cAAP,IAAO,UAAP,CAAiB,QAAjB,CAAiB,GAAjB,IAAO,CAAE,QAAQ,EAAC,WAAa;gBACvD,KAAK,CAAC,oBAAoB,IACxB,KAA6B,GAA7B,GAAG,CAAC,OAAO,EAAC,cAAgB,gBAA5B,KAA6B,UAA7B,CAAuC,QAAvC,CAAuC,GAAvC,KAA6B,CAAE,QAAQ,EAAC,WAAa;gBACvD,KAAK,CAAC,SAAS,GAAG,eAAe,IAAI,oBAAoB;gBAEzD,GAAG,CAAC,UAAU,OA/VqD,IAAK,QAgWtE,SAAS,GAAG,GAAG,CAAC,GAAG,GAAK,GAAG,CAAC,OAAO,EAAC,cAAgB,IACpD,IAAI;gBAEN,KAAK,GAAG,QAAQ,GAAE,KAAK,MAAK,UAAU;gBACtC,GAAG,CAAC,eAAe,GAAG,QAAQ;gBAE9B,GAAG,CAAC,oBAAoB,GAAG,SAAS,GAChC,eAAe,CAAC,OAAO,kBACvB,eAAe;gBAEnB,EAAE,EAAE,IAAI,EAAE,CAAC;oBACT,KAAK,CAAC,gBAAgB,OA/RI,oBAA0C,sBAgSlE,eAAe,KAAI,CAAG,GACtB,IAAI,CAAC,OAAO;oBAGd,EAAE,EAAE,gBAAgB,CAAC,cAAc,EAAE,CAAC;wBACpC,SAAS,CAAC,KAAK,CAAC,YAAY,GAAG,gBAAgB,CAAC,cAAc;oBAChE,CAAC;gBACH,CAAC;gBAED,EAAE,EAAE,SAAS,EAAE,CAAC;oBACd,eAAe,OA3SW,oBAAyB,sBA2Sb,eAAe;oBACrD,oBAAoB,OA5SM,oBAAyB,sBA4SR,oBAAoB;gBACjE,CAAC;gBAED,KAAK,CAAC,aAAa,OA3VpB,MAA4B,iBA2VU,oBAAoB;gBACzD,KAAK,CAAC,gBAAgB;gBAEtB,gBAAgB,CAAC,IAAI,SAAS,YAAY,CAAC,QAAQ,CAAC,WAAW;gBAC/D,gBAAgB,CAAC,IAAI,SAAS,YAAY,CAAC,QAAQ,CAAC,UAAU;gBAC9D,gBAAgB,CAAC,IAAI,SAAS,YAAY,CAAC,QAAQ,CAAC,QAAQ;gBAE5D,KAAK,CAAC,KAAK,OAjTM,OAAuD;oBAkTtE,aAAa;oBACb,IAAI,EAAE,oBAAoB;oBAC1B,IAAI,OAAO,UAAU,CAAC,IAAI;oBAC1B,QAAQ,OAAO,UAAU,CAAC,QAAQ;oBAClC,QAAQ,EAAE,gBAAgB;;gBAG5B,KAAK,CAAC,cAAc,CAAC,GAAG,EAAE,SAAS;gBAEnC,EAAyD,AAAzD,uDAAyD;gBACzD,EAAE,EAAE,aAAa,EAAE,CAAC;oBAClB,GAAG,CAAC,MAAM;;oBAEV,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,KAAK,EAAE,KAAK;oBACpC,KAAK,CAAC,YAAY,GAAG,KAAK,CAAC,2BAA2B,CACpD,SAAS,CAAC,KAAK;oBAGjB,EAAE,EAAE,YAAY,CAAC,cAAc,EAAE,CAAC;wBAChC,MAAM,GAAG,YAAY,CAAC,MAAM;oBAC9B,CAAC,MAAM,EAAE,EAAE,GAAG,CAAC,OAAO,EAAC,mBAAqB,IAAG,CAAC;wBAC9C,KAAK,CAAC,IAAI;;wBACV,MAAM,GAAG,KAAK,CAAC,yBAAyB,CACtC,GAAG,EACH,IAAI,EACH,SAAS,CAAC,KAAK,CAAC,YAAY;wBAG/B,EAAE,EAAE,IAAI,CAAC,MAAM,EAAE,CAAC;4BAChB,SAAS,CAAC,KAAK,CAAC,YAAY,GAAG,IAAI,CAAC,MAAM;wBAC5C,CAAC;oBACH,CAAC,MAAM,CAAC;wBACN,MAAM,GAAG,KAAK,CAAC,mBAAmB,CAAE,oBAAoB;oBAC1D,CAAC;oBAED,EAAE,EAAE,MAAM,EAAE,CAAC;wBACX,MAAM,GAAG,KAAK,CAAC,2BAA2B,CAAC,MAAM,EAAE,MAAM;wBAEzD,eAAe,GAAG,KAAK,CAAC,sBAAsB,CAC5C,eAAe,EACf,MAAM;wBAER,GAAG,CAAC,GAAG,GAAG,KAAK,CAAC,sBAAsB,CAAC,GAAG,CAAC,GAAG,EAAG,MAAM;oBACzD,CAAC;oBAED,EAAE,EAAE,eAAe,IAAI,oBAAoB,EAAE,CAAC;wBAC5C,GAAG,CAAC,GAAG,OAhbwD,IAAK;+BAib/D,UAAU;4BACb,QAAQ,EAAE,eAAe;;oBAE7B,CAAC;oBAED,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,KAAK,EAAE,MAAM;oBACrC,KAAK,CAAC,kBAAkB,CAAC,GAAG,EAAE,IAAI;gBACpC,CAAC;gBAED,SAAS,CAAC,QAAQ,MAAM,QAAQ,SAC9B,eAAe,MAAK,CAAG,KAAI,QAAQ,QAAQ,eAAe;YAE9D,CAAC;YAEC,GAAG,CAAS,sBAAsB,IAAG,IAAU,GAAV,GAAG,CAAC,MAAM,cAAV,IAAU,UAAV,CAAyB,QAAzB,CAAyB,GAAzB,IAAU,CAAE,aAAa;YAChE,EAAE,GAAE,IAAU,GAAV,GAAG,CAAC,MAAM,cAAV,IAAU,UAAV,CAAkB,QAAlB,CAAkB,GAAlB,IAAU,CAAE,MAAM,EAAE,CAAC;gBACrB,GAAG,CAAS,oBAAoB,GAAG,IAAI;YAC3C,CAAC;YAED,EAAE,GAAE,IAAU,GAAV,GAAG,CAAC,MAAM,cAAV,IAAU,UAAV,CAAgB,QAAhB,CAAgB,GAAhB,IAAU,CAAE,IAAI,CAAC,cAAc,EAAE,CAAC;gBACpC,GAAG,CAAC,GAAG,OArc4D,IAAK,SAqcpD,GAAG;gBACrB,GAAG,CAAS,oBAAoB,GAAG,IAAI;gBACzC,EAAE,EAAE,GAAG,CAAC,QAAQ,MAAK,IAAM,KAAI,GAAG,CAAC,QAAQ,CAAC,UAAU,EAAC,KAAO,IAAG,CAAC;gCACpD,SAAS,CAAC,GAAG,EAAE,GAAG,EAAE,SAAS;gBAC3C,CAAC;YACH,CAAC;YAED,EAAE,QAAQ,WAAW,KAAK,SAAS,CAAC,KAAK,CAAC,YAAY,EAAE,CAAC;oBACnD,KAAW;gBAAf,EAAE,EAAE,GAAG,aAAH,GAAG,UAAH,CAAW,QAAX,CAAW,IAAX,KAAW,GAAX,GAAG,CAAE,MAAM,cAAX,KAAW,UAAX,CAAW,QAAX,CAAW,GAAX,KAAW,CAAE,MAAM,EAAE,CAAC;oBACxB,SAAS,CAAC,KAAK,CAAC,YAAY,GAAG,GAAG,CAAC,MAAM,CAAC,MAAM;gBAClD,CAAC;YACH,CAAC;YAED,EAAE,EAAE,GAAG,aAAH,GAAG,UAAH,CAAW,QAAX,CAAW,IAAX,IAAW,GAAX,GAAG,CAAE,MAAM,cAAX,IAAW,UAAX,CAAW,QAAX,CAAW,GAAX,IAAW,CAAE,aAAa,EAAE,CAAC;gBAC/B,SAAS,CAAC,KAAK,CAAC,mBAAmB,GAAG,GAAG,CAAC,MAAM,CAAC,aAAa;YAChE,CAAC;YAED,EAAE,GAAE,IAAU,GAAV,GAAG,CAAC,MAAM,cAAV,IAAU,UAAV,CAAoB,QAApB,CAAoB,GAApB,IAAU,CAAE,QAAQ,EAAE,CAAC;gBACzB,GAAG,CAAC,SAAS,EAAC,QAAU,GAAE,GAAG,CAAC,MAAM,CAAC,QAAQ;gBAC7C,GAAG,CAAC,UAAU,GA/bf,UAAyB;gBAgcxB,GAAG,CAAC,GAAG;;YAET,CAAC;YAED,GAAG,CAAC,UAAU,GAAG,GAAG;8BACF,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,SAAS;QAC3C,CAAC,QAAQ,GAAG,EAAE,CAAC;YACb,EAAE,EACC,GAAG,WAAW,GAAG,MAAK,MAAQ,KAAI,GAAG,CAAC,IAAI,MAAK,eAAiB,KACjE,GAAG,YA3bJ,OAAqB,cA4bpB,CAAC;gBACD,GAAG,CAAC,UAAU,GAAG,GAAG;4BACR,WAAW,CAAC,IAAI,EAAE,GAAG,EAAE,GAAG,GAAE,OAAS;;YACnD,CAAC;YAED,EAAE,OAAO,WAAW,SAAS,UAAU,CAAC,GAAG,EAAE,CAAC;gBAC5C,KAAK,CAAC,GAAG;YACX,CAAC;iBACI,QAAQ,CAAC,GAAG;YACjB,GAAG,CAAC,UAAU,GAAG,GAAG;YACpB,GAAG,CAAC,GAAG,EAAC,qBAAuB;QACjC,CAAC;IACH,CAAC;IAEM,iBAAiB,GAAG,CAAC;oBACd,aAAa,CAAC,IAAI;IAChC,CAAC;IAEM,cAAc,CAAC,MAAe,EAAQ,CAAC;aACvC,UAAU,CAAC,WAAW,GAAG,MAAM,GAAG,MAAM,CAAC,OAAO;IACvD,CAAC;IAED,EAA0B,AAA1B,wBAA0B;UACb,OAAO,GAAkB,CAAC;IAAA,CAAC;IAExC,EAA0B,AAA1B,wBAA0B;UACV,KAAK,GAAkB,CAAC;IAAA,CAAC;IAE/B,6BAA6B,CAAC,GAAmB,EAAQ,CAAC;QAClE,GAAG,CAAC,SAAS,EAAC,aAAe,IAAE,mCAAqC;IACtE,CAAC;IAES,eAAe,GAAiB,CAAC;QACzC,KAAK,CAAC,YAAY,GAAG,OAAO,KA1gBa,KAAM,YA0gBR,OAAO,EA3e3C,UAAyB;QA4e5B,GAAG,CAAC,QAAQ;QAEZ,EAAsD,AAAtD,oDAAsD;QACtD,EAAkD,AAAlD,gDAAkD;QAClD,EAA6B,AAA7B,2BAA6B;QAC7B,EAAE,EAAE,KAAK,CAAC,OAAO,CAAC,YAAY,CAAC,QAAQ,GAAG,CAAC;YACzC,QAAQ;gBACN,WAAW;gBACX,UAAU,EAAE,YAAY,CAAC,QAAQ;gBACjC,QAAQ;;QAEZ,CAAC,MAAM,CAAC;YACN,QAAQ,GAAG,YAAY,CAAC,QAAQ;QAClC,CAAC;eACM,MAAM,CAAC,MAAM,CAAC,YAAY;YAAI,QAAQ;;IAC/C,CAAC;IAGS,oBAAoB,GAAsB,CAAC;QACnD,EAAE,OAAO,sBAAsB,EAAE,CAAC;wBACpB,sBAAsB;QACpC,CAAC;QACD,KAAK,CAAC,QAAQ,GAAG,OAAO,KAjiBiB,KAAM,YAiiBZ,OAAO,EAlgBvC,UAAyB;oBAmgBf,sBAAsB,GAAG,QAAQ;IAChD,CAAC;IAES,eAAe,GAAsB,CAAC;oBAClC,oBAAoB,GAAG,OAAO;IAC5C,CAAC;IAES,cAAc,GAetB,CAAC;YA2cU,KAAoB;QA1c/B,KAAK,CAAC,MAAM;QACZ,KAAK,CAAC,YAAY,GA9jBP,GAAI,SA8jBS,UAAU,MAAM,SAAS,SACxC,oBAAoB;QAG7B,KAAK,CAAC,gBAAgB,QAAQ,YAAY;;gBAGlC,EAA2D,AAA3D,yDAA2D;gBAC3D,EAAqE,AAArE,mEAAqE;gBACrE,EAA0D,AAA1D,wDAA0D;gBAC1D,EAA0D,AAA1D,wDAA0D;gBAC1D,KAAK,MAngBV,OAAU,SAmgBQ,cAAgB;gBAC7B,IAAI,GAAE,eAAiB;gBACvB,EAAE,SAAS,GAAG,EAAE,GAAG,EAAE,MAAM,EAAE,SAAS,GAAK,CAAC;oBAC1C,KAAK,CAAC,CAAC,OAxkBwB,KAAM,YAwkBjB,GAAG,GAAE,MAAQ,MAAK,MAAM,CAAC,IAAI;+BACtC,WAAW,CAAC,GAAG,EAAE,GAAG,EAAE,CAAC,EAAE,SAAS;;wBAE3C,QAAQ,EAAE,IAAI;;gBAElB,CAAC;;;QAKT,KAAK,CAAC,QAAQ;;gBAEV,KAAK,MAlhBN,OAAU,SAkhBI,oBAAsB;gBACnC,IAAI,GAAE,KAAO;gBACb,IAAI,GAAE,qBAAuB;gBAC7B,EAAE,SAAS,GAAG,EAAE,GAAG,EAAE,MAAM,EAAE,SAAS,GAAK,CAAC;oBAC1C,EAA4C,AAA5C,0CAA4C;oBAC5C,EAAE,GAAG,MAAM,CAAC,IAAI,EAAE,CAAC;mCACN,SAAS,CAAC,GAAG,EAAE,GAAG,EAAE,SAAS;;4BAEtC,QAAQ,EAAE,IAAI;;oBAElB,CAAC;oBAED,EAAE,EACA,MAAM,CAAC,IAAI,CAAC,CAAC,MAlkBlB,UAAyB,gCAmkBpB,MAAM,CAAC,IAAI,CAAC,CAAC,OAAM,MAAQ,KAC3B,MAAM,CAAC,IAAI,CAAC,CAAC,OAAM,GAAK,KACxB,MAAM,CAAC,IAAI,CAAC,CAAC,OAAM,KAAO,KAC1B,MAAM,CAAC,IAAI,CAAC,CAAC,OAAM,KAAO,KAC1B,MAAM,CAAC,IAAI,CAAC,CAAC,WAAW,OAAO,IAC/B,MAAM,CAAC,IAAI,CAAC,CAAC,OAAM,KAAO,KAC1B,MAAM,CAAC,IAAI,CAAC,CAAC,OAAM,KAAO,GAC1B,CAAC;6BACI,6BAA6B,CAAC,GAAG;oBACxC,CAAC;oBACD,KAAK,CAAC,CAAC,OA5mB4B,KAAM,YA6mBlC,OAAO,EA9kBjB,UAAyB,8BAglBhB,MAAM,CAAC,IAAI;+BAEN,WAAW,CAAC,GAAG,EAAE,GAAG,EAAE,CAAC,EAAE,SAAS;;wBAE3C,QAAQ,EAAE,IAAI;;gBAElB,CAAC;;;gBAGD,KAAK,MAtjBN,OAAU,SAsjBI,kBAAoB;gBACjC,IAAI,GAAE,KAAO;gBACb,IAAI,GAAE,mBAAqB;gBAC3B,EAAE,SAAS,GAAG,EAAE,GAAG,EAAE,MAAM,EAAE,UAAU,GAAK,CAAC;oBAC3C,EAA+C,AAA/C,6CAA+C;oBAC/C,EAAmD,AAAnD,iDAAmD;oBACnD,EAAE,GAAG,MAAM,CAAC,IAAI,IAAI,MAAM,CAAC,IAAI,CAAC,CAAC,WAAW,OAAO,EAAE,CAAC;mCACzC,SAAS,CAAC,GAAG,EAAE,GAAG,EAAE,UAAU;;4BAEvC,QAAQ,EAAE,IAAI;;oBAElB,CAAC;oBACD,EAA0B,AAA1B,wBAA0B;oBAC1B,MAAM,CAAC,IAAI,CAAC,KAAK;oBAEjB,EAAwC,AAAxC,sCAAwC;oBACxC,EAAE,GAAG,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE,QAAQ,EAAC,KAAO,IAAG,CAAC;mCAChD,SAAS,CAAC,GAAG,EAAE,GAAG,EAAE,UAAU;;4BAEvC,QAAQ,EAAE,IAAI;;oBAElB,CAAC;oBAED,EAA4B,AAA5B,0BAA4B;oBAC5B,GAAG,CAAC,QAAQ,IAAI,CAAC,EAAE,MAAM,CAAC,IAAI,CAAC,IAAI,EAAC,CAAG;oBACvC,QAAQ,OAlkBgB,sBAAsD,UAkkB7C,QAAQ,GAAE,KAAO;oBAElD,KAAK,GAAG,IAAI,WAAU,UAAU;oBAEhC,EAAE,EAAE,IAAI,EAAE,CAAC;wBACT,KAAK,GAAG,IAAI,OAAK,GAAG,aAAH,GAAG,UAAH,CAAY,QAAZ,CAAY,GAAZ,GAAG,CAAE,OAAO;;wBAC7B,EAAmD,AAAnD,iDAAmD;wBACnD,KAAK,CAAC,QAAQ,GAAG,IAAI,aAAJ,IAAI,UAAJ,CAAW,QAAX,CAAW,GAAX,IAAI,CAAE,KAAK,EAAC,CAAG,GAAE,CAAC,EAAE,WAAW;wBAChD,KAAK,CAAC,gBAAgB,OAvkBE,oBAA0C,sBAukBrB,QAAQ,EAAE,IAAI,CAAC,OAAO;wBACnE,KAAK,GAAG,aAAa,UAtkBE,mBAAyC,qBAukB3C,IAAI,CAAC,OAAO,EAAE,QAAQ;;wBAE3C,GAAG,CAAC,cAAc;wBAElB,EAAE,EAAE,gBAAgB,CAAC,cAAc,EAAE,CAAC;4BACpC,QAAQ,GAAG,gBAAgB,CAAC,QAAQ;4BACpC,cAAc,GAAG,gBAAgB,CAAC,cAAc;wBAClD,CAAC;wBAED,UAAU,CAAC,KAAK,CAAC,YAAY,GAAG,cAAc;wBAC9C,UAAU,CAAC,KAAK,CAAC,mBAAmB,GAClC,aAAa,IAAI,IAAI,CAAC,aAAa;wBAErC,EAAE,GAAG,cAAc,EAAE,CAAC;4BACpB,UAAU,CAAC,KAAK,CAAC,YAAY,GAC3B,UAAU,CAAC,KAAK,CAAC,mBAAmB;uCAC3B,SAAS,CAAC,GAAG,EAAE,GAAG,EAAE,UAAU;;gCAChC,QAAQ,EAAE,IAAI;;wBACzB,CAAC;oBACH,CAAC;oBAED,KAAK,CAAC,SAAS,OA1qBkD,IAAK,QA0qB3C,QAAQ,EAAE,IAAI;+BAE9B,MAAM,CACf,GAAG,EACH,GAAG,EACH,QAAQ;2BACH,UAAU,CAAC,KAAK;wBAAE,YAAY,GAAE,CAAG;uBACxC,SAAS;;wBAGT,QAAQ,EAAE,IAAI;;gBAElB,CAAC;;;gBAGD,KAAK,MA7nBN,OAAU,SA6nBI,YAAc;gBAC3B,IAAI,GAAE,KAAO;gBACb,IAAI,GAAE,oBAAsB;gBAC5B,EAAE,GAAG,GAAG,EAAE,GAAG,EAAE,OAAO,EAAE,SAAS,GAAK,CAAC;oBACrC,EAAE,OAAO,WAAW,EAAE,CAAC;wBACrB,GAAG,CAAC,UAAU,GAAG,GAAG;wBACpB,GAAG,CAAC,GAAG,EAAC,WAAa;;4BAEnB,QAAQ,EAAE,IAAI;;oBAElB,CAAC;oBACD,KAAK,GAAG,cAAc,MACpB,OAAO,EAAC,iBAAmB;2BAEtB,cAAc,CACnB,MAAM,EACN,GAAG,EACH,GAAG,EACH,SAAS,EACT,MAAM,CAAC,UAAU,EACjB,MAAM,CAAC,OAAO,OACT,UAAU,CAAC,GAAG;gBAEvB,CAAC;;;gBAGD,KAAK,MAvpBN,OAAU,SAupBI,aAAe;gBAC5B,IAAI,GAAE,KAAO;gBACb,IAAI,GAAE,cAAgB;gBACtB,EAAmG,AAAnG,iGAAmG;gBACnG,EAAE,SAAS,GAAG,EAAE,GAAG,EAAE,OAAO,EAAE,SAAS,GAAK,CAAC;+BAChC,SAAS,CAAC,GAAG,EAAE,GAAG,EAAE,SAAS;;wBAEtC,QAAQ,EAAE,IAAI;;gBAElB,CAAC;;eAEA,YAAY;eACZ,gBAAgB;;QAGrB,KAAK,CAAC,uBAAuB;aAAI,MAAQ;UAAE,GAAG,EAAE,CAAC,QAC1C,UAAU,CAAC,QAAQ,WAAW,UAAU,CAAC,QAAQ,GAAG,CAAC,KAAK,CAAC;;QAGlE,KAAK,CAAC,cAAc,IAClB,CAA8B,EAC9B,IAAe,GACZ,CAAC;YACJ,KAAK,CAAC,KAAK,GAAG,qBAAqB,CACjC,CAAC,CAAC,MAAM,GACN,CAAC,CAAS,QAAQ,IACf,KAAa,OAluBnB,iBAA2B,mBAouBlB,KAAK,EACL,IAAI,MAAK,QAAU,IAAG,uBAAuB,GAAG,SAAS;eAE7D,SAAS;;mBAIV,CAAC;gBACJ,IAAI;gBACJ,KAAK;gBACL,IAAI,EAAE,IAAI;gBACV,EAAE,SAAS,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE,UAAU;wBAAQ,QAAQ,EAAE,KAAK;;;QAErE,CAAC;QAED,EAA0B,AAA1B,wBAA0B;QAC1B,KAAK,CAAC,OAAO,QAAQ,WAAW,aAEvB,YAAY,CAAC,OAAO,CAAC,GAAG,EAAE,CAAC,GAAK,CAAC;YACpC,KAAK,CAAC,WAAW,GAAG,cAAc,CAAC,CAAC,GAAE,MAAQ;;gBAE5C,KAAK,EAAE,WAAW,CAAC,KAAK;gBACxB,GAAG,EAAE,WAAW,CAAC,GAAG;gBACpB,IAAI,EAAE,WAAW,CAAC,IAAI;gBACtB,IAAI,KAAK,WAAW,CAAC,IAAI,CAAC,CAAC,EAAE,WAAW,CAAC,MAAM,CAAC,aAAa;gBAC7D,EAAE,SAAS,IAAI,EAAE,GAAG,EAAE,MAAM,EAAE,UAAU,GAAK,CAAC;oBAC5C,KAAK,CAAC,SAAS,GAAG,MAAM,CAAC,IAAI,CAAC,MAAM,EAAE,MAAM,GAAG,CAAC;yBAE3C,KAAK,CAAC,MAAM,IAAK,WAAW,CAAY,OAAO,CAAE,CAAC;wBACrD,GAAG,GAAG,GAAG,GAAE,KAAK,MAAK,MAAM;wBAC3B,EAAE,EAAE,SAAS,EAAE,CAAC;4BACd,GAAG,OA/sBd,mBAAgD,iBA+sBhB,GAAG,EAAE,MAAM;4BAChC,KAAK,OAhtBhB,mBAAgD,iBAgtBd,KAAK,EAAE,MAAM;wBACtC,CAAC;wBACD,GAAG,CAAC,SAAS,CAAC,GAAG,EAAE,KAAK;oBAC1B,CAAC;;wBACQ,QAAQ,EAAE,KAAK;;gBAC1B,CAAC;;QAEL,CAAC;QAEL,EAA8D,AAA9D,4DAA8D;QAC9D,EAAiE,AAAjE,+DAAiE;QACjE,EAAiC,AAAjC,+BAAiC;QACjC,KAAK,CAAC,cAAc,IAAI,GAAoB,EAAE,KAAqB,GAAK,CAAC;YACvE,KAAK,CAAC,kBAAkB,GAAG,MAAM,CAAC,MAAM,CAAE,GAAG,CAAS,iBAAiB;uBA7xBtE,YAAa,YA+xBK,KAAK,EAAE,SAAS,EAAE,SAAS;gBAC5C,kBAAkB,EAAC,KAAK,EAAE,CAAC;oBACzB,EAAE,EAAE,kBAAkB,CAAC,IAAI,EAAE,GAAG,GAAK,GAAG,KAAK,KAAK;uBAAG,CAAC;+BAC7C,kBAAkB,CAAC,KAAK;oBACjC,CAAC;2BACM,KAAK;gBACd,CAAC;;QAEL,CAAC;QAED,KAAK,CAAC,SAAS,QAAQ,WAAW,aAEzB,YAAY,CAAC,SAAS,CAAC,GAAG,EAAE,QAAQ,GAAK,CAAC;YAC7C,KAAK,CAAC,aAAa,GAAG,cAAc,CAAC,QAAQ,GAAE,QAAU;;gBAEvD,QAAQ,EAAE,aAAa,CAAC,QAAQ;gBAChC,IAAI,EAAE,aAAa,CAAC,IAAI;gBACxB,KAAK,EAAE,aAAa,CAAC,KAAK;gBAC1B,GAAG,EAAE,aAAa,CAAC,GAAG;gBACtB,UAAU,EAAE,aAAa,CAAC,UAAU;gBACpC,IAAI,GAAG,eAAe,EAAE,aAAa,CAAC,MAAM;gBAC5C,EAAE,SAAS,GAAG,EAAE,GAAG,EAAE,MAAM,EAAE,SAAS,GAAK,CAAC;oBAC1C,KAAK,GAAG,iBAAiB,UArvBhC,mBAAgD,UAsvBvC,aAAa,CAAC,WAAW,EACzB,MAAM,EACN,SAAS,CAAC,KAAK,EACf,KAAK;oBAGP,KAAK,GAAG,KAAK,MAAK,iBAAiB;2BAC3B,iBAAiB,CAAS,KAAK;oBAEvC,iBAAiB,CAAC,MAAM,GAAG,cAAc,CAAC,GAAG,EAAE,KAAK;oBAEpD,GAAG,CAAC,kBAAkB,OAh0BuC,IAAK,SAg0B/B,iBAAiB;oBAEpD,EAAE,EAAE,kBAAkB,CAAC,UAAU,EAAC,CAAG,IAAG,CAAC;wBACvC,kBAAkB,OA5xB3B,OAAqB,2BA6xBe,kBAAkB;oBAC/C,CAAC;oBAED,GAAG,CAAC,SAAS,EAAC,QAAU,GAAE,kBAAkB;oBAC5C,GAAG,CAAC,UAAU,OA7zBrB,iBAA2B,oBA6zBe,aAAa;oBAEhD,EAA0D,AAA1D,wDAA0D;oBAC1D,EAAqC,AAArC,mCAAqC;oBACrC,EAAE,EAAE,GAAG,CAAC,UAAU,KAAK,GAAG,EAAE,CAAC;wBAC3B,GAAG,CAAC,SAAS,EAAC,OAAS,IAAG,MAAM,EAAE,kBAAkB;oBACtD,CAAC;oBAED,GAAG,CAAC,GAAG,CAAC,kBAAkB;;wBAExB,QAAQ,EAAE,IAAI;;gBAElB,CAAC;;QAEL,CAAC;QAEL,KAAK,CAAC,YAAY,IAAI,OAAgB,EAAE,KAAK,GAAG,IAAI,GAAK,CAAC;YACxD,KAAK,CAAC,YAAY,GAAG,cAAc,CAAC,OAAO,GAAE,OAAS;;mBAEjD,YAAY;gBACf,KAAK;gBACL,IAAI,EAAE,YAAY,CAAC,IAAI;gBACvB,IAAI,GAAG,cAAc,EAAE,YAAY,CAAC,MAAM;gBAC1C,KAAK,EAAE,YAAY,CAAC,KAAK;gBACzB,EAAE,SAAS,GAAG,EAAE,GAAG,EAAE,MAAM,EAAE,SAAS,GAAK,CAAC;oBAC1C,KAAK,GAAG,MAAM,GAAE,iBAAiB,UAlyBpC,mBAAgD,UAmyB3C,YAAY,CAAC,WAAW,EACxB,MAAM,EACN,SAAS,CAAC,KAAK,EACf,IAAI;oBAGN,EAA6B,AAA7B,2BAA6B;oBAC7B,EAAE,EAAE,iBAAiB,CAAC,QAAQ,EAAE,CAAC;wBAC/B,KAAK,GAAG,KAAK,MAAK,iBAAiB;+BAC3B,iBAAiB,CAAS,KAAK;wBACvC,iBAAiB,CAAC,MAAM,GAAG,cAAc,CAAC,GAAG,EAAE,KAAK;wBAEpD,KAAK,CAAC,MAAM,OA92BmD,IAAK,SA82B3C,iBAAiB;wBAC1C,KAAK,CAAC,KAAK,GAAG,GAAG,CAt3BX,UAA+B;4BAu3BnC,MAAM;4BACN,YAAY,EAAE,IAAI;4BAClB,UAAU,EAAE,IAAI;4BAChB,IAAI,EAAE,IAAI;4BACV,YAAY,EAAE,KAAM;;8BAGhB,GAAG,CAAC,OAAO,EAAE,YAAY,EAAE,WAAW,GAAK,CAAC;4BAChD,GAAG,CAAC,QAAQ,GAAG,KAAK;4BAEpB,KAAK,CAAC,EAAE,EAAC,QAAU,IAAG,QAAQ,GAAK,CAAC;gCAClC,QAAQ,CAAC,EAAE,EAAC,KAAO,OAAQ,CAAC;oCAC1B,EAAE,GAAG,QAAQ,EAAE,CAAC;wCACd,QAAQ,GAAG,IAAI;wCACf,YAAY,CAAC,IAAI;oCACnB,CAAC;gCACH,CAAC;4BACH,CAAC;4BACD,KAAK,CAAC,EAAE,EAAC,KAAO,IAAG,GAAG,GAAK,CAAC;gCAC1B,EAAE,GAAG,QAAQ,EAAE,CAAC;oCACd,QAAQ,GAAG,IAAI;oCACf,WAAW,CAAC,GAAG;gCACjB,CAAC;4BACH,CAAC;4BACD,KAAK,CAAC,GAAG,CAAC,GAAG,EAAE,GAAG;wBACpB,CAAC;;4BAGC,QAAQ,EAAE,IAAI;;oBAElB,CAAC;oBACC,GAAG,CAAS,eAAe,GAAG,MAAM;oBACpC,GAAG,CAAS,eAAe,GAC1B,GAAG,CAAS,eAAe,KAAK,GAAG,CAAC,GAAG;;wBAGxC,QAAQ,EAAE,KAAK;wBACf,QAAQ,EAAE,MAAM;wBAChB,KAAK,EAAE,iBAAiB,CAAC,KAAK;;gBAElC,CAAC;;QAEL,CAAC;QAED,GAAG,CAAC,WAAW;QACf,GAAG,CAAC,UAAU;QACd,GAAG,CAAC,QAAQ;QAEZ,EAAE,QAAQ,WAAW,EAAE,CAAC;YACtB,EAAE,EAAE,KAAK,CAAC,OAAO,MAAM,YAAY,CAAC,QAAQ,GAAG,CAAC;gBAC9C,UAAU,QAAQ,YAAY,CAAC,QAAQ,CAAC,GAAG,EAAE,CAAC,GAAK,YAAY,CAAC,CAAC;;YACnE,CAAC,MAAM,CAAC;gBACN,WAAW,QAAQ,YAAY,CAAC,QAAQ,CAAC,WAAW,CAAC,GAAG,EAAE,CAAC,GACzD,YAAY,CAAC,CAAC,EAAE,KAAK;;gBAEvB,UAAU,QAAQ,YAAY,CAAC,QAAQ,CAAC,UAAU,CAAC,GAAG,EAAE,CAAC,GACvD,YAAY,CAAC,CAAC;;gBAEhB,QAAQ,QAAQ,YAAY,CAAC,QAAQ,CAAC,QAAQ,CAAC,GAAG,EAAE,CAAC,GACnD,YAAY,CAAC,CAAC;;YAElB,CAAC;QACH,CAAC;QAED,KAAK,CAAC,aAAa;YACjB,KAAK,MAr3BJ,OAAU,SAq3BE,OAAS;YACtB,IAAI,GAAE,KAAO;YACb,IAAI,GAAE,eAAiB;YACvB,EAAE,SAAS,GAAG,EAAE,GAAG,EAAE,OAAO,EAAE,SAAS,GAAK,CAAC;gBAC3C,GAAG,GAAG,QAAQ,GAAE,KAAK,MAAK,SAAS;gBACnC,EAAE,GAAG,QAAQ,EAAE,CAAC;oBACd,KAAK,CAAC,GAAG,CAAC,KAAK,EAAC,qBAAuB;gBACzC,CAAC;gBAED,EAAwD,AAAxD,sDAAwD;gBACxD,QAAQ,OAn3BwB,uBAAoC,0BAm3BjC,QAAQ;gBAE3C,EAAE,OAAO,UAAU,CAAC,IAAI,EAAE,CAAC;wBAGvB,KAAoB;oBAFtB,KAAK,CAAC,gBAAgB,OAl3BI,oBAA0C,sBAm3BlE,QAAQ,GACR,KAAoB,QAAf,UAAU,CAAC,IAAI,cAApB,KAAoB,UAApB,CAA6B,QAA7B,CAA6B,GAA7B,KAAoB,CAAE,OAAO;oBAG/B,EAAE,EAAE,gBAAgB,CAAC,cAAc,EAAE,CAAC;wBACpC,QAAQ,GAAG,gBAAgB,CAAC,QAAQ;wBACpC,SAAS,CAAC,KAAK,CAAC,YAAY,GAAG,gBAAgB,CAAC,cAAc;oBAChE,CAAC;gBACH,CAAC;gBACD,KAAK,CAAC,gBAAgB,KAAK,KAAK,CAAC,qBAAqB;gBAEtD,EAAE,EAAE,QAAQ,MAAK,IAAM,KAAI,QAAQ,CAAC,UAAU,EAAC,KAAO,IAAG,CAAC;2BACjD,KAAK,CAAC,qBAAqB;oBAElC,KAAK,CAAC,OAAO,cAAc,gBAAgB,CACzC,GAAG,EACH,GAAG,EACH,QAAQ,EACR,KAAK;oBAEP,EAAE,EAAE,OAAO,EAAE,CAAC;;4BACH,QAAQ,EAAE,IAAI;;oBACzB,CAAC;gBACH,CAAC;oBAEG,CAAC;+BACQ,MAAM,CAAC,GAAG,EAAE,GAAG,EAAE,QAAQ,EAAE,KAAK,EAAE,SAAS;;wBAGpD,QAAQ,EAAE,IAAI;;gBAElB,CAAC,QAAQ,GAAG,EAAE,CAAC;oBACb,EAAE,EAAE,GAAG,YAAY,eAAe,IAAI,gBAAgB,EAAE,CAAC;;4BAErD,QAAQ,EAAE,KAAK;;oBAEnB,CAAC;oBACD,KAAK,CAAC,GAAG;gBACX,CAAC;YACH,CAAC;;QAGH,KAAK,GAAG,yBAAyB,WAAU,UAAU;QAErD,EAAE,EAAE,yBAAyB,EAAE,CAAC;iBACzB,aAAa,QAAQ,gBAAgB;QAC5C,CAAC;;YAGC,OAAO;YACP,QAAQ;YACR,QAAQ;gBACN,WAAW;gBACX,UAAU;gBACV,QAAQ;;YAEV,SAAS;YACT,aAAa;YACb,yBAAyB;YACzB,aAAa,OAAO,aAAa;YACjC,QAAQ,OAAO,UAAU,CAAC,QAAQ;YAClC,WAAW,OAAO,OAAO,CAAC,IAAI;YAC9B,OAAO,IAAE,KAAoB,QAAf,UAAU,CAAC,IAAI,cAApB,KAAoB,UAApB,CAA6B,QAA7B,CAA6B,GAA7B,KAAoB,CAAE,OAAO;;IAE1C,CAAC;UAEa,WAAW,CACvB,QAAgB,EAChB,OAAkB,EACD,CAAC;mBA/8B2B,QAAW,cAi9BtD,QAAQ,OACH,OAAO,OACP,iBAAiB,OACjB,UAAU,CAAC,GAAG,EACnB,OAAO;IAEX,CAAC;UAEe,OAAO,CAAC,QAAgB,EAAoB,CAAC;QAC3D,GAAG,CAAC,KAAK,GAAG,KAAK;YACb,CAAC;gBAGD,KAAoB;YAFtB,KAAK,gBAAiB,WAAW,CAC/B,QAAQ,GACR,KAAoB,QAAf,UAAU,CAAC,IAAI,cAApB,KAAoB,UAApB,CAA6B,QAA7B,CAA6B,GAA7B,KAAoB,CAAE,OAAO;QAEjC,CAAC,QAAQ,CAAC,EAAE,CAAC;QAAA,CAAC;eAEP,KAAK;IACd,CAAC;UAEe,qBAAqB,CACnC,IAAqB,EACrB,IAAoB,EACpB,OAAe,EACf,UAA8B,EACZ,CAAC;eACZ,KAAK;IACd,CAAC;IAED,EAAwC,AAAxC,sCAAwC;UACxB,aAAa,CAAC,SAAiB,EAAiB,CAAC;IAAA,CAAC;IAElE,EAKG,AALH;;;;;GAKG,AALH,EAKG,OACW,gBAAgB,CAC5B,GAAoB,EACpB,GAAmB,EACnB,QAAgB,EAChB,KAAqB,EACH,CAAC;QACnB,GAAG,CAAC,IAAI,GAAG,QAAQ;QACnB,GAAG,CAAC,MAAM,GAAqB,KAAK;QACpC,GAAG,CAAC,SAAS,cAAc,OAAO,CAAC,IAAI;QAEvC,EAAE,GAAG,SAAS,SAAS,aAAa,EAAE,CAAC;iBAChC,KAAK,CAAC,YAAY,SAAS,aAAa,CAAE,CAAC;gBAC9C,MAAM,GAAG,YAAY,CAAC,KAAK,CAAC,QAAQ;gBACpC,EAAE,EAAE,YAAY,CAAC,IAAI,CAAC,UAAU,EAAC,IAAM,MAAK,MAAM,EAAE,CAAC;oBACnD,IAAI,GAAG,YAAY,CAAC,IAAI;oBACxB,SAAS,GAAG,IAAI;;gBAElB,CAAC;YACH,CAAC;QACH,CAAC;QAED,EAAE,GAAG,SAAS,EAAE,CAAC;mBACR,KAAK;QACd,CAAC;QACD,EAAsD,AAAtD,oDAAsD;QACtD,EAA0C,AAA1C,wCAA0C;mBAC/B,aAAa,CAAC,IAAI;QAE7B,GAAG,CAAC,aAAa;YACb,CAAC;YACH,aAAa,cAAc,WAAW,CAAC,IAAI;QAC7C,CAAC,QAAQ,GAAG,EAAE,CAAC;YACb,EAAE,EAAE,GAAG,CAAC,IAAI,MAAK,MAAQ,GAAE,CAAC;uBACnB,KAAK;YACd,CAAC;YACD,KAAK,CAAC,GAAG;QACX,CAAC;QAED,KAAK,CAAC,UAAU,SAAS,OAAO,CAAC,aAAa;QAC9C,KAAK;eAAQ,KAAK;eAAK,MAAM;;eAEtB,KAAK,CAAC,YAAY;eAClB,KAAK,CAAC,mBAAmB;QAEhC,EAAE,QAAQ,UAAU,CAAC,GAAG,SAAS,iBAAiB,EAAE,CAAC;YACnD,EAAE,SAAS,UAAU,CAAC,OAAO,MAAK,QAAU,GAAE,CAAC;gBAC7C,oBAAoB,CAAC,GAAG,EAAE,KAAK;sBACzB,UAAU,CAAC,OAAO,CAAC,GAAG,EAAE,GAAG;uBAC1B,IAAI;YACb,CAAC;QACH,CAAC;kBAhjCE,SAAa,cAmjCd,GAAG,EACH,GAAG,EACH,KAAK,EACL,UAAU,OACL,UAAU,CAAC,YAAY,OACvB,WAAW,OACX,UAAU,CAAC,GAAG,EACnB,IAAI;eAEC,IAAI;IACb,CAAC;IAES,oBAAoB,GAAY,CAAC;QACzC,KAAK,CAAC,WAAW,GAAG,GAAG,CAAC,GAAG,KA7jCM,qBAA8B,4BA8jCnC,SAAS,EAAE,GAAG,EAAE,CAAC,GACzC,SAAS,CAAC,CAAC,CAAC,OAAO,SAAQ,CAAG;;;;gBAM9B,KAAK,MA1jCN,OAAU,SA0jCI,OAAS;gBACtB,IAAI,GAAE,sBAAwB;gBAC9B,EAAE,SAAS,GAAG,EAAE,GAAG,EAAE,MAAM,EAAE,SAAS,GAAK,CAAC;oBAC1C,KAAK,CAAC,SAAS,GAAa,MAAM,CAAC,IAAI;oBACvC,KAAK,GAAG,QAAQ,WAAU,UAAU;oBAEpC,EAA+C,AAA/C,6CAA+C;oBAC/C,EAAE,EAAE,QAAQ,EAAE,CAAC;wBACb,KAAK,CAAC,aAAa,GAAG,QAAQ,CAAC,KAAK,EAAC,CAAG;wBACxC,EAA2B,AAA3B,yBAA2B;wBAC3B,aAAa,CAAC,KAAK;wBAEnB,EAAE,GACC,aAAa,CAAC,KAAK,EAAE,IAAY,EAAE,GAAW,GAAK,CAAC;mCAC5C,IAAI,KAAK,SAAS,CAAC,GAAG;wBAC/B,CAAC,GACD,CAAC;;gCACQ,QAAQ,EAAE,KAAK;;wBAC1B,CAAC;wBAED,SAAS,CAAC,MAAM,CAAC,CAAC,EAAE,aAAa,CAAC,MAAM;oBAC1C,CAAC;oBAED,GAAG,CAAC,IAAI,IAAI,CAAC,EAAE,SAAS,CAAC,IAAI,EAAC,CAAG;oBAEjC,EAAE,GAAG,WAAW,CAAC,GAAG,CAAC,IAAI,GAAG,CAAC;wBAC3B,EAAyD,AAAzD,uDAAyD;wBACzD,EAAqD,AAArD,mDAAqD;wBACrD,EAAwD,AAAxD,sDAAwD;wBACxD,IAAI,GAAG,SAAS,CAAC,IAAI;oBACvB,CAAC;oBAED,EAAE,EAAE,WAAW,CAAC,GAAG,CAAC,IAAI,GAAG,CAAC;mCACf,WAAW,CACpB,GAAG,EACH,GAAG,MA/pC4B,KAAM,YAgqC3B,SAAS,KAAK,SAAS,GACjC,SAAS;;4BAGT,QAAQ,EAAE,IAAI;;oBAElB,CAAC;;wBAEC,QAAQ,EAAE,KAAK;;gBAEnB,CAAC;;;IAGP,CAAC;IAES,gBAAgB,GAA4B,CAAC;QACrD,KAAK,CAAC,UAAU,GAAG,GAAG,CAAC,GAAG;mBA3oCvB,MAA4B,kBA8oC7B,MAAM,CAAC,IAAI,MAAM,aAAa,EAAG,GAAG,EACjC,IAAI;gBACuB,KAAoB;uBAnmCpB,oBAA0C,sBAmmChD,IAAI,GAAE,KAAoB,QAAf,UAAU,CAAC,IAAI,cAApB,KAAoB,UAApB,CAA6B,QAA7B,CAA6B,GAA7B,KAAoB,CAAE,OAAO,EAAE,QAAQ;YAGpE,GAAG,EAAE,IAAI,GAAK,CAAC;YACd,EAAE,EAAE,UAAU,CAAC,GAAG,CAAC,IAAI,UAppCxB,MAA4B,iBAopCiB,IAAI,UAAU,IAAI;YAC9D,UAAU,CAAC,GAAG,CAAC,IAAI;;gBAEjB,IAAI;gBACJ,KAAK,MAxpCR,MAA4B,sBAA5B,MAA4B,gBAwpCY,IAAI;;QAE7C,CAAC,EACA,MAAM,EAAE,IAAI,GAA+B,OAAO,CAAC,IAAI;;IAC5D,CAAC;IAEO,iBAAiB,CAAC,GAAoB,EAAE,GAAmB,EAAQ,CAAC;QAC1E,EAAE,OAAO,WAAW,EAAE,CAAC;iBAChB,WAAW,CAAC,GAAG,EAAE,GAAG,MAAQ,CAAC;YAAA,CAAC;QACrC,CAAC;IACH,CAAC;UAEe,GAAG,CACjB,GAAoB,EACpB,GAAmB,EACnB,SAA6B,EACd,CAAC;aACX,iBAAiB,CAAC,GAAG,EAAE,GAAG;YAE3B,CAAC;YACH,KAAK,CAAC,OAAO,cAAc,MAAM,CAAC,OAAO,CAAC,GAAG,EAAE,GAAG,EAAE,SAAS;YAC7D,EAAE,EAAE,OAAO,EAAE,CAAC;;YAEd,CAAC;QACH,CAAC,QAAQ,GAAG,EAAE,CAAC;YACb,EAAE,EAAE,GAAG,YAzqCN,OAAqB,cAyqCU,CAAC;gBAC/B,GAAG,CAAC,UAAU,GAAG,GAAG;4BACR,WAAW,CAAC,IAAI,EAAE,GAAG,EAAE,GAAG,GAAE,OAAS;;YACnD,CAAC;YACD,KAAK,CAAC,GAAG;QACX,CAAC;mBAEU,SAAS,CAAC,GAAG,EAAE,GAAG,EAAE,SAAS;IAC1C,CAAC;UAEa,IAAI,CAChB,EAA4D,EAC5D,cAKC,EACc,CAAC;QAChB,EAA+C,AAA/C,6CAA+C;QAC/C,KAAK,CAAC,iBAAiB,GAAG,IAAI;QAC9B,KAAK,CAAC,GAAG;eACJ,cAAc;YACjB,UAAU;wBACA,UAAU;gBAClB,iBAAiB;;;QAGrB,KAAK,CAAC,OAAO,SAAS,EAAE,CAAC,GAAG;QAC5B,EAAE,EAAE,OAAO,KAAK,IAAI,EAAE,CAAC;;QAEvB,CAAC;QACD,KAAK,GAAG,GAAG,GAAE,GAAG,MAAK,GAAG;QACxB,KAAK,GAAG,IAAI,GAAE,IAAI,GAAE,iBAAiB,MAAK,OAAO;QACjD,EAAE,OA3sCC,OAAqB,YA2sCT,GAAG,GAAG,CAAC;YACpB,KAAK,GAAG,aAAa,EAAb,cAAa,GAAE,eAAe,GAAE,GAAG,EAAH,IAAG,WAAU,UAAU;YAC/D,EAAE,EAAE,IAAG,EAAE,CAAC;gBACR,EAAoD,AAApD,kDAAoD;gBACpD,GAAG,CAAC,SAAS,EAAC,aAAe,IAAE,yBAA2B;YAC5D,CAAC;uBAvrCgD,YAAgB;gBAyrC/D,GAAG;gBACH,GAAG;gBACH,eAAe,EAAE,iBAAiB,aAvrCmB,OAAS;oBAwrCnC,IAAI;qBAC3B,IAAI;gBACR,IAAI;gBACJ,aAAa,EAAb,cAAa;gBACb,eAAe;gBACf,OAAO,EAAE,iBAAiB;;QAE9B,CAAC;IACH,CAAC;UAEa,aAAa,CACzB,EAA4D,EAC5D,cAKC,EACuB,CAAC;QACzB,KAAK,CAAC,OAAO,SAAS,EAAE;eACnB,cAAc;YACjB,UAAU;wBACA,UAAU;gBAClB,iBAAiB,EAAE,IAAI;;;QAG3B,EAAE,EAAE,OAAO,KAAK,IAAI,EAAE,CAAC;mBACd,IAAI;QACb,CAAC;mBAptCwD,OAAS;YAqtC1C,OAAO,CAAC,IAAI;;IACtC,CAAC;UAEY,MAAM,CACjB,GAAoB,EACpB,GAAmB,EACnB,QAAgB,EAChB,KAAqB;OACrB,SAA8B,EACf,CAAC;QAChB,EAAE,GAAG,QAAQ,CAAC,UAAU,EAAC,CAAG,IAAG,CAAC;YAC9B,OAAO,CAAC,IAAI,EACT,8BAA8B,EAAE,QAAQ,CAAC,kBAAkB,EAAE,QAAQ,CAAC,iFAAiF;QAE5J,CAAC;QAED,EAAE,OACK,UAAU,CAAC,YAAY,IAC5B,QAAQ,MAAK,MAAQ,iBACR,OAAO,EAAC,MAAQ,IAC7B,CAAC;YACD,EAAqD,AAArD,mDAAqD;YACrD,EAAwC,AAAxC,sCAAwC;YACxC,QAAQ,IAAG,CAAG;QAChB,CAAC;QAED,KAAK,CAAC,GAAG,GAAQ,GAAG,CAAC,GAAG;QAExB,EAAsD,AAAtD,oDAAsD;QACtD,EAA2D,AAA3D,yDAA2D;QAC3D,EAA2D,AAA3D,yDAA2D;QAC3D,EAAkE,AAAlE,gEAAkE;QAClE,EAAE,QACM,WAAW,KAChB,KAAK,CAAC,YAAY,KAClB,GAAG,CAAC,KAAK,uBACF,YAAY,IAAI,GAAG,CAAC,KAAK,kBACjC,CAAC;wBACW,aAAa,CAAC,GAAG,EAAE,GAAG,EAAE,SAAS;QAC/C,CAAC;QAED,EAAsE,AAAtE,oEAAsE;QACtE,EAAE,OAAO,UAAU,CAAC,YAAY,EAAE,CAAC;iBAC5B,iBAAiB,CAAC,GAAG,EAAE,GAAG;QACjC,CAAC;QAED,EAAE,MAnwCuD,OAAS,gBAmwChD,QAAQ,GAAG,CAAC;wBAChB,SAAS,CAAC,GAAG,EAAE,GAAG,EAAE,SAAS;QAC3C,CAAC;oBAEW,IAAI,EAAE,GAAG,QAAU,gBAAgB,CAAC,GAAG;;YACjD,GAAG;YACH,GAAG;YACH,QAAQ;YACR,KAAK;;IAET,CAAC;UAEe,kBAAkB,CAChC,QAAgB,EAChB,KAAqB;OACrB,MAAqB,GAAG,IAAI,EACU,CAAC;QACvC,GAAG,CAAC,KAAK;YACP,EAAyC,AAAzC,uCAAyC;YACzC,KAAK,CAAC,GAAG,OAvyCmB,kBAAuB,oBAuyCrB,QAAQ,KAAI,IAAM,IAAG,IAAI;YACvD,QAAQ;UACR,MAAM,CAAC,OAAO;QAEhB,EAAE,EAAE,KAAK,CAAC,YAAY,EAAE,CAAC;YACvB,KAAK;mBACA,KAAK,CAAC,GAAG,EACT,IAAI,IAAM,CAAC,EAAE,KAAK,CAAC,YAAY,GAAG,IAAI,MAAK,CAAG,SAAQ,IAAI;;mBAE1D,KAAK;;QAEZ,CAAC;aAEI,KAAK,CAAC,QAAQ,IAAI,KAAK,CAAE,CAAC;gBACzB,CAAC;gBACH,KAAK,CAAC,UAAU,aAvzCiC,eAAmB,sBAwzC7D,OAAO,EACZ,QAAQ,QACF,UAAU,CAAC,GAAG,SAAS,iBAAiB;gBAGhD,EAAE,EACA,KAAK,CAAC,YAAY,WACX,UAAU,CAAC,SAAS,MAAK,MAAQ,OACvC,QAAQ,aAAR,QAAQ,UAAR,CAAoB,QAApB,CAAoB,GAApB,QAAQ,CAAE,UAAU,EAAE,CAAC,EAAE,KAAK,CAAC,YAAY,MAC5C,CAAC;;gBAIH,CAAC;;oBAGC,UAAU;oBACV,KAAK;2BACC,UAAU,CAAC,cAAc;4BAEvB,GAAG,EAAE,KAAK,CAAC,GAAG;4BACd,YAAY,EAAE,KAAK,CAAC,YAAY;4BAChC,YAAY,EAAE,KAAK,CAAC,YAAY;4BAChC,mBAAmB,EAAE,KAAK,CAAC,mBAAmB;4BAEhD,KAAK;2BACL,MAAM;;;;YAGhB,CAAC,QAAQ,GAAG,EAAE,CAAC;gBACb,EAAE,EAAE,GAAG,CAAC,IAAI,MAAK,MAAQ,GAAE,KAAK,CAAC,GAAG;YACtC,CAAC;QACH,CAAC;eACM,IAAI;IACb,CAAC;UAEe,cAAc,CAAC,QAAgB,EAG5C,CAAC;QACF,EAAoE,AAApE,kEAAoE;QACpE,EAAuC,AAAvC,qCAAuC;QACvC,KAAK,CAAC,WAAW,GAAG,SAAS;QAE7B,EAA+D,AAA/D,6DAA+D;QAC/D,KAAK,CAAC,aAAa,QACZ,oBAAoB,GAAG,aAAa,CAAC,QAAQ,EAAE,QAAQ;;YAG5D,WAAW;YACX,YAAY,SACH,aAAa,MAAK,MAAQ,KAC7B,MAAQ,IACR,aAAa,KAAK,IAAI,IACtB,QAAU,IACV,KAAK;;IAEf,CAAC;UAEa,8BAA8B,GACxC,GAAG,GAAE,GAAG,GAAE,QAAQ,GAAE,UAAU,EAAE,IAAI,OACpC,UAAU,GAAE,KAAK,KACc,CAAC;YA0C9B,KAAoB,EA0BtB,KAAoB;QAnEtB,KAAK,CAAC,SAAS,GAAG,QAAQ,MAAK,IAAM;QACrC,KAAK,CAAC,SAAS,GAAG,QAAQ,MAAK,IAAM;QAErC,KAAK,CAAC,gBAAgB,UACb,UAAU,CAAC,SAAS,MAAK,MAAQ,YAChC,UAAU,CAAC,SAAS,CAAS,eAAe,MAAK,QAAU;QACrE,KAAK,CAAC,KAAK,KAAK,UAAU,CAAC,cAAc;QACzC,KAAK,CAAC,cAAc,KAAK,UAAU,CAAC,kBAAkB;QACtD,KAAK,CAAC,cAAc,KAAK,UAAU,CAAC,cAAc;QAClD,KAAK,CAAC,kBAAkB,KAAM,UAAU,CAAC,SAAS,CAAS,eAAe;QAE1E,EAA+C,AAA/C,6CAA+C;QAC/C,KAAK,CAAC,SAAS,KAAK,KAAK,CAAC,YAAY,KAAK,KAAK,IAAI,cAAc;eAC3D,KAAK,CAAC,YAAY;QAEzB,EAAgE,AAAhE,8DAAgE;QAChE,EAAE,EAAE,SAAS,KAAK,SAAS,EAAE,CAAC;YAC5B,GAAG,CAAC,UAAU,GAAG,GAAG;QACtB,CAAC;QAED,EAA2D,AAA3D,yDAA2D;QAC3D,EAAqB,AAArB,mBAAqB;QACrB,EAAE,EAt6CC,UAAyB,qBAs6CJ,QAAQ,CAAC,QAAQ,GAAG,CAAC;YAC3C,GAAG,CAAC,UAAU,GAAG,QAAQ,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE;QAClD,CAAC;QAED,EAAqB,AAArB,mBAAqB;QACrB,EAAE,SAAS,UAAU,CAAC,SAAS,MAAK,MAAQ,GAAE,CAAC;;gBAE3C,IAAI,GAAE,IAAM;gBACZ,EAAiD,AAAjD,+CAAiD;gBACjD,IAAI,EAv8CW,cAAmC,SAu8CjC,EAAE,CAAC,UAAU,CAAC,SAAS;;QAE5C,CAAC;QAED,EAAE,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC;mBACR,KAAK,CAAC,GAAG;QAClB,CAAC;QAED,KAAK,CAAC,MAAM,GAAG,KAAK,CAAC,YAAY;QACjC,KAAK,CAAC,aAAa,GAAG,KAAK,IACvB,KAAoB,QAAf,UAAU,CAAC,IAAI,cAApB,KAAoB,UAApB,CAAmC,QAAnC,CAAmC,GAAnC,KAAoB,CAAE,aAAa,GAClC,KAAK,CAAC,mBAAmB;QAE9B,KAAK,GAAG,IAAI,WAAU,UAAU;QAChC,KAAK,CAAC,OAAO,GAAG,IAAI,aAAJ,IAAI,UAAJ,CAAa,QAAb,CAAa,GAAb,IAAI,CAAE,OAAO;QAE7B,GAAG,CAAC,WAAW;QACf,GAAG,CAAC,aAAa,GAAG,KAAK;QAEzB,EAAE,EAAE,cAAc,IAAI,KAAK,EAAE,CAAC;YAC5B,WAAW,OA96CV,SAAa,oBA86CkB,GAAG,EAAE,GAAG,OAAO,UAAU,CAAC,YAAY;YACtE,aAAa,GAAG,WAAW,KAAK,KAAK;QACvC,CAAC;QAED,EAA0D,AAA1D,wDAA0D;QAC1D,EAA4D,AAA5D,0DAA4D;QAC5D,EAAwD,AAAxD,sDAAwD;QACxD,GAAG,CAAC,WAAW,OAn+CwD,IAAK,QAm+CjD,GAAG,CAAC,GAAG,QAAQ,QAAQ,KAAI,CAAG;QAEzD,GAAG,CAAC,mBAAmB,GAAI,GAAG,CAAS,eAAe,GACjD,GAAG,CAAS,eAAe,GAC5B,WAAW;QAEf,WAAW,OAj6CyB,uBAAoC,0BAi6ClC,WAAW;QACjD,mBAAmB,OA95Ca,oBAA0C,0BAJtC,uBAAoC,0BAm6C9C,mBAAmB,IAC3C,KAAoB,QAAf,UAAU,CAAC,IAAI,cAApB,KAAoB,UAApB,CAA6B,QAA7B,CAA6B,GAA7B,KAAoB,CAAE,OAAO,EAC7B,QAAQ;QAEV,KAAK,CAAC,iBAAiB,IAAI,IAAY,GAAK,CAAC;YAC3C,EAAE,EAAE,IAAI,CAAC,QAAQ,MAAM,OAAO,GAAG,CAAC;gBAChC,KAAK,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAC9B,IAAI,CAAC,OAAO,MAAM,OAAO,SAAS,OAAO,CAAC,MAAM;gBAGlD,IAAI,OA16CwB,oBAAyB,sBA06C1B,SAAS,CAAC,OAAO;YAC9C,CAAC;YAED,EAAE,OAAO,UAAU,CAAC,IAAI,EAAE,CAAC;2BA56CG,oBAA0C,sBA66C3C,IAAI,EAAE,OAAO,EAAE,QAAQ;YACpD,CAAC;mBACM,IAAI;QACb,CAAC;QAED,KAAK,CAAC,cAAc,IAAI,QAAa,GAAK,CAAC;YACzC,KAAK,CAAC,QAAQ;gBACZ,WAAW,EAAE,QAAQ,CAAC,SAAS,CAAC,YAAY;gBAC5C,UAAU,EAAE,QAAQ,CAAC,SAAS,CAAC,mBAAmB;gBAClD,QAAQ,EAAE,QAAQ,CAAC,SAAS,CAAC,sBAAsB;;YAErD,KAAK,CAAC,UAAU,OAz/Cf,iBAA2B,oBAy/CS,QAAQ;YAC7C,KAAK,GAAG,QAAQ,WAAU,UAAU;YAEpC,EAAE,EACA,QAAQ,IACR,QAAQ,CAAC,QAAQ,KAAK,KAAK,IAC3B,QAAQ,CAAC,WAAW,CAAC,UAAU,EAAC,CAAG,IACnC,CAAC;gBACD,QAAQ,CAAC,WAAW,MAAM,QAAQ,GAAG,QAAQ,CAAC,WAAW;YAC3D,CAAC;YAED,EAAE,EAAE,QAAQ,CAAC,WAAW,CAAC,UAAU,EAAC,CAAG,IAAG,CAAC;gBACzC,QAAQ,CAAC,WAAW,OAz+CrB,OAAqB,2BAy+C4B,QAAQ,CAAC,WAAW;YACtE,CAAC;YAED,EAAE,EAAE,UAAU,KA1/Cb,UAAyB,4BA0/CoB,CAAC;gBAC7C,GAAG,CAAC,SAAS,EAAC,OAAS,IAAG,MAAM,EAAE,QAAQ,CAAC,WAAW;YACxD,CAAC;YAED,GAAG,CAAC,UAAU,GAAG,UAAU;YAC3B,GAAG,CAAC,SAAS,EAAC,QAAU,GAAE,QAAQ,CAAC,WAAW;YAC9C,GAAG,CAAC,GAAG;QACT,CAAC;QAED,EAA2D,AAA3D,yDAA2D;QAC3D,EAA8C,AAA9C,4CAA8C;QAC9C,EAAE,EAAE,SAAS,EAAE,CAAC;YACd,mBAAmB,GAAG,iBAAiB,CAAC,mBAAmB;YAC3D,WAAW,GAAG,iBAAiB,CAAC,WAAW;QAC7C,CAAC;QAED,GAAG,CAAC,WAAW,GACb,aAAa,KAAK,KAAK,SAAS,WAAW,GACvC,IAAI,AAAC,CAAkC,AAAlC,EAAkC,AAAlC,gCAAkC;cACpC,MAAM,IAAI,CAAC,EAAE,MAAM,WACnB,QAAQ,MAAK,CAAG,KAAI,mBAAmB,MAAK,CAAG,MAAK,MAAM,QAEvD,mBAAmB,GACtB,KAAK,CAAC,GAAG,IAAG,IAAM;QAE3B,EAAE,GAAG,SAAS,IAAI,SAAS,KAAK,KAAK,EAAE,CAAC;YACtC,WAAW,MAAM,MAAM,IAAI,CAAC,EAAE,MAAM,UAAU,QAAQ,GACpD,KAAK,CAAC,GAAG,IAAG,IAAM;QAEtB,CAAC;QAED,EAAE,EAAE,WAAW,EAAE,CAAC;YAChB,EAAwD,AAAxD,sDAAwD;YACxD,EAAwD,AAAxD,sDAAwD;YACxD,EAAuD,AAAvD,qDAAuD;YACvD,EAAsE,AAAtE,oEAAsE;YAEtE,EAA8D,AAA9D,4DAA8D;YAC9D,EAAkC,AAAlC,gCAAkC;YAClC,WAAW,GAAG,WAAW,CACtB,KAAK,EAAC,CAAG,GACT,GAAG,EAAE,GAAG,GAAK,CAAC;oBACT,CAAC;oBACH,GAAG,OA/+CkB,qBAAmD,UA++C7C,kBAAkB,CAAC,GAAG,GAAG,IAAI;gBAC1D,CAAC,QAAQ,CAAC,EAAE,CAAC;oBACX,EAAyC,AAAzC,uCAAyC;oBACzC,KAAK,CAAC,GAAG,CA1hDd,OAAqB,cA0hDM,sBAAwB;gBAChD,CAAC;uBACM,GAAG;YACZ,CAAC,EACA,IAAI,EAAC,CAAG;QACb,CAAC;QAED,KAAK,CAAC,QAAQ,aAAyD,CAAC;YACtE,GAAG,CAAC,QAAQ;YACZ,GAAG,CAAC,IAAI;YACR,GAAG,CAAC,aAAa;YACjB,GAAG,CAAC,UAAU;YACd,GAAG,CAAC,UAAU;YAEd,EAAoB,AAApB,kBAAoB;YACpB,EAAE,EAAE,gBAAgB,EAAE,CAAC;gBACrB,KAAK,CAAC,YAAY,SAChB,UAAU,CAAC,SAAS,CACpB,eAAe,CAAC,GAAG,EAAE,GAAG,GAAE,WAAa;oBACvC,MAAM;oBACN,OAAO;oBACP,aAAa;oBACb,WAAW,OAAO,UAAU,CAAC,WAAW;oBACxC,OAAO,OAAO,OAAO;oBACrB,YAAY,OAAO,UAAU,CAAC,YAAY;oBAC1C,aAAa,OAAO,UAAU,CAAC,aAAa;;gBAG9C,IAAI,GAAG,YAAY,CAAC,IAAI;gBACxB,QAAQ,GAAG,YAAY,CAAC,UAAU,CAAC,QAAQ;gBAC3C,aAAa,GAAG,YAAY,CAAC,UAAU,CAAC,UAAU;gBAClD,UAAU,GAAG,YAAY,CAAC,UAAU,CAAC,UAAU;gBAC/C,UAAU,GAAG,YAAY,CAAC,UAAU,CAAC,UAAU;YACjD,CAAC,MAAM,CAAC;gBACN,KAAK,CAAC,SAAS,OAnmDoD,IAAK,QAmmD7C,GAAG,CAAC,GAAG,QAAQ,IAAI,EAAE,KAAK;gBACrD,KAAK,CAAC,gBAAgB,GACpB,WAAW,MAAK,CAAG,UAAS,UAAU,CAAC,aAAa;gBAEtD,KAAK,CAAC,WAAW,OAvmDkD,IAAK;oBAwmDtE,QAAQ,KAAK,mBAAmB,GAAG,gBAAgB,IAAG,CAAG;oBACzD,EAAuD,AAAvD,qDAAuD;oBACvD,KAAK,EAAE,SAAS;;gBAGlB,KAAK,CAAC,UAAU;uBACX,UAAU;uBACV,IAAI;oBACP,SAAS;oBACT,WAAW;oBACX,MAAM;oBACN,OAAO;oBACP,aAAa;oBACb,EAAuF,AAAvF,qFAAuF;oBACvF,EAA8D,AAA9D,4DAA8D;oBAC9D,EAAS,AAAT,OAAS;oBACT,cAAc,EACZ,cAAc,IAAI,kBAAkB,OAznD2B,IAAK;wBA2nD9D,EAAiE,AAAjE,+DAAiE;wBACjE,EAAU,AAAV,QAAU;wBACV,QAAQ,KAAK,WAAW,GAAG,gBAAgB,IAAG,CAAG;wBACjD,KAAK,EAAE,SAAS;yBAElB,WAAW;;gBAGnB,KAAK,CAAC,YAAY,aA/kDkC,OAAU,eAglD5D,GAAG,EACH,GAAG,EACH,QAAQ,EACR,KAAK,EACL,UAAU;gBAGZ,IAAI,GAAG,YAAY;gBACnB,EAAqD,AAArD,mDAAqD;gBACrD,QAAQ,GAAI,UAAU,CAAS,QAAQ;gBACvC,aAAa,GAAI,UAAU,CAAS,UAAU;gBAC9C,UAAU,GAAI,UAAU,CAAS,UAAU;gBAC3C,UAAU,GAAI,UAAU,CAAS,UAAU;YAC7C,CAAC;YAED,GAAG,CAAC,KAAK;YACT,EAAE,EAAE,UAAU,EAAE,CAAC;gBACf,KAAK,GAAG,IAAI;YACd,CAAC,MAAM,EAAE,EAAE,UAAU,EAAE,CAAC;gBACtB,KAAK;oBAAK,IAAI,GAAE,QAAU;oBAAE,KAAK,EAAE,QAAQ;;YAC7C,CAAC,MAAM,CAAC;gBACN,EAAE,GAAG,IAAI,EAAE,CAAC;2BACH,IAAI;gBACb,CAAC;gBACD,KAAK;oBAAK,IAAI,GAAE,IAAM;oBAAE,IAAI,EAAE,IAAI;oBAAE,QAAQ;;YAC9C,CAAC;;gBACQ,UAAU,EAAE,aAAa;gBAAE,KAAK;;QAC3C,CAAC;QAED,KAAK,CAAC,UAAU,cAAc,aAAa,CAAC,GAAG,CAC7C,WAAW,SACJ,WAAW,GAAK,CAAC;YACtB,KAAK,CAAC,YAAY,SAAS,UAAU,CAAC,GAAG;YACzC,KAAK,CAAC,iBAAiB,OAtoDxB,MAA4B,iBAsoDc,QAAQ;YACjD,KAAK,CAAC,UAAU,GAAG,WAAW,QA/nD/B,OAAqB,YA+nDwB,GAAG;YAE/C,KAAK,GAAG,WAAW,GAAE,YAAY,MAAK,cAAc,cACrC,cAAc,CAAC,QAAQ;gBAChC,WAAW,EAAE,SAAS;gBAAE,YAAY,EAAE,KAAK;;YAEjD,EAAoE,AAApE,kEAAoE;YACpE,EAAkC,AAAlC,gCAAkC;YAClC,EAAE;YACF,EAAgC,AAAhC,8BAAgC;YAChC,EAAE;YACF,EAA0C,AAA1C,wCAA0C;YAC1C,EAAE;YACF,EAAwE,AAAxE,sEAAwE;YACxE,EAAE;YACF,EAAiE,AAAjE,+DAAiE;YACjE,EAAyB,AAAzB,uBAAyB;YACzB,EAAE;YACF,EAAiE,AAAjE,+DAAiE;YACjE,EAAqE,AAArE,mEAAqE;YACrE,EAAE;YACF,EAAE,OACK,WAAW,KAAK,IAAI,IACzB,YAAY,MAAK,QAAU,KAC3B,WAAW,KACV,UAAU,KACV,aAAa,IACd,iBAAiB,IACjB,EAA8D,AAA9D,4DAA8D;YAC9D,EAAmB,AAAnB,iBAAmB;aAClB,YAAY,KACV,WAAW,KACX,WAAW,CAAC,QAAQ,CACnB,EAA2D,AAA3D,yDAA2D;YAC3D,EAA+D,AAA/D,6DAA+D;YAC/D,KAAK,CAAC,GAAG,GAAG,WAAW,CAAC,OAAO,iBAAiB,WAAW,IAE/D,CAAC;gBACD,EAAE,EACA,EAA2D,AAA3D,yDAA2D;gBAC3D,EAAkB,AAAlB,gBAAkB;iBACjB,YAAY,IAAI,WAAW,KAC5B,EAA2D,AAA3D,yDAA2D;gBAC3D,YAAY,MAAK,MAAQ,GACzB,CAAC;oBACD,KAAK,CAAC,GAAG,CAAC,eAAe;gBAC3B,CAAC;gBAED,EAAE,GAAG,SAAS,EAAE,CAAC;oBACf,EAA0D,AAA1D,wDAA0D;oBAC1D,EAAE,EAAE,YAAY,EAAE,CAAC;wBACjB,KAAK,CAAC,IAAI,cAAc,gBAAgB,CAAC,WAAW,CAClD,MAAM,IAAI,CAAC,EAAE,MAAM,GAAG,QAAQ,KAAK,QAAQ;;4BAG3C,KAAK;gCACH,IAAI,GAAE,IAAM;gCACZ,IAAI,EA9tDC,cAAmC,SA8tDvB,EAAE,CAAC,IAAI;gCACxB,QAAQ;;;;oBAGd,CAAC,MAEI,CAAC;wBACJ,KAAK,CAAC,cAAc,IAAG,IAAM;wBAC7B,EAAE,EAAE,gBAAgB,EAAE,CAAC;4BACrB,oBAAoB,CAAC,GAAG,EAAE,KAAK;wBACjC,CAAC;wBACD,KAAK,CAAC,MAAM,SAAS,QAAQ;wBAC7B,EAAE,GAAG,MAAM,EAAE,CAAC;mCACL,IAAI;wBACb,CAAC;wBACD,EAA8B,AAA9B,4BAA8B;+BACvB,MAAM,CAAC,UAAU;+BACjB,MAAM;oBACf,CAAC;gBACH,CAAC;YACH,CAAC;YAED,KAAK,CAAC,MAAM,SAAS,QAAQ;YAC7B,EAAE,GAAG,MAAM,EAAE,CAAC;uBACL,IAAI;YACb,CAAC;;mBAEI,MAAM;gBACT,UAAU,EACR,MAAM,CAAC,UAAU,KAAK,SAAS,GAC3B,MAAM,CAAC,UAAU,GACjB,EAAiE,AAAjE,6DAAiE,AAAjE,EAAiE,CAAC,CAAC;;QAE7E,CAAC;QAGH,EAAE,GAAG,UAAU,EAAE,CAAC;YAChB,EAAE,EAAE,WAAW,EAAE,CAAC;gBAChB,EAAgE,AAAhE,8DAAgE;gBAChE,EAAoE,AAApE,kEAAoE;gBACpE,EAAkE,AAAlE,gEAAkE;gBAClE,EAAmE,AAAnE,iEAAmE;gBACnE,EAAyB,AAAzB,uBAAyB;gBACzB,KAAK,CAAC,GAAG,CAAC,KAAK,EAAC,iDAAmD;YACrE,CAAC;mBACM,IAAI;QACb,CAAC;QAED,KAAK,GAAG,UAAU,GAAE,KAAK,EAAE,UAAU,MAAK,UAAU;QACpD,KAAK,CAAC,iBAAiB,UACd,UAAU,MAAK,SAAW,YAC1B,UAAU,CAAC,GAAG,IAAK,cAAc,KAAK,SAAS;YAEhD,EAAyD,AAAzD,uDAAyD;YACzD,OAAO,EAAE,aAAa,IAAI,SAAS;YACnC,QAAQ,GAAG,KAAK;YAChB,UAAU;YAEZ,SAAS;QAEf,EAAE,GAAG,UAAU,EAAE,CAAC;YAChB,EAAE,EAAE,iBAAiB,EAAE,CAAC;oBA5tDyB,YAAgB,uBA6tD1C,GAAG,EAAE,iBAAiB;YAC7C,CAAC;YACD,EAAE,EAAE,SAAS,EAAE,CAAC;gBACd,GAAG,CAAC,UAAU,GAAG,GAAG;gBACpB,GAAG,CAAC,GAAG,EAAC,iBAAmB;uBACpB,IAAI;YACb,CAAC,MAAM,CAAC;2BACK,SAAS,CAAC,GAAG,EAAE,GAAG;oBAC3B,QAAQ;oBACR,KAAK;;uBAEA,IAAI;YACb,CAAC;QACH,CAAC,MAAM,EAAE,EAAE,UAAU,CAAC,IAAI,MAAK,QAAU,GAAE,CAAC;YAC1C,EAAE,EAAE,SAAS,EAAE,CAAC;;oBAEZ,IAAI,GAAE,IAAM;oBACZ,IAAI,EA7yDS,cAAmC,SA6yD/B,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,KAAK;oBACnD,iBAAiB;;YAErB,CAAC,MAAM,CAAC;sBACA,cAAc,CAAC,UAAU,CAAC,KAAK;uBAC9B,IAAI;YACb,CAAC;QACH,CAAC,MAAM,CAAC;;gBAEJ,IAAI,EAAE,SAAS,IAAG,IAAM,KAAG,IAAM;gBACjC,IAAI,EAAE,SAAS,GAvzDA,cAAmC,SAwzDnC,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,QAAQ,KAChD,UAAU,CAAC,IAAI;gBACnB,iBAAiB;;QAErB,CAAC;IACH,CAAC;UAEa,gBAAgB,CAC5B,GAAmB,EACc,CAAC;QAClC,KAAK,GAAG,GAAG,GAAE,KAAK,GAAE,QAAQ,MAAK,GAAG;QACpC,GAAG,CAAC,IAAI,GAAG,QAAQ;QACnB,KAAK,CAAC,gBAAgB,KAAK,KAAK,CAAC,qBAAqB;eAC/C,KAAK,CAAC,qBAAqB;YAE9B,CAAC;YACH,KAAK,CAAC,MAAM,cAAc,kBAAkB,CAAC,QAAQ,EAAE,KAAK;YAC5D,EAAE,EAAE,MAAM,EAAE,CAAC;oBACP,CAAC;sCACe,8BAA8B,CAAC,GAAG,EAAE,MAAM;gBAC9D,CAAC,QAAQ,GAAG,EAAE,CAAC;oBACb,KAAK,CAAC,iBAAiB,GAAG,GAAG,YAAY,eAAe;oBAExD,EAAE,GAAG,iBAAiB,IAAK,iBAAiB,IAAI,gBAAgB,EAAG,CAAC;wBAClE,KAAK,CAAC,GAAG;oBACX,CAAC;gBACH,CAAC;YACH,CAAC;YAED,EAAE,OAAO,aAAa,EAAE,CAAC;qBAClB,KAAK,CAAC,YAAY,SAAS,aAAa,CAAE,CAAC;oBAC9C,KAAK,CAAC,MAAM,GAAG,YAAY,CAAC,KAAK,CAAC,QAAQ;oBAC1C,EAAE,GAAG,MAAM,EAAE,CAAC;;oBAEd,CAAC;oBAED,KAAK,CAAC,kBAAkB,cAAc,kBAAkB,CACtD,YAAY,CAAC,IAAI,EACjB,KAAK,EACL,MAAM;oBAER,EAAE,EAAE,kBAAkB,EAAE,CAAC;4BACnB,CAAC;4BACH,IAAI,GAAG,YAAY,CAAC,IAAI;8CACN,8BAA8B;mCAEzC,GAAG;gCACN,QAAQ,EAAE,YAAY,CAAC,IAAI;gCAC3B,UAAU;uCACL,GAAG,CAAC,UAAU;oCACjB,MAAM;;+BAGV,kBAAkB;wBAEtB,CAAC,QAAQ,GAAG,EAAE,CAAC;4BACb,KAAK,CAAC,iBAAiB,GAAG,GAAG,YAAY,eAAe;4BAExD,EAAE,GACC,iBAAiB,IACjB,iBAAiB,IAAI,gBAAgB,EACtC,CAAC;gCACD,KAAK,CAAC,GAAG;4BACX,CAAC;wBACH,CAAC;oBACH,CAAC;gBACH,CAAC;YACH,CAAC;QACH,CAAC,QAAQ,GAAG,EAAE,CAAC;YACb,EAAE,EAAE,GAAG,YAAY,eAAe,IAAI,gBAAgB,EAAE,CAAC;gBACvD,KAAK,CAAC,GAAG;YACX,CAAC;YACD,EAAE,EAAE,GAAG,YA11DN,OAAqB,cA01DU,CAAC;gBAC/B,GAAG,CAAC,UAAU,GAAG,GAAG;kCACF,qBAAqB,CAAC,GAAG,EAAE,GAAG;YAClD,CAAC;YAED,GAAG,CAAC,UAAU,GAAG,GAAG;YACpB,KAAK,CAAC,cAAc,GAAG,GAAG,YAAY,iBAAiB;YACvD,KAAK,CAAC,QAAQ,cAAc,qBAAqB,CAC/C,GAAG,EACH,cAAc,GAAG,GAAG,CAAC,UAAU,GAAG,GAAG;YAGvC,EAAE,GAAG,cAAc,EAAE,CAAC;gBACpB,EAAE,OAAO,WAAW,SAAS,UAAU,CAAC,GAAG,EAAE,CAAC;oBAC5C,EAAE,EAAE,GAAG,EAAE,CAAC;wBACR,GAAG,CAAC,IAAI,GAAG,IAAI;oBACjB,CAAC;oBACD,KAAK,CAAC,GAAG;gBACX,CAAC;qBACI,QAAQ,CAAC,GAAG;YACnB,CAAC;mBACM,QAAQ;QACjB,CAAC;QACD,GAAG,CAAC,UAAU,GAAG,GAAG;oBACR,qBAAqB,CAAC,GAAG,EAAE,IAAI;IAC7C,CAAC;UAEY,YAAY,CACvB,GAAoB,EACpB,GAAmB,EACnB,QAAgB,EAChB,KAAqB;OACG,CAAC;oBACb,aAAa,EAAE,GAAG,QAAU,gBAAgB,CAAC,GAAG;;YAC1D,GAAG;YACH,GAAG;YACH,QAAQ;YACR,KAAK;;IAET,CAAC;UAEY,WAAW,CACtB,GAAiB,EACjB,GAAoB,EACpB,GAAmB,EACnB,QAAgB,EAChB,KAAqB;OACrB,UAAU,GAAG,IAAI,EACF,CAAC;QAChB,EAAE,EAAE,UAAU,EAAE,CAAC;YACf,GAAG,CAAC,SAAS,EACX,aAAe,IACf,8CAAgD;QAEpD,CAAC;oBAEW,IAAI,QACP,GAAG,GAAK,CAAC;YACd,KAAK,CAAC,QAAQ,cAAc,qBAAqB,CAAC,GAAG,EAAE,GAAG;YAC1D,EAAE,OAAO,WAAW,IAAI,GAAG,CAAC,UAAU,KAAK,GAAG,EAAE,CAAC;gBAC/C,KAAK,CAAC,GAAG;YACX,CAAC;mBACM,QAAQ;QACjB,CAAC;YACC,GAAG;YAAE,GAAG;YAAE,QAAQ;YAAE,KAAK;;IAE/B,CAAC;UAWa,qBAAqB,CACjC,GAAmB,EACnB,IAAkB,EACe,CAAC;QAClC,KAAK,GAAG,GAAG,GAAE,KAAK,MAAK,GAAG;QAC1B,GAAG,CAAC,GAAG,GAAG,IAAI;QACd,EAAE,OAAO,UAAU,CAAC,GAAG,KAAK,GAAG,IAAI,GAAG,CAAC,UAAU,KAAK,GAAG,EAAE,CAAC;YAC1D,GAAG,GAAG,GAAG,CAAC,KAAK,EACb,wDAA0D,KACxD,oDAAsD;QAE5D,CAAC;YACG,CAAC;YACH,GAAG,CAAC,MAAM,GAAgC,IAAI;YAE9C,KAAK,CAAC,KAAK,GAAG,GAAG,CAAC,UAAU,KAAK,GAAG;YACpC,GAAG,CAAC,YAAY,GAAG,KAAK;YAExB,EAAuD,AAAvD,qDAAuD;YACvD,EAAE,EAAE,KAAK,EAAE,CAAC;gBACV,MAAM,cAAc,kBAAkB,EAAC,IAAM,GAAE,KAAK;gBACpD,YAAY,GAAG,MAAM,KAAK,IAAI;YAChC,CAAC;YACD,GAAG,CAAC,UAAU,IAAI,CAAC,EAAE,GAAG,CAAC,UAAU;YAEnC,EAAE,GAAG,MAAM,IA98DV,UAAyB,qBA88DS,QAAQ,CAAC,UAAU,GAAG,CAAC;gBACxD,MAAM,cAAc,kBAAkB,CAAC,UAAU,EAAE,KAAK;YAC1D,CAAC;YAED,EAAE,GAAG,MAAM,EAAE,CAAC;gBACZ,MAAM,cAAc,kBAAkB,EAAC,OAAS,GAAE,KAAK;gBACvD,UAAU,IAAG,OAAS;YACxB,CAAC;YAED,EAAE,EACA,OAAO,CAAC,GAAG,CAAC,QAAQ,MAAK,UAAY,MACpC,YAAY,eACD,OAAO,EAAC,OAAS,kBAChB,OAAO,EAAC,IAAM,IAC3B,CAAC;qBACI,oBAAoB;YAC3B,CAAC;gBAEG,CAAC;kCACe,8BAA8B;uBAEzC,GAAG;oBACN,QAAQ,EAAE,UAAU;oBACpB,UAAU;2BACL,GAAG,CAAC,UAAU;wBACjB,GAAG;;mBAGP,MAAM;YAEV,CAAC,QAAQ,kBAAkB,EAAE,CAAC;gBAC5B,EAAE,EAAE,kBAAkB,YAAY,eAAe,EAAE,CAAC;oBAClD,KAAK,CAAC,GAAG,CAAC,KAAK,EAAC,sCAAwC;gBAC1D,CAAC;gBACD,KAAK,CAAC,kBAAkB;YAC1B,CAAC;QACH,CAAC,QAAQ,iBAAiB,EAAE,CAAC;YAC3B,KAAK,CAAC,cAAc,GAAG,iBAAiB,YAAY,iBAAiB;YACrE,EAAE,GAAG,cAAc,EAAE,CAAC;qBACf,QAAQ,CAAC,iBAAiB;YACjC,CAAC;YACD,GAAG,CAAC,UAAU,GAAG,GAAG;YACpB,KAAK,CAAC,kBAAkB,cAAc,0BAA0B;YAEhE,EAAE,EAAE,kBAAkB,EAAE,CAAC;4BACX,8BAA8B;uBAEnC,GAAG;oBACN,QAAQ,GAAE,OAAS;oBACnB,UAAU;2BACL,GAAG,CAAC,UAAU;wBACjB,EAAsD,AAAtD,oDAAsD;wBACtD,EAAsC,AAAtC,oCAAsC;wBACtC,GAAG,EAAE,cAAc,GACf,iBAAiB,CAAC,UAAU,GAC5B,iBAAiB;;;oBAIvB,KAAK;oBACL,UAAU,EAAE,kBAAkB;;YAGpC,CAAC;;gBAEC,IAAI,GAAE,IAAM;gBACZ,IAAI,EAxiEW,cAAmC,SAwiEjC,EAAE,EAAC,qBAAuB;;QAE/C,CAAC;IACH,CAAC;UAEY,iBAAiB,CAC5B,GAAiB,EACjB,GAAoB,EACpB,GAAmB,EACnB,QAAgB,EAChB,KAAqB;OACG,CAAC;oBACb,aAAa,EAAE,GAAG,QAAU,qBAAqB,CAAC,GAAG,EAAE,GAAG;;YACpE,GAAG;YACH,GAAG;YACH,QAAQ;YACR,KAAK;;IAET,CAAC;UAEe,0BAA0B,GAA6C,CAAC;QACtF,EAAiE,AAAjE,+DAAiE;eAC1D,IAAI;IACb,CAAC;UAEY,SAAS,CACpB,GAAoB,EACpB,GAAmB,EACnB,SAA8B,EAC9B,UAAU,GAAG,IAAI,EACF,CAAC;QAChB,KAAK,CAAC,GAAG,GAAQ,GAAG,CAAC,GAAG;QACxB,KAAK,GAAG,QAAQ,GAAE,KAAK,MAAK,SAAS,GAAG,SAAS,OAzkEsB,IAAK,QAykEf,GAAG,EAAE,IAAI;QACtE,KAAK,GAAG,IAAI,WAAU,UAAU;QAEhC,EAAE,EAAE,IAAI,EAAE,CAAC;YACT,KAAK,CAAC,YAAY,GAAG,KAAK,CAAC,YAAY,IAAI,IAAI,CAAC,aAAa;YAC7D,KAAK,CAAC,mBAAmB,GACvB,KAAK,CAAC,mBAAmB,IAAI,IAAI,CAAC,aAAa;QACnD,CAAC;QAED,GAAG,CAAC,UAAU,GAAG,GAAG;oBACR,WAAW,CAAC,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE,QAAQ,EAAG,KAAK,EAAE,UAAU;IACtE,CAAC;UAEY,WAAW,CACtB,GAAoB,EACpB,GAAmB,EACnB,IAAY,EACZ,SAA8B,EACf,CAAC;QAChB,EAAE,QAAQ,cAAc,CAAC,IAAI,GAAG,CAAC;wBACnB,SAAS,CAAC,GAAG,EAAE,GAAG,EAAE,SAAS;QAC3C,CAAC;QAED,EAAE,IAAI,GAAG,CAAC,MAAM,MAAK,GAAK,KAAI,GAAG,CAAC,MAAM,MAAK,IAAM,IAAG,CAAC;YACrD,GAAG,CAAC,UAAU,GAAG,GAAG;YACpB,GAAG,CAAC,SAAS,EAAC,KAAO;iBAAG,GAAK;iBAAE,IAAM;;wBACzB,WAAW,CAAC,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI;QAC9C,CAAC;YAEG,CAAC;sBAriEmB,YAAgB,cAsiEpB,GAAG,EAAE,GAAG,EAAE,IAAI;QAClC,CAAC,QAAQ,GAAG,EAAE,CAAC;YACb,EAAE,EAAE,GAAG,CAAC,IAAI,MAAK,MAAQ,KAAI,GAAG,CAAC,UAAU,KAAK,GAAG,EAAE,CAAC;qBAC/C,SAAS,CAAC,GAAG,EAAE,GAAG,EAAE,SAAS;YACpC,CAAC,MAAM,EAAE,EAAE,GAAG,CAAC,UAAU,KAAK,GAAG,EAAE,CAAC;gBAClC,GAAG,CAAC,UAAU,GAAG,GAAG;4BACR,WAAW,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI;YAC7C,CAAC,MAAM,CAAC;gBACN,KAAK,CAAC,GAAG;YACX,CAAC;QACH,CAAC;IACH,CAAC;IAGO,kBAAkB,GAAgB,CAAC;QACzC,EAAE,OAAO,uBAAuB,EAAE,CAAC;wBACrB,uBAAuB;QACrC,CAAC;QAED,KAAK,CAAC,mBAAmB,OAhoEgB,KAAM,YAgoET,GAAG,GAAE,MAAQ;QACnD,GAAG,CAAC,eAAe;QACnB,EAAE,OAAO,YAAY,IAtoEV,GAAI,SAsoEa,UAAU,CAAC,mBAAmB,GAAG,CAAC;YAC5D,eAAe,OA5kEgB,qBAA8B,uBA4kEtB,mBAAmB,EAAE,GAAG,EAAE,CAAC,OAnoE3B,KAAM,QAooEtC,CAAG,IAAE,MAAQ,GAAE,CAAC;;QAEzB,CAAC;QAED,GAAG,CAAC,eAAe;QACnB,EAAE,OAAO,SAAS,IA7oEP,GAAI,SA6oEU,UAAU,MAAM,SAAS,GAAG,CAAC;YACpD,eAAe,OAnlEgB,qBAA8B,4BAmlEjB,SAAS,EAAE,GAAG,EAAE,CAAC,OA1oEtB,KAAM,QA2oEtC,CAAG,IAAE,MAAQ,GAAE,CAAC;;QAEzB,CAAC;QAED,GAAG,CAAC,eAAe;QACnB,eAAe,SAAS,WAAW,OAzlEF,qBAA8B,2BAvDtB,KAAM,YAipEZ,OAAO,GAAE,MAAQ,IAAG,GAAG,EAAE,CAAC,OAjpEpB,KAAM,QAkpEpC,CAAG,OAlpE2B,KAAM,gBAkpEjB,GAAG,OAAO,OAAO,IAAG,MAAQ,GAAE,CAAC;;oBAIhD,uBAAuB,GAAG,GAAG,CAAC,GAAG;eACzC,eAAe;eACf,eAAe;eACf,eAAe;;IAEtB,CAAC;IAES,cAAc,CAAC,gBAAwB,EAAW,CAAC;QAC3D,EAA6D,AAA7D,2DAA6D;QAC7D,EAAyB,AAAzB,uBAAyB;QACzB,EAAgE,AAAhE,8DAAgE;QAChE,EAAqE,AAArE,mEAAqE;QACrE,EAAc,AAAd,YAAc;QACd,EAAkG,AAAlG,gGAAkG;QAElG,GAAG,CAAC,wBAAwB;YACxB,CAAC;YACH,EAAqD,AAArD,mDAAqD;YACrD,wBAAwB,GAAG,kBAAkB,CAAC,gBAAgB;QAChE,CAAC,QAAO,CAAC;mBACA,KAAK;QACd,CAAC;QAED,EAAmD,AAAnD,iDAAmD;QACnD,KAAK,CAAC,iBAAiB,OA9qEkB,KAAM,UA8qEb,wBAAwB;QAE1D,EAAmD,AAAnD,iDAAmD;QACnD,EAAE,EAAE,iBAAiB,CAAC,OAAO,EAAC,IAAI,QAAO,CAAC,EAAE,CAAC;mBACpC,KAAK;QACd,CAAC;QAED,EAA4D,AAA5D,0DAA4D;QAC5D,EAAoC,AAApC,kCAAoC;QACpC,EAAE,GACC,iBAAiB,CAAC,UAAU,KAxrEU,KAAM,YAwrEL,OAAO,GAAE,MAAQ,KAxrElB,KAAM,SAyrE3C,iBAAiB,CAAC,UAAU,KAzrES,KAAM,YAyrEJ,GAAG,GAAE,MAAQ,KAzrEf,KAAM,SA0rE3C,iBAAiB,CAAC,UAAU,KA1rES,KAAM,YA0rEJ,GAAG,GAAE,MAAQ,KA1rEf,KAAM,WA0rEuB,KAAK,EACzE,CAAC;mBACM,KAAK;QACd,CAAC;QAED,EAA0C,AAA1C,wCAA0C;QAC1C,KAAK,CAAC,cAAc,QAAQ,kBAAkB;QAC9C,KAAK,CAAC,QAAQ,OAjsE2B,KAAM,gBAisEhB,GAAG,EAAE,iBAAiB;eAC9C,cAAc,CAAC,GAAG,CAAC,QAAQ;IACpC,CAAC;IAES,WAAW,GAAW,CAAC;QAC/B,KAAK,CAAC,WAAW,OAtsEwB,KAAM,YAssEjB,OAAO,EAvqElC,UAAyB;YAwqExB,CAAC;mBA3sEM,GAAI,SA4sEH,YAAY,CAAC,WAAW,GAAE,IAAM,GAAE,IAAI;QAClD,CAAC,QAAQ,GAAG,EAAE,CAAC;YACb,EAAE,GA9sEO,GAAI,SA8sEL,UAAU,CAAC,WAAW,GAAG,CAAC;gBAChC,KAAK,CAAC,GAAG,CAAC,KAAK,EACZ,0CAA0C,OAAO,OAAO,CAAC,yJAAyJ;YAEvN,CAAC;YAED,KAAK,CAAC,GAAG;QACX,CAAC;IACH,CAAC;QAEa,iBAAiB,GAAY,CAAC;mBA/pEmB,OAAU,8BAgqEpC,UAAU,CAAC,MAAM;IACtD,CAAC;;kBA5kEkB,MAAM;SA+kElB,oBAAoB,CAC3B,GAAoB,EACpB,KAAqB,EACf,CAAC;IACP,KAAK,CAAC,MAAM,OAvtE6D,IAAK,QAutEtD,GAAG,CAAC,GAAG,EAAG,IAAI;IACtC,GAAG,CAAC,GAAG,OAxtEkE,IAAK;WAytEzE,MAAM;QACT,MAAM,EAAE,SAAS;QACjB,KAAK;eACA,MAAM,CAAC,KAAK;eACZ,KAAK;;;AAGd,CAAC;MAEK,eAAe,SAAS,KAAK;;MAItB,iBAAiB,SAAS,KAAK;gBAG9B,UAAiB,CAAE,CAAC;QAC9B,KAAK;aACA,UAAU,GAAG,UAAU;IAC9B,CAAC;;QANU,iBAAiB,GAAjB,iBAAiB"}