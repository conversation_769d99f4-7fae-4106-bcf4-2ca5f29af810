{"version": 3, "sources": ["../../../../../../build/webpack/config/blocks/css/plugins.ts"], "sourcesContent": ["import chalk from 'chalk'\nimport { findConfig } from '../../../../../lib/find-config'\nimport browserslist from 'browserslist'\n\ntype CssPluginCollection_Array = (string | [string, boolean | object])[]\n\ntype CssPluginCollection_Object = { [key: string]: object | boolean }\n\ntype CssPluginCollection =\n  | CssPluginCollection_Array\n  | CssPluginCollection_Object\n\ntype CssPluginShape = [string, object | boolean]\n\nconst genericErrorText = 'Malformed PostCSS Configuration'\n\nfunction getError_NullConfig(pluginName: string) {\n  return `${chalk.red.bold(\n    'Error'\n  )}: Your PostCSS configuration for '${pluginName}' cannot have ${chalk.bold(\n    'null'\n  )} configuration.\\nTo disable '${pluginName}', pass ${chalk.bold(\n    'false'\n  )}, otherwise, pass ${chalk.bold('true')} or a configuration object.`\n}\n\nfunction isIgnoredPlugin(pluginPath: string): boolean {\n  const ignoredRegex =\n    /(?:^|[\\\\/])(postcss-modules-values|postcss-modules-scope|postcss-modules-extract-imports|postcss-modules-local-by-default|postcss-modules)(?:[\\\\/]|$)/i\n  const match = ignoredRegex.exec(pluginPath)\n  if (match == null) {\n    return false\n  }\n\n  const plugin = match.pop()!\n  console.warn(\n    `${chalk.yellow.bold('Warning')}: Please remove the ${chalk.underline(\n      plugin\n    )} plugin from your PostCSS configuration. ` +\n      `This plugin is automatically configured by Next.js.\\n` +\n      'Read more: https://nextjs.org/docs/messages/postcss-ignored-plugin'\n  )\n  return true\n}\n\nconst createLazyPostCssPlugin = (\n  fn: () => import('postcss').AcceptedPlugin\n): import('postcss').AcceptedPlugin => {\n  let result: any = undefined\n  const plugin = (...args: any[]) => {\n    if (result === undefined) result = fn() as any\n    if (result.postcss === true) {\n      return result(...args)\n    } else if (result.postcss) {\n      return result.postcss\n    }\n    return result\n  }\n  plugin.postcss = true\n  return plugin\n}\n\nasync function loadPlugin(\n  dir: string,\n  pluginName: string,\n  options: boolean | object\n): Promise<import('postcss').AcceptedPlugin | false> {\n  if (options === false || isIgnoredPlugin(pluginName)) {\n    return false\n  }\n\n  if (options == null) {\n    console.error(getError_NullConfig(pluginName))\n    throw new Error(genericErrorText)\n  }\n\n  const pluginPath = require.resolve(pluginName, { paths: [dir] })\n  if (isIgnoredPlugin(pluginPath)) {\n    return false\n  } else if (options === true) {\n    return createLazyPostCssPlugin(() => require(pluginPath))\n  } else {\n    const keys = Object.keys(options)\n    if (keys.length === 0) {\n      return createLazyPostCssPlugin(() => require(pluginPath))\n    }\n    return createLazyPostCssPlugin(() => require(pluginPath)(options))\n  }\n}\n\nfunction getDefaultPlugins(\n  baseDirectory: string,\n  isProduction: boolean\n): CssPluginCollection {\n  let browsers: any\n  try {\n    browsers = browserslist.loadConfig({\n      path: baseDirectory,\n      env: isProduction ? 'production' : 'development',\n    })\n  } catch {}\n\n  return [\n    require.resolve('next/dist/compiled/postcss-flexbugs-fixes'),\n    [\n      require.resolve('next/dist/compiled/postcss-preset-env'),\n      {\n        browsers: browsers ?? ['defaults'],\n        autoprefixer: {\n          // Disable legacy flexbox support\n          flexbox: 'no-2009',\n        },\n        // Enable CSS features that have shipped to the\n        // web platform, i.e. in 2+ browsers unflagged.\n        stage: 3,\n        features: {\n          'custom-properties': false,\n        },\n      },\n    ],\n  ]\n}\n\nexport async function getPostCssPlugins(\n  dir: string,\n  isProduction: boolean,\n  defaults: boolean = false\n): Promise<import('postcss').AcceptedPlugin[]> {\n  let config = defaults\n    ? null\n    : await findConfig<{ plugins: CssPluginCollection }>(dir, 'postcss')\n\n  if (config == null) {\n    config = { plugins: getDefaultPlugins(dir, isProduction) }\n  }\n\n  if (typeof config === 'function') {\n    throw new Error(\n      `Your custom PostCSS configuration may not export a function. Please export a plain object instead.\\n` +\n        'Read more: https://nextjs.org/docs/messages/postcss-function'\n    )\n  }\n\n  // Warn user about configuration keys which are not respected\n  const invalidKey = Object.keys(config).find((key) => key !== 'plugins')\n  if (invalidKey) {\n    console.warn(\n      `${chalk.yellow.bold(\n        'Warning'\n      )}: Your PostCSS configuration defines a field which is not supported (\\`${invalidKey}\\`). ` +\n        `Please remove this configuration value.`\n    )\n  }\n\n  // Enforce the user provided plugins if the configuration file is present\n  let plugins = config.plugins\n  if (plugins == null || typeof plugins !== 'object') {\n    throw new Error(\n      `Your custom PostCSS configuration must export a \\`plugins\\` key.`\n    )\n  }\n\n  if (!Array.isArray(plugins)) {\n    // Capture variable so TypeScript is happy\n    const pc = plugins\n\n    plugins = Object.keys(plugins).reduce((acc, curr) => {\n      const p = pc[curr]\n      if (typeof p === 'undefined') {\n        console.error(getError_NullConfig(curr))\n        throw new Error(genericErrorText)\n      }\n\n      acc.push([curr, p])\n      return acc\n    }, [] as CssPluginCollection_Array)\n  }\n\n  const parsed: CssPluginShape[] = []\n  plugins.forEach((plugin) => {\n    if (plugin == null) {\n      console.warn(\n        `${chalk.yellow.bold('Warning')}: A ${chalk.bold(\n          'null'\n        )} PostCSS plugin was provided. This entry will be ignored.`\n      )\n    } else if (typeof plugin === 'string') {\n      parsed.push([plugin, true])\n    } else if (Array.isArray(plugin)) {\n      const pluginName = plugin[0]\n      const pluginConfig = plugin[1]\n      if (\n        typeof pluginName === 'string' &&\n        (typeof pluginConfig === 'boolean' || typeof pluginConfig === 'object')\n      ) {\n        parsed.push([pluginName, pluginConfig])\n      } else {\n        if (typeof pluginName !== 'string') {\n          console.error(\n            `${chalk.red.bold(\n              'Error'\n            )}: A PostCSS Plugin must be provided as a ${chalk.bold(\n              'string'\n            )}. Instead, we got: '${pluginName}'.\\n` +\n              'Read more: https://nextjs.org/docs/messages/postcss-shape'\n          )\n        } else {\n          console.error(\n            `${chalk.red.bold(\n              'Error'\n            )}: A PostCSS Plugin was passed as an array but did not provide its configuration ('${pluginName}').\\n` +\n              'Read more: https://nextjs.org/docs/messages/postcss-shape'\n          )\n        }\n        throw new Error(genericErrorText)\n      }\n    } else if (typeof plugin === 'function') {\n      console.error(\n        `${chalk.red.bold(\n          'Error'\n        )}: A PostCSS Plugin was passed as a function using require(), but it must be provided as a ${chalk.bold(\n          'string'\n        )}.\\nRead more: https://nextjs.org/docs/messages/postcss-shape`\n      )\n      throw new Error(genericErrorText)\n    } else {\n      console.error(\n        `${chalk.red.bold(\n          'Error'\n        )}: An unknown PostCSS plugin was provided (${plugin}).\\n` +\n          'Read more: https://nextjs.org/docs/messages/postcss-shape'\n      )\n      throw new Error(genericErrorText)\n    }\n  })\n\n  const resolved = await Promise.all(\n    parsed.map((p) => loadPlugin(dir, p[0], p[1]))\n  )\n  const filtered: import('postcss').AcceptedPlugin[] = resolved.filter(\n    Boolean\n  ) as import('postcss').AcceptedPlugin[]\n\n  return filtered\n}\n"], "names": [], "mappings": ";;;;QA2HsB,iBAAiB,GAAjB,iBAAiB;AA3HrB,GAAO,CAAP,MAAO;AACE,GAAgC,CAAhC,WAAgC;AAClC,GAAc,CAAd,aAAc;;;;;;AAYvC,KAAK,CAAC,gBAAgB,IAAG,+BAAiC;SAEjD,mBAAmB,CAAC,UAAkB,EAAE,CAAC;cAhBhC,MAAO,SAiBP,GAAG,CAAC,IAAI,EACtB,KAAO,GACP,kCAAkC,EAAE,UAAU,CAAC,cAAc,EAnB/C,MAAO,SAmBgD,IAAI,EACzE,IAAM,GACN,6BAA6B,EAAE,UAAU,CAAC,QAAQ,EArBpC,MAAO,SAqBqC,IAAI,EAC9D,KAAO,GACP,kBAAkB,EAvBJ,MAAO,SAuBK,IAAI,EAAC,IAAM,GAAE,2BAA2B;AACtE,CAAC;SAEQ,eAAe,CAAC,UAAkB,EAAW,CAAC;IACrD,KAAK,CAAC,YAAY;IAElB,KAAK,CAAC,KAAK,GAAG,YAAY,CAAC,IAAI,CAAC,UAAU;IAC1C,EAAE,EAAE,KAAK,IAAI,IAAI,EAAE,CAAC;eACX,KAAK;IACd,CAAC;IAED,KAAK,CAAC,MAAM,GAAG,KAAK,CAAC,GAAG;IACxB,OAAO,CAAC,IAAI,IAnCI,MAAO,SAoCZ,MAAM,CAAC,IAAI,EAAC,OAAS,GAAE,oBAAoB,EApCtC,MAAO,SAoCuC,SAAS,CACnE,MAAM,EACN,yCAAyC,KACxC,qDAAqD,KACtD,kEAAoE;WAEjE,IAAI;AACb,CAAC;AAED,KAAK,CAAC,uBAAuB,IAC3B,EAA0C,GACL,CAAC;IACtC,GAAG,CAAC,MAAM,GAAQ,SAAS;IAC3B,KAAK,CAAC,MAAM,OAAO,IAAI,GAAY,CAAC;QAClC,EAAE,EAAE,MAAM,KAAK,SAAS,EAAE,MAAM,GAAG,EAAE;QACrC,EAAE,EAAE,MAAM,CAAC,OAAO,KAAK,IAAI,EAAE,CAAC;mBACrB,MAAM,IAAI,IAAI;QACvB,CAAC,MAAM,EAAE,EAAE,MAAM,CAAC,OAAO,EAAE,CAAC;mBACnB,MAAM,CAAC,OAAO;QACvB,CAAC;eACM,MAAM;IACf,CAAC;IACD,MAAM,CAAC,OAAO,GAAG,IAAI;WACd,MAAM;AACf,CAAC;eAEc,UAAU,CACvB,GAAW,EACX,UAAkB,EAClB,OAAyB,EAC0B,CAAC;IACpD,EAAE,EAAE,OAAO,KAAK,KAAK,IAAI,eAAe,CAAC,UAAU,GAAG,CAAC;eAC9C,KAAK;IACd,CAAC;IAED,EAAE,EAAE,OAAO,IAAI,IAAI,EAAE,CAAC;QACpB,OAAO,CAAC,KAAK,CAAC,mBAAmB,CAAC,UAAU;QAC5C,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,gBAAgB;IAClC,CAAC;IAED,KAAK,CAAC,UAAU,GAAG,OAAO,CAAC,OAAO,CAAC,UAAU;QAAI,KAAK;YAAG,GAAG;;;IAC5D,EAAE,EAAE,eAAe,CAAC,UAAU,GAAG,CAAC;eACzB,KAAK;IACd,CAAC,MAAM,EAAE,EAAE,OAAO,KAAK,IAAI,EAAE,CAAC;eACrB,uBAAuB,KAAO,OAAO,CAAC,UAAU;;IACzD,CAAC,MAAM,CAAC;QACN,KAAK,CAAC,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,OAAO;QAChC,EAAE,EAAE,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;mBACf,uBAAuB,KAAO,OAAO,CAAC,UAAU;;QACzD,CAAC;eACM,uBAAuB,KAAO,OAAO,CAAC,UAAU,EAAE,OAAO;;IAClE,CAAC;AACH,CAAC;SAEQ,iBAAiB,CACxB,aAAqB,EACrB,YAAqB,EACA,CAAC;IACtB,GAAG,CAAC,QAAQ;QACR,CAAC;QACH,QAAQ,GA9Fa,aAAc,SA8FX,UAAU;YAChC,IAAI,EAAE,aAAa;YACnB,GAAG,EAAE,YAAY,IAAG,UAAY,KAAG,WAAa;;IAEpD,CAAC,QAAO,CAAC;IAAA,CAAC;;QAGR,OAAO,CAAC,OAAO,EAAC,yCAA2C;;YAEzD,OAAO,CAAC,OAAO,EAAC,qCAAuC;;gBAErD,QAAQ,EAAE,QAAQ,aAAR,QAAQ,cAAR,QAAQ;qBAAK,QAAU;;gBACjC,YAAY;oBACV,EAAiC,AAAjC,+BAAiC;oBACjC,OAAO,GAAE,OAAS;;gBAEpB,EAA+C,AAA/C,6CAA+C;gBAC/C,EAA+C,AAA/C,6CAA+C;gBAC/C,KAAK,EAAE,CAAC;gBACR,QAAQ;qBACN,iBAAmB,GAAE,KAAK;;;;;AAKpC,CAAC;eAEqB,iBAAiB,CACrC,GAAW,EACX,YAAqB,EACrB,QAAiB,GAAG,KAAK,EACoB,CAAC;IAC9C,GAAG,CAAC,MAAM,GAAG,QAAQ,GACjB,IAAI,aAhIiB,WAAgC,aAiIF,GAAG,GAAE,OAAS;IAErE,EAAE,EAAE,MAAM,IAAI,IAAI,EAAE,CAAC;QACnB,MAAM;YAAK,OAAO,EAAE,iBAAiB,CAAC,GAAG,EAAE,YAAY;;IACzD,CAAC;IAED,EAAE,SAAS,MAAM,MAAK,QAAU,GAAE,CAAC;QACjC,KAAK,CAAC,GAAG,CAAC,KAAK,EACZ,oGAAoG,KACnG,4DAA8D;IAEpE,CAAC;IAED,EAA6D,AAA7D,2DAA6D;IAC7D,KAAK,CAAC,UAAU,GAAG,MAAM,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,EAAE,GAAG,GAAK,GAAG,MAAK,OAAS;;IACtE,EAAE,EAAE,UAAU,EAAE,CAAC;QACf,OAAO,CAAC,IAAI,IAlJE,MAAO,SAmJV,MAAM,CAAC,IAAI,EAClB,OAAS,GACT,uEAAuE,EAAE,UAAU,CAAC,KAAK,KACxF,uCAAuC;IAE9C,CAAC;IAED,EAAyE,AAAzE,uEAAyE;IACzE,GAAG,CAAC,OAAO,GAAG,MAAM,CAAC,OAAO;IAC5B,EAAE,EAAE,OAAO,IAAI,IAAI,WAAW,OAAO,MAAK,MAAQ,GAAE,CAAC;QACnD,KAAK,CAAC,GAAG,CAAC,KAAK,EACZ,gEAAgE;IAErE,CAAC;IAED,EAAE,GAAG,KAAK,CAAC,OAAO,CAAC,OAAO,GAAG,CAAC;QAC5B,EAA0C,AAA1C,wCAA0C;QAC1C,KAAK,CAAC,EAAE,GAAG,OAAO;QAElB,OAAO,GAAG,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,MAAM,EAAE,GAAG,EAAE,IAAI,GAAK,CAAC;YACpD,KAAK,CAAC,CAAC,GAAG,EAAE,CAAC,IAAI;YACjB,EAAE,SAAS,CAAC,MAAK,SAAW,GAAE,CAAC;gBAC7B,OAAO,CAAC,KAAK,CAAC,mBAAmB,CAAC,IAAI;gBACtC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,gBAAgB;YAClC,CAAC;YAED,GAAG,CAAC,IAAI;gBAAE,IAAI;gBAAE,CAAC;;mBACV,GAAG;QACZ,CAAC;IACH,CAAC;IAED,KAAK,CAAC,MAAM;IACZ,OAAO,CAAC,OAAO,EAAE,MAAM,GAAK,CAAC;QAC3B,EAAE,EAAE,MAAM,IAAI,IAAI,EAAE,CAAC;YACnB,OAAO,CAAC,IAAI,IArLA,MAAO,SAsLR,MAAM,CAAC,IAAI,EAAC,OAAS,GAAE,IAAI,EAtL1B,MAAO,SAsL2B,IAAI,EAC9C,IAAM,GACN,yDAAyD;QAE/D,CAAC,MAAM,EAAE,SAAS,MAAM,MAAK,MAAQ,GAAE,CAAC;YACtC,MAAM,CAAC,IAAI;gBAAE,MAAM;gBAAE,IAAI;;QAC3B,CAAC,MAAM,EAAE,EAAE,KAAK,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC;YACjC,KAAK,CAAC,UAAU,GAAG,MAAM,CAAC,CAAC;YAC3B,KAAK,CAAC,YAAY,GAAG,MAAM,CAAC,CAAC;YAC7B,EAAE,SACO,UAAU,MAAK,MAAQ,aACtB,YAAY,MAAK,OAAS,YAAW,YAAY,MAAK,MAAQ,IACtE,CAAC;gBACD,MAAM,CAAC,IAAI;oBAAE,UAAU;oBAAE,YAAY;;YACvC,CAAC,MAAM,CAAC;gBACN,EAAE,SAAS,UAAU,MAAK,MAAQ,GAAE,CAAC;oBACnC,OAAO,CAAC,KAAK,IAtML,MAAO,SAuMJ,GAAG,CAAC,IAAI,EACf,KAAO,GACP,yCAAyC,EAzMrC,MAAO,SAyMsC,IAAI,EACrD,MAAQ,GACR,oBAAoB,EAAE,UAAU,CAAC,IAAI,KACrC,yDAA2D;gBAEjE,CAAC,MAAM,CAAC;oBACN,OAAO,CAAC,KAAK,IA/ML,MAAO,SAgNJ,GAAG,CAAC,IAAI,EACf,KAAO,GACP,kFAAkF,EAAE,UAAU,CAAC,KAAK,KACpG,yDAA2D;gBAEjE,CAAC;gBACD,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,gBAAgB;YAClC,CAAC;QACH,CAAC,MAAM,EAAE,SAAS,MAAM,MAAK,QAAU,GAAE,CAAC;YACxC,OAAO,CAAC,KAAK,IAzND,MAAO,SA0NR,GAAG,CAAC,IAAI,EACf,KAAO,GACP,0FAA0F,EA5NlF,MAAO,SA4NmF,IAAI,EACtG,MAAQ,GACR,4DAA4D;YAEhE,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,gBAAgB;QAClC,CAAC,MAAM,CAAC;YACN,OAAO,CAAC,KAAK,IAlOD,MAAO,SAmOR,GAAG,CAAC,IAAI,EACf,KAAO,GACP,0CAA0C,EAAE,MAAM,CAAC,IAAI,KACvD,yDAA2D;YAE/D,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,gBAAgB;QAClC,CAAC;IACH,CAAC;IAED,KAAK,CAAC,QAAQ,SAAS,OAAO,CAAC,GAAG,CAChC,MAAM,CAAC,GAAG,EAAE,CAAC,GAAK,UAAU,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;;IAE7C,KAAK,CAAC,QAAQ,GAAuC,QAAQ,CAAC,MAAM,CAClE,OAAO;WAGF,QAAQ;AACjB,CAAC"}