{"version": 3, "sources": ["../../../../../server/lib/squoosh/png/squoosh_png.js"], "sourcesContent": ["import { TextDecoder } from '../text-decoder'\n\nlet wasm\n\nlet cachedTextDecoder = new TextDecoder('utf-8', {\n  ignoreBOM: true,\n  fatal: true,\n})\n\ncachedTextDecoder.decode()\n\nlet cachegetUint8Memory0 = null\nfunction getUint8Memory0() {\n  if (\n    cachegetUint8Memory0 === null ||\n    cachegetUint8Memory0.buffer !== wasm.memory.buffer\n  ) {\n    cachegetUint8Memory0 = new Uint8Array(wasm.memory.buffer)\n  }\n  return cachegetUint8Memory0\n}\n\nfunction getStringFromWasm0(ptr, len) {\n  return cachedTextDecoder.decode(getUint8Memory0().subarray(ptr, ptr + len))\n}\n\nlet cachegetUint8ClampedMemory0 = null\nfunction getUint8ClampedMemory0() {\n  if (\n    cachegetUint8ClampedMemory0 === null ||\n    cachegetUint8ClampedMemory0.buffer !== wasm.memory.buffer\n  ) {\n    cachegetUint8ClampedMemory0 = new Uint8ClampedArray(wasm.memory.buffer)\n  }\n  return cachegetUint8ClampedMemory0\n}\n\nfunction getClampedArrayU8FromWasm0(ptr, len) {\n  return getUint8ClampedMemory0().subarray(ptr / 1, ptr / 1 + len)\n}\n\nconst heap = new Array(32).fill(undefined)\n\nheap.push(undefined, null, true, false)\n\nlet heap_next = heap.length\n\nfunction addHeapObject(obj) {\n  if (heap_next === heap.length) heap.push(heap.length + 1)\n  const idx = heap_next\n  heap_next = heap[idx]\n\n  heap[idx] = obj\n  return idx\n}\n\nlet WASM_VECTOR_LEN = 0\n\nfunction passArray8ToWasm0(arg, malloc) {\n  const ptr = malloc(arg.length * 1)\n  getUint8Memory0().set(arg, ptr / 1)\n  WASM_VECTOR_LEN = arg.length\n  return ptr\n}\n\nlet cachegetInt32Memory0 = null\nfunction getInt32Memory0() {\n  if (\n    cachegetInt32Memory0 === null ||\n    cachegetInt32Memory0.buffer !== wasm.memory.buffer\n  ) {\n    cachegetInt32Memory0 = new Int32Array(wasm.memory.buffer)\n  }\n  return cachegetInt32Memory0\n}\n\nfunction getArrayU8FromWasm0(ptr, len) {\n  return getUint8Memory0().subarray(ptr / 1, ptr / 1 + len)\n}\n/**\n * @param {Uint8Array} data\n * @param {number} width\n * @param {number} height\n * @returns {Uint8Array}\n */\nexport function encode(data, width, height) {\n  try {\n    const retptr = wasm.__wbindgen_export_1.value - 16\n    wasm.__wbindgen_export_1.value = retptr\n    var ptr0 = passArray8ToWasm0(data, wasm.__wbindgen_malloc)\n    var len0 = WASM_VECTOR_LEN\n    wasm.encode(retptr, ptr0, len0, width, height)\n    var r0 = getInt32Memory0()[retptr / 4 + 0]\n    var r1 = getInt32Memory0()[retptr / 4 + 1]\n    var v1 = getArrayU8FromWasm0(r0, r1).slice()\n    wasm.__wbindgen_free(r0, r1 * 1)\n    return v1\n  } finally {\n    wasm.__wbindgen_export_1.value += 16\n  }\n}\n\nfunction getObject(idx) {\n  return heap[idx]\n}\n\nfunction dropObject(idx) {\n  if (idx < 36) return\n  heap[idx] = heap_next\n  heap_next = idx\n}\n\nfunction takeObject(idx) {\n  const ret = getObject(idx)\n  dropObject(idx)\n  return ret\n}\n/**\n * @param {Uint8Array} data\n * @returns {ImageData}\n */\nexport function decode(data) {\n  var ptr0 = passArray8ToWasm0(data, wasm.__wbindgen_malloc)\n  var len0 = WASM_VECTOR_LEN\n  var ret = wasm.decode(ptr0, len0)\n  return takeObject(ret)\n}\n\nasync function load(module, imports) {\n  if (typeof Response === 'function' && module instanceof Response) {\n    if (typeof WebAssembly.instantiateStreaming === 'function') {\n      try {\n        return await WebAssembly.instantiateStreaming(module, imports)\n      } catch (e) {\n        if (module.headers.get('Content-Type') !== 'application/wasm') {\n          console.warn(\n            '`WebAssembly.instantiateStreaming` failed because your server does not serve wasm with `application/wasm` MIME type. Falling back to `WebAssembly.instantiate` which is slower. Original error:\\n',\n            e\n          )\n        } else {\n          throw e\n        }\n      }\n    }\n\n    const bytes = await module.arrayBuffer()\n    return await WebAssembly.instantiate(bytes, imports)\n  } else {\n    const instance = await WebAssembly.instantiate(module, imports)\n\n    if (instance instanceof WebAssembly.Instance) {\n      return { instance, module }\n    } else {\n      return instance\n    }\n  }\n}\n\nasync function init(input) {\n  if (typeof input === 'undefined') {\n    // input = import.meta.url.replace(/\\.js$/, '_bg.wasm')\n    throw new Error('invariant')\n  }\n  const imports = {}\n  imports.wbg = {}\n  imports.wbg.__wbg_newwithownedu8clampedarrayandsh_787b2db8ea6bfd62 =\n    function (arg0, arg1, arg2, arg3) {\n      var v0 = getClampedArrayU8FromWasm0(arg0, arg1).slice()\n      wasm.__wbindgen_free(arg0, arg1 * 1)\n      var ret = new ImageData(v0, arg2 >>> 0, arg3 >>> 0)\n      return addHeapObject(ret)\n    }\n  imports.wbg.__wbindgen_throw = function (arg0, arg1) {\n    throw new Error(getStringFromWasm0(arg0, arg1))\n  }\n  if (\n    typeof input === 'string' ||\n    (typeof Request === 'function' && input instanceof Request) ||\n    (typeof URL === 'function' && input instanceof URL)\n  ) {\n    input = fetch(input)\n  }\n\n  const { instance, module } = await load(await input, imports)\n\n  wasm = instance.exports\n  init.__wbindgen_wasm_module = module\n\n  return wasm\n}\n\nexport default init\n\n// Manually remove the wasm and memory references to trigger GC\nexport function cleanup() {\n  wasm = null\n  cachegetUint8ClampedMemory0 = null\n  cachegetUint8Memory0 = null\n  cachegetInt32Memory0 = null\n}\n"], "names": [], "mappings": ";;;;QAqFgB,MAAM,GAAN,MAAM;QAoCN,MAAM,GAAN,MAAM;QAyEN,OAAO,GAAP,OAAO;;AAlMK,GAAiB,CAAjB,YAAiB;AAE7C,GAAG,CAAC,IAAI;AAER,GAAG,CAAC,iBAAiB,GAAG,GAAG,CAJC,YAAiB,cAIL,KAAO;IAC7C,SAAS,EAAE,IAAI;IACf,KAAK,EAAE,IAAI;;AAGb,iBAAiB,CAAC,MAAM;AAExB,GAAG,CAAC,oBAAoB,GAAG,IAAI;SACtB,eAAe,GAAG,CAAC;IAC1B,EAAE,EACA,oBAAoB,KAAK,IAAI,IAC7B,oBAAoB,CAAC,MAAM,KAAK,IAAI,CAAC,MAAM,CAAC,MAAM,EAClD,CAAC;QACD,oBAAoB,GAAG,GAAG,CAAC,UAAU,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM;IAC1D,CAAC;WACM,oBAAoB;AAC7B,CAAC;SAEQ,kBAAkB,CAAC,GAAG,EAAE,GAAG,EAAE,CAAC;WAC9B,iBAAiB,CAAC,MAAM,CAAC,eAAe,GAAG,QAAQ,CAAC,GAAG,EAAE,GAAG,GAAG,GAAG;AAC3E,CAAC;AAED,GAAG,CAAC,2BAA2B,GAAG,IAAI;SAC7B,sBAAsB,GAAG,CAAC;IACjC,EAAE,EACA,2BAA2B,KAAK,IAAI,IACpC,2BAA2B,CAAC,MAAM,KAAK,IAAI,CAAC,MAAM,CAAC,MAAM,EACzD,CAAC;QACD,2BAA2B,GAAG,GAAG,CAAC,iBAAiB,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM;IACxE,CAAC;WACM,2BAA2B;AACpC,CAAC;SAEQ,0BAA0B,CAAC,GAAG,EAAE,GAAG,EAAE,CAAC;WACtC,sBAAsB,GAAG,QAAQ,CAAC,GAAG,GAAG,CAAC,EAAE,GAAG,GAAG,CAAC,GAAG,GAAG;AACjE,CAAC;AAED,KAAK,CAAC,IAAI,GAAG,GAAG,CAAC,KAAK,CAAC,EAAE,EAAE,IAAI,CAAC,SAAS;AAEzC,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK;AAEtC,GAAG,CAAC,SAAS,GAAG,IAAI,CAAC,MAAM;SAElB,aAAa,CAAC,GAAG,EAAE,CAAC;IAC3B,EAAE,EAAE,SAAS,KAAK,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC;IACxD,KAAK,CAAC,GAAG,GAAG,SAAS;IACrB,SAAS,GAAG,IAAI,CAAC,GAAG;IAEpB,IAAI,CAAC,GAAG,IAAI,GAAG;WACR,GAAG;AACZ,CAAC;AAED,GAAG,CAAC,eAAe,GAAG,CAAC;SAEd,iBAAiB,CAAC,GAAG,EAAE,MAAM,EAAE,CAAC;IACvC,KAAK,CAAC,GAAG,GAAG,MAAM,CAAC,GAAG,CAAC,MAAM,GAAG,CAAC;IACjC,eAAe,GAAG,GAAG,CAAC,GAAG,EAAE,GAAG,GAAG,CAAC;IAClC,eAAe,GAAG,GAAG,CAAC,MAAM;WACrB,GAAG;AACZ,CAAC;AAED,GAAG,CAAC,oBAAoB,GAAG,IAAI;SACtB,eAAe,GAAG,CAAC;IAC1B,EAAE,EACA,oBAAoB,KAAK,IAAI,IAC7B,oBAAoB,CAAC,MAAM,KAAK,IAAI,CAAC,MAAM,CAAC,MAAM,EAClD,CAAC;QACD,oBAAoB,GAAG,GAAG,CAAC,UAAU,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM;IAC1D,CAAC;WACM,oBAAoB;AAC7B,CAAC;SAEQ,mBAAmB,CAAC,GAAG,EAAE,GAAG,EAAE,CAAC;WAC/B,eAAe,GAAG,QAAQ,CAAC,GAAG,GAAG,CAAC,EAAE,GAAG,GAAG,CAAC,GAAG,GAAG;AAC1D,CAAC;SAOe,MAAM,CAAC,IAAI,EAAE,KAAK,EAAE,MAAM,EAAE,CAAC;QACvC,CAAC;QACH,KAAK,CAAC,MAAM,GAAG,IAAI,CAAC,mBAAmB,CAAC,KAAK,GAAG,EAAE;QAClD,IAAI,CAAC,mBAAmB,CAAC,KAAK,GAAG,MAAM;QACvC,GAAG,CAAC,IAAI,GAAG,iBAAiB,CAAC,IAAI,EAAE,IAAI,CAAC,iBAAiB;QACzD,GAAG,CAAC,IAAI,GAAG,eAAe;QAC1B,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,MAAM;QAC7C,GAAG,CAAC,EAAE,GAAG,eAAe,GAAG,MAAM,GAAG,CAAC,GAAG,CAAC;QACzC,GAAG,CAAC,EAAE,GAAG,eAAe,GAAG,MAAM,GAAG,CAAC,GAAG,CAAC;QACzC,GAAG,CAAC,EAAE,GAAG,mBAAmB,CAAC,EAAE,EAAE,EAAE,EAAE,KAAK;QAC1C,IAAI,CAAC,eAAe,CAAC,EAAE,EAAE,EAAE,GAAG,CAAC;eACxB,EAAE;IACX,CAAC,QAAS,CAAC;QACT,IAAI,CAAC,mBAAmB,CAAC,KAAK,IAAI,EAAE;IACtC,CAAC;AACH,CAAC;SAEQ,SAAS,CAAC,GAAG,EAAE,CAAC;WAChB,IAAI,CAAC,GAAG;AACjB,CAAC;SAEQ,UAAU,CAAC,GAAG,EAAE,CAAC;IACxB,EAAE,EAAE,GAAG,GAAG,EAAE;IACZ,IAAI,CAAC,GAAG,IAAI,SAAS;IACrB,SAAS,GAAG,GAAG;AACjB,CAAC;SAEQ,UAAU,CAAC,GAAG,EAAE,CAAC;IACxB,KAAK,CAAC,GAAG,GAAG,SAAS,CAAC,GAAG;IACzB,UAAU,CAAC,GAAG;WACP,GAAG;AACZ,CAAC;SAKe,MAAM,CAAC,IAAI,EAAE,CAAC;IAC5B,GAAG,CAAC,IAAI,GAAG,iBAAiB,CAAC,IAAI,EAAE,IAAI,CAAC,iBAAiB;IACzD,GAAG,CAAC,IAAI,GAAG,eAAe;IAC1B,GAAG,CAAC,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,IAAI;WACzB,UAAU,CAAC,GAAG;AACvB,CAAC;eAEc,IAAI,CAAC,MAAM,EAAE,OAAO,EAAE,CAAC;IACpC,EAAE,SAAS,QAAQ,MAAK,QAAU,KAAI,MAAM,YAAY,QAAQ,EAAE,CAAC;QACjE,EAAE,SAAS,WAAW,CAAC,oBAAoB,MAAK,QAAU,GAAE,CAAC;gBACvD,CAAC;6BACU,WAAW,CAAC,oBAAoB,CAAC,MAAM,EAAE,OAAO;YAC/D,CAAC,QAAQ,CAAC,EAAE,CAAC;gBACX,EAAE,EAAE,MAAM,CAAC,OAAO,CAAC,GAAG,EAAC,YAAc,QAAM,gBAAkB,GAAE,CAAC;oBAC9D,OAAO,CAAC,IAAI,EACV,iMAAmM,GACnM,CAAC;gBAEL,CAAC,MAAM,CAAC;oBACN,KAAK,CAAC,CAAC;gBACT,CAAC;YACH,CAAC;QACH,CAAC;QAED,KAAK,CAAC,KAAK,SAAS,MAAM,CAAC,WAAW;qBACzB,WAAW,CAAC,WAAW,CAAC,KAAK,EAAE,OAAO;IACrD,CAAC,MAAM,CAAC;QACN,KAAK,CAAC,QAAQ,SAAS,WAAW,CAAC,WAAW,CAAC,MAAM,EAAE,OAAO;QAE9D,EAAE,EAAE,QAAQ,YAAY,WAAW,CAAC,QAAQ,EAAE,CAAC;;gBACpC,QAAQ;gBAAE,MAAM;;QAC3B,CAAC,MAAM,CAAC;mBACC,QAAQ;QACjB,CAAC;IACH,CAAC;AACH,CAAC;eAEc,IAAI,CAAC,KAAK,EAAE,CAAC;IAC1B,EAAE,SAAS,KAAK,MAAK,SAAW,GAAE,CAAC;QACjC,EAAuD,AAAvD,qDAAuD;QACvD,KAAK,CAAC,GAAG,CAAC,KAAK,EAAC,SAAW;IAC7B,CAAC;IACD,KAAK,CAAC,OAAO;;IACb,OAAO,CAAC,GAAG;;IACX,OAAO,CAAC,GAAG,CAAC,sDAAsD,YACtD,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC;QACjC,GAAG,CAAC,EAAE,GAAG,0BAA0B,CAAC,IAAI,EAAE,IAAI,EAAE,KAAK;QACrD,IAAI,CAAC,eAAe,CAAC,IAAI,EAAE,IAAI,GAAG,CAAC;QACnC,GAAG,CAAC,GAAG,GAAG,GAAG,CAAC,SAAS,CAAC,EAAE,EAAE,IAAI,KAAK,CAAC,EAAE,IAAI,KAAK,CAAC;eAC3C,aAAa,CAAC,GAAG;IAC1B,CAAC;IACH,OAAO,CAAC,GAAG,CAAC,gBAAgB,YAAa,IAAI,EAAE,IAAI,EAAE,CAAC;QACpD,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,kBAAkB,CAAC,IAAI,EAAE,IAAI;IAC/C,CAAC;IACD,EAAE,SACO,KAAK,MAAK,MAAQ,YACjB,OAAO,MAAK,QAAU,KAAI,KAAK,YAAY,OAAO,WAClD,GAAG,MAAK,QAAU,KAAI,KAAK,YAAY,GAAG,EAClD,CAAC;QACD,KAAK,GAAG,KAAK,CAAC,KAAK;IACrB,CAAC;IAED,KAAK,GAAG,QAAQ,GAAE,MAAM,YAAW,IAAI,OAAO,KAAK,EAAE,OAAO;IAE5D,IAAI,GAAG,QAAQ,CAAC,OAAO;IACvB,IAAI,CAAC,sBAAsB,GAAG,MAAM;WAE7B,IAAI;AACb,CAAC;eAEc,IAAI;;SAGH,OAAO,GAAG,CAAC;IACzB,IAAI,GAAG,IAAI;IACX,2BAA2B,GAAG,IAAI;IAClC,oBAAoB,GAAG,IAAI;IAC3B,oBAAoB,GAAG,IAAI;AAC7B,CAAC"}