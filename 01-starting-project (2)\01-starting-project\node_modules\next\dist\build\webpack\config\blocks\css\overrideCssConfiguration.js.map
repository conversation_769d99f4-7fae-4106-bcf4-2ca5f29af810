{"version": 3, "sources": ["../../../../../../build/webpack/config/blocks/css/overrideCssConfiguration.ts"], "sourcesContent": ["import { webpack } from 'next/dist/compiled/webpack/webpack'\nimport { getPostCssPlugins } from './plugins'\n\nexport async function __overrideCssConfiguration(\n  rootDirectory: string,\n  isProduction: boolean,\n  config: webpack.Configuration\n) {\n  const postCssPlugins = await getPostCssPlugins(rootDirectory, isProduction)\n\n  function patch(rule: webpack.RuleSetRule) {\n    if (\n      rule.options &&\n      typeof rule.options === 'object' &&\n      typeof rule.options.postcssOptions === 'object'\n    ) {\n      rule.options.postcssOptions.plugins = postCssPlugins\n    } else if (Array.isArray(rule.oneOf)) {\n      rule.oneOf.forEach(patch)\n    } else if (Array.isArray(rule.use)) {\n      rule.use.forEach((u) => {\n        if (typeof u === 'object') {\n          patch(u)\n        }\n      })\n    }\n  }\n\n  config.module?.rules?.forEach((entry) => {\n    patch(entry)\n  })\n}\n"], "names": [], "mappings": ";;;;QAGsB,0BAA0B,GAA1B,0BAA0B;AAFd,GAAW,CAAX,QAAW;eAEvB,0BAA0B,CAC9C,aAAqB,EACrB,YAAqB,EACrB,MAA6B,EAC7B,CAAC;QAqBD,GAAa;IApBb,KAAK,CAAC,cAAc,aAPY,QAAW,oBAOI,aAAa,EAAE,YAAY;aAEjE,KAAK,CAAC,IAAyB,EAAE,CAAC;QACzC,EAAE,EACA,IAAI,CAAC,OAAO,WACL,IAAI,CAAC,OAAO,MAAK,MAAQ,YACzB,IAAI,CAAC,OAAO,CAAC,cAAc,MAAK,MAAQ,GAC/C,CAAC;YACD,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC,OAAO,GAAG,cAAc;QACtD,CAAC,MAAM,EAAE,EAAE,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,GAAG,CAAC;YACrC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK;QAC1B,CAAC,MAAM,EAAE,EAAE,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC;YACnC,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,CAAC,GAAK,CAAC;gBACvB,EAAE,SAAS,CAAC,MAAK,MAAQ,GAAE,CAAC;oBAC1B,KAAK,CAAC,CAAC;gBACT,CAAC;YACH,CAAC;QACH,CAAC;IACH,CAAC;KAED,GAAa,GAAb,MAAM,CAAC,MAAM,cAAb,GAAa,UAAb,CAAoB,QAApB,CAAoB,WAApB,GAAa,CAAE,KAAK,4BAApB,CAAoB,QAApB,CAAoB,QAAE,OAAO,EAAE,KAAK,GAAK,CAAC;QACxC,KAAK,CAAC,KAAK;IACb,CAAC;AACH,CAAC"}