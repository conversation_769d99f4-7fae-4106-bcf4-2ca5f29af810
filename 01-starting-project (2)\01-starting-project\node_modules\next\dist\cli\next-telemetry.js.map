{"version": 3, "sources": ["../../cli/next-telemetry.ts"], "sourcesContent": ["#!/usr/bin/env node\nimport chalk from 'chalk'\nimport arg from 'next/dist/compiled/arg/index.js'\nimport { printAndExit } from '../server/lib/utils'\nimport { cliCommand } from '../bin/next'\nimport { Telemetry } from '../telemetry/storage'\n\nconst nextTelemetry: cliCommand = (argv) => {\n  const validArgs: arg.Spec = {\n    // Types\n    '--help': Boolean,\n    '--enable': <PERSON>olean,\n    '--disable': <PERSON><PERSON><PERSON>,\n    // Aliases\n    '-h': '--help',\n  }\n  let args: arg.Result<arg.Spec>\n  try {\n    args = arg(validArgs, { argv })\n  } catch (error) {\n    if (error.code === 'ARG_UNKNOWN_OPTION') {\n      return printAndExit(error.message, 1)\n    }\n    throw error\n  }\n\n  if (args['--help']) {\n    console.log(\n      `\n      Description\n        Allows you to control Next.js' telemetry collection\n\n      Usage\n        $ next telemetry [enable/disable]\n\n      You may pass the 'enable' or 'disable' argument to turn Next.js' telemetry collection on or off.\n\n      Learn more: ${chalk.cyan('https://nextjs.org/telemetry')}\n    `\n    )\n    return\n  }\n\n  const telemetry = new Telemetry({ distDir: process.cwd() })\n\n  let isEnabled = telemetry.isEnabled\n\n  if (args['--enable'] || args._[0] === 'enable') {\n    telemetry.setEnabled(true)\n    console.log(chalk.cyan('Success!'))\n    console.log()\n\n    isEnabled = true\n  } else if (args['--disable'] || args._[0] === 'disable') {\n    telemetry.setEnabled(false)\n    if (isEnabled) {\n      console.log(chalk.cyan('Your preference has been saved.'))\n    } else {\n      console.log(\n        chalk.yellow(`Next.js' telemetry collection is already disabled.`)\n      )\n    }\n    console.log()\n\n    isEnabled = false\n  } else {\n    console.log(chalk.bold('Next.js Telemetry'))\n    console.log()\n  }\n\n  console.log(\n    `Status: ${\n      isEnabled ? chalk.bold.green('Enabled') : chalk.bold.red('Disabled')\n    }`\n  )\n  console.log()\n\n  if (isEnabled) {\n    console.log(\n      `Next.js telemetry is completely anonymous. Thank you for participating!`\n    )\n  } else {\n    console.log(`You have opted-out of Next.js' anonymous telemetry program.`)\n    console.log(`No data will be collected from your machine.`)\n  }\n\n  console.log(`Learn more: https://nextjs.org/telemetry`)\n  console.log()\n}\n\nexport { nextTelemetry }\n"], "names": [], "mappings": ";;;;;;AACkB,GAAO,CAAP,MAAO;AACT,GAAiC,CAAjC,QAAiC;AACpB,GAAqB,CAArB,MAAqB;AAExB,GAAsB,CAAtB,QAAsB;;;;;;AAEhD,KAAK,CAAC,aAAa,IAAgB,IAAI,GAAK,CAAC;IAC3C,KAAK,CAAC,SAAS;QACb,EAAQ,AAAR,MAAQ;SACR,MAAQ,GAAE,OAAO;SACjB,QAAU,GAAE,OAAO;SACnB,SAAW,GAAE,OAAO;QACpB,EAAU,AAAV,QAAU;SACV,EAAI,IAAE,MAAQ;;IAEhB,GAAG,CAAC,IAAI;QACJ,CAAC;QACH,IAAI,OAhBQ,QAAiC,UAgBlC,SAAS;YAAI,IAAI;;IAC9B,CAAC,QAAQ,KAAK,EAAE,CAAC;QACf,EAAE,EAAE,KAAK,CAAC,IAAI,MAAK,kBAAoB,GAAE,CAAC;uBAjBjB,MAAqB,eAkBxB,KAAK,CAAC,OAAO,EAAE,CAAC;QACtC,CAAC;QACD,KAAK,CAAC,KAAK;IACb,CAAC;IAED,EAAE,EAAE,IAAI,EAAC,MAAQ,IAAG,CAAC;QACnB,OAAO,CAAC,GAAG,EACR,iQASW,EApCA,MAAO,SAoCC,IAAI,EAAC,4BAA8B,GAAE,KAC3D;;IAGF,CAAC;IAED,KAAK,CAAC,SAAS,GAAG,GAAG,CAtCG,QAAsB;QAsCZ,OAAO,EAAE,OAAO,CAAC,GAAG;;IAEtD,GAAG,CAAC,SAAS,GAAG,SAAS,CAAC,SAAS;IAEnC,EAAE,EAAE,IAAI,EAAC,QAAU,MAAK,IAAI,CAAC,CAAC,CAAC,CAAC,OAAM,MAAQ,GAAE,CAAC;QAC/C,SAAS,CAAC,UAAU,CAAC,IAAI;QACzB,OAAO,CAAC,GAAG,CAhDG,MAAO,SAgDH,IAAI,EAAC,QAAU;QACjC,OAAO,CAAC,GAAG;QAEX,SAAS,GAAG,IAAI;IAClB,CAAC,MAAM,EAAE,EAAE,IAAI,EAAC,SAAW,MAAK,IAAI,CAAC,CAAC,CAAC,CAAC,OAAM,OAAS,GAAE,CAAC;QACxD,SAAS,CAAC,UAAU,CAAC,KAAK;QAC1B,EAAE,EAAE,SAAS,EAAE,CAAC;YACd,OAAO,CAAC,GAAG,CAvDC,MAAO,SAuDD,IAAI,EAAC,+BAAiC;QAC1D,CAAC,MAAM,CAAC;YACN,OAAO,CAAC,GAAG,CAzDC,MAAO,SA0DX,MAAM,EAAE,kDAAkD;QAEpE,CAAC;QACD,OAAO,CAAC,GAAG;QAEX,SAAS,GAAG,KAAK;IACnB,CAAC,MAAM,CAAC;QACN,OAAO,CAAC,GAAG,CAjEG,MAAO,SAiEH,IAAI,EAAC,iBAAmB;QAC1C,OAAO,CAAC,GAAG;IACb,CAAC;IAED,OAAO,CAAC,GAAG,EACR,QAAQ,EACP,SAAS,GAvEG,MAAO,SAuED,IAAI,CAAC,KAAK,EAAC,OAAS,KAvE1B,MAAO,SAuE6B,IAAI,CAAC,GAAG,EAAC,QAAU;IAGvE,OAAO,CAAC,GAAG;IAEX,EAAE,EAAE,SAAS,EAAE,CAAC;QACd,OAAO,CAAC,GAAG,EACR,uEAAuE;IAE5E,CAAC,MAAM,CAAC;QACN,OAAO,CAAC,GAAG,EAAE,2DAA2D;QACxE,OAAO,CAAC,GAAG,EAAE,4CAA4C;IAC3D,CAAC;IAED,OAAO,CAAC,GAAG,EAAE,wCAAwC;IACrD,OAAO,CAAC,GAAG;AACb,CAAC;QAEQ,aAAa,GAAb,aAAa"}