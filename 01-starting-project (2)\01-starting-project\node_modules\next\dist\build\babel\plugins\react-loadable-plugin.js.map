{"version": 3, "sources": ["../../../../build/babel/plugins/react-loadable-plugin.ts"], "sourcesContent": ["/**\nCOPYRIGHT (c) 2017-present <PERSON> <<EMAIL>>\n MIT License\n Permission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n The above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWAR\n*/\n// This file is https://github.com/jamiebuilds/react-loadable/blob/master/src/babel.js\n// Modified to also look for `next/dynamic`\n// Modified to put `webpack` and `modules` under `loadableGenerated` to be backwards compatible with next/dynamic which has a `modules` key\n// Modified to support `dynamic(import('something'))` and `dynamic(import('something'), options)\n\nimport {\n  NodePath,\n  PluginObj,\n  types as BabelTypes,\n} from 'next/dist/compiled/babel/core'\n\nimport { relative as relativePath } from 'path'\n\nexport default function ({\n  types: t,\n}: {\n  types: typeof BabelTypes\n}): PluginObj {\n  return {\n    visitor: {\n      ImportDeclaration(\n        path: NodePath<BabelTypes.ImportDeclaration>,\n        state: any\n      ) {\n        let source = path.node.source.value\n        if (source !== 'next/dynamic') return\n\n        let defaultSpecifier = path.get('specifiers').find((specifier) => {\n          return specifier.isImportDefaultSpecifier()\n        })\n\n        if (!defaultSpecifier) return\n\n        const bindingName = defaultSpecifier.node.local.name\n        const binding = path.scope.getBinding(bindingName)\n\n        if (!binding) {\n          return\n        }\n\n        binding.referencePaths.forEach((refPath) => {\n          let callExpression = refPath.parentPath\n\n          if (\n            callExpression.isMemberExpression() &&\n            callExpression.node.computed === false\n          ) {\n            const property = callExpression.get('property')\n            if (\n              !Array.isArray(property) &&\n              property.isIdentifier({ name: 'Map' })\n            ) {\n              callExpression = callExpression.parentPath\n            }\n          }\n\n          if (!callExpression.isCallExpression()) return\n\n          const callExpression_ =\n            callExpression as NodePath<BabelTypes.CallExpression>\n\n          let args = callExpression_.get('arguments')\n          if (args.length > 2) {\n            throw callExpression_.buildCodeFrameError(\n              'next/dynamic only accepts 2 arguments'\n            )\n          }\n\n          if (!args[0]) {\n            return\n          }\n\n          let loader\n          let options\n\n          if (args[0].isObjectExpression()) {\n            options = args[0]\n          } else {\n            if (!args[1]) {\n              callExpression_.node.arguments.push(t.objectExpression([]))\n            }\n            // This is needed as the code is modified above\n            args = callExpression_.get('arguments')\n            loader = args[0]\n            options = args[1]\n          }\n\n          if (!options.isObjectExpression()) return\n          const options_ = options as NodePath<BabelTypes.ObjectExpression>\n\n          let properties = options_.get('properties')\n          let propertiesMap: {\n            [key: string]: NodePath<\n              | BabelTypes.ObjectProperty\n              | BabelTypes.ObjectMethod\n              | BabelTypes.SpreadElement\n            >\n          } = {}\n\n          properties.forEach((property) => {\n            const key: any = property.get('key')\n            propertiesMap[key.node.name] = property\n          })\n\n          if (propertiesMap.loadableGenerated) {\n            return\n          }\n\n          if (propertiesMap.loader) {\n            loader = propertiesMap.loader.get('value')\n          }\n\n          if (propertiesMap.modules) {\n            loader = propertiesMap.modules.get('value')\n          }\n\n          if (!loader || Array.isArray(loader)) {\n            return\n          }\n          const dynamicImports: BabelTypes.Expression[] = []\n          const dynamicKeys: BabelTypes.Expression[] = []\n\n          loader.traverse({\n            Import(importPath) {\n              const importArguments = importPath.parentPath.get('arguments')\n              if (!Array.isArray(importArguments)) return\n              const node: any = importArguments[0].node\n              dynamicImports.push(node)\n              dynamicKeys.push(\n                t.binaryExpression(\n                  '+',\n                  t.stringLiteral(\n                    (state.file.opts.caller?.pagesDir\n                      ? relativePath(\n                          state.file.opts.caller.pagesDir,\n                          state.file.opts.filename\n                        )\n                      : state.file.opts.filename) + ' -> '\n                  ),\n                  node\n                )\n              )\n            },\n          })\n\n          if (!dynamicImports.length) return\n\n          options.node.properties.push(\n            t.objectProperty(\n              t.identifier('loadableGenerated'),\n              t.objectExpression([\n                t.objectProperty(\n                  t.identifier('webpack'),\n                  t.arrowFunctionExpression(\n                    [],\n                    t.arrayExpression(\n                      dynamicImports.map((dynamicImport) => {\n                        return t.callExpression(\n                          t.memberExpression(\n                            t.identifier('require'),\n                            t.identifier('resolveWeak')\n                          ),\n                          [dynamicImport]\n                        )\n                      })\n                    )\n                  )\n                ),\n                t.objectProperty(\n                  t.identifier('modules'),\n                  t.arrayExpression(dynamicKeys)\n                ),\n              ])\n            )\n          )\n\n          // Turns `dynamic(import('something'))` into `dynamic(() => import('something'))` for backwards compat.\n          // This is the replicate the behavior in versions below Next.js 7 where we magically handled not executing the `import()` too.\n          // We'll deprecate this behavior and provide a codemod for it in 7.1.\n          if (loader.isCallExpression()) {\n            const arrowFunction = t.arrowFunctionExpression([], loader.node)\n            loader.replaceWith(arrowFunction)\n          }\n        })\n      },\n    },\n  }\n}\n"], "names": [], "mappings": ";;;;;AA+ByC,GAAM,CAAN,KAAM;oBAG7C,KAAK,EAAE,CAAC,KAGI,CAAC;;QAEX,OAAO;YACL,iBAAiB,EACf,IAA4C,EAC5C,KAAU,EACV,CAAC;gBACD,GAAG,CAAC,MAAM,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK;gBACnC,EAAE,EAAE,MAAM,MAAK,YAAc;gBAE7B,GAAG,CAAC,gBAAgB,GAAG,IAAI,CAAC,GAAG,EAAC,UAAY,GAAE,IAAI,EAAE,SAAS,GAAK,CAAC;2BAC1D,SAAS,CAAC,wBAAwB;gBAC3C,CAAC;gBAED,EAAE,GAAG,gBAAgB;gBAErB,KAAK,CAAC,WAAW,GAAG,gBAAgB,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI;gBACpD,KAAK,CAAC,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,WAAW;gBAEjD,EAAE,GAAG,OAAO,EAAE,CAAC;;gBAEf,CAAC;gBAED,OAAO,CAAC,cAAc,CAAC,OAAO,EAAE,OAAO,GAAK,CAAC;oBAC3C,GAAG,CAAC,cAAc,GAAG,OAAO,CAAC,UAAU;oBAEvC,EAAE,EACA,cAAc,CAAC,kBAAkB,MACjC,cAAc,CAAC,IAAI,CAAC,QAAQ,KAAK,KAAK,EACtC,CAAC;wBACD,KAAK,CAAC,QAAQ,GAAG,cAAc,CAAC,GAAG,EAAC,QAAU;wBAC9C,EAAE,GACC,KAAK,CAAC,OAAO,CAAC,QAAQ,KACvB,QAAQ,CAAC,YAAY;4BAAG,IAAI,GAAE,GAAK;4BACnC,CAAC;4BACD,cAAc,GAAG,cAAc,CAAC,UAAU;wBAC5C,CAAC;oBACH,CAAC;oBAED,EAAE,GAAG,cAAc,CAAC,gBAAgB;oBAEpC,KAAK,CAAC,eAAe,GACnB,cAAc;oBAEhB,GAAG,CAAC,IAAI,GAAG,eAAe,CAAC,GAAG,EAAC,SAAW;oBAC1C,EAAE,EAAE,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;wBACpB,KAAK,CAAC,eAAe,CAAC,mBAAmB,EACvC,qCAAuC;oBAE3C,CAAC;oBAED,EAAE,GAAG,IAAI,CAAC,CAAC,GAAG,CAAC;;oBAEf,CAAC;oBAED,GAAG,CAAC,MAAM;oBACV,GAAG,CAAC,OAAO;oBAEX,EAAE,EAAE,IAAI,CAAC,CAAC,EAAE,kBAAkB,IAAI,CAAC;wBACjC,OAAO,GAAG,IAAI,CAAC,CAAC;oBAClB,CAAC,MAAM,CAAC;wBACN,EAAE,GAAG,IAAI,CAAC,CAAC,GAAG,CAAC;4BACb,eAAe,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC,gBAAgB;wBACxD,CAAC;wBACD,EAA+C,AAA/C,6CAA+C;wBAC/C,IAAI,GAAG,eAAe,CAAC,GAAG,EAAC,SAAW;wBACtC,MAAM,GAAG,IAAI,CAAC,CAAC;wBACf,OAAO,GAAG,IAAI,CAAC,CAAC;oBAClB,CAAC;oBAED,EAAE,GAAG,OAAO,CAAC,kBAAkB;oBAC/B,KAAK,CAAC,QAAQ,GAAG,OAAO;oBAExB,GAAG,CAAC,UAAU,GAAG,QAAQ,CAAC,GAAG,EAAC,UAAY;oBAC1C,GAAG,CAAC,aAAa;;oBAQjB,UAAU,CAAC,OAAO,EAAE,QAAQ,GAAK,CAAC;wBAChC,KAAK,CAAC,GAAG,GAAQ,QAAQ,CAAC,GAAG,EAAC,GAAK;wBACnC,aAAa,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,IAAI,QAAQ;oBACzC,CAAC;oBAED,EAAE,EAAE,aAAa,CAAC,iBAAiB,EAAE,CAAC;;oBAEtC,CAAC;oBAED,EAAE,EAAE,aAAa,CAAC,MAAM,EAAE,CAAC;wBACzB,MAAM,GAAG,aAAa,CAAC,MAAM,CAAC,GAAG,EAAC,KAAO;oBAC3C,CAAC;oBAED,EAAE,EAAE,aAAa,CAAC,OAAO,EAAE,CAAC;wBAC1B,MAAM,GAAG,aAAa,CAAC,OAAO,CAAC,GAAG,EAAC,KAAO;oBAC5C,CAAC;oBAED,EAAE,GAAG,MAAM,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC;;oBAEvC,CAAC;oBACD,KAAK,CAAC,cAAc;oBACpB,KAAK,CAAC,WAAW;oBAEjB,MAAM,CAAC,QAAQ;wBACb,MAAM,EAAC,UAAU,EAAE,CAAC;gCASX,GAAsB;4BAR7B,KAAK,CAAC,eAAe,GAAG,UAAU,CAAC,UAAU,CAAC,GAAG,EAAC,SAAW;4BAC7D,EAAE,GAAG,KAAK,CAAC,OAAO,CAAC,eAAe;4BAClC,KAAK,CAAC,IAAI,GAAQ,eAAe,CAAC,CAAC,EAAE,IAAI;4BACzC,cAAc,CAAC,IAAI,CAAC,IAAI;4BACxB,WAAW,CAAC,IAAI,CACd,CAAC,CAAC,gBAAgB,EAChB,CAAG,GACH,CAAC,CAAC,aAAa,IACZ,GAAsB,GAAtB,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,cAAtB,GAAsB,UAAtB,CAAgC,QAAhC,CAAgC,GAAhC,GAAsB,CAAE,QAAQ,QAzHZ,KAAM,WA2HrB,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,EAC/B,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,IAE1B,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,KAAI,IAAM,IAExC,IAAI;wBAGV,CAAC;;oBAGH,EAAE,GAAG,cAAc,CAAC,MAAM;oBAE1B,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,CAC1B,CAAC,CAAC,cAAc,CACd,CAAC,CAAC,UAAU,EAAC,iBAAmB,IAChC,CAAC,CAAC,gBAAgB;wBAChB,CAAC,CAAC,cAAc,CACd,CAAC,CAAC,UAAU,EAAC,OAAS,IACtB,CAAC,CAAC,uBAAuB,KAEvB,CAAC,CAAC,eAAe,CACf,cAAc,CAAC,GAAG,EAAE,aAAa,GAAK,CAAC;mCAC9B,CAAC,CAAC,cAAc,CACrB,CAAC,CAAC,gBAAgB,CAChB,CAAC,CAAC,UAAU,EAAC,OAAS,IACtB,CAAC,CAAC,UAAU,EAAC,WAAa;gCAE3B,aAAa;;wBAElB,CAAC;wBAIP,CAAC,CAAC,cAAc,CACd,CAAC,CAAC,UAAU,EAAC,OAAS,IACtB,CAAC,CAAC,eAAe,CAAC,WAAW;;oBAMrC,EAAuG,AAAvG,qGAAuG;oBACvG,EAA8H,AAA9H,4HAA8H;oBAC9H,EAAqE,AAArE,mEAAqE;oBACrE,EAAE,EAAE,MAAM,CAAC,gBAAgB,IAAI,CAAC;wBAC9B,KAAK,CAAC,aAAa,GAAG,CAAC,CAAC,uBAAuB,KAAK,MAAM,CAAC,IAAI;wBAC/D,MAAM,CAAC,WAAW,CAAC,aAAa;oBAClC,CAAC;gBACH,CAAC;YACH,CAAC;;;AAGP,CAAC"}