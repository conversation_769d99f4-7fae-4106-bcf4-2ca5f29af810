{"version": 3, "sources": ["../../cli/next-build.ts"], "sourcesContent": ["#!/usr/bin/env node\nimport { existsSync } from 'fs'\nimport arg from 'next/dist/compiled/arg/index.js'\nimport { resolve } from 'path'\nimport * as Log from '../build/output/log'\nimport { cliCommand } from '../bin/next'\nimport build from '../build'\nimport { printAndExit } from '../server/lib/utils'\n\nconst nextBuild: cliCommand = (argv) => {\n  const validArgs: arg.Spec = {\n    // Types\n    '--help': Boolean,\n    '--profile': Boolean,\n    '--debug': Boolean,\n    '--no-lint': Boole<PERSON>,\n    // Aliases\n    '-h': '--help',\n    '-d': '--debug',\n  }\n\n  let args: arg.Result<arg.Spec>\n  try {\n    args = arg(validArgs, { argv })\n  } catch (error) {\n    if (error.code === 'ARG_UNKNOWN_OPTION') {\n      return printAndExit(error.message, 1)\n    }\n    throw error\n  }\n  if (args['--help']) {\n    printAndExit(\n      `\n      Description\n        Compiles the application for production deployment\n\n      Usage\n        $ next build <dir>\n\n      <dir> represents the directory of the Next.js application.\n      If no directory is provided, the current directory will be used.\n\n      Options\n      --profile     Can be used to enable React Production Profiling\n      --no-lint     Disable linting\n    `,\n      0\n    )\n  }\n  if (args['--profile']) {\n    Log.warn('Profiling is enabled. Note: This may affect performance')\n  }\n  if (args['--no-lint']) {\n    Log.warn('Linting is disabled')\n  }\n  const dir = resolve(args._[0] || '.')\n\n  // Check if the provided directory exists\n  if (!existsSync(dir)) {\n    printAndExit(`> No such directory exists as the project root: ${dir}`)\n  }\n\n  return build(\n    dir,\n    null,\n    args['--profile'],\n    args['--debug'],\n    !args['--no-lint']\n  ).catch((err) => {\n    console.error('')\n    console.error('> Build error occurred')\n    printAndExit(err)\n  })\n}\n\nexport { nextBuild }\n"], "names": [], "mappings": ";;;;;;AAC2B,GAAI,CAAJ,GAAI;AACf,GAAiC,CAAjC,QAAiC;AACzB,GAAM,CAAN,KAAM;AAClB,GAAG,CAAH,GAAG;AAEG,GAAU,CAAV,MAAU;AACC,GAAqB,CAArB,MAAqB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAElD,KAAK,CAAC,SAAS,IAAgB,IAAI,GAAK,CAAC;IACvC,KAAK,CAAC,SAAS;QACb,EAAQ,AAAR,MAAQ;SACR,MAAQ,GAAE,OAAO;SACjB,SAAW,GAAE,OAAO;SACpB,OAAS,GAAE,OAAO;SAClB,SAAW,GAAE,OAAO;QACpB,EAAU,AAAV,QAAU;SACV,EAAI,IAAE,MAAQ;SACd,EAAI,IAAE,OAAS;;IAGjB,GAAG,CAAC,IAAI;QACJ,CAAC;QACH,IAAI,OArBQ,QAAiC,UAqBlC,SAAS;YAAI,IAAI;;IAC9B,CAAC,QAAQ,KAAK,EAAE,CAAC;QACf,EAAE,EAAE,KAAK,CAAC,IAAI,MAAK,kBAAoB,GAAE,CAAC;uBAlBjB,MAAqB,eAmBxB,KAAK,CAAC,OAAO,EAAE,CAAC;QACtC,CAAC;QACD,KAAK,CAAC,KAAK;IACb,CAAC;IACD,EAAE,EAAE,IAAI,EAAC,MAAQ,IAAG,CAAC;YAvBM,MAAqB,gBAyB3C,2XAaH,GACE,CAAC;IAEL,CAAC;IACD,EAAE,EAAE,IAAI,EAAC,SAAW,IAAG,CAAC;QA7Cd,GAAG,CA8CP,IAAI,EAAC,uDAAyD;IACpE,CAAC;IACD,EAAE,EAAE,IAAI,EAAC,SAAW,IAAG,CAAC;QAhDd,GAAG,CAiDP,IAAI,EAAC,mBAAqB;IAChC,CAAC;IACD,KAAK,CAAC,GAAG,OApDa,KAAM,UAoDR,IAAI,CAAC,CAAC,CAAC,CAAC,MAAK,CAAG;IAEpC,EAAyC,AAAzC,uCAAyC;IACzC,EAAE,OAzDuB,GAAI,aAyDb,GAAG,GAAG,CAAC;YAnDI,MAAqB,gBAoDhC,gDAAgD,EAAE,GAAG;IACrE,CAAC;eAtDe,MAAU,UAyDxB,GAAG,EACH,IAAI,EACJ,IAAI,EAAC,SAAW,IAChB,IAAI,EAAC,OAAS,KACb,IAAI,EAAC,SAAW,IACjB,KAAK,EAAE,GAAG,GAAK,CAAC;QAChB,OAAO,CAAC,KAAK;QACb,OAAO,CAAC,KAAK,EAAC,sBAAwB;YA/Db,MAAqB,eAgEjC,GAAG;IAClB,CAAC;AACH,CAAC;QAEQ,SAAS,GAAT,SAAS"}