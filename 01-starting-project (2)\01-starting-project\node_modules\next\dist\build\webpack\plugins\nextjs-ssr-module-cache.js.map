{"version": 3, "sources": ["../../../../build/webpack/plugins/nextjs-ssr-module-cache.ts"], "sourcesContent": ["import { webpack, sources } from 'next/dist/compiled/webpack/webpack'\nimport { join, relative, dirname } from 'path'\nimport getRouteFromEntrypoint from '../../../server/get-route-from-entrypoint'\nconst SSR_MODULE_CACHE_FILENAME = 'ssr-module-cache.js'\n\n// By default webpack keeps initialized modules per-module.\n// This means that if you have 2 entrypoints loaded into the same app\n// they will *not* share the same instance\n// This creates many issues when developers / libraries rely on the singleton pattern\n// As this pattern assumes every module will have 1 instance\n// This plugin overrides webpack's code generation step to replace `installedModules`\n// The replacement is a require for a file that's also generated here that only exports an empty object\n// Because of Node.js's single instance modules this makes webpack share all initialized instances\n// Do note that this module is only geared towards the `node` compilation target.\n// For the client side compilation we use `runtimeChunk: 'single'`\nexport default class NextJsSsrImportPlugin {\n  private options: { outputPath: string }\n\n  constructor(options: { outputPath: string }) {\n    this.options = options\n  }\n  apply(compiler: webpack.Compiler) {\n    const { outputPath } = this.options\n    compiler.hooks.emit.tapAsync(\n      'NextJsSSRModuleCache',\n      (compilation, callback) => {\n        compilation.assets[SSR_MODULE_CACHE_FILENAME] = new sources.RawSource(`\n      /* This cache is used by webpack for instantiated modules */\n      module.exports = {}\n      `)\n        callback()\n      }\n    )\n    compiler.hooks.compilation.tap(\n      'NextJsSSRModuleCache',\n      (compilation: any) => {\n        compilation.mainTemplate.hooks.localVars.intercept({\n          register(tapInfo: any) {\n            if (tapInfo.name === 'MainTemplate') {\n              const originalFn = tapInfo.fn\n              tapInfo.fn = (source: any, chunk: any) => {\n                // If the chunk is not part of the pages directory we have to keep the original behavior,\n                // otherwise webpack will error out when the file is used before the compilation finishes\n                // this is the case with mini-css-extract-plugin\n\n                if (!getRouteFromEntrypoint(chunk.name)) {\n                  return originalFn(source, chunk)\n                }\n                const pagePath = join(outputPath, dirname(chunk.name))\n                let relativePathToBaseDir = relative(\n                  pagePath,\n                  join(outputPath, SSR_MODULE_CACHE_FILENAME)\n                )\n\n                // Make sure even in windows, the path looks like in unix\n                // Node.js require system will convert it accordingly\n                const relativePathToBaseDirNormalized =\n                  relativePathToBaseDir.replace(/\\\\/g, '/')\n                return (webpack as any).Template.asString([\n                  source,\n                  '// The module cache',\n                  `var installedModules = require('${relativePathToBaseDirNormalized}');`,\n                ])\n              }\n            }\n            return tapInfo\n          },\n        })\n      }\n    )\n  }\n}\n"], "names": [], "mappings": ";;;;;AAAiC,GAAoC,CAApC,QAAoC;AAC7B,GAAM,CAAN,KAAM;AACX,GAA2C,CAA3C,uBAA2C;;;;;;AAC9E,KAAK,CAAC,yBAAyB,IAAG,mBAAqB;MAYlC,qBAAqB;gBAG5B,OAA+B,CAAE,CAAC;aACvC,OAAO,GAAG,OAAO;IACxB,CAAC;IACD,KAAK,CAAC,QAA0B,EAAE,CAAC;QACjC,KAAK,GAAG,UAAU,WAAU,OAAO;QACnC,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,QAAQ,EAC1B,oBAAsB,IACrB,WAAW,EAAE,QAAQ,GAAK,CAAC;YAC1B,WAAW,CAAC,MAAM,CAAC,yBAAyB,IAAI,GAAG,CA1B1B,QAAoC,SA0BD,SAAS,EAAE,oGAGzE;YACE,QAAQ;QACV,CAAC;QAEH,QAAQ,CAAC,KAAK,CAAC,WAAW,CAAC,GAAG,EAC5B,oBAAsB,IACrB,WAAgB,GAAK,CAAC;YACrB,WAAW,CAAC,YAAY,CAAC,KAAK,CAAC,SAAS,CAAC,SAAS;gBAChD,QAAQ,EAAC,OAAY,EAAE,CAAC;oBACtB,EAAE,EAAE,OAAO,CAAC,IAAI,MAAK,YAAc,GAAE,CAAC;wBACpC,KAAK,CAAC,UAAU,GAAG,OAAO,CAAC,EAAE;wBAC7B,OAAO,CAAC,EAAE,IAAI,MAAW,EAAE,KAAU,GAAK,CAAC;4BACzC,EAAyF,AAAzF,uFAAyF;4BACzF,EAAyF,AAAzF,uFAAyF;4BACzF,EAAgD,AAAhD,8CAAgD;4BAEhD,EAAE,OA3CiB,uBAA2C,UA2ClC,KAAK,CAAC,IAAI,GAAG,CAAC;uCACjC,UAAU,CAAC,MAAM,EAAE,KAAK;4BACjC,CAAC;4BACD,KAAK,CAAC,QAAQ,OA/CU,KAAM,OA+CR,UAAU,MA/CR,KAAM,UA+CY,KAAK,CAAC,IAAI;4BACpD,GAAG,CAAC,qBAAqB,OAhDD,KAAM,WAiD5B,QAAQ,MAjDc,KAAM,OAkDvB,UAAU,EAAE,yBAAyB;4BAG5C,EAAyD,AAAzD,uDAAyD;4BACzD,EAAqD,AAArD,mDAAqD;4BACrD,KAAK,CAAC,+BAA+B,GACnC,qBAAqB,CAAC,OAAO,SAAQ,CAAG;mCAzDzB,QAAoC,SA0D7B,QAAQ,CAAC,QAAQ;gCACvC,MAAM;iCACN,mBAAqB;iCACpB,gCAAgC,EAAE,+BAA+B,CAAC,GAAG;;wBAE1E,CAAC;oBACH,CAAC;2BACM,OAAO;gBAChB,CAAC;;QAEL,CAAC;IAEL,CAAC;;kBAvDkB,qBAAqB"}