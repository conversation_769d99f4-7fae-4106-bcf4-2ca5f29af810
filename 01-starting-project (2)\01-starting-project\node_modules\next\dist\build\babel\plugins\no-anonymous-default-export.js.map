{"version": 3, "sources": ["../../../../build/babel/plugins/no-anonymous-default-export.ts"], "sourcesContent": ["import { PluginObj, types as BabelTypes } from 'next/dist/compiled/babel/core'\nimport chalk from 'chalk'\n\nexport default function NoAnonymousDefaultExport({\n  types: t,\n  ...babel\n}: {\n  types: typeof BabelTypes\n  caller: (callerCallback: (caller: any) => any) => any\n}): PluginObj<any> {\n  let onWarning: ((reason: string | Error) => void) | null = null\n  babel.caller((caller) => {\n    onWarning = caller.onWarning\n    return '' // Intentionally empty to not invalidate cache\n  })\n\n  if (typeof onWarning !== 'function') {\n    return { visitor: {} }\n  }\n\n  const warn: any = onWarning\n  return {\n    visitor: {\n      ExportDefaultDeclaration(path) {\n        const def = path.node.declaration\n\n        if (\n          !(\n            def.type === 'ArrowFunctionExpression' ||\n            def.type === 'FunctionDeclaration'\n          )\n        ) {\n          return\n        }\n\n        switch (def.type) {\n          case 'ArrowFunctionExpression': {\n            warn(\n              [\n                chalk.yellow.bold(\n                  'Anonymous arrow functions cause Fast Refresh to not preserve local component state.'\n                ),\n                'Please add a name to your function, for example:',\n                '',\n                chalk.bold('Before'),\n                chalk.cyan('export default () => <div />;'),\n                '',\n                chalk.bold('After'),\n                chalk.cyan('const Named = () => <div />;'),\n                chalk.cyan('export default Named;'),\n                '',\n                `A codemod is available to fix the most common cases: ${chalk.cyan(\n                  'https://nextjs.link/codemod-ndc'\n                )}`,\n              ].join('\\n')\n            )\n            break\n          }\n          case 'FunctionDeclaration': {\n            const isAnonymous = !Boolean(def.id)\n            if (isAnonymous) {\n              warn(\n                [\n                  chalk.yellow.bold(\n                    'Anonymous function declarations cause Fast Refresh to not preserve local component state.'\n                  ),\n                  'Please add a name to your function, for example:',\n                  '',\n                  chalk.bold('Before'),\n                  chalk.cyan('export default function () { /* ... */ }'),\n                  '',\n                  chalk.bold('After'),\n                  chalk.cyan('export default function Named() { /* ... */ }'),\n                  '',\n                  `A codemod is available to fix the most common cases: ${chalk.cyan(\n                    'https://nextjs.link/codemod-ndc'\n                  )}`,\n                ].join('\\n')\n              )\n            }\n            break\n          }\n          default: {\n            // eslint-disable-next-line @typescript-eslint/no-unused-vars\n            const _: never = def\n          }\n        }\n      },\n    },\n  }\n}\n"], "names": [], "mappings": ";;;;kBAGwB,wBAAwB;AAF9B,GAAO,CAAP,MAAO;;;;;;SAED,wBAAwB,GAC9C,KAAK,EAAE,CAAC,MACL,KAAK,IAIS,CAAC;IAClB,GAAG,CAAC,SAAS,GAA8C,IAAI;IAC/D,KAAK,CAAC,MAAM,EAAE,MAAM,GAAK,CAAC;QACxB,SAAS,GAAG,MAAM,CAAC,SAAS;iBAClB,CAA8C,AAA9C,EAA8C,AAA9C,4CAA8C;;IAC1D,CAAC;IAED,EAAE,SAAS,SAAS,MAAK,QAAU,GAAE,CAAC;;YAC3B,OAAO;;;IAClB,CAAC;IAED,KAAK,CAAC,IAAI,GAAQ,SAAS;;QAEzB,OAAO;YACL,wBAAwB,EAAC,IAAI,EAAE,CAAC;gBAC9B,KAAK,CAAC,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,WAAW;gBAEjC,EAAE,IAEE,GAAG,CAAC,IAAI,MAAK,uBAAyB,KACtC,GAAG,CAAC,IAAI,MAAK,mBAAqB,IAEpC,CAAC;;gBAEH,CAAC;uBAEO,GAAG,CAAC,IAAI;0BACT,uBAAyB;wBAAE,CAAC;4BAC/B,IAAI;gCApCE,MAAO,SAsCH,MAAM,CAAC,IAAI,EACf,mFAAqF;iCAEvF,gDAAkD;;gCAzChD,MAAO,SA2CH,IAAI,EAAC,MAAQ;gCA3CjB,MAAO,SA4CH,IAAI,EAAC,6BAA+B;;gCA5CxC,MAAO,SA8CH,IAAI,EAAC,KAAO;gCA9ChB,MAAO,SA+CH,IAAI,EAAC,4BAA8B;gCA/CvC,MAAO,SAgDH,IAAI,EAAC,qBAAuB;;iCAEjC,qDAAqD,EAlDpD,MAAO,SAkDqD,IAAI,EAChE,+BAAiC;8BAEnC,IAAI,EAAC,EAAI;;wBAGf,CAAC;0BACI,mBAAqB;wBAAE,CAAC;4BAC3B,KAAK,CAAC,WAAW,IAAI,OAAO,CAAC,GAAG,CAAC,EAAE;4BACnC,EAAE,EAAE,WAAW,EAAE,CAAC;gCAChB,IAAI;oCA5DA,MAAO,SA8DD,MAAM,CAAC,IAAI,EACf,yFAA2F;qCAE7F,gDAAkD;;oCAjElD,MAAO,SAmED,IAAI,EAAC,MAAQ;oCAnEnB,MAAO,SAoED,IAAI,EAAC,wCAA0C;;oCApErD,MAAO,SAsED,IAAI,EAAC,KAAO;oCAtElB,MAAO,SAuED,IAAI,EAAC,6CAA+C;;qCAEzD,qDAAqD,EAzEtD,MAAO,SAyEuD,IAAI,EAChE,+BAAiC;kCAEnC,IAAI,EAAC,EAAI;4BAEf,CAAC;;wBAEH,CAAC;;wBACQ,CAAC;4BACR,EAA6D,AAA7D,2DAA6D;4BAC7D,KAAK,CAAC,CAAC,GAAU,GAAG;wBACtB,CAAC;;YAEL,CAAC;;;AAGP,CAAC"}