{"version": 3, "sources": ["../../../../../build/webpack/plugins/wellknown-errors-plugin/parseNotFoundError.ts"], "sourcesContent": ["import Chalk from 'chalk'\nimport { SimpleWebpackError } from './simpleWebpackError'\nimport { createOriginalStackFrame } from '@next/react-dev-overlay/lib/middleware'\nimport { isWebpack5 } from 'next/dist/compiled/webpack/webpack'\nimport path from 'path'\n\nconst chalk = new Chalk.constructor({ enabled: true })\n\n// Based on https://github.com/webpack/webpack/blob/fcdd04a833943394bbb0a9eeb54a962a24cc7e41/lib/stats/DefaultStatsFactoryPlugin.js#L422-L431\n/*\nCopyright JS Foundation and other contributors\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n'Software'), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED 'AS IS', WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT.\nIN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY\nCLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT,\nTORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE\nSOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n\n*/\nfunction getModuleTrace(input: any, compilation: any) {\n  const visitedModules = new Set()\n  const moduleTrace = []\n  let current = input.module\n  while (current) {\n    if (visitedModules.has(current)) break // circular (technically impossible, but who knows)\n    visitedModules.add(current)\n    const origin = compilation.moduleGraph.getIssuer(current)\n    if (!origin) break\n    moduleTrace.push({ origin, module: current })\n    current = origin\n  }\n\n  return moduleTrace\n}\n\nexport async function getNotFoundError(\n  compilation: any,\n  input: any,\n  fileName: string\n) {\n  if (input.name !== 'ModuleNotFoundError') {\n    return false\n  }\n\n  const loc = input.loc\n    ? input.loc\n    : input.dependencies.map((d: any) => d.loc).filter(Boolean)[0]\n  const originalSource = input.module.originalSource()\n\n  try {\n    const result = await createOriginalStackFrame({\n      line: loc.start.line,\n      column: loc.start.column,\n      source: originalSource,\n      rootDirectory: compilation.options.context,\n      frame: {},\n    })\n\n    // If we could not result the original location we still need to show the existing error\n    if (!result) {\n      return input\n    }\n\n    const errorMessage = input.error.message\n      .replace(/ in '.*?'/, '')\n      .replace(/Can't resolve '(.*)'/, `Can't resolve '${chalk.green('$1')}'`)\n\n    const importTrace = () => {\n      if (!isWebpack5) {\n        return ''\n      }\n\n      let importTraceLine = '\\nImport trace for requested module:\\n'\n      const moduleTrace = getModuleTrace(input, compilation)\n\n      for (const { origin } of moduleTrace) {\n        if (!origin.resource) {\n          continue\n        }\n        const filePath = path.relative(\n          compilation.options.context,\n          origin.resource\n        )\n        importTraceLine += `./${filePath}\\n`\n      }\n\n      return importTraceLine + '\\n'\n    }\n\n    const frame = result.originalCodeFrame ?? ''\n\n    const message =\n      chalk.red.bold('Module not found') +\n      `: ${errorMessage}` +\n      '\\n' +\n      frame +\n      (frame !== '' ? '\\n' : '') +\n      importTrace() +\n      '\\nhttps://nextjs.org/docs/messages/module-not-found'\n\n    return new SimpleWebpackError(\n      `${chalk.cyan(fileName)}:${chalk.yellow(\n        result.originalStackFrame.lineNumber?.toString() ?? ''\n      )}:${chalk.yellow(result.originalStackFrame.column?.toString() ?? '')}`,\n      message\n    )\n  } catch (err) {\n    console.log('Failed to parse source map:', err)\n    // Don't fail on failure to resolve sourcemaps\n    return input\n  }\n}\n"], "names": [], "mappings": ";;;;QAgDsB,gBAAgB,GAAhB,gBAAgB;AAhDpB,GAAO,CAAP,MAAO;AACU,GAAsB,CAAtB,mBAAsB;AAChB,GAAwC,CAAxC,WAAwC;AACtD,GAAoC,CAApC,QAAoC;AAC9C,GAAM,CAAN,KAAM;;;;;;AAEvB,KAAK,CAAC,KAAK,GAAG,GAAG,CANC,MAAO,SAMD,WAAW;IAAG,OAAO,EAAE,IAAI;;AAEnD,EAA6I,AAA7I,2IAA6I;AAC7I,EAsBE,AAtBF;;;;;;;;;;;;;;;;;;;;;;AAsBE,AAtBF,EAsBE,UACO,cAAc,CAAC,KAAU,EAAE,WAAgB,EAAE,CAAC;IACrD,KAAK,CAAC,cAAc,GAAG,GAAG,CAAC,GAAG;IAC9B,KAAK,CAAC,WAAW;IACjB,GAAG,CAAC,OAAO,GAAG,KAAK,CAAC,MAAM;UACnB,OAAO,CAAE,CAAC;QACf,EAAE,EAAE,cAAc,CAAC,GAAG,CAAC,OAAO,SAAS,CAAmD,AAAnD,EAAmD,AAAnD,iDAAmD;QAC1F,cAAc,CAAC,GAAG,CAAC,OAAO;QAC1B,KAAK,CAAC,MAAM,GAAG,WAAW,CAAC,WAAW,CAAC,SAAS,CAAC,OAAO;QACxD,EAAE,GAAG,MAAM;QACX,WAAW,CAAC,IAAI;YAAG,MAAM;YAAE,MAAM,EAAE,OAAO;;QAC1C,OAAO,GAAG,MAAM;IAClB,CAAC;WAEM,WAAW;AACpB,CAAC;eAEqB,gBAAgB,CACpC,WAAgB,EAChB,KAAU,EACV,QAAgB,EAChB,CAAC;IACD,EAAE,EAAE,KAAK,CAAC,IAAI,MAAK,mBAAqB,GAAE,CAAC;eAClC,KAAK;IACd,CAAC;IAED,KAAK,CAAC,GAAG,GAAG,KAAK,CAAC,GAAG,GACjB,KAAK,CAAC,GAAG,GACT,KAAK,CAAC,YAAY,CAAC,GAAG,EAAE,CAAM,GAAK,CAAC,CAAC,GAAG;MAAE,MAAM,CAAC,OAAO,EAAE,CAAC;IAC/D,KAAK,CAAC,cAAc,GAAG,KAAK,CAAC,MAAM,CAAC,cAAc;QAE9C,CAAC;YAqDC,GAAoC,EACpB,IAAgC;QArDpD,KAAK,CAAC,MAAM,aA7DyB,WAAwC;YA8D3E,IAAI,EAAE,GAAG,CAAC,KAAK,CAAC,IAAI;YACpB,MAAM,EAAE,GAAG,CAAC,KAAK,CAAC,MAAM;YACxB,MAAM,EAAE,cAAc;YACtB,aAAa,EAAE,WAAW,CAAC,OAAO,CAAC,OAAO;YAC1C,KAAK;;;QAGP,EAAwF,AAAxF,sFAAwF;QACxF,EAAE,GAAG,MAAM,EAAE,CAAC;mBACL,KAAK;QACd,CAAC;QAED,KAAK,CAAC,YAAY,GAAG,KAAK,CAAC,KAAK,CAAC,OAAO,CACrC,OAAO,kBACP,OAAO,0BAA0B,eAAe,EAAE,KAAK,CAAC,KAAK,EAAC,EAAI,GAAE,CAAC;QAExE,KAAK,CAAC,WAAW,OAAS,CAAC;YACzB,EAAE,GA9EmB,QAAoC,aA8ExC,CAAC;;YAElB,CAAC;YAED,GAAG,CAAC,eAAe,IAAG,sCAAwC;YAC9D,KAAK,CAAC,WAAW,GAAG,cAAc,CAAC,KAAK,EAAE,WAAW;iBAEhD,KAAK,GAAG,MAAM,OAAM,WAAW,CAAE,CAAC;gBACrC,EAAE,GAAG,MAAM,CAAC,QAAQ,EAAE,CAAC;;gBAEvB,CAAC;gBACD,KAAK,CAAC,QAAQ,GAxFL,KAAM,SAwFO,QAAQ,CAC5B,WAAW,CAAC,OAAO,CAAC,OAAO,EAC3B,MAAM,CAAC,QAAQ;gBAEjB,eAAe,KAAK,EAAE,EAAE,QAAQ,CAAC,EAAE;YACrC,CAAC;mBAEM,eAAe,IAAG,EAAI;QAC/B,CAAC;YAEa,kBAAwB;QAAtC,KAAK,CAAC,KAAK,IAAG,kBAAwB,GAAxB,MAAM,CAAC,iBAAiB,cAAxB,kBAAwB,cAAxB,kBAAwB;QAEtC,KAAK,CAAC,OAAO,GACX,KAAK,CAAC,GAAG,CAAC,IAAI,EAAC,gBAAkB,MAChC,EAAE,EAAE,YAAY,MACjB,EAAI,IACJ,KAAK,IACJ,KAAK,WAAU,EAAI,UACpB,WAAW,MACX,mDAAqD;YAInD,IAAgD,EAChC,IAA4C;eAHzD,GAAG,CAhHqB,mBAAsB,uBAiHhD,KAAK,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC,EAAE,KAAK,CAAC,MAAM,EACrC,IAAgD,IAAhD,GAAoC,GAApC,MAAM,CAAC,kBAAkB,CAAC,UAAU,cAApC,GAAoC,UAApC,CAA8C,QAA9C,CAA8C,GAA9C,GAAoC,CAAE,QAAQ,gBAA9C,IAAgD,cAAhD,IAAgD,OAChD,CAAC,EAAE,KAAK,CAAC,MAAM,EAAC,IAA4C,IAA5C,IAAgC,GAAhC,MAAM,CAAC,kBAAkB,CAAC,MAAM,cAAhC,IAAgC,UAAhC,CAA0C,QAA1C,CAA0C,GAA1C,IAAgC,CAAE,QAAQ,gBAA1C,IAA4C,cAA5C,IAA4C,UAC9D,OAAO;IAEX,CAAC,QAAQ,GAAG,EAAE,CAAC;QACb,OAAO,CAAC,GAAG,EAAC,2BAA6B,GAAE,GAAG;QAC9C,EAA8C,AAA9C,4CAA8C;eACvC,KAAK;IACd,CAAC;AACH,CAAC"}